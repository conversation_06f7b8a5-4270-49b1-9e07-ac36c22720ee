package com.ecco.dom.contacts;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import lombok.*;
import org.jspecify.annotations.NonNull;

import org.jspecify.annotations.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "addresshistory")
@Builder
@AllArgsConstructor
public class AddressHistory extends AbstractIntKeyedEntity implements HistoryItem {

    private static final long serialVersionUID = 1L;

    /**
     * The id of the service recipient
     */
    @Column(name = "serviceRecipientId", nullable = false)
    private int serviceRecipientId;

    /**
     * Contact who has the address - for easier access
     */
    @Nullable
    private Long contactId;

    // set on SRAddressLocationChange command
    @Nullable
    private Integer addressLocationId;

    // set on SRAddressLocationChange command
    @Nullable
    private Integer buildingLocationId;

    @NonNull
    @Column(name = "validFrom", nullable = false)
    public LocalDateTime validFrom;

    @Column(name = "validTo")
    public LocalDateTime validTo;

    public AddressHistory() {
    }

    public AddressHistory(int serviceRecipientId, @NonNull LocalDateTime validFrom, @Nullable Long contactId) {
        this.serviceRecipientId = serviceRecipientId;
        this.validFrom = validFrom;
        this.contactId = contactId;
    }

}
