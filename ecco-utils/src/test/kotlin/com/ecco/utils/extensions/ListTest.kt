package com.ecco.utils.extensions

import kotlin.test.*

class ListTest {
    @Test
    fun transposeEmptyList() {
        assertEquals(emptyList(), listOf<List<Nothing>>().transpose().toList())
    }

    @Test
    fun transposeOneEmptyRow() {
        assertEquals(emptyList(), listOf(listOf<Nothing>()).transpose().toList())
    }

    @Test
    fun transposeTwoEmptyRows() {
        assertEquals(emptyList(), listOf(listOf<Nothing>(), listOf()).transpose().toList())
    }

    @Test
    fun transposeOneItem() {
        assertEquals(listOf(listOf(1)), listOf(listOf(1)).transpose().toList())
    }

    @Test
    fun transposeOneRow() {
        assertEquals(
            listOf(listOf(1), listOf(2), listOf(3), listOf(4), listOf(5)),
            listOf(listOf(1, 2, 3, 4, 5)).transpose().toList(),
        )
    }

    @Test
    fun transposeOneColumn() {
        assertEquals(
            listOf(listOf(1, 2, 3, 4, 5)),
            listOf(listOf(1), listOf(2), listOf(3), listOf(4), listOf(5)).transpose().toList(),
        )
    }

    @Test
    fun transposeMatrix() {
        assertEquals(
            listOf(listOf(1, 4, 7), listOf(2, 5, 8), listOf(3, 6, 9)),
            listOf(listOf(1, 2, 3), listOf(4, 5, 6), listOf(7, 8, 9)).transpose().toList(),
        )
    }

    @Test
    fun transposeUnbalancedTopHeavy() {
        assertEquals(
            listOf(listOf(1, 4, 6), listOf(2, 5, null), listOf(3, null, null)),
            listOf(listOf(1, 2, 3), listOf(4, 5), listOf(6)).transpose().toList(),
        )
    }

    @Test
    fun transposeUnbalancedBottomHeavy() {
        assertEquals(
            listOf(listOf(1, 2, 4), listOf(null, 3, 5), listOf(null, null, 6)),
            listOf(listOf(1), listOf(2, 3), listOf(4, 5, 6)).transpose().toList(),
        )
    }
}