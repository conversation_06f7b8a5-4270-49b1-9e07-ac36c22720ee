/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-buildings"))
    implementation(project(":ecco-calendar"))
    implementation(project(":ecco-calendar-cosmo"))
    implementation(project(":ecco-config"))
    implementation(project(":ecco-contacts"))
    implementation(project(":ecco-contracts"))
    implementation(project(":ecco-dao"))
    implementation(project(":ecco-dom"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-finance"))
    implementation(project(":ecco-hr"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-service"))
    implementation(project(":ecco-service-config"))

//    testImplementation(project(":ecco-service-config"))
    testImplementation(project(":ecco-security-core"))
    testImplementation(project(":ecco-upload-web")) // Should this be api() from ecco-service for UploadConfig?
    testImplementation(project(":test-support"))

    api("org.optaplanner:optaplanner-core:7.39.0.Final")
    implementation("org.springframework:spring-webmvc")
    implementation("org.springframework:spring-web")
    implementation("org.springframework.hateoas:spring-hateoas")
    implementation("com.fasterxml.jackson.core:jackson-core")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-joda")
    implementation("com.google.guava:guava")
}

description = "ecco-rota"
