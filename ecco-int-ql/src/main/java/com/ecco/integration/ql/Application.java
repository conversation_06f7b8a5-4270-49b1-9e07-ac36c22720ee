package com.ecco.integration.ql;


import com.ecco.integration.core.AfterStartup;
import com.ecco.integration.core.WebSecurityConfigurer;
import com.ecco.integration.core.WebSocketTunnelConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.util.StringUtils;

@Configuration
@ComponentScan
@EnableAutoConfiguration
@EnableConfigurationProperties({QLSettings.class})
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
@PropertySource("classpath:git.properties")
@Import({WebSecurityConfigurer.class, WebSocketTunnelConfiguration.class})
public class Application {

    @Autowired
    private Environment env;

    @Bean
    public AfterStartup afterStartup(QLClientQueryAdapter queryAdapter) {
        return new AfterStartup(queryAdapter::queryClientsByExample);
    }

    @Bean
    public PropertiesFactoryBean referenceDataMapping() {
        // NB using spring.config.location is an alternative, but that expects all files in the new location
        // so perhaps look into a @Configuration and @PropertySource
        var file = env.getProperty("mapping.location");
        if (StringUtils.hasText(file)) {
            PropertiesFactoryBean bean = new PropertiesFactoryBean();
            bean.setLocation(new FileSystemResource(file));
            return bean;
        } else {
            PropertiesFactoryBean bean = new PropertiesFactoryBean();
            bean.setLocation(new ClassPathResource("/com/ecco/integration/ql/mapping.properties"));
            return bean;
        }
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
