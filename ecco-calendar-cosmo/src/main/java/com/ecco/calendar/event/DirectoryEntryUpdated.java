package com.ecco.calendar.event;

import org.springframework.context.ApplicationEvent;

import java.net.URI;

/**
 * Event fired when a directory entry is updated, allowing all iCalendar CAL-ADDRESS properties referencing that directory
 * entry to also be updated.
 * This event is used to keep ATTENDEE properties in sync with calendar owners, for example.
 */
public class DirectoryEntryUpdated extends ApplicationEvent {
    private final URI directoryEntryReference;
    private final String commonName;
    private final String email;

    public DirectoryEntryUpdated(Object source, URI directoryEntryReference, String commonName, String email) {
        super(source);
        this.directoryEntryReference = directoryEntryReference;
        this.commonName = commonName;
        this.email = email;
    }

    public URI getDirectoryEntryReference() {
        return directoryEntryReference;
    }

    public String getCommonName() {
        return commonName;
    }

    public String getEmail() {
        return email;
    }
}
