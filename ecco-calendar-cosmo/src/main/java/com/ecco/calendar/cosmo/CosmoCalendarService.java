package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.*;
import com.ecco.dom.contacts.Contact;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.infrastructure.hibernate.AntiProxyUtils;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import com.ecco.calendar.dom.EventEntryDefinition;
import net.fortuna.ical4j.model.Component;
import net.fortuna.ical4j.model.ComponentList;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.component.Available;
import net.fortuna.ical4j.model.component.VAvailability;
import net.fortuna.ical4j.model.parameter.Value;
import net.fortuna.ical4j.model.property.Priority;
import net.fortuna.ical4j.util.Dates;
import org.joda.time.*;
import org.jspecify.annotations.NonNull;
import org.osaf.cosmo.calendar.EntityConverter;
import org.osaf.cosmo.calendar.query.CalendarFilter;
import org.osaf.cosmo.calendar.query.CalendarFilterEvaluater;
import org.osaf.cosmo.calendar.query.ComponentFilter;
import org.osaf.cosmo.calendar.query.TimeRangeFilter;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.filter.EventStampFilter;
import org.osaf.cosmo.model.filter.NoteItemFilter;
import org.osaf.cosmo.model.hibernate.HibCalendarCollectionStamp;
import org.osaf.cosmo.model.hibernate.HibCollectionItem;
import org.osaf.cosmo.model.hibernate.HibNoteItem;
import org.osaf.cosmo.model.hibernate.HibUser;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.dao.DataAccessException;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.osaf.cosmo.icalendar.ICalendarConstants.COMPONENT_VAVAILABLITY;

@WriteableTransaction
public class CosmoCalendarService extends CosmoCalendarBase implements CalendarService {

    private static final Logger log = LoggerFactory.getLogger(CosmoCalendarService.class);
    public static final String SYSTEM_COLLECTION = "SystemCollection";
    private CosmoConverter converter;
    private final UserService userService;
    private final CalendarRecurringService recurringService;

    /** For CGLIB only */
    public CosmoCalendarService() {
        this(null, null, null, null, null, null);
    }

    @PersistenceContext
    private EntityManager em;

    public CosmoCalendarService(ContentService contentService, UserService userService,
                                CalendarRecurringService recurringService,
                                EntityUriMapper entityUriMapper, MessageBus<ApplicationEvent> messageBus,
                                final CosmoConverter entryConverter) {
        super(contentService, userService, entityUriMapper, messageBus);
        this.converter = entryConverter;
        this.userService = userService;
        this.recurringService = recurringService;
    }

    /**
     * Get the calendarId of the owner of the item. This is so that when we clone the entry, we know where to save it.
     *
     * The owner is a User, and the calendarId is a CollectionItem. When creating a calendar/user we create a 'SystemCollection'
     * (see CosmoCalendarService.createCalendar) since the root/home collection doesn't take part. Creating a DemandSchedule
     * creates a recurring entry item using the users calendarId 'SystemCollection'.
     * The user cannot get to their HomeCollection to get the SystemCollection - except via 'contentService.getRootItem'
     * and then root.getChildByName("SystemCollection").getUid().
     * Alternatively, we can filter through the attendees of the item and look for the CollectionItem that belongs to the user.
     *
     * NB From the calendaring system, we can get Entry.calendarIdUserReferenceUri as the owner's uid
     * when setting an entry on the calendar, we call 'noteItem.setOwner(calendar.getOwner())'
     */
    public static CollectionItem getOwnerParent(Item noteItem) {
        return noteItem.getParents().stream()
                .filter(p -> p.getOwner().equals(noteItem.getOwner()))
                .findFirst()
                .orElseThrow();
    }

    public void setCosmoConverter(CosmoConverter cosmoConverter) {
        this.converter = cosmoConverter;
    }

    @Override
    public String findCalendarIdUserReferenceUri(String calendarId) {
        CollectionItem calendar = findCalendar(calendarId);
        return calendar == null ? null : entityUriMapper.uriForEntity(calendar.getOwner()).toString();
    }

    /**
     * Called currently by:
     *  - /sync-entries/ which had a cron schedule - relating to lone worker work
     */
    @Override
    public CalendarEntries findEntries(@NonNull Interval interval) {
        EventStampFilter eventFilter = new EventStampFilter();
        eventFilter.setTimeRange(interval.getStart().toDate(), interval.getEnd().toDate());
        eventFilter.setExpandRecurringEvents(true); // TODO: test without this

        NoteItemFilter itemFilter = new NoteItemFilter();
        itemFilter.getStampFilters().add(eventFilter);

        itemFilter.setFilterProperty(EventStampFilter.PROPERTY_INCLUDE_MASTER_ITEMS, "false"); // Only return the expanded events

        var items = contentService.findItems(itemFilter);
//        log.info("calendarId: {}, interval: {} returned {} item(s)", new Object[]{calendarId, interval, items.size()});
        return new CalendarEntries(null, interval, converter.convert(items.stream()).collect(toSet()));

    }

    /**
     * Called currently by:
     *  - CalendarNotificationAgent - populating email templates for upcoming appointments
     *  - here.. findEntries
     *
     * We always end up with a recurrence with the recurring entry handle populated correctly, so we can find the appointment schedule...
     * @param calendarId the calendar to search for recurrences
     * @param interval the interval in which to search for a recurring entry
     * @return recurrences
     */

    @Override
    public CalendarEntries findEntries(String calendarId, @Nullable Interval interval) {
        var items = findNoteItems(calendarId, interval);
        return new CalendarEntries(calendarId, interval, converter.convert(items).collect(toSet()));
    }

    /**
     * Called currently by:
     *  - /calendarIds EventController - appears unused
     *  - /cal/{calendarUid}/items/ - appears unused
     *  - /nearby (but with one calendar) - for client file, offline
     *  - BaseRotaHandler addEventsWithCategory for holiday/sick etc on the rota
     */
    @Override
    public Set<CalendarEntries> findEntries(Set<String> calendarIds, @Nullable Interval interval) {
        Set<CalendarEntries> calEntries = new LinkedHashSet<>();
        for (String calendarId : calendarIds) {
            calEntries.add(findEntries(calendarId, interval));
        }
        return calEntries;
    }

    /**
     * Called currently by:
     *  - EventServiceImpl to get one event
     *  - RotaActivityInvoiceController to addLineToInvoice
     */
    @Override
    public Entry findEntry(String id) {
        NoteItem item = null;
        try {
            item = (NoteItem) contentService.findItemByUid(id);

        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not find entry using id " + id, e);
        }
        if (item == null) {
            throw new CalendarException("No entry with id " + id + " exists.");
        }
        return converter.convertItemToEntry(item);
    }

    /**
     * Called currently by:
     *  - no callee
     */
    @Override
    public Entry addEntry(String collectionUid, Entry entry) {
        NoteItem item = converter.convert(entry);
        try {
            CollectionItem calendar = findCalendar(collectionUid);
            item.setOwner(calendar.getOwner());
            item = (NoteItem) contentService.createContent(calendar, item);

        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not add entry " + entry, e);

        }
        return converter.convertItemToEntry(item);
    }


    /**
     * Called currently by:
     *  - no callee
     */
    @Override
    public void editEntry(Entry entry) {
        try {
            NoteItem item = (NoteItem) contentService.findItemByUid(entry.getItemUid());
            converter.overlay(item, entry);
            contentService.updateContent(item);

        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not edit entry " + entry, e);
        }
    }

    /**
     * Called currently by:
     *  - DemandSchedule.removeRecurringCalendarEvent
     *  NB The handlers actually call 'drop' for non-rota recurring entries, with non-recurring using older style persistAndSync.
     */
    @Override
    public void deleteEntry(String entryId) {
        try {
            contentService.removeItem(contentService.findItemByUid(entryId));
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not delete entry with id " + entryId, e);
        }
    }

    /**
     * Called currently by:
     *  - /items/{itemId}/move/from/{srcCalendarId}/to/{dstCalendarId} ItemController.moveItem with ROLE_SYSADMIN
     *
     * Reassigning the ownership of an entry to another calendar.
     * TODO: need to ensure that {@link Property#ATTENDEE} references are update, I THINK...
     */
    @Override
    public void moveEntryToAnotherCalendar(String srcCalendarId, String dstCalendarId, String entryUid) {
        NoteItem item = (NoteItem) contentService.findItemByUid(entryUid);
        CollectionItem srcCalendar = findCalendar(srcCalendarId);
        CollectionItem dstCalendar = findCalendar(dstCalendarId);
        moveCalendarItem(item, srcCalendar, dstCalendar);
    }

    private void moveCalendarItem(NoteItem item, CollectionItem srcCalendar, CollectionItem dstCalendar) {
        Assert.notNull(item);
        Assert.notNull(srcCalendar);
        Assert.notNull(dstCalendar);
        Assert.state(item.getParents().contains(srcCalendar), "entry: " + item.getUid() + " is not on calendar: " + srcCalendar.getUid());
        HibNoteItem hibNoteItem = (HibNoteItem)item;
        hibNoteItem.removeParent(srcCalendar);
        hibNoteItem.addParent(dstCalendar);
        contentService.updateContent(item);
    }

    @Override
    public String createCalendar(CalendarOwnerDefinition owner) {
        User user = new HibUser();
        user.setUsername(owner.getUsername());
        user.setPassword(owner.getPassword());
        user.setEmail(owner.getEmail());
        user.setFirstName(owner.getFirstName());
        user.setLastName(owner.getLastName());
        user.setAdmin(Boolean.FALSE);
        user.setLocked(Boolean.FALSE);
        user = userService.createUser(user);
        CollectionItem collection = new HibCollectionItem();
        collection.setName(SYSTEM_COLLECTION);
        collection.setDisplayName(collection.getName());
        collection.setOwner(user);
        HibCalendarCollectionStamp collectionStamp = new HibCalendarCollectionStamp(collection);
        collection.addStamp(collectionStamp);

        CollectionItem home = contentService.getRootItem(user);
        CollectionItem calendar = contentService.createCollection(home, collection);
        return calendar.getUid();
    }

    /**
     * Called currently by:
     *  - CalendarUserSyncAgent
     */
    @Override
    public void updateCalendarOwner(String calendarId, CalendarOwnerDefinition owner) {
        final CollectionItem calendar = findCalendar(calendarId);
        User user = calendar.getOwner();
        boolean fireDirectoryEntryUpdated = false;

        if (owner.getEmail() != null) {
            user.setEmail(owner.getEmail());
            fireDirectoryEntryUpdated = true;
        }
        if (owner.getFirstName() != null && !owner.getFirstName().equals(user.getFirstName())) {
            user.setFirstName(owner.getFirstName());
            fireDirectoryEntryUpdated = true;
        }
        if (owner.getLastName() != null && !owner.getLastName().equals(user.getLastName())) {
            user.setLastName(owner.getLastName());
            fireDirectoryEntryUpdated = true;
        }
        if (owner.getUsername() != null && !owner.getUsername().equals(user.getUsername())) {
            user.setUsername(owner.getUsername());
            fireDirectoryEntryUpdated = true;
        }
        if (owner.getPassword() != null && !owner.getPassword().equals(user.getPassword())) {
            user.setPassword(owner.getPassword());
            fireDirectoryEntryUpdated = true;
        }
        user = userService.updateUser(user);
        if (fireDirectoryEntryUpdated) {
            fireDirectoryEntryUpdated(user);
        }
    }


    /**
     * When retrieving availability records, we may need to merge two or more availability components from the calendar
     * to construct the availability over the requested interval. If that availability is subsequently saved back, then
     * the original components will be affected.
     * <p>
     * iCalendar defines priorities for availability. {@code baselineOnly} returns only availability records with the
     * default priority. If the flag is not set, then availability records at higher priority will override the
     * baseline availability retrieved at the lowest level and the returned object will provide a consolidated view.
     *
     * @param calendarId {@inheritDoc}
     * @param requestedInterval {@inheritDoc}
     * @param baselineOnly {@inheritDoc}
     * @return the availability, possibly merged from two or more individual availability records of shorter intervals
     */
    @Override
    public Availability findAvailability(String calendarId, Interval requestedInterval, boolean baselineOnly) {
        final CollectionItem calendar = findCalendar(calendarId);
        final String displayName = calendar.getOwner().getUsername();
        Multimap<Priority, AvailabilityItem> availabilityItems = findAvailabilityItemsByPriority(calendar, requestedInterval,
                baselineOnly? priority -> Objects.equals(priority, Priority.UNDEFINED) : priority -> true);

        // Work from highest priority to lowest. When we finish a priority, any intervals covered by it can be ignored for lower priorities.
        // Merge the final result into a single Availability record.
        final Availability returnValue = new Availability(null, calendarId, displayName, requestedInterval);

        // TODO: This code would look MUCH neater with JDK8
        Set<Interval> overriddenIntervals = new HashSet<>(); // Holds intervals overridden by higher priorities
        SortedSet<Priority> priorities = new TreeSet<>(new PriorityComparator());
        priorities.addAll(availabilityItems.keySet());
        for (Priority priority : priorities) {
            Set<Interval> priorityIntervals = new HashSet<>(); // Holds intervals encountered at this priority level
            Collection<AvailabilityItem> priorityItems = availabilityItems.get(priority);
            for (AvailabilityItem availabilityItem : priorityItems) {
                Availability converted = converter.convert(availabilityItem);
                priorityIntervals.add(converted.getInterval()); // Add to overridden intervals later.

                for (AvailabilityAttribute availabilityAttribute : converted.getAttributes()) {
                    returnValue.addAttribute(availabilityAttribute.getKey(), availabilityAttribute.getValue());
                }
                for (AvailableInterval availableInterval : converted.getAvailableIntervals()) {
                    final Interval overlap = availableInterval.getInterval().overlap(requestedInterval);
                    if (overlap != null) {
                        // This interval is of interest. We will check all the overrides now.
                        Set<Interval> availableIntervals = Sets.newHashSet(overlap);
                        for (Interval overriddenInterval : overriddenIntervals) {
                            availableIntervals = applyOverride(overriddenInterval, availableIntervals);
                        }
                        for (Interval interval : availableIntervals) {
                            returnValue.addAvailableInterval(interval);
                        }
                    }
                }
            }
            overriddenIntervals.addAll(priorityIntervals); // Ignore all these intervals at lower priorities
        }
        return returnValue;
    }

    /**
     * Process all the available intervals and return a new set containing the newly available intervals,
     * having removed, truncated or split any that are affected by the override.
     * This logic is similar to that in {@link #updateAvailability(Availability)}
     */
    private Set<Interval> applyOverride(Interval overriddenInterval, Set<Interval> availableIntervals) {
        Set<Interval> updatedAvailability = new HashSet<>();
        for (Interval availableInterval : availableIntervals) {
            if (overriddenInterval.contains(availableInterval)) {
                // This interval is wholly overriden, so we can exclude it altogether.
                continue;
            } else if (overriddenInterval.overlaps(availableInterval)) {
                if (overriddenInterval.contains(availableInterval.getStart())) {
                    // Override overlaps start of this availability, so truncate it to start at the end of the override.
                    updatedAvailability.add(availableInterval.withStart(overriddenInterval.getEnd()));
                } else if (overriddenInterval.contains(availableInterval.getEnd().minusMillis(1))) {
                    // Override overlaps end of this availability, so truncate it to finish at the start of the override.
                    updatedAvailability.add(availableInterval.withEnd(overriddenInterval.getStart()));
                } else {
                    // This override falls somewhere in the middle of the available interval, so split it in two.
                    updatedAvailability.add(availableInterval.withEnd(overriddenInterval.getStart()));
                    updatedAvailability.add(availableInterval.withStart(overriddenInterval.getEnd()));
                }
            } else {
                // No override affects this one, so retain it.
                updatedAvailability.add(availableInterval);
            }
        }
        return updatedAvailability;
    }


    private Multimap<Priority, AvailabilityItem> findAvailabilityItemsByPriority(CollectionItem parent, ReadableInterval interval, Predicate<Priority> priorityFilter) {
        final Multimap<Priority, AvailabilityItem> calendarItems = HashMultimap.create();
        net.fortuna.ical4j.model.TimeZone tz = null;

        // Find any availability records in the interval specified
        // If the calendar collection has a timezone attribute, then use that to convert floating date/times to UTC
        if (parent != null) {
            CalendarCollectionStamp ccs = StampUtils.getCalendarCollectionStamp(parent);
            if (ccs != null) {
                tz = ccs.getTimezone();
            }
            ComponentFilter availabilityFilter = new ComponentFilter(Component.VAVAILABILITY);
            TimeRangeFilter timeRangeFilter = new TimeRangeFilter(interval.getStart().toDate(), interval.getEnd().toDate());
            if (tz != null) {
                timeRangeFilter.setTimezone(tz.getVTimeZone());
            }
            availabilityFilter.setTimeRangeFilter(timeRangeFilter);
            ComponentFilter calendarFilter = new ComponentFilter(net.fortuna.ical4j.model.Calendar.VCALENDAR);
            calendarFilter.getComponentFilters().add(availabilityFilter);
            CalendarFilter rootFilter = new CalendarFilter();
            rootFilter.setFilter(calendarFilter);

            CalendarFilterEvaluater evaluater = new CalendarFilterEvaluater();
            EntityConverter entityConverter = new EntityConverter(null);

            // Evaluate filter against all calendar items
            for (Item child : parent.getChildren()) {

                // only care about availability items, obviously
                if (child instanceof AvailabilityItem) {

                    AvailabilityItem content = (AvailabilityItem) child;
                    net.fortuna.ical4j.model.Calendar calendar = entityConverter.convertAvailability(content);

                    if (calendar != null && evaluater.evaluate(calendar, rootFilter)) {
                        final ComponentWrapper<Component> vAvailability = new ComponentWrapper<>(calendar.getComponent(COMPONENT_VAVAILABLITY));
                        final Priority priority = MoreObjects.firstNonNull(vAvailability.getComponent().getProperty(Property.PRIORITY), Priority.UNDEFINED);
                        if (priorityFilter.test(priority)) {
                            calendarItems.put(priority != null ? priority : Priority.UNDEFINED, content);
                        }
                    }
                }
            }
        }

        return calendarItems;
    }

    /**
     * {@inheritDoc}
     * <p>This only operates on VAVAILABILITY records with default priority.
     * @param availability {@inheritDoc}
     */
    @Override
    public void updateAvailability(Availability availability) {
        try {
            final Collection<AvailabilityItem> existingItems = findAvailabilityItemsByPriority(findCalendar(availability.getCalendarId()), availability.getInterval(),
                    priority -> Objects.equals(priority, Priority.UNDEFINED)).values();
            CollectionItem calendar = findCalendar(availability.getCalendarId());

            for (AvailabilityItem existingItem : existingItems) {
                ComponentWrapper<VAvailability> vAvailability = new ComponentWrapper<>(existingItem.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY));
                final DateTime startDate = new DateTime(vAvailability.getStartDate().getDate());
                final DateTime endDate = new DateTime(vAvailability.getEndDate().getDate());
                final Interval existingInterval = new Interval(startDate, endDate);
                if (availability.getInterval().contains(existingInterval)) {
                    // To be replaced
                    contentService.removeItem(existingItem);
                } else if (availability.getInterval().overlaps(existingInterval)) {
                    if (availability.getInterval().contains(startDate)) { // New record contains start of old one
                        final Date truncatedStart = availability.getInterval().getEnd().toDate();
                        truncateStart(vAvailability, truncatedStart);

                        contentService.updateContent(existingItem);
                    } else if (availability.getInterval().contains(endDate.minusMillis(1))) { // New record contains end of old one
                        final Date truncatedEnd = availability.getInterval().getStart().toDate();
                        truncateEnd(vAvailability, truncatedEnd);

                        contentService.updateContent(existingItem);
                    } else {
                        // If a new availability entry is inserted which overlaps an existing one, the existing one is split and
                        // the overlapping part of the date range discarded.
                        final Date truncatedStart = availability.getInterval().getEnd().toDate();
                        // Create new AvailabilityItem covering the period from the truncatedStart to the old item's end
                        final AvailabilityItem splitOutItem = copyItem(existingItem);
                        truncateStart(new ComponentWrapper<>(splitOutItem.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY)), truncatedStart);
                        contentService.createContent(calendar, splitOutItem);

                        final Date truncatedEnd = availability.getInterval().getStart().toDate();
                        // Truncate the end of the existing one to be only the part before the new record
                        truncateEnd(vAvailability, truncatedEnd);
                        contentService.updateContent(existingItem);
                    }
                }
            }

            final AvailabilityItem item = converter.convert(availability);
            item.setOwner(calendar.getOwner());
            contentService.createContent(calendar, item);
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not add availability " + availability, e);
        }
    }

    private AvailabilityItem copyItem(AvailabilityItem existingItem) {
        final AvailabilityItem splitOutItem = (AvailabilityItem) existingItem.copy();
        splitOutItem.setIcalUid(null);
        // Copy doesn't copy the AVAILABLE records, so we need to do those ourselves
        final ComponentList<Available> splitOutAvailableList = ((VAvailability) splitOutItem.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY)).getAvailable();
        for (Available component : ((VAvailability) existingItem.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY)).getAvailable()) {
            try {
                splitOutAvailableList.add((Available) component.copy());
            } catch (Exception e) {
                throw new RuntimeException("Error copying AVAILABLE component.");
            }
        }
        return splitOutItem;
    }

    private void truncateStart(ComponentWrapper<VAvailability> vAvailability, Date truncatedStart) {
        // Truncate start of VAVAILABILITY
        vAvailability.setStartDate(new net.fortuna.ical4j.model.DateTime(truncatedStart));
        Set<Available> componentsToRemove = new HashSet<>();
        // Truncate child AVAILABLE records
        for (Available component : vAvailability.getComponent().getAvailable()) {
            ComponentWrapper<Available> available = new ComponentWrapper<>(component);
            if (available.getStartDate().getDate().before(vAvailability.getStartDate().getDate())) {
                // AVAILABLE starts earlier than truncated VAVAILABILITY start
                if (!(available.getEndDate().getDate().after(vAvailability.getStartDate().getDate()))) {
                    // AVAILABLE is wholly outside VAVAILABILITY now
                    componentsToRemove.add(component);
                } else {
                    // Truncate the start of the AVAILABLE to match the VAVAILABILITY
                    available.setStartDate(vAvailability.getStartDate().getDate());
                }
            }
        }
        vAvailability.getComponent().getAvailable().removeAll(componentsToRemove);
    }

    private void truncateEnd(ComponentWrapper<VAvailability> vAvailability, Date truncatedEnd) {
        // Truncate end of VAVAILABILITY
        vAvailability.setEndDate(new net.fortuna.ical4j.model.DateTime(truncatedEnd));
        Set<Available> componentsToRemove = new HashSet<>();
        // Truncate child AVAILABLE records
        for (Available component : vAvailability.getComponent().getAvailable()) {
            ComponentWrapper<Available> available = new ComponentWrapper<>(component);
            if (available.getEndDate().getDate().after(vAvailability.getEndDate().getDate())) {
                // AVAILABLE finishes later than truncated VAVAILABILITY end
                if (!(available.getStartDate().getDate().before(vAvailability.getEndDate().getDate()))) {
                    // AVAILABLE is wholly outside VAVAILABILITY now
                    componentsToRemove.add(component);
                } else {
                    // Truncate the end of the AVAILABLE to match the VAVAILABILITY
                    available.setEndDate(vAvailability.getEndDate().getDate());
                }
            }
        }
        vAvailability.getComponent().getAvailable().removeAll(componentsToRemove);
    }

    @Override
    public UnavailableInterval blockAvailability(String calendarId, final UnavailableIntervalDefinition entry) {
        AvailabilityItem availabilityItem = converter.convert(entry);
        try {
            CollectionItem calendar = findCalendar(calendarId);
            availabilityItem.setOwner(calendar.getOwner());
            availabilityItem = (AvailabilityItem) contentService.createContent(calendar, availabilityItem);
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not add entry " + entry, e);
        }
        return converter.convertUnavailability(availabilityItem);
    }

    @Override
    public UnavailableInterval updateUnavailableInterval(UnavailableInterval.Handle handle, final UnavailableIntervalDefinition entry) {
        AvailabilityItem availabilityItem = (AvailabilityItem) contentService.findItemByUid(handle.toString());
        try {
            converter.overlay(availabilityItem, entry);
            availabilityItem = (AvailabilityItem) contentService.updateContent(availabilityItem);
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not update entry " + handle + " with " + entry, e);
        }
        return converter.convertUnavailability(availabilityItem);
    }

    @Override
    public void reinstateAvailability(UnavailableInterval.Handle handle) {
        try {
            final AvailabilityItem availabilityItem = (AvailabilityItem) contentService.findItemByUid(handle.toString());
            contentService.removeItem(availabilityItem);
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not remove unavailable interval " + handle, e);
        }
    }







    // =====================
    // =====================
    // pre-AOP code
    // =====================
    // =====================

    private final CosmoConverterEntryToItem converterEntryToItem = new CosmoConverterEntryToItem();

    // just a way to get to the method in the convertor to allow us to change advice ordering
    public String getUUID() {
        return converterEntryToItem.getUUID();
    }

    // create = post
    // request with /collection/{uid}/{projection}?/{format}? (see examples - not text! - of http://chandlerproject.org/Projects/CosmoFeedItems)
    // handled by org.osaf.cosmo.atom.AtomServlet
    // AtomServlet calls service to create an abdera ServletRequestContext
    //        passing in the provider org.osaf.cosmo.atom.provider.StandardProvider and request
    //        the constructor calls initTarget() which calls StandardProvider.resolveTarget
    // AtomServlet calls service.chain which calls provider.process - which loops through the provider's filters (?) calling process
    //        StandardProvider.process does request.getTarget();
    //            calls servlet (AbstractRequestContext).getTarget
    //            which returns a target from StandardTargetResolver.resolve which returns a target from...
    //            org.osaf.cosmo.atom.provider.StandardTargetResolver.createCollectionTarget
    //            which gets the uid of the collection (also an Item)
    //            so the target is the collection
    //        then StandardProvider.getCollectionAdapter is called, which is ItemCollectionAdapter
    //        then StandardProvider determines its a POST and...
    //            if its an atom request, ItemCollectionAdapter.put, post etc is called....
    //            if its not an atom request (application/x-www/form-urlencoded) then calls ItemCollectionAdapter.extensionRequest
    //                extensionRequest means that ItemCollectionAdapter.addItemToCollection is called
    // so if its a new item, the post on the adapter is called
    // we copied and pasted, but replaced calls with compilable ones

    /**
     * Called currently by:
     * - EventServiceImpl persistAndSync
     */
    public void postEntry(EventEntryDefinition event, String collectionUid, String modifiedByUsername, DateTimeZone timeZone) {

        if (event instanceof AbstractLongKeyedEntity) {
            // it's actually a CustomEventImpl which is elsewhere
            event = (EventEntryDefinition) AntiProxyUtils.ensureManaged(em, (AbstractLongKeyedEntity)event);
        }

        // StandardProvider.process does getTarget which calls createCollectionTarget
        // START org.osaf.cosmo.atom.provider.StandardTargetResolver.createCollectionTarget
        Item item = contentService.findItemByUid(collectionUid);
        if (item == null) {
            return;
        }
        if (! (item instanceof CollectionItem))
        {
            return;
            // END
        }


        // START org.osaf.cosmo.atom.provider.ItemCollectionAdapter.postEntry
        CollectionItem collection = (CollectionItem) item;

        if (log.isDebugEnabled()) {
            log.debug("creating entry in collection " + collection.getUid());
        }

        try {
            NoteItem newItem = (NoteItem) converterEntryToItem.convertEntryToItem(event, collection, modifiedByUsername, timeZone);
            // set the managedBy at the point we create newItem - just to avoid checking the entry against rota data
            // NB this postEntry() is only called by persistAndSyncAddNonRecurringCalendarEntry() - which is non-recurring events
            // where as recurring events set their own managedBy
            converter.setManagedBy(newItem, entityUriMapper.uriForEntity(event));
            item = contentService.createContent(collection, newItem);

            CosmoHelper.syncAttendeesWithCalendars(item, entityUriMapper);
            if (event.getLocation() != null) {
                CosmoHelper.setItemLocation(item, event.getLocation());
            } else {
                final Contact locationContact = event.getLocationContact();
                if (locationContact != null) {
                    CosmoHelper.setItemLocation(item, locationContact, entityUriMapper);
                }
            }
            contentService.updateContent((NoteItem) item);
        } catch (UidInUseException e) {
            throw new CalendarException("error creating cosmo event: " + "Uid already in use", e);
        } catch (IcalUidInUseException e) {
            throw new CalendarException("error creating cosmo event: " + "IcalUid already in use", e);
        } catch (CollectionLockedException e) {
            throw new CalendarException("error creating cosmo event: " + "collection locked", e);
        }
    }

    // update = put
    // request with /item/{uid}/{projection}?/{format}? uri means we are updating an item (if a 'put')
    // handled by org.osaf.cosmo.atom.AtomServlet
    // which calls service to create an abdera ServletRequestContext
    // passing in the provider org.osaf.cosmo.atom.provider.StandardProvider and request
    // which then calls provider.process
    //  servletRequestContext.getTarget is called which calls provider.resolveTarget
    //    which returns a target from StandardTargetResolver.resolve which returns a target from...
    //    org.osaf.cosmo.atom.provider.StandardTargetResolver.createItemTarget
    //    which gets the item
    //    so the target is the item
    //  then getCollectionAdapter is called, which is ItemCollectionAdapter
    //    then put is called if put, post if post....
    // so the method on the adapter is called, here being put
    //
    // we didn't copy and past and then adapt this code, but perhaps should have for clarity
    // so the general method we copied did this:
    //   org.osaf.cosmo.atom.provider.StandardTargetResolver
    //   gets the item's collection
    //   checks basic stuff of the post - size etc
    //   gets processor based on content-type
    //   call processCreation
    //   call contentService.createContent(collection, item);
    //   return the new item in the response

    /**
     * Called currently by:
     * - EventServiceImpl persistAndSync
     */
    public void putEntry(EventEntryDefinition event, String modifiedByUsername, DateTimeZone timeZone) {

        // load the existing item
        Item originalItem = contentService.findItemByUid(event.getUid());
        if (originalItem == null) {
            return;
        }
        if (! (originalItem instanceof NoteItem)) {
            return;
        }
        // then gets the <entry ...to create the content processor
        // to call processEntryUpdate - which we have modified to skip the processing
        // and use an Eim object directly
        //originalItem = originalItem.copy();and
        //NoteItem updatedItem = (NoteItem) converterEntryToItem.convertEntryToItem(event, originalItem, modifiedByUsername, timeZone);
        NoteItem updatedItem = (NoteItem) originalItem;
        converterEntryToItem.convertFromUpdatedEvent(event, updatedItem, modifiedByUsername, timeZone);  //TODO: Does event have all info needed?
        processEntryUpdate((NoteItem) originalItem, updatedItem); //TODO: does this therefore do nothing useful
    }

    // this is very similar to ThisAndFutureHelper#breakRecurringEvent
    private void processEntryUpdate(NoteItem originalItem, NoteItem updatedItem) {
        EventStamp origStamp = StampUtils.getEventStamp(originalItem);
        net.fortuna.ical4j.model.Date oldstart = origStamp != null && origStamp.isRecurring() ? origStamp.getStartDate() : null;

        // oldStart will have a value if the item has an EventStamp
        // and the EventStamp is recurring
        if (oldstart != null) {
            EventStamp updatedItemStamp = StampUtils.getEventStamp(updatedItem);
            // Case 1: EventStamp was removed from recurring event, so we
            // have to remove all modifications (a modification doesn't make
            // sense if there is no recurring event)
            if (updatedItemStamp == null) {
                LinkedHashSet<ContentItem> updates = new LinkedHashSet<>();
                for (NoteItem mod : updatedItem.getModifications()) {
                    mod.setIsActive(false);
                    updates.add(mod);
                }
                updates.add(updatedItem);

                // Update item and remove modifications in one atomic service call
                contentService.updateContentItems(updatedItem.getParents(), updates);
            }
            // Case 2: Start date may have changed on master event.
            else {
                net.fortuna.ical4j.model.Date newstart = updatedItemStamp.getStartDate();

                // If changed, we have to update all the recurrenceIds
                // for any modifications
                if (newstart != null && !newstart.equals(oldstart)) {
                    long delta = newstart.getTime() - oldstart.getTime();
                    log.debug("master event start date changed. Adjusting modifications by {} milliseconds", delta);

                    LinkedHashSet<ContentItem> updates = new LinkedHashSet<>();
                    HashSet<NoteItem> copies = new HashSet<>();
                    HashSet<NoteItem> removals = new HashSet<>();

                    // copy each modification and update the copy's uid
                    // with the new start date
                    for (NoteItem mod : updatedItem.getModifications()) {
                        // ignore modifications without event stamp
                        if (StampUtils.getEventExceptionStamp(mod) == null) {
                            continue;
                        }

                        mod.setIsActive(false);
                        removals.add(mod);

                        NoteItem copy = (NoteItem) mod.copy();
                        copy.setModifies(updatedItem);

                        EventExceptionStamp ees =
                                StampUtils.getEventExceptionStamp(copy);

                        net.fortuna.ical4j.model.DateTime oldRid = (net.fortuna.ical4j.model.DateTime) ees.getRecurrenceId();
                        java.util.Date newRidTime =
                                new java.util.Date(oldRid.getTime() + delta);
                        net.fortuna.ical4j.model.DateTime newRid = (net.fortuna.ical4j.model.DateTime)
                                Dates.getInstance(newRidTime, Value.DATE_TIME);
                        if (oldRid.isUtc()) {
                            newRid.setUtc(true);
                        } else {
                            newRid.setTimeZone(oldRid.getTimeZone());
                        }

                        copy.setUid(new ModificationUid(updatedItem, newRid).toString());
                        ees.setRecurrenceId(newRid);

                        // If the modification's dtstart is missing, then
                        // we have to adjust dtstart to be equal to the
                        // recurrenceId.
                        if (isDtStartMissing(StampUtils.getBaseEventStamp(mod))) {
                            ees.setStartDate(ees.getRecurrenceId());
                        }

                        copies.add(copy);
                    }

                    // add removals first
                    updates.addAll(removals);
                    // then additions
                    updates.addAll(copies);
                    // then updates
                    updates.add(updatedItem);

                    // Update everything in one atomic service call
                    contentService.updateContentItems(updatedItem.getParents(), updates);
                } else {
                    // otherwise use simple update
                    updatedItem = (NoteItem) contentService.updateContent(updatedItem);
                }
            }
        } else {
            // use simple update
            updatedItem = (NoteItem) contentService.updateContent(updatedItem);
        }

    }

    private boolean isDtStartMissing(BaseEventStamp stamp) {

        if(stamp.getStartDate()==null || stamp.getRecurrenceId()==null) {
            return false;
        }

        // "missing" startDate is represented as startDate==recurrenceId
        if(!stamp.getStartDate().equals(stamp.getRecurrenceId())) {
            return false;
        }

        // "missing" anyTime is represented as null
        return stamp.isAnyTime() == null;

    }

    // delete = delete
    // request with /item/{uid}/{projection}?/{format}? uri means we are deleting an item (if a 'delete')
    // handled by org.osaf.cosmo.atom.AtomServlet
    // which calls service to create an abdera ServletRequestContext
    // passing in the provider org.osaf.cosmo.atom.provider.StandardProvider and request
    // which then calls provider.process
    //  servletRequestContext.getTarget is called which calls provider.resolveTarget
    //    which returns a target from StandardTargetResolver.resolve which returns a target from...
    //    org.osaf.cosmo.atom.provider.StandardTargetResolver.createItemTarget
    //    which gets the uid of the item
    //    so the target is the item
    //  then getCollectionAdapter is called, which is ItemCollectionAdapter
    //    then put is called if put, post if post....
    // so the method on the adapter is called, here being delete
    // then copied and pasted, but replaced calls with compilable ones

    /**
     * Called currently by:
     *  - EventServiceImpl persistAndSync
     */
    public void deleteEntry(EventEntryDefinition event) throws CalendarException {
        Item item = contentService.findItemByUid(event.getUid());
        if (item == null) {
            return;
        }
        if (! (item instanceof NoteItem)) {
            return;
        }

        // handle case where item is an occurrence, return unknown
        if(item instanceof NoteOccurrence) {
            throw new CalendarException("Item not found: " + event.getUid());
        }

        if (log.isDebugEnabled()) {
            log.debug("deleting entry for item " + item.getUid());
        }

        try {
            contentService.removeItem(item);
        } catch (CollectionLockedException e) {
            throw new CalendarException("error deleting cosmo event: " + "collection locked", e);
        }
    }

    @Override
    public Integer allocateAsyncAfterDays() {
        return recurringService.allocateAsyncAfterDays();
    }

    @Override
    public RecurringEntry createRecurringEntryNonRota(String calendarId, RecurringEntryDefinition entry) {
        return recurringService.createRecurringEntryNonRota(calendarId, entry);
    }

    @Override
    public RecurringEntryHandle getEntryHandleFromAnyHandle(String handle) {
        return recurringService.getEntryHandleFromAnyHandle(handle);
    }

    @Override
    public RecurringEntryHandle getEntryHandleFromRecurrenceHandle(Recurrence.RecurrenceHandle recurrenceHandle) {
        return recurringService.getEntryHandleFromRecurrenceHandle(recurrenceHandle);
    }

    @Override
    public RecurringEntryHandle getSeriesHandleFromAnyHandle(String handle) {
        return recurringService.getSeriesHandleFromAnyHandle(handle);
    }

    @Override
    public RecurringEntry getRecurringEntry(RecurringEntryHandle handle) {
        return recurringService.getRecurringEntry(handle);
    }

    @Override
    public Instant recurrenceToInstant(Recurrence.RecurrenceHandle recurrence) {
        return recurringService.recurrenceToInstant(recurrence);
    }

    @Override
    public Recurrence findFirstRecurrence(RecurringEntryHandle handle, Interval interval) {
        return recurringService.findFirstRecurrence(handle, interval);
    }

    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Range<Instant> interval, Recurrence.Status status) {
        return recurringService.findRecurrences(handle, interval, status);
    }

    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Interval interval, Recurrence.Status status) {
        return recurringService.findRecurrences(handle, interval, status);
    }

    /**
     * For TESTS only, to avoid tx issues on hydrating from streams
     */
    @Override
    @Deprecated
    public List<Recurrence> findRecurrencesList(String calendarId, Interval interval) {
        return recurringService.findRecurrencesFromCalendar(calendarId, interval).collect(toList());
    }
    @Override
    @Deprecated
    public Set<Attendee> findRecurrenceAttendees(Recurrence recurrence) {
        return recurrence.getAttendees();
    }

    /**
     * For TESTS only, to avoid tx issues on hydrating from streams
     */
    @Override
    @Deprecated
    public Set<Attendee> findRecurrencesListAttendees(String calendarId, Interval interval, String calendarIdResourceUserReferenceUri) {
        var events = recurringService.findRecurrencesFromCalendar(calendarId, interval);
        // NB We force the attendees here to populate (by cloning the code), to help with the tests
        return events
                .map(Recurrence::getAttendees)
                .flatMap(Collection::stream)
                .filter(a -> calendarIdResourceUserReferenceUri.equals(a.getCalendarIdUserReferenceUri()))
                .collect(toSet());
    }

    @Override
    public RecurringEntryHandle getRecurringEntrySeriesHandle(RecurringEntryHandle recurringEntryHandle) {
        return recurringService.getRecurringEntrySeriesHandle(recurringEntryHandle);
    }

    @Override
    public Stream<Recurrence> findRecurrencesFromCalendar(String calendarId, Interval interval) {
        return recurringService.findRecurrencesFromCalendar(calendarId, interval);
    }

    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Interval interval) {
        return recurringService.findRecurrenceExceptions(handle, interval);
    }

    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Range<Instant> interval) {
        return recurringService.findRecurrenceExceptions(handle, interval);
    }

    @Override
    public Stream<Recurrence> findModifiedRecurrences(RecurringEntryHandle handle, LocalDate equalOrAfter) {
        return recurringService.findModifiedRecurrences(handle, equalOrAfter);
    }

    @Override
    public RecurringEntry createRecurringEntry(String calendarId, RecurringEntryDefinition entry) {
        return recurringService.createRecurringEntry(calendarId, entry);
    }

    @Override
    public void recreateRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range, URI updatedBy) {
        recurringService.recreateRecurrencesInRange(recurringEntryHandle, range, updatedBy);
        em.flush();
    }

    @Override
    public int confirmRecurrencesInRange(RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                         @Nullable DaysOfWeek matchDays, URI updatedBy, @Nullable Integer allocateRescheduledMins,
                                         Range<LocalDate> rescheduleBounds, String allocateToResourceCalendarId) {
        // splitting a series calls cosmo's flush in createContent
        int count = recurringService.confirmRecurrencesInRange(recurringEntryHandle, range, matchDays, updatedBy,
                allocateRescheduledMins, rescheduleBounds, allocateToResourceCalendarId);
        // ensure flush so anything after cosmo's createContent flush is also persisted
        em.flush();
        return count;
    }

    @Override
    public int unConfirmRecurrencesInRange(RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                           @Nullable DaysOfWeek matchDays, URI updatedBy,
                                           String deallocateFromResourceCalendarId) {
        return recurringService.unConfirmRecurrencesInRange(recurringEntryHandle, range, matchDays, updatedBy, deallocateFromResourceCalendarId);
    }

    @Override
    public void rescheduleRecurrence(Recurrence.RecurrenceHandle recurrence, @Nullable String newTitle,
                                     @Nullable LocalDateTime newDateTime, @Nullable Integer newDurationMins,
                                     URI updatedBy, Range<LocalDate> recurrenceBounds) {
        recurringService.rescheduleRecurrence(recurrence, newTitle, newDateTime, newDurationMins, updatedBy, recurrenceBounds);
    }

    @Override
    public void updateRecurringEntryManagedBy(RecurringEntryHandle handle, URI managedByUri) {
        recurringService.updateRecurringEntryManagedBy(handle, managedByUri);
    }

    @Override
    public void updateRecurringEntryBoundsEnd(RecurringEntryHandle handle, LocalDate endDate) {
        recurringService.updateRecurringEntryBoundsEnd(handle, endDate);
    }

    @Override
    public void updateRecurringEntryBoundsStart(RecurringEntryHandle handle, LocalDate startDate) {
        recurringService.updateRecurringEntryBoundsStart(handle, startDate);
    }

    @Override
    public void updateRecurringEntryBounds(RecurringEntryHandle handle, @Nullable LocalDate startDate, @Nullable LocalDate endDate, boolean setEndDate) {
        recurringService.updateRecurringEntryBounds(handle, startDate, endDate, setEndDate);
    }

    @Override
    public void updateRecurringEntryBounds(RecurringEntryHandle handle, @Nullable LocalDate startDate, @Nullable LocalDate endDate) {
        recurringService.updateRecurringEntryBounds(handle, startDate, endDate);
    }

    @Override
    public void confirmRecurringEntry(RecurringEntryHandle recurringEntryHandle, Recurrence.Status eventStatus,
                                      Attendee.Status attendeeStatus, URI updatedBy, String... calendarIds) {
        recurringService.confirmRecurringEntry(recurringEntryHandle, eventStatus, attendeeStatus, updatedBy, calendarIds);
    }

    @Override
    public void unConfirmRecurringEntry(RecurringEntryHandle recurringEntryHandle, URI updatedBy, String... calendarIds) {
        recurringService.unConfirmRecurringEntry(recurringEntryHandle, updatedBy, calendarIds);
    }

    @Override
    public void confirmRecurrence(Recurrence.RecurrenceHandle recurrenceHandle, URI updatedBy, String... calendarIds) {
        recurringService.confirmRecurrence(recurrenceHandle, updatedBy, calendarIds);
    }

    @Override
    public void unconfirmRecurrence(Recurrence.RecurrenceHandle recurrenceHandle, URI updatedBy, boolean reset, String... calendarIds) {
        recurringService.unconfirmRecurrence(recurrenceHandle, updatedBy, reset, calendarIds);
    }

    @Override
    public void ensureConcreteRecurrence(Recurrence.RecurrenceHandle recurrenceHandle, URI updatedBy) {
        recurringService.ensureConcreteRecurrence(recurrenceHandle, updatedBy);
    }

    @Override
    public void editRecurrence(Recurrence.RecurrenceHandle recurrenceHandle, @Nullable String title,
                               @Nullable DateTime rescheduledTime, @Nullable Integer durationMins,
                               URI updatedBy) {
        recurringService.editRecurrence(recurrenceHandle, title, rescheduledTime, durationMins, updatedBy);
    }

    @Override
    public void resetRecurrence(Recurrence.RecurrenceHandle recurrenceHandle) {
        recurringService.resetRecurrence(recurrenceHandle);
    }

    @Override
    public void dropRecurrence(Recurrence.RecurrenceHandle recurrenceHandle) {
        recurringService.dropRecurrence(recurrenceHandle);
    }

    @Override
    public void deleteRecurringEntry(RecurringEntryHandle handle, boolean safely) {
        recurringService.deleteRecurringEntry(handle, safely);
    }

    @Override
    public void reinstateRecurrence(Recurrence.RecurrenceHandle recurrenceHandle) {
        recurringService.reinstateRecurrence(recurrenceHandle);
    }

    @Override
    public void transferRelevantConcreteAndExDateEntries(RecurringEntryHandle newEntry, RecurringEntryHandle oldEntry) {
        recurringService.transferRelevantConcreteAndExDateEntries(newEntry, oldEntry);
    }

    @Override
    public void clearRelevantConcreteAndExDateEntries(RecurringEntryHandle newEntry, RecurringEntryHandle oldEntry) {
        recurringService.clearRelevantConcreteAndExDateEntries(newEntry, oldEntry);
    }

    @Override
    public void clearRelevantConcreteAndExDateEntries(RecurringEntryHandle entry, Instant from) {
        recurringService.clearRelevantConcreteAndExDateEntries(entry, from);
    }

}
