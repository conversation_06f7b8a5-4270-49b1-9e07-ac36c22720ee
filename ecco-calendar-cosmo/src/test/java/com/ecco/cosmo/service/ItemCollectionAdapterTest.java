package com.ecco.cosmo.service;

import com.ecco.dom.contacts.Address;
import com.ecco.dom.contacts.Contact;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.calendar.cosmo.CosmoCalendarService;
import com.ecco.calendar.cosmo.CosmoConverter;
import com.ecco.calendar.dom.EventEntryDefinition;
import com.ecco.calendar.core.util.DateTimeUtils;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.component.VEvent;
import org.hamcrest.Matcher;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.hibernate.HibEventStamp;
import org.osaf.cosmo.model.hibernate.HibNoteItem;
import org.osaf.cosmo.service.ContentService;

import java.net.URI;
import java.util.UUID;

import static com.ecco.calendar.cosmo.CosmoTestFixtureUtil.*;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemCollectionAdapterTest {
    private static final String CAL_1_ID = UUID.randomUUID().toString();
    private static final String CAL_2_ID = UUID.randomUUID().toString();
    private static final String CAL_1_OWNER = "arthur.christmas";
    private static final String USER_1_EMAIL = "<EMAIL>";
    private static final String USER_1_FIRST_NAME = "Arthur";
    private static final String USER_1_LAST_NAME = "Christmas";
    private static final String USER_1_DISPLAY_NAME = USER_1_FIRST_NAME + " " + USER_1_LAST_NAME;
    private static final URI USER_1_URI = URI.create("festive://User/ONE");
    private static final String CONTACT_1_ADDRESS = "30 Acacia Avenue, Great Hampington, Littleshire, JV4 1KM";
    private static final URI CONTACT_1_URI = URI.create("fruity://Contact/banana");
    private static final String USER_2_EMAIL = "<EMAIL>";
    private static final String USER_2_FIRST_NAME = "Benjamin";
    private static final String USER_2_LAST_NAME = "Button";
    private static final String USER_2_DISPLAY_NAME = USER_2_FIRST_NAME + " " + USER_2_LAST_NAME;
    private static final URI USER_2_URI = URI.create("backwards://User/2");
    private static final String LOCATION_TEXT = "Any old location";

    @Mock
    private ContentService contentService;
    @Mock
    private EntityUriMapper entityUriMapper;
    @Mock
    private CosmoConverter cosmoConverter;
    @Mock
    private CollectionItem calendar1;
    @Mock
    private User user1;
    @Mock
    private Contact contact1;
    private Address address1 = new Address() {
        @Override
        public String toString() {
            return CONTACT_1_ADDRESS;
        }
    };

    @Mock
    private CollectionItem calendar2;
    @Mock
    private User user2;
    private CosmoCalendarService itemCollectionAdapter;

    @Before
    public void setUp() {
        itemCollectionAdapter = new CosmoCalendarService();
        itemCollectionAdapter.setCosmoConverter(cosmoConverter);
        itemCollectionAdapter.setContentService(contentService);
        itemCollectionAdapter.setEntityUriMapper(entityUriMapper);

        when(contentService.findItemByUid(eq(CAL_1_ID))).thenReturn(calendar1);
        when(contentService.findItemByUid(eq(CAL_2_ID))).thenReturn(calendar2);
        when(calendar1.getOwner()).thenReturn(user1);
        when(calendar2.getOwner()).thenReturn(user2);
        when(calendar1.getUid()).thenReturn(CAL_1_ID);
        when(calendar2.getUid()).thenReturn(CAL_2_ID);
        when(user1.getEmail()).thenReturn(USER_1_EMAIL);
        when(user2.getEmail()).thenReturn(USER_2_EMAIL);
        when(user1.getFirstName()).thenReturn(USER_1_FIRST_NAME);
        when(user2.getFirstName()).thenReturn(USER_2_FIRST_NAME);
        when(user1.getLastName()).thenReturn(USER_1_LAST_NAME);
        when(user2.getLastName()).thenReturn(USER_2_LAST_NAME);
        when(contact1.getAddress()).thenReturn(address1);
        when(entityUriMapper.uriForEntity(user1)).thenReturn(USER_1_URI);
        when(entityUriMapper.uriForEntity(user2)).thenReturn(USER_2_URI);
        when(entityUriMapper.uriForEntity(contact1)).thenReturn(CONTACT_1_URI);
    }

    @Test
    public void givenEventLocationSetWhenPostingEntryThenLocationPropertySynced() {
        EventEntryDefinition event = Mockito.mock(EventEntryDefinition.class);
        final DateTime now = new DateTime();
        when(event.getEventDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now, true));
        when(event.getEventEndDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now.plusHours(1), true));
        when(event.getUid()).thenReturn(UUID.randomUUID().toString());
        when(event.getLocation()).thenReturn(LOCATION_TEXT);
        // Creating content returns its argument, with IDs populated
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(contentService.createContent(any(CollectionItem.class), any(NoteItem.class))).thenAnswer(persistedNoteItem());
        when(contentService.updateContent(cosmoItem.capture())).thenAnswer(persistedNoteItem());

        itemCollectionAdapter.postEntry(event, CAL_1_ID, CAL_1_OWNER, DateTimeZone.UTC);

        // Check that what we stored in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), contains(collectionWithUid(CAL_1_ID)));
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(noteItem);
        final VEvent vEvent = stamp.getEvent();
        final Matcher<Property> location = allOf(locationWithValue(LOCATION_TEXT));
        assertThat("vEvent location", vEvent.getProperty(Property.LOCATION), location);
    }

    @Test
    public void givenEventLocationNotSetWhenPostingEntryThenContactUsedForLocation() {
        EventEntryDefinition event = Mockito.mock(EventEntryDefinition.class);
        final DateTime now = new DateTime();
        when(event.getEventDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now, true));
        when(event.getEventEndDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now.plusHours(1), true));
        when(event.getUid()).thenReturn(UUID.randomUUID().toString());
        when(event.getLocation()).thenReturn(null);
        when(event.getLocationContact()).thenReturn(contact1);
        // Creating content returns its argument, with IDs populated
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(contentService.createContent(any(CollectionItem.class), any(NoteItem.class))).thenAnswer(persistedNoteItem());
        when(contentService.updateContent(cosmoItem.capture())).thenAnswer(persistedNoteItem());

        itemCollectionAdapter.postEntry(event, CAL_1_ID, CAL_1_OWNER, DateTimeZone.UTC);

        // Check that what we stored in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), contains(collectionWithUid(CAL_1_ID)));
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(noteItem);
        final VEvent vEvent = stamp.getEvent();
        final Matcher<Property> location = allOf(locationWithValue(CONTACT_1_ADDRESS), locationWithAlternateRepresentation(CONTACT_1_URI.toString()));
        assertThat("vEvent location", vEvent.getProperty(Property.LOCATION), location);
    }

    @Test
    public void whenPostingEntryThenAttendeesAreSynced() {
        EventEntryDefinition event = Mockito.mock(EventEntryDefinition.class);
        final DateTime now = new DateTime();
        when(event.getEventDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now, true));
        when(event.getEventEndDateToPersist()).thenReturn(DateTimeUtils.convertToMedDate(now.plusHours(1), true));
        when(event.getUid()).thenReturn(UUID.randomUUID().toString());
        // Creating content returns its argument, with IDs populated
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(contentService.createContent(any(CollectionItem.class), any(NoteItem.class))).thenAnswer(persistedNoteItem());
        when(contentService.updateContent(cosmoItem.capture())).thenAnswer(persistedNoteItem());

        itemCollectionAdapter.postEntry(event, CAL_1_ID, CAL_1_OWNER, DateTimeZone.UTC);

        // Check that what we stored in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), contains(collectionWithUid(CAL_1_ID)));
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(noteItem);
        final VEvent vEvent = stamp.getEvent();
        // each attendee Parameter.DIR is matched against the calendarId as 'entityUriFor://CalendarOwner/" + calendarId'
        final Matcher<Property> attendee1 = allOf(attendeeWithEmail(USER_1_EMAIL), attendeeWithCommonName(USER_1_DISPLAY_NAME), attendeeWithDirectoryEntryReference(USER_1_URI.toString()));
        assertThat("vEvent attendees", (Iterable<? extends Property>) vEvent.getProperties(Property.ATTENDEE), contains(attendee1));
    }

    @Test
    public void whenAddingItemToCollectionsThenAttendeesAreSynced() {
        final String itemUid = UUID.randomUUID().toString();
        final HibNoteItem originalItem = new HibNoteItem();
        originalItem.setUid(itemUid);
        originalItem.addParent(calendar1);
        EventStamp es = new HibEventStamp(originalItem);
        originalItem.addStamp(es);
        es.createCalendar();

        when(contentService.findItemByUid(itemUid)).thenReturn(originalItem);
        doAnswer(itemWithUpdatedParents()).when(contentService).addItemToCollection(any(HibNoteItem.class), same(calendar2));
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(contentService.updateContent(cosmoItem.capture())).thenAnswer(persistedNoteItem());

        // second attendee is set as an additional parent
        itemCollectionAdapter.addAttendeesToEntry(CAL_2_ID, itemUid);

        // Check that what we stored in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), containsInAnyOrder(collectionWithUid(CAL_1_ID), collectionWithUid(CAL_2_ID)));
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(noteItem);
        final VEvent vEvent = stamp.getEvent();
        // each attendee Parameter.DIR is matched against the calendarId as 'entityUriFor://CalendarOwner/" + calendarId'
        final Matcher<Property> attendee1 = allOf(attendeeWithEmail(USER_1_EMAIL), attendeeWithCommonName(USER_1_DISPLAY_NAME), attendeeWithDirectoryEntryReference(USER_1_URI.toString()));
        final Matcher<Property> attendee2 = allOf(attendeeWithEmail(USER_2_EMAIL), attendeeWithCommonName(USER_2_DISPLAY_NAME), attendeeWithDirectoryEntryReference(USER_2_URI.toString()));
        assertThat("vEvent attendees", (Iterable<? extends Property>) vEvent.getProperties(Property.ATTENDEE), containsInAnyOrder(attendee1, attendee2));
    }

}
