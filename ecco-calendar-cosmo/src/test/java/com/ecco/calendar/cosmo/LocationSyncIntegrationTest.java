package com.ecco.calendar.cosmo;

import com.ecco.calendar.config.CalendarServiceConfig;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.config.service.StubSettingsService;
import com.ecco.dom.Individual;
import com.ecco.dom.contacts.Address;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.infrastructure.config.root.Profiles;
import com.ecco.test.support.TestAppContextInitializer;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.notNullValue;
import static org.junit.Assert.assertNotNull;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = TestAppContextInitializer.class,
        classes = {InfrastructureConfig.class,
                CalendarServiceConfig.class,
                LocationSyncIntegrationTest.ServiceConfig.class})
@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
public class LocationSyncIntegrationTest {

    @PersistenceContext
    EntityManager entityManager;

    @Autowired
    @Qualifier("transactionManager")
    PlatformTransactionManager txMgr;

    @Autowired
    LocationSyncAgentTest testSync;

    @Before
    public void setup() {
        testSync.clearCounter();
    }

    @Test
    @Ignore("works locally")
    public void saveNewContact_shouldNotTriggerSync() {
        Individual contact = createContactWithAddress();
        assertNotNull(contact.getId());

        assertThat("trigger occurred when it shouldn't have -"
                        + "which means we will search for many events and slow creating referrals down",
                testSync.getCounter() == 0);
        // we can use mockito, but we actually need to use the messageBus to trigger it - a custom subscriber is easier
        // and we don't have to modify protected methods to public to mock them easily
        //verify(locationSyncAgent, never()).updateProperties(Mockito.any(URI.class), Mockito.any(LocationUpdated.class));
    }

    @Test
    @Ignore("works locally")
    public void updateContact_shouldTriggerSync() {
        Individual contact = createContactWithAddress();

        Address newAddress = new Address();
        newAddress.setLine1("second address");
        contact.setAddress(newAddress);
        merge(contact);

        assertThat("trigger needs to occur on address (Location) change", testSync.getCounter() == 1);
    }

    private Individual createContactWithAddress() {
        Individual individual = Individual.builder("Jim", "Gong").build();
        Address newAddress = new Address();
        newAddress.setLine1("initial address");
        individual.setAddress(newAddress);
        persist(individual);
        assertThat(individual.getId(), notNullValue());
        return individual;
    }

    protected void persist(final Object... entities) {
        new TransactionTemplate(txMgr).execute(status -> {
            for (Object entity : entities) {
                entityManager.persist(entity);
            }
            entityManager.flush();
            return null;
        });
    }

    protected <T> void merge(final T entity) {
        new TransactionTemplate(txMgr).execute(status -> {
            entityManager.merge(entity);
            entityManager.flush();

            return null;
        });
    }

    @Configuration
    public static class ServiceConfig {

        @Bean
        public SettingsService settingsService() {
            return new StubSettingsService();
        }

        @Bean
        public LocationSyncAgentTest registerTestSync(MessageBus<?> messageBus) {
            return new LocationSyncAgentTest(messageBus);
        }

        @Bean
        public SoftwareFeatureService softwareFeatureService() {
            return Mockito.mock(SoftwareFeatureService.class);
        }
    }

}
