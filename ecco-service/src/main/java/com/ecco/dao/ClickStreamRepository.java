package com.ecco.dao;

import com.ecco.dom.ClickStream;
import java.time.ZonedDateTime;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ClickStreamRepository extends JpaRepository<ClickStream, Long> {

    List<ClickStream> findAllByUsernameAndFirstRequestGreaterThanAndLastRequestLessThan(String username, ZonedDateTime firstRequest, ZonedDateTime lastRequest);
}
