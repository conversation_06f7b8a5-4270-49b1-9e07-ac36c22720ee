package com.ecco.dao;

import com.ecco.dao.querydsl.EntityRestrictionCommonPredicates;
import com.ecco.dom.QIndividual;
import com.ecco.dom.QReferral;
import com.ecco.dom.ReferralStatusName;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DatePath;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;

import static com.ecco.dom.QReferral.referral;

/**
 * Used to create Specification objects which are used to create QueryDSL predicate queries for Referrals.
 * Defines specifications for {@link ReferralRepository#findAll(com.querydsl.core.types.Predicate)} and friends.
 */
public abstract class ReferralPredicates {
    /**
     * Creates a specification used to find referrals for clients whose last name begins with the given search term
     * and first name optionally begins with the given search term. This search is case insensitive.
     * @param lastNamePrefix the last name prefix to use when searching
     * @param firstNamePrefix the first name prefix to use when searching
     * @return a specification used to find referrals matching the client name given
     */
    public static BooleanExpression clientNameIsLike(final String lastNamePrefix, final String firstNamePrefix) {
        Assert.hasText(lastNamePrefix, "last name is required");

        QIndividual contact = referral.client.contact;
        BooleanExpression lastNameMatch = contact.lastName.lower().like(lastNamePrefix.toLowerCase() + '%');

        if (StringUtils.isNotBlank(firstNamePrefix)) {
            BooleanExpression firstNameMatch = contact.firstName.lower().like(firstNamePrefix.toLowerCase() + '%');
            return lastNameMatch.and(firstNameMatch);
        } else {
            return lastNameMatch;
        }
    }

    /**
     * @deprecated Use referralPredicate instead. This is only used for 'referrals list' for legacy reasons we could probably drop.
     */
    @Deprecated
    public static Predicate referralPredicateHideDeleteRequested(ReportCriteriaDto dto, EntityRestrictionService restrictionService, RepositoryBasedServiceCategorisationService svcCatsService) {
        return referralPredicateInner(dto, restrictionService, svcCatsService).and(referral.requestedDelete.isNull()).getValue();
    }
    /**
     * Create a predicate for a referral-based query.
     * The default was received date, but we now make this explicit by forcing a referral status.
     */
    public static Predicate referralPredicate(ReportCriteriaDto dto, EntityRestrictionService restrictionService, RepositoryBasedServiceCategorisationService svcCatsService) {
        return referralPredicateInner(dto, restrictionService, svcCatsService).getValue();
    }

    private static BooleanBuilder referralPredicateInner(ReportCriteriaDto dto, EntityRestrictionService restrictionService, RepositoryBasedServiceCategorisationService svcCatsService) {
        // log.info("Running report for referral criteria" + dto.toString());
        BooleanBuilder p = new BooleanBuilder();

        Predicate pSecurity = EntityRestrictionCommonPredicates.applySecurityPredicate(
                referral.serviceRecipient._super._super, dto, restrictionService, svcCatsService);

        p.and(pSecurity);
        // Method is used by controllers but this only a refer to serviceType to set a param
        // allow to primary referrals only - so never return secondaries in referrals -> list, or reports
        if (!Boolean.TRUE.equals(dto.getIncludeRelated())) {
            BooleanExpression hideOnList = referral.serviceRecipient._super.serviceAllocation.service.serviceType.hideOnList.isFalse();
            p.and(hideOnList);
        }

        // restrict to primary referrals only - so never return secondaries in referrals -> list, or reports
        // since secondary referrals often represent additional support for the same primary referral funding
        // NB reports -> support plans do operate differently - they would include all referrals regardless at present
        if (!Boolean.TRUE.equals(dto.getIncludeRelated())) {
            p.and(referral.primaryReferral.id.isNull());
        }

        if (dto.getSupportWorkerId() != null) {
            p.and(referral.supportWorker.id.eq(dto.getSupportWorkerId().longValue()));
        }

        if (dto.getReferralStatus() == null && dto.getSelectionPropertyPath() == null) {
            dto.setReferralStatus(ReferralStatusName.Received.getName());
        }
        applyReferralReportCriteria(p, referral, dto, dto.getFromDate(), dto.getToDate());

        return p;
    }

    public static Predicate applyReferralReportCriteria(BooleanBuilder p, QReferral referral, ReportCriteriaDto dto,
                                                        LocalDate from, LocalDate to) {

        if (dto.getCompanyId() != null) {
//            p.and(referral.serviceRecipient.serviceAllocation.company.id) // HERE discuss with Adam
        }


        if (dto.getGeographicAreaIdSelected() != null) {
            if (CollectionUtils.isEmpty(dto.getGeographicAreaIds())) {
                p.and(referral.srcGeographicArea.id.eq(dto.getGeographicAreaIdSelected()));
            } else {
                p.and(referral.srcGeographicArea.id.in(dto.getGeographicAreaIds()));
            }
        }

        if (dto.getIsChild() != null) {
            if (dto.getIsChild()) {
                p.and(QReferral.referral.parentReferral.id.isNotNull());
            }
            else {
                p.and(QReferral.referral.parentReferral.id.isNull());
            }
        }

        if (dto.getSelectionPropertyPath() != null) {

            // selectionPropertyPath expects to work against the domain object, so we should whitelist referral paths expected
            // NB existing selectionPropertyPath options, such as of supportWork, are set to null in charts/domain.ts processLegacySelectionCriteria

            // Reports for startDate include reviews and flags, but not any longer so could be dropped (see 3b5f082b)
            // Reports for taskStatus include username
            var nonReferralPaths = Arrays.asList("startDate", "targetDate");
            boolean isReferralCriteria = !nonReferralPaths.contains(dto.getSelectionPropertyPath());

            if (isReferralCriteria) {
                LocalDate fromDate = dto.getFromDate();
                LocalDate toDate = dto.getToDate() != null ? dto.getToDate().plusDays(1) : new LocalDate().plusDays(1);

                DatePath<LocalDate> dateField;
                switch (dto.getSelectionPropertyPath()) {
                    case "dateOfDeath":
                        dateField = referral.client.dateOfDeath;
                        break;
                    default:
                        throw new IllegalArgumentException("Unknown property to select on: " + dto.getSelectionPropertyPath());
                }

                BooleanExpression pProperty = fromDate != null ? dateField.goe(fromDate).and(dateField.before(toDate))
                        : dateField.before(toDate);
                p.and(pProperty);
            }
        }

        if (dto.getReferralStatus() != null) {
            ReferralStatusCommonPredicates statusPredicate = new ReferralStatusCommonPredicates(
                    Boolean.TRUE.equals(dto.getNewReferralsOnly()), from,
                    to, ReferralStatusName.fromString(dto.getReferralStatus()));
            p.and(statusPredicate.getReferralStatus(referral));
        }

        //new line
        return p;
    }
}
