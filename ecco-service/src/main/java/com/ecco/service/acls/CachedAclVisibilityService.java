package com.ecco.service.acls;

import static com.google.common.base.Predicates.in;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.function.Predicate;

import com.ecco.dom.ProjectAclId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.ecco.dom.IndividualUserSummary;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.acl.dom.AclObjectIdentity;
import com.ecco.security.service.UserManagementService;
import com.google.common.base.Predicates;
import com.google.common.collect.FluentIterable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ecco.security.dao.acl.AclObjectIdentityRepository;

/**
 * Service for establishing what to show in user lists for various operations
 */
@Service("aclVisibilityService")
@WriteableTransaction
public class CachedAclVisibilityService {

    private final UserManagementService userManagementService;

    private final AclObjectIdentityRepository objectIdentityRepository;

    private final boolean enableAcls;

    CachedAclVisibilityService() {
        // required by CGLIB
        this(null,null,false);
    }

    @Autowired
    public CachedAclVisibilityService(UserManagementService userManagementService,
            AclObjectIdentityRepository objectIdentityRepository,
            @Qualifier("enableAcls") boolean enableAcls) {
        super();
        this.userManagementService = userManagementService;
        this.objectIdentityRepository = objectIdentityRepository;
        this.enableAcls = enableAcls;
    }


    /**
     * Gets the view.
     * @param serviceId - required
     * @param projectId - optional
     * @param role - optional (e.g. "ROLE_STAFF")
     */
    @Cacheable("aclVisibilityService")
    public List<IndividualUserSummary> getUsersWithAccessTo(Long serviceId, Long projectId, String role) {

        if (enableAcls) {
            return getAclUsersWithAccessTo(serviceId, projectId, role);
        }
        return getUsersWithRole(role);

    }

    @Cacheable("aclByGroups")
    public List<IndividualUserSummary> getUsersWithAccessToByGroups(Long serviceId, Long projectId, String... groupNames) {

        if (enableAcls) {
            return getAclUsersWithAccessToGroups(serviceId, projectId, groupNames);
        }
        return getUsersWithGroups(groupNames);

    }

    private List<IndividualUserSummary> getAclUsersWithAccessTo(Long serviceId, Long projectId, String role) {
        // almost same as getAclUsersWithAccessToGroups
        final Collection<String> allUsersWithAccess = findUsersByServiceAndProject(serviceId, projectId);
        Predicate<IndividualUserSummary> userNameContainedIn = new IndividualUserSummary.UserNameContainedIn(allUsersWithAccess);
        List<IndividualUserSummary> usersWithRole = getUsersWithRole(role);
        return usersWithRole == null ? emptyList() : usersWithRole.stream().filter(userNameContainedIn).collect(toList());
    }


    private List<IndividualUserSummary> getUsersWithRole(String role) {
        List<IndividualUserSummary> individuals = role == null ?
                userManagementService.getAllUserIndividuals() :
                userManagementService.findIndividualsWithAuthority(role);

        if (individuals.isEmpty()) {
            return null;
        }

        return individuals;
    }

    private List<IndividualUserSummary> getAclUsersWithAccessToGroups(Long serviceId, Long projectId, String... groupNames) {
        final Collection<String> allUsersWithAccess = findUsersByServiceAndProject(serviceId, projectId);
        Predicate<IndividualUserSummary> userNameContainedIn = new IndividualUserSummary.UserNameContainedIn(allUsersWithAccess);
        List<IndividualUserSummary> usersWithGroups = getUsersWithGroups(groupNames);
        return usersWithGroups == null ? emptyList() : usersWithGroups.stream().filter(userNameContainedIn).collect(toList());
    }

    private List<IndividualUserSummary> getUsersWithGroups(String... groupNames) {
        List<IndividualUserSummary> individuals = groupNames == null ?
                userManagementService.getAllUserIndividuals() :
                userManagementService.findIndividualsWithGroups(groupNames);

        if (individuals.isEmpty()) {
            return null;
        }

        return individuals;
    }


    /**
     * Find usernames whos ACL grants them access to this service and project
     * @param serviceId - required
     * @param projectId - optional
     */
    private List<String> findUsersByServiceAndProject(Long serviceId, Long projectId) {

        List<String> usersWithAccessToService = findUserBySecuredObject(com.ecco.dom.ServiceAclId.class.getName(), serviceId);
        if (projectId == null) {
            // TODO since this method comes from client side code asking for users 'with access to'
            // TODO we should instead assume that the client asks for specific service/project combinations, and that
            // TODO a null project specifically means testing usersWithAccessToAllProjects
            // TODO this will solve DEV-492 issue 'a user without 'access all projects' could see too many staff (since some may not be assigned the same projects)'
            return usersWithAccessToService; // TODO: review and test against use of EntityRestrictionServiceImpl.ACCESS_NULL_PROJECTS
        }
        HashSet<String> usersWithAccessToProject = Sets.newHashSet(
                findUserBySecuredObject(ProjectAclId.class.getName(), projectId));

        HashSet<String> userWithAccessToAllProjects = Sets.newHashSet(
                findUserBySecuredObject(ProjectAclId.class.getName(), ProjectAclId.ACCESS_ALL_PROJECTS));

        // users who have access to the service and project, or all projects

        return FluentIterable.from(usersWithAccessToService)
                .filter(Predicates.or(in(usersWithAccessToProject), in(userWithAccessToAllProjects)))
                .toList();
    }

    private List<String> findUserBySecuredObject(String className, Long objectId) {
        AclObjectIdentity aclObjectIdentity = objectIdentityRepository.findByObjectIdClass_ClassNameAndObjectIdIdentity(className, objectId);

        if (aclObjectIdentity == null) {
            return emptyList();
        }
        return Lists.newArrayList(Lists.transform(aclObjectIdentity.getAclEntries(), entry -> entry.getSid().getSid()));
    }

}
