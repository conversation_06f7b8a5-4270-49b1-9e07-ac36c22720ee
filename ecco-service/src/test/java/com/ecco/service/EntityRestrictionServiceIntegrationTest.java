package com.ecco.service;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.security.acl.AclHandler;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Integration test to verify that the EntityRestrictionService works correctly
 * with Spring's ApplicationContext and that the self-proxy pattern functions
 * as expected in a real Spring environment.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = EntityRestrictionServiceIntegrationTest.TestConfig.class)
public class EntityRestrictionServiceIntegrationTest {

    @Autowired
    private EntityRestrictionService entityRestrictionService;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testGetRestrictedServicesProjectsDto_WorksWithSpringContext() {
        // Given
        TestingAuthenticationToken auth = new TestingAuthenticationToken("testuser", "password", "ROLE_USER");
        SecurityContextHolder.getContext().setAuthentication(auth);

        // When - this should use the self-proxy pattern internally
        RepositoryBasedServiceCategorisationService mockSvcCatsService = mock(RepositoryBasedServiceCategorisationService.class);
        when(mockSvcCatsService.getServiceCategorisationViewModels()).thenReturn(Collections.emptyList());

        ServicesProjectsDto result = entityRestrictionService.getRestrictedServicesProjectsDto(mockSvcCatsService);

        // Then
        assertNotNull("Result should not be null", result);

        // Verify that the service can be retrieved from the application context
        EntityRestrictionService contextService = applicationContext.getBean(EntityRestrictionService.class);
        assertNotNull("Service should be available from ApplicationContext", contextService);
    }

    @Test
    public void testApplicationContextInjection() {
        // Verify that the ApplicationContext is properly injected
        assertNotNull("ApplicationContext should be injected", applicationContext);

        // Verify that our service is registered in the context
        EntityRestrictionService serviceFromContext = applicationContext.getBean(EntityRestrictionService.class);
        assertNotNull("EntityRestrictionService should be available from context", serviceFromContext);
    }

    @Configuration
    static class TestConfig {

        @Bean
        public AclHandler aclHandler() {
            return mock(AclHandler.class);
        }

        @Bean
        public ServiceRepository serviceRepository() {
            ServiceRepository mock = mock(ServiceRepository.class);
            // Mock some basic data for testing
            List<ServiceAclId> mockServices = Arrays.asList(
                new ServiceAclId(1L, "Test Service 1"),
                new ServiceAclId(2L, "Test Service 2")
            );
            when(mock.findAllAclIds()).thenReturn(mockServices);
            return mock;
        }

        @Bean
        public ProjectRepository projectRepository() {
            ProjectRepository mock = mock(ProjectRepository.class);
            // Mock some basic data for testing - use ArrayList to create mutable list
            // since getRestrictedProjectIds() adds ProjectAclId.accessAllProjectsFakeProject
            List<ProjectAclId> mockProjects = new ArrayList<>(Arrays.asList(
                new ProjectAclId(10L, "Test Project 1"),
                new ProjectAclId(20L, "Test Project 2")
            ));
            when(mock.findAllAclIds()).thenReturn(mockProjects);
            return mock;
        }

        @Bean
        public EntityRestrictionService entityRestrictionService(
                AclHandler aclHandler,
                ServiceRepository serviceRepository,
                ProjectRepository projectRepository,
                ApplicationContext applicationContext) {
            return new EntityRestrictionServiceImpl(aclHandler, serviceRepository, projectRepository, applicationContext);
        }
    }
}
