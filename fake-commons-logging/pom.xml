<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <artifactId>commons-logging</artifactId>
    <groupId>commons-logging</groupId>
    <version>99.0.0.EMPTY</version>
    <packaging>jar</packaging>
    <name>fake-commons-logging</name>

    <description>Creates an empty JAR to allow us to use dependency management to
    this version to exclude commons logging.</description>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <!-- http://maven.apache.org/plugins/maven-compiler-plugin/ -->
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>17</source>
                           <target>17</target>
                    </configuration>
                 </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <encoding>utf-8</encoding>
                    </configuration>
                 </plugin>
              </plugins>
        </pluginManagement>
    </build>

</project>
