package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.data.client.ReferralOptions;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.openqa.selenium.WebDriver;

public class ExitedPage extends EccoBasePage {
    private static final DateTimeFormatter DD_MM_YYYY = DateTimeFormat.forPattern("dd/MM/yyyy");
    private static final String URL = "xxx";

    private static final String BUTTON_CLOSE_NAME = "_eventId_closeOff";
    private static final String EXIT_DATE_FIELD = "exited";
    private static final String REASON_DROPDOWN = "exitReason";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public ExitedPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    @Override
    public EccoBasePage defaultAction() {
        closeOff(options.withExitedDate());
        return referralPage;
    }

    private void closeOff(LocalDate exitDate) {
        setDate(EXIT_DATE_FIELD, exitDate.toDate());
        setSelection(REASON_DROPDOWN, "other");

        clickButton(BUTTON_CLOSE_NAME);
    }

}
