package com.ecco.acceptancetests.ui.pages.referral

import com.ecco.acceptancetests.ui.pages.EccoBasePage
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.junit.Assert.assertEquals
import org.openqa.selenium.WebDriver

class ClientModalPage(webDriver: WebDriver) : EccoBasePage(null, webDriver) {
    override fun defaultAction(): EccoBasePage? = null

    fun saveClient(firstName: String, lastName: String, dob: LocalDate?, gender: String?, addressLine1: String, postCode: String) {
        setFieldSoonByXpathSelector("((//form)[1]//input[@type='text'])[1]", firstName)
        setFieldSoonByXpathSelector("((//form)[1]//input[@type='text'])[2]", lastName)

        if (dob != null) {
            val fmtMth = DateTimeFormat.forPattern("MMM")
            setSelection(FIELD_BIRTHDATE_DAY, dob.dayOfMonth?.toString())
            setSelection(FIELD_BIRTHDATE_MONTH, dob.toString(fmtMth))
            setSelection(FIELD_BIRTHDATE_YEAR, dob.year?.toString())
        }
        if (gender != null) {
            setSelection(FIELD_GENDER, gender)
        }

        clickByXpath("//*[contains(@class, 'MuiDialogContent-root')]//a[contains(text(),'create a new one')]")
        setFieldSoonByXpathSelector("((//form)[2]//input[@type='text'])[1]", addressLine1)
        setFieldSoonByXpathSelector("((//form)[2]//input[@type='text'])[5]", postCode)
        clickByXpath("//*[contains(@class, 'dialog')]//span[contains(text(),'save')]") // Span because MUI button embeds a span
    }

    fun assertPage() {
        assertEquals("client details", getTextByCssSelector("#modal-title h6"))
    }

    companion object {
        private val FIELD_BIRTHDATE_DAY = "birthDate.day"
        private val FIELD_BIRTHDATE_MONTH = "birthDate.month"
        private val FIELD_BIRTHDATE_YEAR = "birthDate.year"
        private val FIELD_GENDER = "genderId"
    }
}