package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.ReportListSteps;
import com.ecco.acceptancetests.ui.pages.*;
import org.openqa.selenium.WebDriver;

import org.jspecify.annotations.NonNull;

public class ReportListStepsWebDriver extends WebDriverUI implements ReportListSteps {

    public static String REPORTS_TEXT = "prev period";

    public ReportListStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @NonNull
    @Override
    public ReportReferralPage openReferralReport() {
        ReportListPage reportsPage = reportsListPage();
        ReportReferralPage reportReferralPage = reportsPage.openReferralReport();
        checkCanSeeText(REPORTS_TEXT);
        return reportReferralPage;
    }

    private ReportListPage reportsListPage() {
        //WelcomePage welcomePage = new WelcomePage(webDriver);
        //welcomePage.menu();

        navigate(ReportListPage.URL);
        ReportListPage reportsPage = new ReportListPage(webDriver);
        waitForPageLoaded();

        // verify access
        reportsPage.verifyIsCurrentPage();
        checkCannotSeeText("You do not have security rights to perform that request.");

        return reportsPage;
    }

}
