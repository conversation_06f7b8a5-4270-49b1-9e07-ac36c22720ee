package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.*;

import com.ecco.acceptancetests.ui.smoke.RotaStepsWebDriver;
import com.ecco.data.client.actors.*;
import org.eeichinger.testing.web.WebDriverFactory;
import org.openqa.selenium.WebDriver;
import org.springframework.web.client.RestTemplate;

/**
 * Factory to create application driver instances (i.e. {@link LoginSteps} and {@link AdminSteps})
 *
 * <AUTHOR>
 */
public class UIFactory {

    private static final UIFactory instance = new UIFactory();


    public static UIFactory getInstance() {
        return instance;
    }

    public AdminSteps getAdminUI() {
        return new AdminStepsWebDriver(getCurrentWebDriver());
    }

    public CalendarSteps getCalendarUI() {
        return new CalendarStepsWebDriver(getCurrentWebDriver());
    }

    public HrSteps getHrUI(WorkerActor workerActor) {
        return new HrStepsWebDriver(getCurrentWebDriver(), workerActor);
    }

    public LoginSteps getLoginUI() {
        return new LoginStepsWebDriver(getCurrentWebDriver());
    }

    public GroupSupportContext getGroupSupportUI() {
        return new GroupSupportStepsWebDriver(getCurrentWebDriver());
    }

    public QUnitUI getQUnitUI() {
        return new QUnitUIWebDriver(getCurrentWebDriver());
    }

    public ReferralSteps getReferralUI() {
        return new ReferralStepsWebDriver(getCurrentWebDriver());
    }

    public ReportLoginAuditSteps getReportUserAuditUI() {
        return new ReportLoginAuditStepsWebDriver(getCurrentWebDriver());
    }

    public ReportListSteps getReportListUI() {
        return new ReportListStepsWebDriver(getCurrentWebDriver());
    }

    public RotaSteps getRotaUI(RestTemplate restTemplate, SessionDataActor sessionDataActor, WorkerActor workerActor,
                               AgreementActor agreementActor, ReferralActor referralActor, CalendarActor calendarActor,
                               BuildingActor buildingActor, RotaActor rotaActor,
                               ServiceRecipientActor serviceRecipientActor,
                               ServiceActor serviceActor,
                               HrSteps hrSteps, ReferralSteps referralSteps) {
        return new RotaStepsWebDriver(getCurrentWebDriver(), restTemplate, sessionDataActor, workerActor,
                agreementActor, referralActor, calendarActor,
                buildingActor, rotaActor, serviceRecipientActor,
                serviceActor, hrSteps, referralSteps);
    }

    public SupportPlanContext getSupportPlanUI() {
        return new SupportPlanStepsWebDriver(getReferralUI(), getCurrentWebDriver());
    }

    public RiskManagementContext getRiskManagementUI() {
        return new RiskManagementStepsWebDriver(getReferralUI(), getCurrentWebDriver());
    }

    public UserManagementSteps getUserManagementUI(UserActor userActor) {
        return new UserManagementStepsWebDriver(getCurrentWebDriver(), userActor);
    }

    public SettingsSteps getSettingsUI() {
        return new SettingsStepsWebDriver(getCurrentWebDriver());
    }

    public void closeAllInstances() {
        WebDriverFactory.getInstance().closeAllInstances();
    }

    private static WebDriver getCurrentWebDriver() {
        return WebDriverFactory.getInstance().getCurrentWebDriver();
    }

    public OfflineStepsWebDriver getOfflineUI() {
        return new OfflineStepsWebDriver(getCurrentWebDriver());
    }
}
