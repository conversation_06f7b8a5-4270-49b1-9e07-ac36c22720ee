package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

import org.joda.time.LocalDate;
import org.openqa.selenium.WebDriver;

import static com.ecco.acceptancetests.ui.pages.BasePageObject.DEFAULT_SAVE_TIMEOUT;

public class FundingPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";
    private static final String FUNDING_DECISION_DATE_FIELD = "fundingDecisionDate";
    private static final String ACCEPTED_FUNDING_CHECKBOX = "fundingAccepted";

    ReferralViewPage referralPage;

    public FundingPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    /**
     * Default data - pick the first project.
     */
    @Override
    public EccoBasePage defaultAction() {
        fundingAccepted(LocalDate.now());
        return referralPage;
    }

    private void fundingAccepted(LocalDate decisionDate) {
        setDatePickerField(FUNDING_DECISION_DATE_FIELD, decisionDate);
        setCheckbox(ACCEPTED_FUNDING_CHECKBOX, true);

        clickButtonByText("save");
        waitForSavedAlert(DEFAULT_SAVE_TIMEOUT);
        clickLink("back to referral");
    }

}
