package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.expectations.SupportPlanExpectation;
import com.ecco.acceptancetests.fixtures.SupportPlanFixture;
import com.ecco.acceptancetests.givens.SupportPlanGiven;
import com.ecco.data.client.ServiceOptions;
import com.ecco.acceptancetests.steps.SupportPlanContext;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.ReferralViewModel;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

import static com.ecco.acceptancetests.ui.pages.supportplan.SupportPlanBasePage.TAB_OUTCOME_EW;
import static com.ecco.acceptancetests.ui.pages.supportplan.SupportPlanBasePage.TAB_OUTCOME_UT;
import static com.ecco.data.client.ServiceOptions.ACCOMMODATION;

public class SupportPlanTests extends BaseSeleniumTest {

    private final ReferralViewModel referral = new ReferralViewModel(); // This will be replaced by .as("referral") 'magic'
    private SupportPlanGiven given;
    private SupportPlanContext when;
    private SupportPlanExpectation expect;

    @BeforeEach
    public void setUp() {
        given = supportPlanSteps.given(this);
        expect = supportPlanSteps.expect();
        when = given.when();
    }

    @Test
    public void cannotOpenSupportPlanIfNoNeedsAssessment() {

        login("sysadmin"); // given I am sysadmin
        givenAllFeaturesEnabledOn(ACCOMMODATION);


        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        String firstName = "James-" + System.currentTimeMillis();
        given.createReferral(firstName, "Blunted", dte, ACCOMMODATION).as("referral");

        // when i open a support plan
        when.openSupportPlan(referral.getClientFirstName(), referral.getClientLastName());

        // i expect to be told to complete a needs assessment
        expect.canSeeText("no information appears in this section since it was not completed in the 'needs assessment'");

        // when I view attachments
        // TODO

        // I don't get an error
        // TODO
        logout();
    }

    @Test
    public void userCanCreateANeedsAssessment() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Blunt", dte, ACCOMMODATION).as("referral");

        // when i open a needs assessment and set some sensible data and then save it
        when
            .openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new Date())
            .setComment("needs assessment comment")
            .changeTab(TAB_OUTCOME_EW)
            .addAction("Support to manage debt")
            .addAction("Advice in relation to paid employment whilst a resident of the project.")
            .save();

        // i expect the needs assessment to be saved successfully
        expect.onSupportHistoryTab()
                .canSeeText("needs assessment comment")
                .canSeeText("Support to manage debt")
                .canSeeText("Advice in relation to paid employment whilst a resident of the project.");

        logout();
    }

    @Test
    public void cannotCreateANeedsAssessmentWithoutADate() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Blunt", dte, ACCOMMODATION).as("referral");

        // when i try to create a needs assessment without a date
        when
            .openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
//            .setDate(null) // TODO: implement clearDate() ?
            .setComment("dodgy comment with no work date");

        // i expect to be told enter a date
        expect.fieldHasValidationErrorByLabel("took place on", "is required");

        logout();
    }

    @Test
    public void cannotCreateANeedsAssessmentWithoutAComment() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Blunt", dte, ACCOMMODATION).as("referral");

        // when i try to create a needs assessment without a comment
        when.openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new Date());

        // i expect to be told to enter a comment
        expect.autoSavefieldHasValidationErrorByName("comment", "is required");

        logout();
    }

    @Test
    public void cannotCreateANeedsAssessmentWithAFutureDate() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Blunt", dte, ACCOMMODATION).as("referral");

        // when i try to create a needs assessment with a future date
        when.openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new DateTime().plusDays(2).toDate())
            .setComment("future work date");

        // i expect to be told this is not allowed, but i am not told why.
        expect.fieldHasValidationErrorByLabel("took place on", "cannot be future dated");

        logout();
    }


    @Test
    public void needsAssessmentCanBeBuiltFromMultipleTabs() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Sharpish", dte, ACCOMMODATION).as("referral");

        // when i create a needs assessment with actions from multiple tabs
        when.openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new Date())
            .setComment("needs assessment comment")
            .changeTab(TAB_OUTCOME_EW)
            .addAction("Support to manage debt")
            .addAction("Advice in relation to paid employment whilst a resident of the project.")
            .changeTab(TAB_OUTCOME_UT)
            .addAction("Accept support and engage with keyworker to investigate further education courses.")
            .addAction("Plan for and ensure attendance at all keyworker session.")
            .save();

        // and when I subsequently open the support plan for james blunt
        var fixture = when.openSupportPlan(referral.getClientFirstName(), referral.getClientLastName());

        // First tab is not active TODO: could auto select the first tab with content
        fixture.changeTab(TAB_OUTCOME_EW);
        expect
            .canSeeText("Support to manage debt")
            .canSeeText("Advice in relation to paid employment whilst a resident of the project.");

        // and when I navigate to the second tab
        fixture.changeTab(TAB_OUTCOME_UT);

        // i expect to see the use of time actions from the needs assessment
        expect
            .canSeeText("Accept support and engage with keyworker to investigate further education courses.")
            .canSeeText("Plan for and ensure attendance at all keyworker session.");

        logout();
    }

    @Test
    public void canSetATargetDateForASupportPlanAction() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 10);
        String task = "Plan for and ensure attendance at all keyworker session.";

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Sharpest", dte, ACCOMMODATION).as("referral");

        // when i create a needs assessment and save it
        when.openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new Date())
            .setComment("needs assessment comment")
            .changeTab(TAB_OUTCOME_EW)
            .addAction("Support to manage debt")
            .addAction("Advice in relation to paid employment whilst a resident of the project.")
            .changeTab(TAB_OUTCOME_UT)
            .addAction("Accept support and engage with keyworker to investigate further education courses.")
            .addAction("Plan for and ensure attendance at all keyworker session.")
            .save();

        // and then open the support plan and set a target date for one of the tasks
        when.openSupportPlan(referral.getClientFirstName(), referral.getClientLastName())
            .setDate(new Date())
            .setComment("support plan work")
            .changeTab(TAB_OUTCOME_UT)
            .addTarget(task, calendar.getTime())
            .save();

        expect.onSupportHistoryTab()
            .canSeeText("target:");


        logout();
    }

    @Test
    public void userCanCreateNeedsAssessmentAndReview() {

        login("sysadmin");

        // given a referral exists for james blunt
        LocalDate dte = new LocalDate(2014, 2, 13);
        given.createReferral("James", "Blunt", dte, ACCOMMODATION).as("referral");

        // when i create a needs assessment
        when.openNeedsAssessment(referral.getClientFirstName(), referral.getClientLastName())
                .setDate(new Date())
                .setComment("needs assessment comment")
                .save();

        // and open the review
        when.reviewSetup(referral.getClientFirstName(), referral.getClientLastName(), LocalDate.now());

        // i expect the review process to proceed tab-by-tab
        testReviewTab(1, "Support to encourage discussion with Key Worker about use of temptation to use drugs/alcohol");
        testReviewTab(2, "Engage and accept support from keyworker the impact substance mis-use has on physical and mental health.");
        testReviewTab(3, "Support to maximise income eg - correct welfare benefits, application for grants etc.");
        testReviewTab(4, "Accept support and engage with keyworker to investigate any opportunities with employment Agency opportunities");

        logout();
    }

    // the reason this works is that the app's behaviour is to always setfocus on the first review tab
    // without a target
    public void testReviewTab(int id, String action) {

        SupportPlanFixture reviewWhen = when
                .reviewSetup(referral.getClientFirstName(), referral.getClientLastName(), null);

        expect.canSeeText(action);

        reviewWhen
            .setDate(new Date())
            .addTarget(action, new Date())
            .setComment("review comment") // NOTE: Do this last as otherwise it leaves datepicker active and obscures save button
            .save();

        if (id < 4) {
            expect.canSeeText("review progress: " + (id * 25) + "%");
        }
        else {
            expect.canSeeText("100%"); // This is on the referral overview tasks tab
        }
    }

    private void givenAllFeaturesEnabledOn(ServiceOptions service) {
        // given lots of features are enabled
        Integer serviceTypeId = serviceTypeActor.getServiceTypeByName(service.getServiceName()).getBody().id;
        serviceTypeActor.changeTaskSetting(serviceTypeId, EvidenceTask.NEEDS_REDUCTION.getTaskName(), "showMenus",
                "calendar", "access", "spidergraph", "overview", "attachments", "addGoal");
        serviceTypeActor.changeTaskSetting(serviceTypeId, EvidenceTask.NEEDS_REDUCTION.getTaskName(), "showCommentComponents",
                "showRiskManagementRequired", "minutesSpent", "type", "attachments", "clientStatus", "meetingStatus",
                "mileage", "travelMinutes");
    }

}
