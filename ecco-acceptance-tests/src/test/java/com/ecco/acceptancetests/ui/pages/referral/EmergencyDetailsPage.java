package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.data.client.ReferralOptions;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class EmergencyDetailsPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralFlow.html";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public EmergencyDetailsPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    /**
     * Default data - none needed, so simply hit the 'next' button
     */
    @Override
    public EccoBasePage defaultAction() {
        verifyIsCurrentPage();
        try {
            setField("descriptionDetails", "really tall"); // New UI
        }
        catch (Exception e) {
            setField("description", "really tall");
        }

        clickButtonByText("save");
        try {
//            clickLink("back to referral");
        }
        catch (Exception e) {
            // ignore because save takes us back to the referral
        }
        return referralPage;
    }

}
