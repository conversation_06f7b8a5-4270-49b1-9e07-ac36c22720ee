package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.pages.Role;

import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

public class AdminTests extends BaseSeleniumTest {

    @Test
    public void checkStaffCantSeeAdminPage() throws Exception {
        login("sysadmin");
        String staff1 = userManagementSteps.createUser("staff1", Role.staff);
        loginSteps.logout();

        // check the new user can't see the admin page
        loginSteps.login("staff1");
        loginSteps.checkCanSeeText(staff1);

        try {
            adminSteps.deleteReferralClient();
            fail();
        } catch (AssertionError e) {
            // TODO: we should be testing that the user got an error message, possibly even a 4xx error
            // but for now we check that the assert of verifyCannotSee in normal operations actually produces what we want
            assertThat(e.getMessage(), equalTo("Text [You do not have security rights to perform that request.] should not be visible, but it is"));
        }

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

    @Test
    public void checkSysadminCanSeeAdminPage() throws Exception {
        loginSteps.loginAsSysadmin();

        adminSteps.deleteReferralClient();

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

    @Test
    public void checkAdminCanSeeDeletionsPage() throws Exception {
        login("sysadmin");
        String admin1 = userManagementSteps.createUser("admin1", Role.site_sysadmin);
        loginSteps.logout();

        // check the new user can't see the admin page
        loginSteps.login("admin1");
        loginSteps.checkCanSeeText(admin1);

        adminSteps.deleteReferralClientDirect();

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

}
