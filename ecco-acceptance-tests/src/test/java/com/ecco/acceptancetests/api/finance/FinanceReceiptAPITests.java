package com.ecco.acceptancetests.api.finance;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.dto.ChangeViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.finance.FinanceReceiptCommandViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static org.junit.Assert.*;

public class FinanceReceiptAPITests  extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;

    @Test
    public void createUpdateDeleteReceipt() {
        // CREATE
        String firstNameUnique = unique.clientFirstNameFor("finance");
        String lastNameUnique = unique.clientLastNameFor("receipt");
        ReferralViewModel vm1 = referralActor.createMinimalReferralAndClient("FINREC1", firstNameUnique, lastNameUnique, DEMO_ALL);
        var result = financeReceiptActor.createFinanceReceipt(vm1.serviceRecipientId, LocalDate.now(), new BigDecimal("99.45"), "broke the TV");
        assertNotNull(result.receiptId);
        assertEquals(new BigDecimal("99.45"), result.getAmount());
        assertEquals(LocalDate.now(), result.getReceivedDate());
        assertEquals("broke the TV", result.getDescription());
        assertEquals(vm1.serviceRecipientId, result.getServiceRecipientId());

        // CREATE + UPDATE + DELETE
        String firstNameUpdateUnique = unique.clientFirstNameFor("finance");
        String lastNameUpdateUnique = unique.clientLastNameFor("receipt-update");
        ReferralViewModel vmUpdate1 = referralActor.createMinimalReferralAndClient("FINREC-UPD1", firstNameUpdateUnique, lastNameUpdateUnique, DEMO_ALL);
        financeReceiptActor.createFinanceReceipt(vmUpdate1.serviceRecipientId, LocalDate.now(), new BigDecimal("11.11"), "broke the pen");
        FinanceReceiptCommandViewModel command = new FinanceReceiptCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, vmUpdate1.serviceRecipientId);
        command.receiptId = result.receiptId;
        command.amount = ChangeViewModel.create(new BigDecimal("11.11"), new BigDecimal("11.22"));
        command.description = ChangeViewModel.create("broke the pen", "broke the dolly");
        command.receivedDate = ChangeViewModel.create(LocalDate.now(), LocalDate.now().minusDays(1));
        commandActor.executeCommand(command);
        var update = financeReceiptActor.getFinanceReceiptById(result.receiptId);
        assertEquals(result.receiptId, update.receiptId);
        assertEquals(new BigDecimal("11.22"), update.getAmount());
        assertEquals(LocalDate.now().minusDays(1), update.getReceivedDate());
        assertEquals("broke the dolly", update.getDescription());
        assertEquals(vm1.serviceRecipientId, update.getServiceRecipientId());

        // DELETE
        FinanceReceiptCommandViewModel commandDel = new FinanceReceiptCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, vmUpdate1.serviceRecipientId);
        commandDel.receiptId = result.receiptId;
        commandActor.executeCommand(commandDel);
        assertThrows(HttpServerErrorException.class, () -> financeReceiptActor.getFinanceReceiptById(result.receiptId));
    }

}
