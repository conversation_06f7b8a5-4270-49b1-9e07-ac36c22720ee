package com.ecco.integration.northgate;

import com.ecco.integration.core.AfterStartup;
import com.ecco.integration.core.WebSecurityConfigurer;
import com.ecco.integration.core.WebSocketTunnelConfiguration;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

@Configuration
@ComponentScan
@EnableAutoConfiguration
// @EnableConfigurationProperties(QLSettings.class)
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
@PropertySource("classpath:git.properties")
@Import({WebSecurityConfigurer.class, WebSocketTunnelConfiguration.class})
public class Application {

    @Bean
    public AfterStartup afterStartup(NorthgateClientQueryAdapter queryAdapter) {
        return new AfterStartup(queryAdapter::queryClientsByExample);
    }

    @Bean
    public PropertiesFactoryBean referenceDataMapping() {
        PropertiesFactoryBean bean = new PropertiesFactoryBean();
        bean.setLocation(new ClassPathResource("/com/ecco/integration/northgate/mapping.properties"));
        return bean;
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
