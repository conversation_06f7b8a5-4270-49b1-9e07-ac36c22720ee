package com.ecco.config.service;

import org.jspecify.annotations.NonNull;

import com.ecco.config.dom.Setting;
import com.ecco.infrastructure.annotations.WriteableTransaction;

/**
 * A simple interface for getting and setting system-wide settings
 */
@WriteableTransaction
public interface SettingsService {

    interface SettingKey {
        String namespace();
        String key();
    }

    /** Get a value for a given setting namespace and key
     * @return the setting associated with {@param key} or {@code null} if the key is not found
     */
    Setting settingFor(String namespace, String key);

    /** Get a value for a given setting
     * @return the setting associated with {@param key}. It is a configuration error for the setting to not exist
     * @throws IllegalArgumentException if key is not found
     */
    @NonNull
    Setting settingFor(SettingKey key);

    /** Create or update a setting
     */
    @SuppressWarnings("SameParameterValue")
    void setSetting(String namespace, String key, String value);

    // TODO: migrate to enum when all namespaces are captured from elsewhere
    interface Namespace {
        String KERBEROS_SETTINGS_NAMESPACE = "com.ecco.authn.kerberos";
        String ACTIVEDIRECTORY_SETTINGS_NAMESPACE = "com.ecco.authn.ad";
        String WORKFLOW = "com.ecco.workflow";
        String AgencyScope = "com.ecco.contacts";
    }

    enum Reports implements SettingKey {
        PageSizeDefault("com.ecco.report", "pageSize.default"),
        PageSizeClients("com.ecco.report", "pageSize.clients"),
        PageSizeReferrals("com.ecco.report", "pageSize.referrals"),
        PageSizeContacts("com.ecco.report", "pageSize.contacts"),
        PageSizeSupportWork("com.ecco.report", "pageSize.supportWork"),
        PageSizeRiskWork("com.ecco.report", "pageSize.riskWork"),
        PageSizeRiskWorkFlags("com.ecco.report", "pageSize.riskWorkFlags"),
        PageSizeQuestionnaireWork("com.ecco.report", "pageSize.questionnaireWork"),
        PageSizeCustomFormWork("com.ecco.report", "pageSize.customFormWork"),
        PageSizeActivities("com.ecco.report", "pageSize.activities"),
        PageSizeAttendances("com.ecco.report", "pageSize.activityAttendances"),
        PageSizeReviews("com.ecco.report", "pageSize.reviews"),
        PageSizeCommands("com.ecco.report", "pageSize.commands"),
        PageSizeUsers("com.ecco.report", "pageSize.users"),
        PageSizeTasks("com.ecco.report", "pageSize.tasks");

        private final String namespace, key;
        Reports(String namespace, String key) {
            this.namespace = namespace;
            this.key = key;
        }
        public String namespace() { return namespace; }
        public String key() { return key; }
    }

    enum Evidence implements SettingKey {
        PageSizeHistory("com.ecco.evidence", "pageSize.history");

        private final String namespace, key;
        Evidence(String namespace, String key) {
            this.namespace = namespace;
            this.key = key;
        }
        public String namespace() { return namespace; }
        public String key() { return key; }
    }

    enum Audits implements SettingKey {
        PageSizeAudits("com.ecco.audits", "pageSize.history");

        private final String namespace, key;
        Audits(String namespace, String key) {
            this.namespace = namespace;
            this.key = key;
        }
        public String namespace() { return namespace; }
        public String key() { return key; }
    }

    interface SecurityClientSearch {
        String NAMESPACE = "com.ecco.authn.clientsearch";
    }
    interface SecurityAuthnPasswd {
        String NAMESPACE = "com.ecco.authn.password";
    }
    interface SecurityAuthnAcl {
        String NAMESPACE = "com.ecco.authn.acl";
        String CONFIG_NAMESPACE = "com.ecco.authn.acl.config"; // see userManagementFlow
        //
    }
    interface SecurityGroups {
        String NAMESPACE = "com.ecco.authn.groups";
        String GROUPS_TO_EXCLUDE = "GroupsToExclude";
    }
    interface Mail {
        String NAMESPACE = "com.ecco.mail";
        String ENABLED = "ENABLED";
    }
    interface MailGun {
        String NAMESPACE = "com.ecco.mail.mailgun";
        String API_KEY = "apiKey";
        String DOMAIN = "sourceEmailDomain";
    }
    interface AgencySearchScope {
        String NAMESPACE = "com.ecco.contacts";
        String AGENCY_SCOPE = "AgencyScope";
    }
}
