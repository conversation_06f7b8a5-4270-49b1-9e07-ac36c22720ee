package com.ecco.config.repositories;

import com.ecco.config.dom.ListDefinitionEntry;

import org.jspecify.annotations.NullMarked;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

@NullMarked
public interface ListDefinitionRepository extends JpaRepository<ListDefinitionEntry, Integer> {

    @Cacheable(cacheNames={"listDefinitionsById"}, key="'ALL'")
    List<ListDefinitionEntry> findAll();

    @Cacheable(cacheNames="listDefinitionsById")
    Optional<ListDefinitionEntry> findById(Integer id);

    Optional<ListDefinitionEntry> findByBusinessKey(String businessKey);

    List<ListDefinitionEntry> findByListName(String listName);

    List<ListDefinitionEntry> findByParentId(Integer parentId);

    Optional<ListDefinitionEntry> findOneByListNameAndName(String listName, String name);

    List<ListDefinitionEntry> findByListNameAndName(String listName, String name);

    @Override
    @CacheEvict(cacheNames="listDefinitionsById", allEntries=true)
    <S extends ListDefinitionEntry> S save(S entity);

    @CacheEvict(cacheNames="listDefinitionsById", allEntries=true)
    Iterable<ListDefinitionEntry> save(Iterable<ListDefinitionEntry> entities);
}
