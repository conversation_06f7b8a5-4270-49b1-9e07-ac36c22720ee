package com.ecco.integration.core;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.net.URI;

import lombok.Getter;
import lombok.Setter;

/**
 * Settings for ecco configuration properties.
 *
 * @since 01/08/2016
 */
@Setter
@Getter
@ConfigurationProperties(prefix = "ecco")
public class EccoSettings {

    private InstanceSettings instance;

    @Setter
    @Getter
    public static class InstanceSettings {

        private URI baseUrl;
        private TunnelSettings tunnel;

        @Setter
        @Getter
        public static class TunnelSettings {
            private boolean enabled;
            private URI webSocketPath;
        }
    }
}
