package com.ecco.integration.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Properties;

@Slf4j
public class Utils {

    public static String getMappedReferenceData(Properties referenceDataMapping, String prefix, String key) {
        return getMappedReferenceData(referenceDataMapping, prefix + "." + StringUtils.trim(key));
    }

    public static String getMappedReferenceData(Properties referenceDataMapping, String key) {
        String result = referenceDataMapping.getProperty(StringUtils.trim(key));
        if (result == null) {
            log.warn("No mapping found for database value '" + key + "'");
        }
        return result;
    }

}
