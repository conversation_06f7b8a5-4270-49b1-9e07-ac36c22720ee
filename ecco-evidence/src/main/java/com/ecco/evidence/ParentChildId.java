package com.ecco.evidence;

import org.jspecify.annotations.Nullable;

/**
 * A class to hold parentId and childId
 */
public class ParentChildId {

    private final int parentId;
    @Nullable
    private final Integer childId;

    /**
     * @param parentId the parentId, or 'this' id
     * @param childId the childId if it exists
     */
    public ParentChildId(int parentId, @Nullable Integer childId) {
        this.parentId = parentId;
        this.childId = childId;
    }

    public int getParentServiceRecipientId() {
        return parentId;
    }
    @Nullable
    public Integer getChildServiceRecipientId() {
        return childId;
    }

    /**
     * TODO sort this parent/child:
     * Since the parentId is the default if there is no parent
     * we need to determine if there is a parent from the child!
     */
    public boolean hasParentId() {
        return childId != null;
    }

}
