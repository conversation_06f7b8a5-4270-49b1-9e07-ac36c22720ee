package com.ecco.evidence.dom;

import java.io.Serializable;
import java.util.UUID;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;

import org.hibernate.annotations.Type;

import lombok.*;

@Access(AccessType.FIELD)
@EqualsAndHashCode
@Getter
@Setter
@AllArgsConstructor
public class WorkActionPk implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column
    private Long actionId;

    @Column(columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID workUuid;


    WorkActionPk() {
    }
}
