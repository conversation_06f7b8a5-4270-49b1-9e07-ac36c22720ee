package com.ecco.evidence.dom;

import java.util.UUID;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "evdnc_supportwork_actions")
public class EvidenceSupportWorkAction {
    @EmbeddedId
    private WorkActionPk workActionId;

    EvidenceSupportWorkAction() {
    }

    public EvidenceSupportWorkAction(long actionDefId, UUID workUuid) {
        workActionId = new WorkActionPk(actionDefId, workUuid);
    }

    public WorkActionPk getWorkActionId() {
        return workActionId;
    }
    public void setWorkActionId(WorkActionPk workActionId) {
        this.workActionId = workActionId;
    }
}
