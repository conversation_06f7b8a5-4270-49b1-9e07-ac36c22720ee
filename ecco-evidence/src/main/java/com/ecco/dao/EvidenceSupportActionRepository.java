package com.ecco.dao;

import com.ecco.dom.EvidenceSupportAction;
import com.ecco.evidence.dom.AssociatedAction;
import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import java.util.List;
import java.util.UUID;

/**
 * @since 21/09/2014
 */
public interface EvidenceSupportActionRepository extends Repository<EvidenceSupportAction, Long> {

    @Query("SELECT NEW com.ecco.dao.EvidenceSupportActionSummary(" +
            " a.id, c.name, a.goalName, a.goalPlan, a.actionInstanceUuid," +
            " a.parentActionInstanceUuid, a.hierarchy, a.position," +
            " c.id, r.id, o.id," +
            " a.status, a.statusChange, a.score, a.statusChangeReason.id," +
            " a.target, a.targetSchedule, a.expiryDate, a.work.id, a.work.workDate" +
            ")" +
            " from EvidenceSupportAction a left join a.action c left join c.actionGroup r left join r.outcome o" +
            " where a.work.serviceRecipient.id = ?1")
    List<EvidenceSupportActionSummary> findAllSupportActionSummaryByWork_serviceRecipientId(Integer serviceRecipientId);


    @Query("SELECT new com.ecco.evidence.dom.AssociatedAction(a.id, w.id)" +
            " FROM EvidenceSupportWork w JOIN w.associatedActions a" +
            " WHERE w.serviceRecipient.id = ?1")
    List<AssociatedAction> findAllAssociatedActionsByWork_serviceRecipientId(Integer serviceRecipientId);

    /**
     * DEPRECATED - delete it, in favour of finding the whole snapshot at a point in time using existing code (then filter).
     * This is because this query finds the latest 'created', just as the below deprecated method finds the latest id', but these only works
     * because at the moment we only ever allow users to see the latest snapshot - but when it comes to editing, this won't work.
     */
    @NonNull
    @Query("SELECT action FROM EvidenceSupportAction action " +
            "WHERE action.serviceRecipient.id = ?1 " +
            "    AND action.actionInstanceUuid = ?2 " +
            "    AND action.created <= ?3 " +
            "ORDER BY action.created DESC")
    @Deprecated
    List<EvidenceSupportAction> findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo(
            int serviceRecipientId, UUID actionInstanceUuid,
            @NonNull DateTime timestamp, @NonNull Pageable pageable);

    @NonNull
    EvidenceSupportAction save(@NonNull EvidenceSupportAction action);

    EvidenceSupportAction findFirstByActionInstanceUuidOrderByCreatedDesc(UUID actionInstanceUuid);
}
