package com.ecco.dao;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import com.querydsl.core.annotations.QueryProjection;

/** Represents UUIDs of support work which are managed by the UUID threat work */
public class EvidenceSupportWorkRiskManagementHandledSummary {

    @NonNull
    private final UUID supportWorkUuid;

    @NonNull
    private final UUID threatWorkUuid;

    // for cglib
    EvidenceSupportWorkRiskManagementHandledSummary() {
        this.supportWorkUuid = null;
        this.threatWorkUuid = null;
    }

    @QueryProjection
    public EvidenceSupportWorkRiskManagementHandledSummary(@NonNull UUID supportWorkUuid, @NonNull UUID threatWorkUuid) {
        this.supportWorkUuid = supportWorkUuid;
        this.threatWorkUuid = threatWorkUuid;
    }

    public UUID getSupportWorkUuid() {
        return supportWorkUuid;
    }

    public UUID getThreatWorkUuid() {
        return threatWorkUuid;
    }

}
