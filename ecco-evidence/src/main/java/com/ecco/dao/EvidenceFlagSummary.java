package com.ecco.dao;

import lombok.Getter;

import java.util.UUID;

/**
 * Internal representation of a work flag.
 */
@Getter
public class EvidenceFlagSummary {

    private final Long id;
    private final Integer flagId;
    private final UUID workId;
    private final boolean value;

    EvidenceFlagSummary() {
        this(null, null, false, null); // for cglib
    }

    public EvidenceFlagSummary(Long id, Integer flagId, boolean value, UUID workId) {
        this.id = id;
        this.flagId = flagId;
        this.value = value;
        this.workId = workId;
    }

}
