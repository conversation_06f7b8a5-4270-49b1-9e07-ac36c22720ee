package com.ecco.dom;

import java.util.UUID;

import javax.persistence.*;

import com.ecco.serviceConfig.dom.Question;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@javax.persistence.Entity
@javax.persistence.Table(name="evdnc_supportanswers")
@NamedQueries(@NamedQuery(name = EvidenceSupportAnswer.BULK_UPDATE_AUTHOR_QUERY, query = "update EvidenceSupportAnswer set author = :newContact where author = :oldContact"))
public class EvidenceSupportAnswer extends EvidenceAnswer implements ServiceRecipientId {

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceSupportAnswer.bulkUpdateAuthor";

    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceSupportWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    EvidenceSupportWork work;

    public EvidenceSupportAnswer() {
        // for Hibernate etc
    }

    public EvidenceSupportAnswer(int serviceRecipientId) {
        super(serviceRecipientId);
    }


    public EvidenceWork getWork() {
        return work;
    }
    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    @Override
    public Integer getParentId() {
        return getServiceRecipientId();
    }

    public void setWork(EvidenceSupportWork work) {
        this.work = work;
    }

    public static class Builder {

        private final EvidenceSupportAnswer a;

        private Builder(EvidenceSupportAnswer a) {
            this.a = a;
        }

        public EvidenceSupportAnswer build() {
            return a;
        }

        public Builder withQuestionId(long id) {
            a.question = new Question();
            a.question.setId(id);
            return this;
        }

        public Builder withAnswer(String answer) {
            a.answer = answer;
            return this;
        }
    }

    public static Builder builder(int serviceRecipientId, long questionId) {
        return builder(serviceRecipientId)
                .withQuestionId(questionId);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceSupportAnswer a = new EvidenceSupportAnswer(serviceRecipientId);
        return new Builder(a);
    }

    /** Create a new action with defaults from the previous action */
    public static EvidenceSupportAnswer fromPrevious(EvidenceSupportAnswer previousSnapshot) {
        EvidenceSupportAnswer newSnapshot = new EvidenceSupportAnswer(previousSnapshot.getServiceRecipientId());
        newSnapshot.question = previousSnapshot.question;
        newSnapshot.answer = previousSnapshot.answer;
        return newSnapshot;
    }

}
