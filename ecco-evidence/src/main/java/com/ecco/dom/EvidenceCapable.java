package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;

/**
 * A thing that is capable of supporting the recording of evidence and therefore supports getServiceRecipient() to
 * find the related common entity.
 */
public interface EvidenceCapable {

    BaseServiceRecipient getServiceRecipient();

    /**
     * See if the parent of this service recipient (eg Referral)
     * has siblings underneath a grand parent - eg Client.
     * This is a useful when deleting the last sibling to see if we can
     * also delete the grand parent - which is often a token record without commands etc.
     * @return count or null if unknown (not implemented)
     */
    Integer countSiblings();

    /**
     * Get the grand parent of this service recipient (eg Client)
     */
    Identified getGrandParent();

    /**
     * Get the parent ServiceRecipient, if there is one
     * @return ServiceRecipient (e.g. Referral, Worker) or null if there isn't one
     */
    EvidenceCapable getParentEvidenceCapable();
}
