package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.dom.TaskStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
public abstract class BaseServiceRecipientEvidence extends BaseServiceRecipient {

    private static final long serialVersionUID = 1L;

    @SuppressWarnings("unused")
    @OneToMany(mappedBy = "serviceRecipient", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<Review> reviews;

    @SuppressWarnings("unused")
    @OneToMany(mappedBy = "serviceRecipient", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<EvidenceSupportWork> supportWork;

    @SuppressWarnings("unused")
    @OneToMany(mappedBy = "serviceRecipient", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<EvidenceThreatWork> threatWork;

    @SuppressWarnings("unused")
    @OneToMany(mappedBy = "serviceRecipient", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<EvidenceFormWork> formWork;

    @SuppressWarnings({"JpaModelReferenceInspection", "unused"})
    @OneToMany(mappedBy = "serviceRecipientId", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<TaskStatus> tasks;

    /** return the Referral, Worker etc */
    public abstract EvidenceCapable getTargetEntity();

}