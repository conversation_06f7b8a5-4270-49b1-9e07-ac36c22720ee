package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("signWork")
public class SignWorkCommand extends GoalCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public SignWorkCommand() {
    }

    public SignWorkCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                           long userId, @NonNull String body, int serviceRecipientId, String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, null, evidenceGroupKey, taskName);
    }
}
