package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("goalUpdate")
public class GoalUpdateCommand extends GoalCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public GoalUpdateCommand() {
    }

    public GoalUpdateCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                             long userId, @NonNull String body, int serviceRecipientId,
                             long actionDefId, @NonNull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, actionDefId, evidenceGroupKey, taskName);
    }
}
