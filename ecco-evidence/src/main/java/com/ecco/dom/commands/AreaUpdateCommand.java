package com.ecco.dom.commands;

import java.util.UUID;
import org.jspecify.annotations.NonNull;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("areaUpdate")
public class AreaUpdateCommand extends ServiceRecipientCommand {
    @NonNull
    @Column(nullable = false)
    protected String evidenceGroupKey;

    @Nullable
    @Column(nullable = true)
    protected Long areaDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public AreaUpdateCommand() {
        super();
    }

    public AreaUpdateCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                             long userId, @NonNull String body, int serviceRecipientId,
                             @Nullable Long areaDefId, @NonNull String evidenceGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
        this.areaDefId = areaDefId;
        this.evidenceGroupKey = evidenceGroupKey;
    }

    @NonNull
    public String getEvidenceGroupKey() {
        return evidenceGroupKey;
    }

    public long getAreaDefId() {
        return areaDefId;
    }
}
