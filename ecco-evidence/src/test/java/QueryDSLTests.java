import com.ecco.dom.EvidenceSupportAction;
import com.ecco.dom.QEvidenceSupportAction;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import org.junit.Test;

import java.util.UUID;

import static com.ecco.dom.QEvidenceSupportComment.evidenceSupportComment;
import static com.ecco.dom.QEvidenceSupportWork.evidenceSupportWork;
import static com.ecco.dom.QEvidenceThreatComment.evidenceThreatComment;
import static com.ecco.dom.QEvidenceThreatWork.evidenceThreatWork;
import static org.junit.Assert.assertEquals;
import static org.assertj.core.api.Assertions.assertThat;


public class QueryDSLTests {

    @Test
    public void splitWhereClauseIsEquivalent_AND_notImmutable() {
        UUID oneUuid = UUID.randomUUID();
        UUID twoUuid = UUID.randomUUID();
        QEvidenceSupportAction item = QEvidenceSupportAction.evidenceSupportAction;

        // 2 - clauses applied separately
        JPQLQuery<EvidenceSupportAction> query1 = JPAExpressions
                .selectFrom(item)
                .where(item.actionInstanceUuid.eq(oneUuid));
        // additional impossible clause added separately
        // note no return value, the object is mutated
        query1.where(item.actionInstanceUuid.eq(twoUuid));

        // 2 - clauses all together
        JPQLQuery<EvidenceSupportAction> query2 = JPAExpressions
                .selectFrom(item)
                .where(item.actionInstanceUuid.eq(oneUuid),
                       item.actionInstanceUuid.eq(twoUuid));

        // if the query combined predicates, as we expect, there should be no results
        assertEquals(query1, query2);
    }

    @Test
    public void serviceShouldBeAvailableViaComment_ServiceRecipient() {
        /* see QueryInit on {@link com.ecco.dom.evidenceSupportComment#serviceRecipient} */
        assertThat(evidenceSupportComment.serviceRecipient.serviceAllocation.service).isNotNull();
        assertThat(evidenceThreatComment.serviceRecipient.serviceAllocation.service).isNotNull();
    }

    @Test
    public void serviceShouldBeAvailableViaComment_Work_ServiceRecipient() {
        /* see QueryInit on {@link com.ecco.dom.evidenceSupportComment#work} */
        assertThat(evidenceSupportComment.work.serviceRecipient.serviceAllocation.service).isNotNull();
        assertThat(evidenceThreatComment.work.serviceRecipient.serviceAllocation.service).isNotNull();
    }

    @Test
    public void serviceShouldBeAvailableViaWork_ServiceRecipient() {
        /* see QueryInit on {@link com.ecco.dom.evidenceSupportWork#serviceRecipient} */
        assertThat(evidenceSupportWork.serviceRecipient.serviceAllocation.service).isNotNull();
        assertThat(evidenceThreatWork.serviceRecipient.serviceAllocation.service).isNotNull();
    }
}
