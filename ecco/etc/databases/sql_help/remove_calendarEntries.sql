-- work out the entries to delete
-- joining to cosmo on event.cal_uid = cosmo.item_uid

-- find calendar entries at the user level
select group_concat(e.id separator ',') as eventIds from events e inner join contacts_events ec on ec.eventId=e.id inner join contacts c on ec.contactId=c.id inner join users u on c.usersId=u.id where u.username='sysadmin' order by e.id;
select group_concat(ci.id separator ',') as itemIds from events e inner join contacts_events ec on ec.eventId=e.id inner join contacts c on ec.contactId=c.id inner join users u on c.usersId=u.id inner join cosmo_item ci on ci.item_uid=e.cal_uid where u.username='sysadmin' order by e.id;

-- find calendar entries at the referral level
select e.id as eventids from events e inner join referrals r on e.referralId=r.id where e.name like 'Meeting:%' order by referralId;
select ci.id as itemIds from events e inner join referrals r on e.referralId=r.id inner join cosmo_item ci on ci.item_uid=e.cal_uid where e.name like 'Review:%' and s.id=2689 and e.eventmonth >=11 order by referralId;

-- find calendar entries at the service level
select e.id as eventids from events e inner join referrals r on e.referralId=r.id inner join services s on s.id=r.referredServiceId where e.name like 'Review:%' and s.id=2689 and e.eventmonth >=11 order by referralId;
select ci.id as itemIds from events e inner join referrals r on e.referralId=r.id inner join services s on s.id=r.referredServiceId inner join cosmo_item ci on ci.item_uid=e.cal_uid where e.name like 'Review:%' and s.id=2689 and e.eventmonth >=11 order by referralId;

-- find calendar entries at the contact level
select e.id as eventids from events e where e.id in ();
select ci.id as itemIds from events e inner join cosmo_item ci on ci.item_uid=e.cal_uid where e.id in ();

-- delete from ecco
delete from contacts_events where eventId in (<eventIds>);
delete from events where id in (<eventIds>);

-- delete from cosmo
delete from cosmo_collection_item where collectionid in (<itemIds>);
delete from cosmo_collection_item where itemid in (<itemIds>);
delete from cosmo_tombstones where itemid in (<itemIds>);
delete from cosmo_event_stamp where stampid in (select id from cosmo_stamp where itemid in (<itemIds>));
delete from cosmo_stamp where itemid in (<itemIds>);
delete from cosmo_item where id in (<itemIds>);
