-- assuming this is NOT attached to a user....we just delete the worker details

delete from hrfromtos where workerId in (<wId>);
delete from workers_jobs where workerId in (<wId>);
delete from workerattachments where workerId in (<wId>);

-- find the users associated with the worker??

delete from workers where id in (<wId>);

-- remove the user, assuming no other data attached in the system
-- copied from remove_client

-- or find user
select id as usersId from users where username in ("<username>");
select id as contactsId from contacts where usersId in (<usersId>);

-- select c.id, c.firstname, c.lastname, u.username as username from contacts c inner join users u on c.usersId=u.id where c.id in (<contactsId>);
select group_concat(u.id SEPARATOR ',') as userIds from contacts c inner join users u on c.usersId=u.id where c.id in (<contactsId>);
select group_concat(u.username SEPARATOR '","') as usernames from contacts c inner join users u on c.usersId=u.id where c.id in (<contactsId>);
select group_concat(id SEPARATOR ',') as cosmouid from cosmo_users where username in ("<username>");
select group_concat(id SEPARATOR ',') as itemids from cosmo_item where ownerid in (<cosmouid>);
select group_concat(eventId SEPARATOR ',') as eventids from contacts_events where contactId in (<contactsId>);

-- delete events (also refer to <remove_referral.sql>)
delete from contacts_events where eventId in (<eventids>);
delete from events where id in (<eventids>);
delete from contacts where id in (<contactsId>);

-- delete cosmo
delete from cosmo_collection_item where collectionid in (<itemids>);
delete from cosmo_collection_item where itemid in (<itemids>);
delete from cosmo_tombstones where itemid in (<itemids>);
delete from cosmo_event_stamp where stampid in (select id from cosmo_stamp where itemid in (<itemids>));
delete from cosmo_stamp where itemid in (<itemids>);
delete from cosmo_item where ownerid in (<cosmouid>);
delete from cosmo_users where id in (<cosmouid>);

-- delete ecco user
delete from group_members where username in ("<username>");
delete from authorities where username in ("<username>");
delete from users where username in ("<username>");
delete from users_AUD where id in (<usersId>);
delete from passwordhistory where username in ("<username>");
delete from audits;
