INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (2, 'ui-messages', 'menu.linktext.referrals', 'applications');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (3, 'ui-messages', 'referralBreadcrumb.referralView', 'applications');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (4, 'ui-messages', 'referralView.toProject', 'destination of application');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (5, 'ui-messages', 'referralView.from', 'source of application');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (6, 'ui-messages', 'referralView.referralDetails', 'details of application');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (7, 'ui-messages', 'referralView.referralAccepted', 'appropriate CCG');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (8, 'ui-messages', 'referralView.referralsAccepted', 'appropriate applications');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (9, 'ui-messages', 'referralDecision.acceptReferral', 'appropriate CCG');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (10, 'ui-messages', 'referralView.newMultipleReferral', 'new application');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (11, 'ui-base-messages', 'organisationName', 'NHS');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (12, 'ui-messages', 'menu.linktext.list_referrals', 'list applications');
INSERT INTO `messages` (`id`, `basename`, `msg_key`, `message`) VALUES (13, 'ui-messages', 'menu.linktext.find_referral', 'find application');

UPDATE menuitem SET `imageUrl`='/icons/crystal/user/png/user-48.png', `linkText`='menu.linktext.client_perspective' WHERE `id`='4';
UPDATE menuitem SET `roles`='ROLE_ADMIN,ROLE_COMMISSIONER,ROLE_CLIENT' WHERE `id`='2';
UPDATE menuitem SET `roles`='ROLE_ADMIN' WHERE `id`='5';
UPDATE menuitem SET `roles`='ROLE_ADMIN' WHERE `id`='6';
UPDATE menuitem SET `roles`='ROLE_ADMIN,ROLE_HR-VOLUNTEER,ROLE_HR' WHERE `id`='7';
UPDATE menuitem SET `roles`='ROLE_ADMIN' WHERE `id`='8';

UPDATE softwaremodule SET enabled=1 WHERE name='client-centric';
UPDATE softwaremodule SET enabled=0 WHERE name='community';
UPDATE softwaremodule SET enabled=0 WHERE name='hr';
UPDATE softwaremodule SET enabled=1 WHERE name='workflow';
UPDATE softwaremodule SET enabled=0 WHERE name='rota';

UPDATE menuitem SET acceleratorKey = NULL;
--UPDATE menuitem SET module_name='dev-only' WHERE id='1';
UPDATE menuitem SET module_name='dev-only' WHERE id='2';
UPDATE menuitem SET module_name='dev-only' WHERE id='5';
UPDATE menuitem SET module_name='dev-only' WHERE id='6';
UPDATE menuitem SET module_name='dev-only' WHERE id='41';



-- BELOW SHOULD a removal from LIQUIBASE

-- this question group belong in another servicetype 'fast track' which is not in demo
delete from servicetypes_questiongroups where questiongroupid=100138;

-- clear other demo services
delete from services_projects where serviceId<7;
-- insert into services_projects (serviceId, projectId) values (100154, 1);
delete from services where id < 7;
--delete non-Activiti version of service and rename activiti one for clarity
update services set name='chc' where id=100;

insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (9,'Contingency plan',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (10,'Decision Support Tool meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (11,'Quality review meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (12,'Allocation',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (13,'Duty intervention',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (14,'Management Instruction',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (15,'Appeal',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (16,'Telephone call to',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (17,'Telephone call from',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (18,'Email to (copy/paste email in)',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (19,'Email from (copy/paste email in)',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (20,'Letter to',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (21,'Letter from',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (22,'Visit',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (23,'Direct contact',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (24,'Safeguarding – Regulation 18',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (25,'Case Management Meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (26,'Safeguarding Strategy Discussion',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (27,'Safeguarding Strategy Meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (28,'Safeguarding Case Conference',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (29,'Safeguarding Protection Plan',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (30,'Safeguarding Review Meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (31,'PCP - Person Centred Planning Meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (32,'CPA - Care Program Approach Meeting',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (33,'MAPPA – Multi-agency Public Protection Plan Arrangements',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (34,'Safeguarding referral',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (35,'Case Transfer Summary',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (36,'Case Closure Summary',0,0,100);
insert into commenttypes (id,name,version,disabled,servicetypeId) VALUES (37,'Hospital Admission',0,0,100);

-- no longer needed -- update referralaspects set friendlyName='package (case manager)' where id=79;

-- https://www.eccosolutions.co.uk/scnhs/dynamic/secure/admin/reloadResources.html

delete from commmenttypes where id in (5,6,7,8);
delete from actions where id in (100009,100011);
update actions set name='Admin to check/chase next of kin details and add as ecco contact' where id=100010;
update actions set riskName = `name` where id = 100010;

