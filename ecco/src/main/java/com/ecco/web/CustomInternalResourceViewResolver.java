package com.ecco.web;

import java.util.Locale;

import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import org.springframework.web.servlet.view.RedirectView;

public class CustomInternalResourceViewResolver extends InternalResourceViewResolver {

    public static final String REDIRECT_NOMODEL_URL_PREFIX = "redirectNoModel:";

    @Override
    protected View createView(String viewName, Locale locale) throws Exception {

        // Check for special "redirectNoModel:" prefix.
        if (viewName.startsWith(REDIRECT_NOMODEL_URL_PREFIX)) {
            String redirectUrl = viewName.substring(REDIRECT_NOMODEL_URL_PREFIX.length());
            return new RedirectView(redirectUrl, isRedirectContextRelative(), isRedirectHttp10Compatible(), false);
        }

        return super.createView(viewName, locale);

    }

}
