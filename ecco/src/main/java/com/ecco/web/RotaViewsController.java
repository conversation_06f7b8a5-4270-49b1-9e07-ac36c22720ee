package com.ecco.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.ecco.hr.dao.WorkerRepository;

import static org.springframework.util.StringUtils.hasText;

/**
 * Controller for the entry pages which serve the AJAX based
 * pages which talk to the rest of the rota.
 */
@Controller
public class RotaViewsController extends BaseController {

    @Autowired
    private WorkerRepository workerRepository;

    @RequestMapping(value = "/secure/rota/", method = RequestMethod.GET)
    public String rota(
            @RequestParam(required=false) String date,
            Model model) {

        if (date == null) {
            date = "";
        }

        model.addAttribute("dateToShow", date);
        model.addAttribute("demandType", "worker");
        model.addAttribute("resourceFilter", "workers:all");
        model.addAttribute("demandFilter", "referrals:all");

        return "rotaView";
    }

    @RequestMapping(value = {"/buildings/{id}/rota", "/buildings/{id}/rota/{resourceType}"}, method = RequestMethod.GET)
    public String buildingWithResourceRotaView(
            @PathVariable Integer id,
            @PathVariable(required = false) String resourceType,
            @RequestParam(required=false) String date,
            Model model) {

        if (date == null) {
            date = "";
        }
        if (!hasText(resourceType)) {
            resourceType = "worker";
        }

        model.addAttribute("dateToShow", date);
        model.addAttribute("demandType", resourceType);
        model.addAttribute("resourceFilter", resourceType+"s:all");
        model.addAttribute("demandFilter", "buildings:" + id);

        return "rotaView";
    }

    @RequestMapping(value = "/secure/rota/resources/{resourceTypeCode}", method = RequestMethod.GET)
    public String rotaResources(
            @RequestParam(required=false) String date,
            // restrict the rota to the group required (eg bed or rooms)
            @PathVariable("resourceTypeCode") String resourceTypeCode,
            Model model) {

        if (date == null) {
            date = "";
        }
        if (resourceTypeCode == null) {
            resourceTypeCode = "all";
        }

        model.addAttribute("dateToShow", date);
        model.addAttribute("demandType", "bookableResource");
        model.addAttribute("resourceFilter", "resources:" + resourceTypeCode);
        model.addAttribute("demandFilter", "referrals:all");

        return "rotaView";
    }
}
