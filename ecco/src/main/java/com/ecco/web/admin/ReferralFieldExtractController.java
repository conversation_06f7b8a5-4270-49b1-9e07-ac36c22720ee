package com.ecco.web.admin;

import com.ecco.dom.Referral;
import com.ecco.service.ReferralService;

import com.ecco.security.ReferenceDataSource;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Controller
@RequestMapping("/secure/admin/referralFieldExtractor.html")
public class ReferralFieldExtractController extends BaseAdminPageController {

    private static final String VIEW_NAME = "admin/referralFieldExtractor";

    private static final Logger log = LoggerFactory.getLogger(ReferralFieldExtractController.class);

    @Autowired
    ReferralService referralService;

    ReferenceDataSource referenceData;
    @Resource(name="applicationReferenceData")
    public void setReferenceData(ReferenceDataSource referenceData) {
        this.referenceData = referenceData;
    }

    @RequestMapping(method=RequestMethod.GET)
    public String get(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return VIEW_NAME;
    }

    @RequestMapping(method=RequestMethod.POST)
    public String handleRequest(ModelMap model, HttpServletRequest request) {

        //String objectName = ServletRequestUtils.getStringParameter(request, "objectName", null);
        Long referralId = ServletRequestUtils.getLongParameter(request, "referralId", -1);
        boolean allreferrals = ServletRequestUtils.getBooleanParameter(request, "allreferrals", false);
        Long useReferralId = ServletRequestUtils.getLongParameter(request, "useReferralId", -1);
        Long serviceId = ServletRequestUtils.getLongParameter(request, "serviceId", -1);
        if (serviceId == -1) {
            serviceId = null;
        }

        if ((!allreferrals && referralId > -1) || (allreferrals && referralId == -1)) {

            // gather ids
            List<Long> referralIds = null;
            if (allreferrals) {
                log.info("loading ids: ALL");
            } else {
                log.info("loading id: " + referralId);
                referralIds = new ArrayList<>();
                referralIds.add(referralId);
            }

            // load referrals
            List<Referral> referrals = referralService.getCustomProperties(referralIds, serviceId);

            // log the keys in the customObjectData and textMap for the first referral
            // the first referral may not represent the whole set as questions might have been added to/made redundant over time
            if (referrals.isEmpty()) {
                log.info("referrals are EMPTY");
            } else {

                // find index of useReferralId
                int useReferralIdIndex = 0;
                int i = 0;
                for (Referral r : referrals) {
                    if (r.getId().equals(useReferralId)) {
                        useReferralIdIndex = i;
                    }
                    i++;
                }
                logCustomDataKeys(referrals.get(useReferralIdIndex));

                // extract
                Set<String> customObjectKeys = referrals.get(useReferralIdIndex).getCustomObjectData().keySet();
                Set<String> customStringKeys = referrals.get(useReferralIdIndex).getTextMap().keySet();
                Set<String> customDateKeys = referrals.get(useReferralIdIndex).getDateMap().keySet();
                model.put("customObjectKeys", customObjectKeys);
                model.put("customStringKeys", customStringKeys);
                model.put("customDateKeys", customDateKeys);
                model.put("entities", referrals);
            }
        }
        return VIEW_NAME;
    }

    private void logCustomDataKeys(Referral referral) {
        HashMap<String, Object> obj = referral.getCustomObjectData();
        for (String str : obj.keySet()) {
            log.info("object data keys: " + str);
        }
        HashMap<String, String> str = referral.getTextMap();
        for (String s : str.keySet()) {
            log.info("string data keys: " + s);
        }
        HashMap<String, LocalDate> date = referral.getDateMap();
        for (String s : str.keySet()) {
            log.info("date data keys: " + s);
        }
    }

}
