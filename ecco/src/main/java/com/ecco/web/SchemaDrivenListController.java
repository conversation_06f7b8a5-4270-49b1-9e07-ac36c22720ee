package com.ecco.web;

import com.ecco.infrastructure.web.UriUtils;
import com.ecco.webApi.controllers.SchemaController;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.util.UriTemplate;

import static org.springframework.hateoas.server.core.SpringAffordanceBuilder.DISCOVERER;

/**
 * The schema-driven version of EntityViewController. Fetching data, specifying the filter
 * and paging is handled at the client. So all this needs to do is forward to the right view.
 *
 * @since 24/01/2016
 */
@Controller("schemaDrivenListController")
@RequestMapping("/secure/entities/{entityType}")
public class SchemaDrivenListController {

    @RequestMapping(value = "/list.html", method = RequestMethod.GET)
    public String getEntities(@PathVariable String entityType, ModelMap model) {
        model.addAttribute("entityType", entityType);

        final String methodMapping = DISCOVERER.getMapping(
                ReflectionUtils.findMethod(SchemaController.class, "getEntitySchema", String.class));
        final UriTemplate template = new UriTemplate(methodMapping == null? "/" : methodMapping);
        model.addAttribute("schemaUrl", UriUtils.hostRelativeLinkBuilder()
                .slash("api").slash(template.expand(entityType)).toString());
        return "schemaDrivenListDef";
    }

}
