<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>


<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>
<%@attribute name="importRequireJsModules" type="java.lang.String" required="false"
        description="A space-separated list of require.js modules to import." %>
<%@attribute name="bootstrapRequired" type="java.lang.Boolean" required="false"
        description="If bootstrap is required (because we embed Bootstrap components)" %>
<%@attribute name="calendarRequired" type="java.lang.Boolean" required="false"
             description="If extra fullcalendar CSS is required" %>
<%@attribute name="jqplotRequired" type="java.lang.Boolean" required="false"
             description="If jqplot is required" %>


<%@attribute name="devMode" type="java.lang.Boolean" required="true"
             description="True if this page should use dev resources." %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs" %>
<%@taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>
<%@taglib prefix="ecco" tagdir="/WEB-INF/tags"%>

<c:set var="resourcePath" scope="request"
  value="${applicationProperties.resourceRootPath}"/>

<%-- provide timout code again, unless we instead use 'stayOnTimeout' to allow -reauth --%>
<%-- TODO ideally the stayOnTimeout still applies to non-editable pages, perhaps via an attribute --%>
<ecco:feature-query var="stayOnTimeout" feature="evidence.stayOnTimeout"/>
<c:set var="timeoutEnabled" value="${!stayOnTimeout}" scope="request"/>
<security:authorize var="roleUser" access="hasAnyRole('ROLE_USER')"/>

<page:page-doctype/>
<html class="v3">
    <!-- material-ui-page.tag -->
    <head>
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1,IE=edge">
        <%-- maximum-scale is a HACK for bldg checklist on safari iOS 9.1 see https://eccosolutions.zendesk.com/agent/tickets/826 --%>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1">

        <!--
        Java Version             <c:out value="${applicationProperties.javaVersion}"/>
        Build CommitId:          <c:out value="${applicationProperties.gitCommitId}"/>
        Build Branch:            <c:out value="${applicationProperties.gitBranch}"/>
        Build CommitTime:        <c:out value="${applicationProperties.gitCommitTime}"/>
        -->

        <link rel="icon" href="${resourcePath}themes/ecco/images/favicon.ico">
        <link rel="shortcut icon" href="${resourcePath}themes/ecco/images/favicon.ico">

        <page:page-title title="${title}"/>

        <c:if test="${bootstrapRequired}">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap.min.css">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-modal-bs3patch.css">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-modal.css">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-theme.min.css">
            <link rel="stylesheet" href="${resourcePath}css/typeahead.css">
        </c:if>

        <c:if test="${calendarRequired}">
            <link rel="stylesheet" href="${resourcePath}css/ecco-fullcalendar.css" type="text/css"/>
        </c:if>
        <c:if test="${jqplotRequired}">
            <link rel="stylesheet" href="${resourcePath}scripts/jquery.jqplot.min.css" type="text/css">
        </c:if>

        <link rel="stylesheet" href="${resourcePath}font-awesome/css/font-awesome.min.css">
        <link rel="stylesheet" href="${resourcePath}css/common/common.css">
        <link rel="stylesheet" href="${resourcePath}css/common/evidence.css">
        <link rel="stylesheet" href="${resourcePath}css/offline/offline.css">

        <page:main_head_jsbase/>

        <c:url var="apiPath" value="/api/"/>
        <requireJs:init root="${resourcePath}" apiPath="${apiPath}" devMode="${devMode}"/>
        <requireJs:import modules="${importRequireJsModules} ${(roleUser && timeoutEnabled) ? 'online/timeout' : ''}"/>

        <style>
            .print {
                display: none;
            }

            @media print {
                .no-print {
                    display: none !important; <%-- needed else mui block still shows --%>
                }
                .print {
                    display: block;
                }
            }
        </style>

    </head>
    <body data-session-len="${empty sessionLength ? 0 : sessionLength}">
        <jsp:doBody/>
        <script>
            var colourOverride = localStorage && localStorage['colourOverride'];
            if (colourOverride) {
                document.getElementsByTagName("html")[0].style.background = colourOverride;
                document.getElementsByTagName("body")[0].style.background = colourOverride;
            }
        </script>
    </body>
</html>
