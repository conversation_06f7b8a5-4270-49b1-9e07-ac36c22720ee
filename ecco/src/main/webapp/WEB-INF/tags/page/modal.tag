<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@attribute name="id" type="java.lang.String" required="true"
        description="The element id of the modal (for triggering)" %>
<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>

<%-- To trigger this, do something like:
    <button class="btn btn-default" data-toggle="modal" data-target="#quickGuide">quick guide</button>
 --%>

<div id="${id}" class="modal modal-lg fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">${title}</h4>
            </div>
            <div class="modal-body">
                <jsp:doBody />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>