<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@attribute name="classes" type="java.lang.String" required="false"
        description="Additional classes to add to the &lt;html&gt; element." %>

<%@attribute name="headFrag" required="false" fragment="true"%>
<%@attribute name="sidebarFrag" required="false" fragment="true"%>
<%@attribute name="menuFrag" required="false" fragment="true"%>
<%@attribute name="actionbarFrag" required="false" fragment="true"%>
<%@attribute name="timeoutEnabled" required="false" type="java.lang.Boolean" %>
<%@attribute name="devMode" type="java.lang.Boolean" required="true"
             description="True if this page should use dev resources." %>

<%@attribute name="title" required="false" type="java.lang.String"%>
<%@attribute name="contentFragPath" required="false" type="java.lang.String"
    description="Just to allow us to put comment in to help developers see in source what file was used"%>
<%@attribute name="disableBootstrapPatch" type="java.lang.Boolean" required="false"
             description="If the bootstrap patch css should be linked." %>

<%@include file="/WEB-INF/views/pathvars.jsp" %>

<%@taglib prefix="ecco" tagdir="/WEB-INF/tags"%>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>

<c:if test="${empty disableBootstrapPatch}">
    <c:set var="disableBootstrapPatch" value="${contentFragPath.startsWith('views/content/referralViews/')}"/>
</c:if>
<c:set var="disableBootstrapPatch" value="${true}"/>

<page:online-page classes="${classes}" title="${title}" timeoutEnabled="${timeoutEnabled}"
                  disableBootstrapPatch="${disableBootstrapPatch}" devMode="${devMode}">
    <!-- === jspContentPath = ${contentFragPath} === -->

    <c:if test="${not empty headFrag}">
        <jsp:invoke fragment="headFrag"/>
    </c:if>

    <div class="container-fluid">
        <c:if test="${not empty sidebarFrag}">
            <div class="row context-badge-row rounded">
                <div id="context-badge" class="col-xs-12">
                    <jsp:invoke fragment="sidebarFrag"/>
                </div>
            </div>
        </c:if>
        <c:if test="${not empty menuFrag}">
            <div class="row">
                <div id="WIPHeader" class="col-xs-12 rounded">
                    <div class="col-right">
                        <jsp:invoke fragment="menuFrag"/>
                    </div>
                </div>
            </div>
        </c:if>
    </div>
    <c:if test="${not empty actionbarFrag}">
        <jsp:invoke fragment="actionbarFrag"/>
    </c:if>
    <div id="main-content" class="container-fluid">
        <jsp:doBody/>
        <c:if test="${exception != null}">
            <page:page-error-content errorMessageKey="${message}" exception="${exception}" />
            <hr>
        </c:if>
    </div>

    <div id="footer" class="hidden-print">
        <page:footer imagesBase="${imagesBase}" footerImageUrl="${footerImageUrl}" organisationName="${organisationName}"/>
    </div>

    <%@ include file="/WEB-INF/views/tiles/main_body_top.jsp" %> <%-- <= Want to reduce what's in this - make part of component behaviour --%>
</page:online-page>