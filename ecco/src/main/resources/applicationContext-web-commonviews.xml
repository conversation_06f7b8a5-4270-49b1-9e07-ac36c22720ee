<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- redirect after a post -->
    <bean abstract="true" name="defaultRedirectView" class="org.springframework.web.servlet.view.RedirectView">
        <property name="contextRelative" value="true"/>
        <property name="http10Compatible" value="false"/>
        <property name="exposeModelAttributes" value="false"/>
        <property name="contentType" value="text/html; charset=UTF-8"/>
        <property name="encodingScheme" value="UTF-8"/>
        <property name="url" value="/index.html"/>
    </bean>

    <!-- common project views -->
    <bean abstract="true" name="defaultView" class="org.springframework.web.servlet.view.JstlView">
        <property name="contentType" value="text/html; charset=UTF-8"/>
    </bean>

    <bean parent="defaultView" name="uploadHiddenView">
        <property name="url" value="/WEB-INF/views/content/uploadHidden.jsp"/>
    </bean>
    <bean parent="defaultView" name="uploadHiddenInnerView" class="org.springframework.web.servlet.view.JstlView">
        <property name="url" value="/WEB-INF/views/content/uploadHiddenInner.jsp"/>
    </bean>
    <bean parent="defaultView" name="resultHiddenView">
        <property name="url" value="/WEB-INF/views/content/resultHiddenView.jsp"/>
    </bean>

    <bean parent="defaultView" name="runtimeErrorView">
        <property name="url" value="/WEB-INF/views/errors/runtimeErrorDef.jsp"/>
    </bean>
    <bean parent="defaultView" name="runtimeErrorSecurityView">
        <property name="url" value="/WEB-INF/views/errors/runtimeErrorSecurityDef.jsp"/>
    </bean>
    <bean parent="defaultView" name="checkedErrorUnhandledView">
        <property name="url" value="/WEB-INF/views/errors/checkedExceptionUnhandledDef.jsp"/>
    </bean>
    <bean parent="defaultView" name="checkedErrorOptimisticLockingView">
        <property name="url" value="/WEB-INF/views/errors/checkedExceptionOptimisticLockingDef.jsp"/>
    </bean>
    <bean parent="defaultView" name="checkedErrorAccessDeniedView">
        <property name="url" value="/WEB-INF/views/errors/checkedExceptionAccessDeniedDef.jsp"/>
    </bean>
    <bean parent="defaultView" name="checkedErrorEntityNotFoundView">
        <property name="url" value="/WEB-INF/views/errors/checkedExceptionEntityNotFoundDef.jsp"/>
    </bean>

    <bean name="checkedErrorUnhandled" class="org.springframework.web.servlet.view.InternalResourceView">
        <constructor-arg value="/nav/secure/checkedErrorUnhandled.html"/>
    </bean>
    <bean name="checkedErrorOptimisticLocking" class="org.springframework.web.servlet.view.InternalResourceView">
        <constructor-arg value="/nav/secure/checkedErrorOptimisticLocking.html"/>
    </bean>
    <bean name="checkedErrorAccessDenied" class="org.springframework.web.servlet.view.InternalResourceView">
        <constructor-arg value="/nav/secure/checkedErrorAccessDenied.html"/>
    </bean>
    <bean name="checkedErrorEntityNotFound" class="org.springframework.web.servlet.view.InternalResourceView">
        <constructor-arg value="/nav/secure/checkedErrorEntityNotFound.html"/>
    </bean>

</beans>
