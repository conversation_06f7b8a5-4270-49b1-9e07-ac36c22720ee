package com.ecco.web.reports;

import com.ecco.security.SecurityUtil;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.IdNameService;
import com.ecco.service.ReferralService;
import com.ecco.service.reports.ReportService;
import com.ecco.serviceConfig.EntityRestrictionService;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Locale;

public class ReportAbstractController {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    /*ReferenceDataSource referenceData;
    @Resource(name="applicationReferenceData")
    public void setReferenceData(ReferenceDataSource referenceData) {
        this.referenceData = referenceData;
    }
    public ReferenceDataSource getReferenceData() {
        return referenceData;
    }*/

    @Autowired
    UserRepository userRepository;
    @Autowired
    ReportService reportService;
    @Autowired
    IdNameService idNameService;
    @Autowired
    EntityRestrictionService entityRestrictionService;
    @Autowired
    ReferralService referralService;

    public static String reportDateStr(DateTime dte) {
        // only primitives are added to the url on RedirectView, so for excel etc we need to change to a String
        // the receiver expects "@DateTimeFormat(style="S-")"
        // which according to JodaDateTimeFormatAnnotationFormatterFactory uses this format
        // could possibly look to invoke the same as the annotation using FormattingConversionService
        // locale is from SecurityUtil.getAuthenticatedUser().getLocale() but reportDashboard date picker uses dd/mm/yy - so we need to use the same format
        // we also need to capture null
        if (dte == null)
            return "";
        String dteStr = org.joda.time.format.DateTimeFormat.forStyle("S-").withLocale(Locale.UK).print(dte); // ignore locale
        return dteStr;
    }

    // the html spec for image submissions is that of name.x - http://stackoverflow.com/questions/2357184/input-type-image-name-and-value-not-being-sent-by-ie-and-opera
    // so we can't map a parameter 'report' and expect it to work in say, let me guess, ie
    protected String findReportName(HttpServletRequest request) {
        // support <button name="report-name" value="the one"><img ...></button>
        String reportName = request.getParameter("report-name");
        if (reportName != null) {
            return reportName;
        }

        // TODO: Replace <input type="image"> with <button name="report-name" value="[the report name]"><img...></button>
        Enumeration<String> e = request.getParameterNames();
        while (e.hasMoreElements()) {
            String name = e.nextElement();
            if (StringUtils.endsWith(name, ".x"))
                return StringUtils.substring(name, "report".length(), name.length()-2);
        }
        return null;
    }

    // use the attribute for all reports to have the 'report' typical data
    // @ModelAttribute
    protected void referenceDataReportPrinting(ModelMap model, HttpServletRequest request) {
        //referenceDataReportScreen(model, request);
        referenceUsername(model);
    }
    protected void referenceDataReportPrinting(ModelMap model, HttpServletRequest request, boolean anonymousServices, boolean clientServices) {
        referenceDataReportScreen(model, request, anonymousServices, clientServices);
        referenceUsername(model);
    }
    protected void referenceUsername(ModelMap model) {
        // stuff for printing reports...the com.ecco.web.ReferenceDataImpl does include 'generatedDateTime'
        // but we could do with the user contact name who generated the report..
        String displayName = userRepository.getByUsername(SecurityUtil.getAuthenticatedUsername()).getContact().getDisplayName();
        model.put("userDisplayName", displayName);
    }

    /*protected void referenceDataReportScreen(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
    }*/
    protected void referenceDataReportScreen(ModelMap model, HttpServletRequest request, boolean anonymousServices, boolean clientServices) {
        //referenceDataReportScreen(model, request);

        /*ServicesProjectsDto restrictions = EntityRestrictionService.getRestrictedServicesProjectsDto(entityRestrictionService);

        // find the services to list
        List<Service> services = restrictions.getRestrictedServices();
        model.put("services", services);

        // the jsp has for a long time shown projects only when a service was selected
        // so we are not reducing functionality by listing the projects when a service is selected
        Long serviceId = ServletRequestUtils.getLongParameter(request, "serviceId", -1);
        if (serviceId > -1) {
            // find the projects to list
            List<Project> restrictedProjects = restrictions.getServiceRestrictedProjects(serviceId);
            model.put("projects", restrictedProjects);
        }

        // leaving the above logic as-is, we build a list of all services/projects instead of doing a round trip to the server for ajax pages (eg dashboard)
        model.put("restrictionsDto", restrictions);*/
    }

}
