package com.ecco.security.ldap.config;

import com.ecco.security.web.ActiveDirectoryDelegatingAuthnProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * When present, adds our ActiveDirectoryDelegatingAuthnProvider into the security config.
 *
 * This uses the modern way to configure based on properties and spring.factories which would contain:
 *
 * org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
 *   com.ecco.security.ldap.config.ActiveDirectorySecurityConfigurer
 */
@ConditionalOnProperty(name = ActiveDirectorySecurityConfigurer.ECCO_SECURITY_AD_ENABLE, havingValue = "true")
@Import(ActiveDirectoryConfig.class)
@Order(94)
@Slf4j
public class ActiveDirectorySecurityConfigurer extends WebSecurityConfigurerAdapter {

    public static final String ECCO_SECURITY_AD_ENABLE = "ecco.security.active-directory.enable";

    @Autowired
    private ActiveDirectoryDelegatingAuthnProvider authnProvider;

    @Override
    public void init(WebSecurity web) throws Exception {
        log.info("Initialising ActiveDirectorySecurityConfigurer");
        super.init(web);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(authnProvider);
    }
}
