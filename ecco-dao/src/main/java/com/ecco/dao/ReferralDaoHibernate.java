package com.ecco.dao;

import com.ecco.dom.Referral;
import org.hibernate.Session;
import org.joda.time.DateTime;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Repository
public class ReferralDaoHibernate implements ReferralDao {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void setCustomProperties(Referral referral) {
        Session session = (Session)entityManager;
        session.createQuery("update versioned Referral set choicesMap=:cData, customObjectData=:oData, textMap=:sData, dateMap=:mData where id=:id")
            .setLong("id", referral.getId())
            .setParameter("oData", referral.getCustomObjectData())
            .setParameter("sData", referral.getTextMap())
            .setParameter("mData", referral.getDateMap())
            .setParameter("cData", referral.getChoicesMap())
            .executeUpdate();
    }

    @Override
    public void setPropertyTimestamp(long referralId, String property, DateTime datetime) {
        Session session = (Session)entityManager;
        session.createQuery("update versioned Referral set "+property+"=:datetime where id=:id")
            .setLong("id", referralId)
            .setParameter("datetime", datetime)
            .executeUpdate();
    }

}
