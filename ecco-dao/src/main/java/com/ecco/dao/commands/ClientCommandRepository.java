package com.ecco.dao.commands;

import com.ecco.dom.clients.ClientCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ClientCommandRepository extends BaseCommandRepository<ClientCommand, Integer> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<ClientCommand> findAllByClientId(int clientId);
}
