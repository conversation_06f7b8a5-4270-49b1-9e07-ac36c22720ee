package com.ecco.hr.command;

import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface HrCommandRepository extends BaseCommandRepository<HrCommand, Integer> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<HrCommand> findAllByWorkerId(long workerId);

}
