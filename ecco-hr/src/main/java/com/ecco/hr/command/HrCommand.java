package com.ecco.hr.command;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import javax.persistence.*;
import java.util.UUID;

@Entity
@Table(name = "hr_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
public class HrCommand extends BaseIntKeyedCommand {

    @Column
    Long workerId;

    public HrCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                     long userId, @NonNull String body, @Nullable Long workerId) {
        super(uuid, remoteCreationTime, userId, body);
        this.workerId = workerId;
    }

    /**
     * Required by JPA/Hibernate.
     */
    @Deprecated
    protected HrCommand() {
    }
}
