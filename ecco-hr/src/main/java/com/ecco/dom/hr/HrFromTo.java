package com.ecco.dom.hr;

import java.math.BigDecimal;

import javax.persistence.*;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DiscriminatorOptions;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name="hr_fromtos")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="discriminator_orm", discriminatorType=DiscriminatorType.STRING)
// force use of correct discriminator where subclasses might get confused when loading
// see last comment on https://forum.hibernate.org/viewtopic.php?p=2343055&sid=119a0c5115055a0fc653b840e2a89dd2
@DiscriminatorOptions(force = true)
public abstract class HrFromTo extends AbstractLongKeyedEntity {

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    DateTime fromDate;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    DateTime toDate;

    BigDecimal value;
}
