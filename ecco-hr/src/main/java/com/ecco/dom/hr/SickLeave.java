package com.ecco.dom.hr;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;

import com.ecco.config.dom.ListDefinitionEntry;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@DiscriminatorValue("sickleave")
public class SickLeave extends CalendarableHrFromTo {

    // due to an oddity this can't really be in the parent class - see http://chriswongdevblog.blogspot.com/2009/10/polymorphic-one-to-many-relationships.html
    @ManyToOne(cascade={}, fetch = FetchType.LAZY)
    //@JoinColumns(value={@JoinColumn(name="workerId"), @JoinColumn(name="jobId")})
    @JoinColumn(name="workerJobId")
    @NotFound(action=NotFoundAction.EXCEPTION)
    WorkerJob workerJob;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "leavereasonId")
    private ListDefinitionEntry leaveReason;
    public static String LEAVEREASON_LISTNAME = "hrLeaveReason";

    @Lob
    String comments;

    @Override
    public Worker _getManagedWorker() {
        return workerJob.getWorker();
    }

    @Override
    public ListDefinitionEntry _getManagedLeaveReason() {
        return leaveReason;
    }
}
