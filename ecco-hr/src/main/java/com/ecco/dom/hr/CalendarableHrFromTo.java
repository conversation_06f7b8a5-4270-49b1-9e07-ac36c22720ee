package com.ecco.dom.hr;

import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.UnavailableInterval;
import com.ecco.calendar.core.UnavailableIntervalDefinition;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.BaseEntity;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.util.Assert;

import java.net.URI;
import javax.persistence.*;

/**
 * Intermediate class providing support for the subclasses of {@link HrFromTo} which result in calendar events being
 * created.
 *
 * @since 18/06/15
 */
@MappedSuperclass
@Configurable
public abstract class CalendarableHrFromTo extends HrFromTo {
    {
        injectServices();
    }

    @PersistenceContext
    @Transient
    transient EntityManager em;

    @Autowired
    @Transient
    private transient CalendarService calendarService;

    @Autowired
    @Transient
    private transient EntityUriMapper entityUriMapper;

    @Column(name = "calendar_event_entry_handle", length = 50)
    private String eventEntryHandleAsString;
    @Column(name = "calendar_availability_handle", length = 50)
    private String unavailableIntervalHandleAsString;

    public Object readResolve() {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    String getEventEntryHandleAsString() {
        return eventEntryHandleAsString;
    }

    void setEventEntryHandleAsString(String eventEntryHandleAsString) {
        this.eventEntryHandleAsString = eventEntryHandleAsString;
    }

    String getUnavailableIntervalHandleAsString() {
        return unavailableIntervalHandleAsString;
    }

    void setUnavailableIntervalHandleAsString(String availEntryHandleAsString) {
        this.unavailableIntervalHandleAsString = availEntryHandleAsString;
    }

    @PrePersist
    @PreUpdate
    void blockAvailabilityForInterval() {
        if (isNewEntity()) {
            final String calendarId = _getManagedWorker().getContact().getCalendarId();
            if (calendarId != null) {
                final UnavailableInterval unavailableInterval = calendarService.blockAvailability(calendarId, new HrFromToEventUnavailableIntervalDefinitionAdapter(this, entityUriMapper));
                setUnavailableIntervalHandleAsString(unavailableInterval.getHandle().toString());
            }
        } else {
            if (unavailableIntervalHandleAsString != null) {
                final UnavailableInterval.Handle handle = UnavailableInterval.Handle.fromString(unavailableIntervalHandleAsString);
                calendarService.updateUnavailableInterval(handle, new HrFromToEventUnavailableIntervalDefinitionAdapter(this, entityUriMapper));
            }
        }
    }

    @PreRemove
    void reinstateAvailabilityForInterval() {
        if (unavailableIntervalHandleAsString != null) {
            final UnavailableInterval.Handle handle = UnavailableInterval.Handle.fromString(unavailableIntervalHandleAsString);
            calendarService.reinstateAvailability(handle);
        }
    }

    /**
     * This method is used by {@link HrFromToEventEntryAdapter}
     * and {@link #blockAvailabilityForInterval()}
     * to obtain a worker from which it then obtains the calendar ID.
     * This method must return a managed entity (i.e. use {@link com.ecco.infrastructure.hibernate.AntiProxyUtils#ensureManaged(EntityManager, BaseEntity)}).
     * We use a stupidly-named method to stop Hibernate being even more stupid.
     *
     * @return the (managed) worker for this entity.
     */
    public abstract Worker _getManagedWorker();

    /**
     * This method is used by {@link HrFromToEventEntryAdapter}
     * and {@link com.ecco.dom.hr.CalendarableHrFromTo.HrFromToEventUnavailableIntervalDefinitionAdapter}
     * to obtain a leave reason to use as a calendar component title.
     * This method must return a managed entity (i.e. use {@link com.ecco.infrastructure.hibernate.AntiProxyUtils#ensureManaged(EntityManager, BaseEntity)}).
     * We use a stupidly-named method to stop Hibernate being even more stupid.
     *
     * @return the (managed) leave reason for this entity.
     */
    public abstract ListDefinitionEntry _getManagedLeaveReason();

    static class HrFromToEventUnavailableIntervalDefinitionAdapter implements UnavailableIntervalDefinition {
        private final CalendarableHrFromTo hrFromTo;
        private final EntityUriMapper entityUriMapper;

        public HrFromToEventUnavailableIntervalDefinitionAdapter(CalendarableHrFromTo hrFromTo, EntityUriMapper entityUriMapper) {
            this.hrFromTo = hrFromTo;
            Assert.notNull(hrFromTo.getFromDate(), "A calendar entry has no start date: " + hrFromTo.getEventEntryHandleAsString());
            // NB the end date didn't generate an error because it simply defaulted to 'now' - but that's probably not such a good idea
            Assert.notNull(hrFromTo.getToDate(), "A calendar entry has no end date: " + hrFromTo.getEventEntryHandleAsString());
            Assert.state(!hrFromTo.getToDate().isBefore(hrFromTo.getFromDate()), "A calendar entry has an end date before the start date: " + hrFromTo.getEventEntryHandleAsString());
            this.entityUriMapper = entityUriMapper;
        }

        @Override
        public String getTitle() {
            final var leaveReason = hrFromTo._getManagedLeaveReason();
            return leaveReason != null? leaveReason.getName() : hrFromTo.getClass().getSimpleName().toLowerCase();
        }

        @Override
        public DateTime getStart() {
            return hrFromTo.fromDate;
        }

        @Override
        public Duration getDuration() {
            return new Duration(hrFromTo.fromDate, hrFromTo.toDate);
        }

        @Override
        public URI getManagedByUri() {
            // TODO: this doesn't work because the ID is null at the time this is created.
            return entityUriMapper.uriForEntity(hrFromTo);
        }
    }

}
