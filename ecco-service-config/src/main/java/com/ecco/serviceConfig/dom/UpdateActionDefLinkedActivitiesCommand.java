package com.ecco.serviceConfig.dom;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("updateActionDefLinkedActivities")
public class UpdateActionDefLinkedActivitiesCommand extends ConfigCommand {
    public UpdateActionDefLinkedActivitiesCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                                  long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected UpdateActionDefLinkedActivitiesCommand() {
    }
}
