package com.ecco.serviceConfig.dom;

import java.util.Map;

public record QuestionGroupQuestionView(
        Long questionGroupId,
        int orderby,
        Long id,
        String name,
        Boolean disabled,
        String answerType,
        Boolean answerRequired,
        Object parameters
) {
    @SuppressWarnings("unchecked")
    public Map<String, Object> getParameters() {
        return (Map<String, Object>) parameters;
    }
}
