package com.ecco.serviceConfig.dom;

import static javax.persistence.CascadeType.MERGE;
import static javax.persistence.CascadeType.PERSIST;
import static lombok.AccessLevel.NONE;
import static lombok.AccessLevel.PROTECTED;

import org.jspecify.annotations.Nullable;
import javax.persistence.*;

import com.ecco.dom.Project;
import com.ecco.dom.Service;
import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.querydsl.core.annotations.QueryInit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

@Configurable
@Entity
@Table(name="services_projects")
@Getter
@Setter
@NoArgsConstructor(access = PROTECTED)
public class ServiceCategorisation extends AbstractIntKeyedEntity {

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient ServiceRepository serviceRepository;
    {
        // This class is instantiated by Hibernate, so not a managed Spring bean.
        injectServices();
    }
    private final void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @Column
    private boolean disabled;

    // TODO: consider allowing serviceId and projectId to be set in same way we do insertable/updatable trick elsewhere
    @JoinColumn(name = "serviceId")
    @ManyToOne(fetch= FetchType.LAZY, optional=false, cascade = {PERSIST, MERGE}) // cascade is mainly for ease of integration tests
    @QueryInit("*.*")
    private Service service;

    @Setter(AccessLevel.PRIVATE)
    @Column(insertable = false, updatable = false)
    private Long serviceId;

    @JoinColumn(name = "projectId")
    @ManyToOne(fetch=FetchType.LAZY, cascade = {PERSIST, MERGE}) // cascade is mainly for ease of integration tests
    private Project project;

    @Setter(AccessLevel.PRIVATE)
    @Column(insertable = false, updatable = false)
    private Long projectId;

    /**
     * The building that we are associated with, so that its rota understands how to load careruns.
     */
    @Column
    @Nullable
    private Integer buildingId;

    @Transient
    public int loadServiceTypeId() {
        return (int) serviceRepository.findServiceTypeId(serviceId);
    }

    @Column
    @Nullable
    private Integer clientGroupId;

    @Column
    @Nullable
    private Integer companyId;

    @Column
    @Nullable
    private Integer serviceGroupId;

    public ServiceCategorisation(Service s, Project p) {
        service = s;
        project = p;
    }

    public String description() {
        var serviceName = this.service.getName();
        var projectName = this.project != null ? this.project.getName() : null;
        return serviceName.concat(projectName == null ? "" : " ".concat(projectName));
    }
}
