package com.ecco.serviceConfig.service;

import java.util.List;

import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;

public interface SessionDataService {

    List<OutcomeViewModel> findOutcomes();
    List<RiskAreaViewModel> findRiskAreas();
    List<QuestionGroupViewModel> findQuestionGroups();

}
