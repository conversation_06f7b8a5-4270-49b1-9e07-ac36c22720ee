package com.ecco.serviceConfig.service;

import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.serviceConfig.dom.ServiceType;
import com.ecco.serviceConfig.dom.ServiceTypeWorkflow;
import com.ecco.serviceConfig.repositories.ServiceTypeRepository;
import com.ecco.serviceConfig.repositories.ServiceTypeWorkflowRepository;
import com.ecco.serviceConfig.viewModel.ServiceTypeToViewModel;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;
import java.util.Optional;

@ReadOnlyTransaction
public class RepositoryBasedServiceTypeService implements ServiceTypeService {

    private ServiceTypeToViewModel serviceTypeToViewModel;

    @NonNull
    private final ServiceTypeRepository serviceTypeRepository;
    @NonNull
    private final ServiceTypeWorkflowRepository serviceTypeWorkflowRepository;

    public RepositoryBasedServiceTypeService() {
        // Required for CGLIB - we only need services wired on the proxy's target
        this.serviceTypeRepository = null;
        this.serviceTypeWorkflowRepository = null;
    }

    @Autowired
    public RepositoryBasedServiceTypeService(@NonNull ServiceTypeRepository serviceTypeRepository,
                                             @NonNull ServiceTypeWorkflowRepository serviceTypeWorkflowRepository) {
        super();
        this.serviceTypeRepository = serviceTypeRepository;
        this.serviceTypeWorkflowRepository = serviceTypeWorkflowRepository;
        serviceTypeToViewModel = new ServiceTypeToViewModel();
    }

//    @Cacheable("serviceTypeService") - this is just SOOO broken with deep collections and back links
    // don't attempt to cache, but instead just migrate to web API where possible.
    @Override
    public ServiceType findOne(Long id) {
        return serviceTypeRepository.findById(id).orElseThrow();
    }

    @Override
    public List<Long> findAllIds() {
        return serviceTypeRepository.findAllIds();
    }

    @Cacheable(cacheNames = "serviceTypeViewModel", key = "#id")
    @Override
    public ServiceTypeViewModel findOneDto(Integer id) {
        ServiceType serviceType = serviceTypeRepository.findById(Long.valueOf(id)).orElse(null);
        return serviceTypeToViewModel.apply(serviceType);
    }

    public Optional<ServiceTypeWorkflow> findWorkflow(long serviceTypeId) {
        return serviceTypeWorkflowRepository.findServiceTypeWorkflowByServiceType_Id(serviceTypeId);
    }

    @CachePut(cacheNames = "serviceTypeViewModel", key = "#serviceTypeViewModel.id")
    @Override
    public ServiceTypeViewModel putOneDtoInCache(ServiceTypeViewModel serviceTypeViewModel) {
        // dummy method for the cache
        return serviceTypeViewModel;
    }

    @CacheEvict(cacheNames = {"serviceTypeViewModel"}, key = "#id")
    @Override
    public void evictOneDtoInCache(Integer id) {
        // dummy method for the cache
    }

}
