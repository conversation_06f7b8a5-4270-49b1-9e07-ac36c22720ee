package com.ecco.serviceConfig.viewModel;

import static java.util.stream.Collectors.toList;

import java.util.function.Function;

import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.dom.Question;

public class QuestionToViewModel implements Function<Question, QuestionViewModel> {

    private static final QuestionChoiceToViewModel questionChoicesToViewModel = new QuestionChoiceToViewModel();

    private static final QuestionAnswerFreeToViewModel freeTypesToViewModel = new QuestionAnswerFreeToViewModel();

    @Override
    @Nullable
    public QuestionViewModel apply(@Nullable Question input) {
        if (input == null) {
            throw new NullPointerException("input Question must not be null (check orderby in questiongroups_questions hasn't got empty slots)");
        }

        QuestionViewModel questionViewModel = new QuestionViewModel();
        questionViewModel.id = input.getId().intValue();
        questionViewModel.name = input.getName();
        questionViewModel.disabled = input.isDisabled();
        questionViewModel.answerRequired = input.isAnswerRequired();
        questionViewModel.answerType = input.getAnswerType();
        questionViewModel.choices = input.getChoices().stream().map(questionChoicesToViewModel).collect(toList());
        questionViewModel.freeTypes = input.getFreeTypes().stream().map(freeTypesToViewModel).collect(toList());
        questionViewModel.parameters = input.getParameters();

        return questionViewModel;
    }

}
