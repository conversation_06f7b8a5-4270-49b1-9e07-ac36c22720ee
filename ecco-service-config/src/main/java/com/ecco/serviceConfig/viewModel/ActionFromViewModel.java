package com.ecco.serviceConfig.viewModel;

import java.util.function.Function;

import com.ecco.serviceConfig.dom.Action;
import org.jspecify.annotations.Nullable;

public class ActionFromViewModel implements Function<ActionViewModel, Action> {

    @Override
    @Nullable
    public Action apply(@Nullable ActionViewModel input) {
        if (input == null) {
            throw new NullPointerException("input Action must not be null");
        }

        Action a = new Action();
        a.setUuid(input.uuid);
        a.setName(input.name);
        a.setOrderby(input.orderby);

        return a;
    }

}
