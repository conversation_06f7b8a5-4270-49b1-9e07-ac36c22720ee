package com.ecco.serviceConfig.viewModel;

import java.util.function.Function;
import java.util.stream.Collectors;

import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.dom.Action;

public class ActionToViewModel implements Function<Action, ActionViewModel> {

    private static final GroupSupportActivityTypeToViewModel activityTypeToViewModel
            = new GroupSupportActivityTypeToViewModel();

    @Override
    @Nullable
    public ActionViewModel apply(@Nullable Action input) {
        if (input == null) {
            throw new NullPointerException("input Action must not be null");
        }

        ActionViewModel vm = new ActionViewModel();
        vm.name = input.getName();
        vm.id = input.getId().intValue();
        vm.uuid = input.getUuid();
        vm.disabled = input.isDisabled();
        vm.initialText = input.getInitialText();
        vm.activityTypes = input.getLinkedActivities() == null
                ? null
                : input.getLinkedActivities().stream().map(activityTypeToViewModel).collect(Collectors.toList());
        vm.orderby = input.getOrderby();
        vm.statusChangeReasonListName = input.getStatusChangeReasonListName();
        return vm;
    }

}
