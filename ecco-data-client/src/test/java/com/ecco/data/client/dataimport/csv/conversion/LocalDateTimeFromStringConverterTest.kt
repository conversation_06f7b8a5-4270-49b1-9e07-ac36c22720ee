package com.ecco.data.client.dataimport.csv.conversion

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.Test

class LocalDateTimeFromStringConverterTest {
    private val converter = LocalDateTimeFromStringConverter()

    @Test
    fun convertYear23() {
        val dateTime = converter.convert("1/7/23 00:55")
        assertThat(dateTime?.year, Matchers.`is`(1923))
        assertThat(dateTime?.monthOfYear, Matchers.`is`(7))
        assertThat(dateTime?.dayOfMonth, Matchers.`is`(1))
        assertThat(dateTime?.hourOfDay, Matchers.`is`(0))
        assertThat(dateTime?.minuteOfHour, Matchers.`is`(55))
        assertThat(dateTime?.secondOfMinute, Matchers.`is`(0))
        assertThat(dateTime?.millisOfSecond, Matchers.`is`(0))
    }

    @Test
    fun convertYear19() {
        val dateTime = converter.convert("29/9/19 03:32")
        assertThat(dateTime?.year, Matchers.`is`(2019))
        assertThat(dateTime?.monthOfYear, Matchers.`is`(9))
        assertThat(dateTime?.dayOfMonth, Matchers.`is`(29))
        assertThat(dateTime?.hourOfDay, Matchers.`is`(3))
        assertThat(dateTime?.minuteOfHour, Matchers.`is`(32))
        assertThat(dateTime?.secondOfMinute, Matchers.`is`(0))
        assertThat(dateTime?.millisOfSecond, Matchers.`is`(0))
    }

    @Test
    fun convertFullYear() {
        val dateTime = converter.convert("3/12/2003 04:33")
        assertThat(dateTime?.year, Matchers.`is`(2003))
        assertThat(dateTime?.monthOfYear, Matchers.`is`(12))
        assertThat(dateTime?.dayOfMonth, Matchers.`is`(3))
        assertThat(dateTime?.hourOfDay, Matchers.`is`(4))
        assertThat(dateTime?.minuteOfHour, Matchers.`is`(33))
        assertThat(dateTime?.secondOfMinute, Matchers.`is`(0))
        assertThat(dateTime?.millisOfSecond, Matchers.`is`(0))
    }

    @Test
    fun convertMissingTimeShortYear() {
        val dateTime = converter.convert("16/12/64")
        assertThat(dateTime?.year, Matchers.`is`(1964))
        assertThat(dateTime?.monthOfYear, Matchers.`is`(12))
        assertThat(dateTime?.dayOfMonth, Matchers.`is`(16))
        assertThat(dateTime?.hourOfDay, Matchers.`is`(0))
        assertThat(dateTime?.minuteOfHour, Matchers.`is`(0))
        assertThat(dateTime?.secondOfMinute, Matchers.`is`(0))
        assertThat(dateTime?.millisOfSecond, Matchers.`is`(0))
    }

    @Test
    fun convertMissingTimeFullYear() {
        val dateTime = converter.convert("12/2/1988")
        assertThat(dateTime?.year, Matchers.`is`(1988))
        assertThat(dateTime?.monthOfYear, Matchers.`is`(2))
        assertThat(dateTime?.dayOfMonth, Matchers.`is`(12))
        assertThat(dateTime?.hourOfDay, Matchers.`is`(0))
        assertThat(dateTime?.minuteOfHour, Matchers.`is`(0))
        assertThat(dateTime?.secondOfMinute, Matchers.`is`(0))
        assertThat(dateTime?.millisOfSecond, Matchers.`is`(0))
    }
}