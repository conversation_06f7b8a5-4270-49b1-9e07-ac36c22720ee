package com.ecco.data.client.dataimport.csv.conversion;

import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.core.convert.converter.Converter;

import org.jspecify.annotations.NonNull;

public class LocalDateTimeFromStringConverter implements Converter<String, LocalDateTime> {

    // NB the import works best with a date of 30/10/2025, or we may need to update the pivot
    private DateTimeFormatter ddMMyyHHmm1920to2019 = DateTimeFormat.forPattern("dd/MM/yy HH:mm").withPivotYear(1970);
    private DateTimeFormatter ddMMyy1920to2019 = DateTimeFormat.forPattern("dd/MM/yy").withPivotYear(1970);

    @Override
    public LocalDateTime convert(@NonNull String source) {
        try {
            return ddMMyyHHmm1920to2019.parseDateTime(source).toLocalDateTime();
        } catch (IllegalArgumentException e) {
            // we may not have a time, but need to still transform to a LocalDateTime
            return ddMMyy1920to2019.parseDateTime(source).toLocalDateTime();
        }
    }
}
