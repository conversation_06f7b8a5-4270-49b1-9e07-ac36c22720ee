package com.ecco.data.client.actors;

import com.ecco.acceptancetests.api.jackson.SliceImpl;
import com.ecco.webApi.evidence.EvidenceFormWorkViewModel;
import com.google.common.collect.ImmutableMap;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import org.jspecify.annotations.NonNull;
import java.util.List;

public class EvidenceFormSnapshotActor extends BaseActor {

    public EvidenceFormSnapshotActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<EvidenceFormWorkViewModel> findLatestSnapshot(int serviceRecipientId, @NonNull String evidenceGroup) {
        return restTemplate.getForEntity(apiBaseUrl +
                        "service-recipients/{serviceRecipientId}/evidence/form/{evidenceGroup}/snapshots/latest/",
                        EvidenceFormWorkViewModel.class,
                        ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceGroup));
    }

    public ResponseEntity<List<EvidenceFormWorkViewModel>> findLatestSnapshotsPerEvidenceGroupByServiceRecipientId(int serviceRecipientId) {
        ParameterizedTypeReference<List<EvidenceFormWorkViewModel>> typeRef = new ParameterizedTypeReference<>() {};
        return restTemplate.exchange(
                apiBaseUrl + "service-recipients/{serviceRecipientId}/evidence/form/snapshots/latest/",
                HttpMethod.GET, null, typeRef,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId));
    }

    public ResponseEntity<SliceImpl<EvidenceFormWorkViewModel>> findAllSnapshotsByServiceRecipientIdAndEvidenceTaskGroupKey(
            int serviceRecipientId,
            String evidenceTaskGroupKey) {
        // getForEntity doesn't support Iterable, so use exchange - see http://stackoverflow.com/questions/6173182/spring-json-convert-a-typed-collection-like-listmypojo
        ParameterizedTypeReference<SliceImpl<EvidenceFormWorkViewModel>> typeRef = new ParameterizedTypeReference<>() {
        };
        return restTemplate.exchange(apiBaseUrl +
                        "service-recipients/{serviceRecipientId}/evidence-form/{evidenceGroup}/",
                HttpMethod.GET, null, typeRef,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceTaskGroupKey));
    }

}
