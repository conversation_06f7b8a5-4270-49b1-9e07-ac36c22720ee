package com.ecco.data.client.actors;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.viewModels.Result;

public class BaseCommandActor {

    protected final RestTemplate restTemplate;
    protected String apiBaseUrl;

    public BaseCommandActor(RestTemplate restTemplate, String apiBaseUrl) {
        this.restTemplate = restTemplate;
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    /** POST a view model as JSON.
     * <p>
     * Note: for commands use {@link #executeCommand(com.ecco.webApi.evidence.BaseCommandViewModel)}
     * */
    public <MODEL> ResponseEntity<Result> postAsJson(String url, MODEL model) {
        final HttpHeaders postHeaders = new HttpHeaders();
        postHeaders.setContentType(MediaType.APPLICATION_JSON);
        final HttpEntity<MODEL> postRequest = new HttpEntity<>(model, postHeaders);

        return restTemplate.postForEntity(url, postRequest, Result.class);
    }

    public ResponseEntity<Result> executeCommand(BaseCommandViewModel vm) {
        Assert.notNull(apiBaseUrl);
        return executeCommand(apiBaseUrl, vm);
    }

    public ResponseEntity<Result> executeCommand(String baseUrl, BaseCommandViewModel vm) {
        Assert.notNull(vm.commandUri);
        ResponseEntity<Result> response = postAsJson(baseUrl + vm.commandUri, vm);
        if (vm.valid()) {
            Assert.state(response.getStatusCode().is2xxSuccessful(),
                    "Valid command failed to execute.Expected 2xx, but got " + response.getStatusCode().toString());
        }
        else {
            Assert.state(response.getStatusCode().is4xxClientError(),
                    "Sent command was not valid, server should return 4xx, but returned " + response.getStatusCode().toString());
        }
        return response;
    }

}