package com.ecco.data.client.dataimport.support;


import org.jspecify.annotations.Nullable;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.ecco.webApi.viewModels.UserViewModel;

class UserHandler extends AbstractHandler<UserViewModel> {

    protected static final String apiPath = "/api/";

    public UserHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    public void processEntity(@Nullable ImportOperation<UserViewModel> operation) {

        // currently we use java to create the username
        // cosmo requires there be a lastname
        if (StringUtils.isEmpty(operation.record.individual.lastName)) {
            operation.record.individual.lastName = "-";
        }
        operation.record.username = StringUtils.lowerCase(operation.record.individual.firstName + StringUtils.left(operation.record.individual.lastName, 1));
        System.err.println("attempting: [" + operation.record.username + "]");

        postAcceptingCreatedOrUnprocessableEntity(operation.baseUri + apiPath + "users/", operation.record);

        /*
        // add the service we know they need access to
        AclEntryViewModel avm = new AclEntryViewModel();
        avm.clazz = "com.ecco.dom.Service";
        avm.permissionMask = 1;
        avm.secureObjectId = 50;
        avm.username = operation.record.username;

        List<AclEntryViewModel> acls = new ArrayList<AclEntryViewModel>();
        acls.add(avm);

        if (operation.record.projectId > 0) {
            // add to the acl project already created
            AclEntryViewModel avmP = new AclEntryViewModel();
            avmP.clazz = "com.ecco.dom.Project";
            avmP.permissionMask = 1;
            avmP.secureObjectId = operation.record.projectId;
            avmP.username = operation.record.username;
            acls.add(avmP);
        }
        postAcceptingCreatedOrUnprocessableEntity(operation.baseUri + apiPath + "acls/entries/", acls);
        */
        System.err.println(operation.record.username);
    }
}
