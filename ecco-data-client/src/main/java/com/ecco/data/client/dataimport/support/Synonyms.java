package com.ecco.data.client.dataimport.support;

import java.util.Map;

import com.google.common.collect.ImmutableMap;

public class Synonyms {

    /** Map from lower case synonym to case sensitive equivalent */
    public static final Map<String, String> synonymsToNormalizedMap = ImmutableMap.<String,String>builder()

            // General equivs of empty
            .put("(blank)", "")
            .put("(not selected)", "")
            .put("unrecorded", "")
            .build();

    // catch some relationship synonmys
    public static final Map<String, String> relationshipSynonyms = ImmutableMap.<String,String>builder()
            .put("grand mother", "grandmother")
            .put("grand father", "grandfather")
            .put("stepmother", "step mother")
            .put("stepfather", "step father")
            .put("stepsister", "step sister")
            .put("stepbrother", "step brother")
            .build();
}
