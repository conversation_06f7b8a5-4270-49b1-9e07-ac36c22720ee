package com.ecco.data.client.actors;

import com.ecco.finance.webApi.dto.ClientSalesChargeInvoiceDetailResource;
import com.google.common.collect.ImmutableMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.util.Optional;

public class FinanceChargeActor extends BaseActor {

    public FinanceChargeActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<ClientSalesChargeInvoiceDetailResource.Line[]> getChargesByServiceRecipientId(
            int serviceRecipientId,
            LocalDate fromDate,
            LocalDate toDate) {
        ImmutableMap<String, String> args = ImmutableMap.of("serviceRecipientId", Integer.toString(serviceRecipientId));
        return restTemplate.getForEntity(
                UriComponentsBuilder.fromHttpUrl(apiBaseUrl + "finance/service-recipients/{serviceRecipientId}/charges/")
                        .queryParamIfPresent("fromDate", Optional.ofNullable(fromDate))
                        .queryParamIfPresent("toDate", Optional.ofNullable(toDate))
                        .build().toUriString(),
                ClientSalesChargeInvoiceDetailResource.Line[].class, args);
    }

    /*
    private static final String financeInvoiceUri = WebApiSettings.APPLICATION_URL + "/api/finance/invoices/";
    public List<ClientSalesChargeInvoiceDetailResource> fetchInvoicesForSrId(Integer serviceRecipientId) {
        var response =
                restTemplate.exchange(invoicesBaseUrl + "serviceRecipient/{id}/", GET, null,
                        CLIENT_SALES_INVOICE_RESOURCE_LIST, Collections.singletonMap("id", serviceRecipientId));
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch invoices failed");
        return response.getBody();
    }
    */

    /*
    public ClientSalesRotaInvoiceDetailResource fetchInvoicesInvoiceDetail(Integer invoiceId) {
        var response =
                restTemplate.getForEntity(invoicesBaseUrl + "id/{id}/", ClientSalesRotaInvoiceDetailResource.class,
                        Collections.singletonMap("id", invoiceId));
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch invoice failed");
        return response.getBody();
    }
    */

}
