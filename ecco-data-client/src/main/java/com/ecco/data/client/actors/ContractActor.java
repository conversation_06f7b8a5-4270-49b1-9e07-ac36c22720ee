package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.RateCardEntry;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.finance.RateCardCommandDto;
import com.ecco.webApi.finance.RateCardEntryCommandDto;
import com.ecco.webApi.finance.RateCardViewModel;
import com.ecco.webApi.rota.*;
import com.ecco.webApi.viewModels.Result;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import org.jspecify.annotations.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;

/**
 * API actions relating to contracts.
 */
public class ContractActor extends BaseActor {
    private static final String contractsUri = WebApiSettings.APPLICATION_URL + "/api/contracts/";

    public ContractActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ContractViewModel getContractById(int id) {
        String uri = contractsUri + id + "/";
        ResponseEntity<ContractViewModel> response = restTemplate.getForEntity(uri, ContractViewModel.class);
        return response.getBody();
    }

    public ContractViewModel createContract(String name, Integer contractTypeId, LocalDate start) {
        ContractTaskContractDetailCommandViewModel command = new ContractTaskContractDetailCommandViewModel(null);
        command.operation = BaseCommandViewModel.OPERATION_ADD;
        command.name = ChangeViewModel.changeNullTo(name);
        command.contractTypeId = contractTypeId != null ? ChangeViewModel.changeNullTo(contractTypeId) : null;
        command.startDateTime = ChangeViewModel.changeNullTo(start != null ? start.atStartOfDay() : LocalDateTime.now().minusDays(1));
        ResponseEntity<Result> response = executeCommand(command);
        int contractId = ContractController.EXTRACT_ID_FN.apply(response.getBody().getLinks()[0].getHref());
        return getContractById(contractId);
    }

    public RateCardViewModel getRateCardById(int id) {
        String uri = contractsUri + "/rateCards/" + id + "/";
        ResponseEntity<RateCardViewModel> response = restTemplate.getForEntity(uri, RateCardViewModel.class);
        return response.getBody();
    }

    // RateCard's aren't rota specific, but the data here is
    public RateCardViewModel createRotaRateCard(String name, int contractId) {
        RateCardCommandDto command = new RateCardCommandDto(null, BaseCommandViewModel.OPERATION_ADD, null);
        command.name = ChangeViewModel.changeNullTo(name);
        command.chargeNameId = ChangeViewModel.changeNullTo(219); // service charges
        command.startDateTime = ChangeViewModel.changeNullTo(LocalDateTime.now().minusDays(1));
        command.endDateTime = ChangeViewModel.changeNullTo(LocalDateTime.now().plusDays(1));
        command.matchingPartsOfWeek = ChangeViewModel.changeNullTo(RateCard.PartsOfWeek.WEEKDAY_WEEKEND);
        command.matchingStartTime = ChangeViewModel.changeNullTo(LocalTime.MIDNIGHT);
        command.matchingEndTime = ChangeViewModel.changeNullTo(LocalTime.MAX);
        command.contractsChange = ChangeViewModel.changeNullTo(Collections.singletonList(contractId));
        ResponseEntity<Result> response = executeCommand(command);
        int rateCardId = ContractController.EXTRACT_ID_FN.apply(response.getBody().getLinks()[0].getHref());
        return getRateCardById(rateCardId);
    }

    // RateCard's aren't rota specific, but the data here is
    public RateCardViewModel createRotaRateCardEntry(int rateCardId, int matchingCategoryId, int matchingOutcomeEventStatusRateId) {
        RateCardEntryCommandDto c = new RateCardEntryCommandDto(BaseCommandViewModel.OPERATION_ADD, rateCardId, null);
        c.chargeTypeFixedTemporal = ChangeViewModel.changeNullTo(RateCardEntry.ChargeType.FIXED.name());
        c.fixedCharge = ChangeViewModel.changeNullTo(new BigDecimal("20.05"));
        c.matchingCategoryTypeId = ChangeViewModel.changeNullTo(matchingCategoryId);
        c.matchingChargeCategoryId = ChangeViewModel.changeNullTo(matchingOutcomeEventStatusRateId);
        ResponseEntity<Result> response = executeCommand(c);
        return getRateCardById(rateCardId);
    }

    // RateCard's aren't rota specific, but the data here is
    public RateCardViewModel createChargeRateCard(String name, int contractId, LocalDate from) {
        RateCardCommandDto command = new RateCardCommandDto(null, BaseCommandViewModel.OPERATION_ADD, null);
        command.name = ChangeViewModel.changeNullTo(name);
        command.startDateTime = ChangeViewModel.changeNullTo(from != null ? from.atStartOfDay() : LocalDateTime.now().minusDays(1));
        command.endDateTime = null;
        command.contractsChange = ChangeViewModel.changeNullTo(Collections.singletonList(contractId));
        ResponseEntity<Result> response = executeCommand(command);
        int rateCardId = ContractController.EXTRACT_ID_FN.apply(response.getBody().getLinks()[0].getHref());
        return getRateCardById(rateCardId);
    }

    // see also FinanceChargeUnitTest
    public RateCardViewModel createChargeRateCardEntry(int rateCardId, BigDecimal charge, int units, @Nullable Integer parentRateCardEntryId) {
        RateCardEntryCommandDto c = new RateCardEntryCommandDto(BaseCommandViewModel.OPERATION_ADD, rateCardId, null);
        c.chargeTypeFixedTemporal = ChangeViewModel.changeNullTo(RateCardEntry.ChargeType.TEMPORAL.name());
        c.unitMeasurementId = ChangeViewModel.changeNullTo(3); // DAY, as in 24hr
        c.units = ChangeViewModel.changeNullTo(units); // number of days
        c.unitCharge = ChangeViewModel.changeNullTo(charge);
        c.unitsToRepeatFor = null;
        c.parentRateCardEntryId = parentRateCardEntryId;
        ResponseEntity<Result> response = executeCommand(c);
        return getRateCardById(rateCardId);
    }

}
