package com.ecco.data.client.actors;

import com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource;
import com.ecco.finance.webApi.dto.ClientSalesInvoiceResource;
import com.ecco.webApi.finance.CreateClientSalesInvoiceCommandViewModel;
import com.ecco.webApi.rota.InvoiceLineCommandViewModel;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource.REL_LINES;
import static com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource.REL_PROFORMA_LINES;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static com.ecco.data.client.WebApiSettings.APPLICATION_URL;
import static org.hamcrest.Matchers.equalTo;
import static org.springframework.hateoas.IanaLinkRelations.SELF;
import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

/**
 * API actions relating to manipulating invoices.
 *
 * @since 12/10/2016
 */
public class RotaActivityInvoiceActor extends BaseActor {
    private static final String invoicesBaseUrl  = APPLICATION_URL + "/api/rota/invoice/";
    private static final ParameterizedTypeReference<List<ClientSalesRotaInvoiceDetailResource>> CLIENT_SALES_INVOICE_RESOURCE_LIST = new ParameterizedTypeReference<>() {
    };
    private static final ParameterizedTypeReference<List<ClientSalesRotaInvoiceDetailResource.Line>> CLIENT_SALES_INVOICE_DETAIL_RESOURCE_LINE_LIST = new ParameterizedTypeReference<>() {
    };

    public RotaActivityInvoiceActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /**
     * @param pathTemplate for use with {@link UriComponentsBuilder#buildAndExpand(Map)}
     */
    private UriComponentsBuilder createInvoiceUriBuilder(String pathTemplate) {
        return UriComponentsBuilder.fromHttpUrl(invoicesBaseUrl + pathTemplate);
    }

    public void createClientSalesInvoice(Integer serviceRecipientId, LocalDate invoiceDate) {
        var command = new CreateClientSalesInvoiceCommandViewModel(serviceRecipientId, invoiceDate);
        final var response = executeCommand(command);
        Assert.state(equalTo(CREATED).matches(response.getStatusCode()), "Create invoice failed");
        final var id = response.getBody().getId();
        Assert.state(id != null, "Create invoice did not return an ID");
        log.info("Created draft sales invoice cmd id {} for srID: {}", id, serviceRecipientId);
    }

    public UUID addClientSalesInvoiceLine(Integer invoiceId, ClientSalesRotaInvoiceDetailResource.Line uninvoicedLine) {
        Assert.state(uninvoicedLine.getWorkUuid()  != null || uninvoicedLine.getEventId() != null,
                "Uninvoiced line did not reference work or event");
        final var command = new InvoiceLineCommandViewModel(invoiceId);
        if (uninvoicedLine.getWorkUuid() != null) {
            command.workUuid = changeNullTo(uninvoicedLine.getWorkUuid());
        }
        else {
            command.eventId = changeNullTo(uninvoicedLine.getEventId());
        }
        command.plannedResourceCalendarId = changeNullTo(uninvoicedLine.getPlannedResourceCalendarId());
        command.description = changeNullTo(uninvoicedLine.getDescription());
        command.amount = changeNullTo(uninvoicedLine.getNetAmount());
        final var response = executeCommand(command);
        Assert.state(equalTo(CREATED).matches(response.getStatusCode()), "Create invoice line failed");
        final var id = response.getBody().getId();
        Assert.state(id != null, "Create invoice line did not return an ID");
        log.info("Created sales invoice line cmd id {} for workUuid: {}", id, uninvoicedLine.getWorkUuid());
        return UUID.fromString(id);
    }

    // Queries...

    public List<ClientSalesRotaInvoiceDetailResource> findAllInvoices(java.time.LocalDate start, java.time.LocalDate end) {
        var response = restTemplate.exchange(
                createInvoiceUriBuilder("")
                        .queryParam("startDate", start)
                        .queryParam("endDate", end)
                        .build().toUri(),
                GET, null, CLIENT_SALES_INVOICE_RESOURCE_LIST);
        return response.getBody();
    }

    public List<ClientSalesRotaInvoiceDetailResource> fetchClientSalesInvoices(Integer serviceRecipientId) {
        var response =
                restTemplate.exchange(invoicesBaseUrl + "serviceRecipient/{id}/", GET, null,
                        CLIENT_SALES_INVOICE_RESOURCE_LIST, Collections.singletonMap("id", serviceRecipientId));
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch invoices failed");
        return response.getBody();
    }

    public ClientSalesRotaInvoiceDetailResource fetchClientSalesInvoiceDetail(Integer invoiceId) {
        var response =
                restTemplate.getForEntity(invoicesBaseUrl + "id/{id}/", ClientSalesRotaInvoiceDetailResource.class,
                        Collections.singletonMap("id", invoiceId));
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch invoice failed");
        return response.getBody();
    }

    public ClientSalesRotaInvoiceDetailResource fetchClientSalesInvoiceDetail(ClientSalesInvoiceResource invoice) {
        return restTemplate.getForObject(invoice.getRequiredLink(SELF).getHref(), ClientSalesRotaInvoiceDetailResource.class);
    }

    public List<ClientSalesRotaInvoiceDetailResource.Line> findAllUninvoicedLines(java.time.LocalDate start, java.time.LocalDate end) {
        var response = restTemplate.exchange(
                createInvoiceUriBuilder("uninvoiced/")
                        .queryParam("startDate", start)
                        .queryParam("endDate", end)
                        .build().toUri(),
                GET, null, CLIENT_SALES_INVOICE_DETAIL_RESOURCE_LINE_LIST);
        return response.getBody();
    }

    public List<ClientSalesRotaInvoiceDetailResource.Line> fetchUninvoicedLines(ClientSalesRotaInvoiceDetailResource invoice) {
        var response =
                restTemplate.exchange(invoice.getRequiredLink(REL_PROFORMA_LINES).getHref(), GET, null,
                        CLIENT_SALES_INVOICE_DETAIL_RESOURCE_LINE_LIST);
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch pro forma invoice lines for uninvoiced work failed");
        return response.getBody();
    }

    public List<ClientSalesRotaInvoiceDetailResource.Line> fetchInvoiceLines(ClientSalesRotaInvoiceDetailResource invoice) {
        var response =
                restTemplate.exchange(invoice.getRequiredLink(REL_LINES).getHref(), GET, null,
                        CLIENT_SALES_INVOICE_DETAIL_RESOURCE_LINE_LIST);
        Assert.state(equalTo(OK).matches(response.getStatusCode()), "Fetch invoice lines failed");
        return response.getBody();
    }
}
