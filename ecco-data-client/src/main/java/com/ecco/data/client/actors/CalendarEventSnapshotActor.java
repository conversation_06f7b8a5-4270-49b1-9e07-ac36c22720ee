package com.ecco.data.client.actors;

import com.ecco.webApi.evidence.CalendarEventSnapshotDtoResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class CalendarEventSnapshotActor extends BaseActor {

    public CalendarEventSnapshotActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<CalendarEventSnapshotDtoResource[]> findAll() {
        return restTemplate.getForEntity(
                apiBaseUrl + "calendarEventSnapshot/all/",
                CalendarEventSnapshotDtoResource[].class);
    }

}
