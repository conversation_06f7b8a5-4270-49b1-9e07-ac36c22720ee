package com.ecco.data.client.actors;

import com.ecco.webApi.evidence.QuestionnaireAnswersSnapshotViewModel;
import com.ecco.webApi.evidence.SupportSmartStepsSnapshotViewModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.dom.ReportCriteriaDto;
import com.ecco.webApi.evidence.EvidenceSupportWorkViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;

public class ReportActor extends BaseActor {

    public ReportActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /**
     * Retrieve all (unpaged) reportReferrals - useful for tests
     */
    public ResponseEntity<ReferralViewModel[]> getAllReportReferrals(ReportCriteriaDto dto) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/referrals/")
                .buildAndExpand();
        ResponseEntity<ReferralViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, ReferralViewModel[].class);
        return response;
    }

    /**
     * Retrieve reportReferrals
     */
    public ResponseEntity<ReferralViewModel[]> getReportReferrals(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/referrals/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<ReferralViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, ReferralViewModel[].class);
        return response;
    }

    /**
     * Retrieve reportSupport
     */
    public ResponseEntity<EvidenceSupportWorkViewModel[]> getReportSupport(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/needs/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<EvidenceSupportWorkViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, EvidenceSupportWorkViewModel[].class);
        return response;
    }

    /**
     * Retrieve reportSupport
     */
    public ResponseEntity<EvidenceSupportWorkViewModel[]> getReportLatestSupportWork(ReportCriteriaDto dto) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/needs/work/snapshot/latestInRange/")
                .buildAndExpand();
        ResponseEntity<EvidenceSupportWorkViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, EvidenceSupportWorkViewModel[].class);
        return response;
    }

    /**
     * Retrieve reportQuestionnaire snapshot
     */
    public ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> getReportQuestionnaireLatestSnapshotBeforeRange(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/questionnaire/snapshot/latestBeforeRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, QuestionnaireAnswersSnapshotViewModel[].class);
        return response;
    }

    public ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> getReportQuestionnaireLatestInRangeSnapshot(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/questionnaire/snapshot/latestInRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, QuestionnaireAnswersSnapshotViewModel[].class);
        return response;
    }

    public ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> getReportQuestionnaireEarliestInRangeSnapshot(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/questionnaire/snapshot/earliestInRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, QuestionnaireAnswersSnapshotViewModel[].class);
        return response;
    }

    /**
     * Retrieve reportSmartsteps snapshot
     */
    public ResponseEntity<SupportSmartStepsSnapshotViewModel[]> getReportSmartStepsLatestSnapshotBeforeRange(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/needs/snapshot/latestBeforeRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, SupportSmartStepsSnapshotViewModel[].class);
        return response;
    }

    public ResponseEntity<SupportSmartStepsSnapshotViewModel[]> getReportSmartStepsLatestInRangeSnapshot(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/needs/snapshot/latestInRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, SupportSmartStepsSnapshotViewModel[].class);
        return response;
    }

    public ResponseEntity<SupportSmartStepsSnapshotViewModel[]> getReportSmartStepsEarliestInRangeSnapshot(ReportCriteriaDto dto, int page) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "reports/evidence/needs/snapshot/earliestInRange/page/{page}/")
                .buildAndExpand(page);
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> response = restTemplate.postForEntity(uri.toUri(), dto, SupportSmartStepsSnapshotViewModel[].class);
        return response;
    }

}
