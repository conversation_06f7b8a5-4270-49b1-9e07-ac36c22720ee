package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.buildings.BuildingCommandViewModel;
import com.ecco.webApi.buildings.BuildingController;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.viewModels.Result;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;
import java.util.List;

/**
 * API actions relating to buildings.
 */
public class BuildingActor extends BaseActor {
    private static final String buildingsUri = WebApiSettings.APPLICATION_URL + "/api/buildings/";

    public BuildingActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    private UriComponentsBuilder createRotaUriBuilder(String action) {
        return UriComponentsBuilder
                .fromHttpUrl(WebApiSettings.APPLICATION_URL + "/api/rota/workers:all/" + action)
                .queryParam("serviceRecipientFilter", "referrals:all");
    }

    public FixedContainerViewModel getBuildingById(int id) {
        String uri = buildingsUri + id + "/";
        ResponseEntity<FixedContainerViewModel> response = restTemplate.getForEntity(uri, FixedContainerViewModel.class);
        return response.getBody();
    }

    public FixedContainerViewModel getBuildingByExternalRef(String ref) {
        String uri = buildingsUri + "/byExternalRef/" + ref + "/";
        ResponseEntity<FixedContainerViewModel> response = restTemplate.getForEntity(uri, FixedContainerViewModel.class);
        return response.getBody();
    }

    public FixedContainerViewModel getBuildingById(Integer id) {
        String uri = buildingsUri + "/" + id + "/";
        ResponseEntity<FixedContainerViewModel> response = restTemplate.getForEntity(uri, FixedContainerViewModel.class);
        return response.getBody();
    }

    public FixedContainerViewModel createBuilding(String buildingName, Integer locationId, Integer chargeCategoryId) {
        var bId = createResource(buildingName, locationId, 132, chargeCategoryId);
        var bvm = getBuildingById(bId);
        log.info("created building: {} with id {}", buildingName, bvm.buildingId);
        return bvm;
    }

    // units are also resourceTypeId 132
    public FixedContainerViewModel createUnit(String unitName, Integer locationId, int parentId) {
        createResource(unitName, locationId, 132, null, parentId, null);
        FixedContainerViewModel bvm = findBuildingByName(unitName);
        log.info("created building unit: {} with id {}", unitName, bvm.buildingId);
        return bvm;
    }

    /**
     * @param resourceTypeId 132 is pre-configured as a building
     * @param chargeCategoryId
     */
    public int createResource(String name, Integer locationId, Integer resourceTypeId, Integer chargeCategoryId) {
        return createResource(name, locationId, resourceTypeId, null, null, chargeCategoryId);
    }

    // NB chargeNameId 219 is set (service charges)
    public int createResource(String name, Integer locationId, Integer resourceTypeId, @Nullable String externalRef, @Nullable Integer parentId,
            Integer chargeCategoryId) {
        BuildingCommandViewModel command = new BuildingCommandViewModel("add", null)
                .changeName(null, name)
                .changeResourceTypeId(null, resourceTypeId)
                .changeChargeCategoryIdForServiceCharges(null, chargeCategoryId)
                .changeExternalRef(null, externalRef)
                .changeParentId(null, parentId);
        if (locationId != null) {
            command.location = ChangeViewModel.changeNullTo(locationId);
        }
        ResponseEntity<Result> response = executeCommand(command);
        return BuildingController.EXTRACT_ID_FN.apply(response.getBody().getLinks()[0].getHref());
    }

    public FixedContainerViewModel findBuildingByName(String buildingName) {
        List<FixedContainerViewModel> buildings = findBuildings();
        return buildings.stream().filter(input -> input.name.equals(buildingName)).findFirst().get();
    }

    public List<FixedContainerViewModel> findBuildings() {
        var typeRef = new ParameterizedTypeReference<List<FixedContainerViewModel>>() {};

        ResponseEntity<List<FixedContainerViewModel>> response = restTemplate.exchange(buildingsUri,
                HttpMethod.GET, null, typeRef);
        return response.getBody();
    }
}
