@charset "utf-8";

/* styling for json evidence screens */

fieldset.field .floatFields .floatFields {
    float: left;
    width: 50%;
    padding: 0 1em;
}
form > p {
    padding: 1em;
}

.forwardPlan {
    list-style: none;
}
.forwardPlan p {
    float: left;
    text-align: left;
}

/* styling for new support and risk screens, including their history */
.evidence-control {
    font-size: 14px; /* Allow selective reset until we can delete the above */
}

.multi-action-control li.smartstep {
    padding: 0 5px 6px;
}

.multi-action-control .panel {
    margin-bottom: 10px; /* bootstrap default 20 */
}

.multi-action-control .panel-body {
    padding-top: 0;
}

.multi-action-controls-hierarchical .multi-action-control {
    background-color: rgba(0,0,0, 0.05);
    padding: 15px;
    margin: 15px;
    border-width: thin;
    border-radius: 5px;
}

ul.smart-steps {
    padding-bottom: 22px;
}

h3.action-group {
    color: #3c83ca;
    font-size: 1.1em;
    font-style: italic;
    font-weight: normal;
    margin: 20px 15px 0;
    padding: 6px 4px;
    width: 95%;
    text-transform: lowercase;
}

li.smartstep {
    margin: 0;
    /*padding: 12px 20px;*/
    line-height: 1.5em; /* we have controls which wrap in here */
    border-top: 1px solid #d8d8d8;
}
li.smartstep:first-child {
    border-top: none;
}

li.smartstep > img {
    width: 24px;
    margin: 5px 15px 5px 3px;
}

li.smartstep > p {
    line-height: 1.4em; /* Cautiously use a more sensible height */
    font-size: 115%;
}

li.smartstep > textarea.form-control {
    margin: 6px;
}

li.smartstep > textarea.form-control {
    width: 95%;
}

ul.smart-steps input[type=date] {
    width: 170px;
    display: inline;
}

ul.smart-steps .form-control {
    display: inline;
    width: initial;
    margin-left: 10px;
}
/**
 * contenteditable override when on smart step areas.
 * Without this, the 'form-control' class on the editable div (above) takes priority
 * with display: inline and the editable div goes really small.
*/
ul.smart-steps div.textarea {
    display: block;
    margin-left: 0;
    margin-top: 6px;
}

ul.smart-steps .input-group > .form-control {
    margin-left: 0;
    min-width: 150px;
}

/* reset bootstrap style where we use both input-group and form-control */
ul.smart-steps .input-group {
    display: inline-table;
    width: 1%;
    position: relative;
    top: 11px; /* this is a fudge to get the baseline of the form-control lining up with the images */
}

ul.smart-steps textarea.form-control {
    width: 70%; /* for trigger/control */
}


li.relevant .fa {
    font-size: 80%;
}

ul.entry-list .user {
    float: right;
}

ul.entry-list .comment {
    margin-top: 10px;
    margin-bottom: 10px;
    background: #e0eeee;
    padding: 10px;
    border-radius: 10px;
    white-space: pre-wrap;
}
.v3 ul.entry-list .comment {
    border-radius: 0;
}
ul.entry-list .comment:empty {
    display: none;
}

ul.entry-list .extra {
    margin: 5px 5px 5px 40px;
}

ul.entry-list .changes li {
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid #e0eeee;
    padding: 10px;
    border-radius: 10px;
}

ul.entry-list img {
    vertical-align: bottom;
}

ul.entry-list .risk-flag-on {
    color: red;
    padding: 5px;
}
ul.entry-list .risk-flag-off {
    color: darkgrey;
    padding: 5px;
}
li .rag, li .recency {
    padding: 2px 6px;
    margin: 2px 6px;
    text-align: center;
    display: inline-block;
    border-radius: 13px;
}
li .recency {
    width: 190px;
    }
li .rag {
    width: 56px;
}

.flag_red {
    color: red;
}
.flag_amber {
    color: yellow;
}
.flag_green {
    color: green;
}

.risk-none {
    color: black;
    background-color: #eee;
}
.risk-red {
    color: white;
    background-color: red;
}
.risk-amber {
    color: black;
    background-color: yellow;
}
.risk-green {
    color: white;
    background-color: green;
}

.support-history-item li img,
.changes div img {
    width: 24px;
    margin: 1px;
    vertical-align: middle;

}
/* link image has not whitespace so add it ourselves */
.support-history-item li img.linked-evidence,
.changes div img.linked-evidence {
    padding: 2px;
}

.support-history-item .header-item {
    padding: 6px 6px 6px 0;
}

ul.entry-list > li.deleted {
    border-color: red;
    opacity: 0.65;
}
ul.entry-list > li.added {
    border: 2px solid lightgreen;
}
ul.entry-list > li.updated {
    border: 2px solid yellow;
}

.support-history-item > .alert {
    margin: 0 -5px;
}
.support-history-item .signature {
    cursor: pointer;
}

.support-history-item .signature > img {
    border-bottom: 1px dashed #a2b7d8;
    width: auto;
    height: 1.42857143em; /* Equal to line-height set by Bootstrap. */
}

.signature-capture {
    border: 1px dashed grey;
    border-radius: 4px;
    margin: 10px 0;
}

.signature-capture canvas {
    margin-bottom: -8px;
}

.action-instance .goal-name {
    border-bottom: 1px solid lightgray;
    margin-bottom: 8px;
    padding-bottom: 4px;
}

/* For when dropdown is selected */
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid #333333 1px;
    outline: 0;
}

.std-flag-image {
    display: inline-block;
    vertical-align: middle;
    width: 24px;
    height: 24px;
    margin: 1px;
}

.clickable-image, .status-image {
    display: inline-block;
    width: 24px;
    height: 24px;
    vertical-align: text-top;
    margin: 8px 4px;
}

.input-group-addon .clickable-image {
    margin: 1px;
}

.clickable-image {
    cursor: pointer;
}

.datepicker {
    background-image: url("./images/datepicker.png");
}

.flag_red24 {
    background-image: url("./images/flag_red24.png");
}

.commentOnly {
    background-image: url("./images/comment-only.svg");
}

.plus24 {
    background-image: url("./images/plus24.png");
}

.plus-faded24 {
    background-image: url("./images/plus24.png");
    filter: grayscale(1) contrast(.8);
}

.star24 {
    background-image: url("./images/star24.png");
}

.star-faded24 {
    filter: grayscale(1);
    background-image: url("./images/star24.png");
}

.tick {
    background-image: url("./images/tick.png");
}

.link {
    background-image: url("./images/link.png");
    background-size: contain;
}
.link-faded {
    opacity: .7;
    background-image: url("./images/link.png");
    background-size: contain;
}

.clickable-image.fa-clock-o {
    line-height: 1.35em;
}

.target-schedule span {
    line-height: 1.25em;
    top: 13px;
    position: relative;
}

/* To bring above MUI modal */
.modal.z-raise {
    z-index: 1400;
}

.MuiDialogActions-root button.btn {
    text-transform: uppercase;
}