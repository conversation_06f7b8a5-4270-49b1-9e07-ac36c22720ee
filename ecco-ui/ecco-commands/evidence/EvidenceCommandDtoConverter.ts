import {CommandDto} from "ecco-dto/command-dto";
import {CommandDtoConverter} from "../CommandDtoConverter";
import {Command} from "../commands";
import {SignWorkCommand} from "./SignWorkCommand";
import {UserAccessAuditCommand} from "./commands";
import {CommentCommand} from "./commentCommands";
import {FormEvidenceUpdateCommand} from "./formEvidenceCommands";

/** Converts between Command domain objects and their corresponding
 * DTO representations. */
export class EvidenceCommandDtoConverter implements CommandDtoConverter<Command, CommandDto> {
    private converters: { [discriminator: string]: (dto: CommandDto) => Command } = {};

    constructor() {
        this.converters[FormEvidenceUpdateCommand.discriminator] =
            FormEvidenceUpdateCommand.fromDto as (dto: CommandDto) => FormEvidenceUpdateCommand;
        this.converters[SignWorkCommand.discriminator] = SignWorkCommand.fromDto as (dto: CommandDto) => SignWorkCommand;
        this.converters[CommentCommand.discriminator] = CommentCommand.fromDto  as (dto: CommandDto) => CommentCommand;
        this.converters[UserAccessAuditCommand.discriminator] = UserAccessAuditCommand.fromDto as (dto: CommandDto) => UserAccessAuditCommand;
    }

    public fromDto(dto: CommandDto): Command {
        let converter = this.converters[dto.commandName!]; // commandName is mandatory in this scenario

        if (converter == null) {
            throw new TypeError("Unknown Command discriminator: " + dto.commandName);
        }
        console.debug("converting %o", dto.commandName);
        return converter(dto);
    }

    public toDto(command: Command): CommandDto {
        return command.toCommandDto();
    }
}
