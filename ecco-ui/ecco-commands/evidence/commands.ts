import {assertNotNull, BaseUpdateCommand, BaseUpdateCommandTransitioning} from "../cmd-queue/commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    ActivityInterestChangeDto,
    AreaUpdateCommandDto,
    AssociatedContactCommandDto,
    AttendanceStatus,
    DeleteEvidenceCommandDto,
    DeleteEvidenceRequestCommandDto,
    GoalUpdateCommandDto,
    LocationDto,
    QuestionAnswerCommandDto,
    WorkEvidenceCommandDto
} from "ecco-dto/evidence-dto";
import {
    asNumberChange,
    asStringChange,
    EvidenceGroup,
    Mergeable,
    NumberChangeOptional,
    StringChangeOptional
} from "ecco-dto";
import {CommandEffect, Delete} from "../commands";
import {SupportWork} from "./domain";
import {EvidenceCommand} from "./EvidenceCommand";
import {
    SourceAudit,
    UserAccessAuditCommandDto,
    UserAccessAuditLevel
} from "ecco-dto/evidence/evidence-command-dto";
import {CommentCommand, CommentCommandBuilder} from "./commentCommands";
import {EccoDateTime} from "@eccosolutions/ecco-common";


export class WorkUuidResolver {
    private workUuid: Uuid | null = null;

    public getWorkUuid(): Uuid {
        if (!this.workUuid) {
            this.workUuid = Uuid.randomV4();
            console.debug("getWorkUuid minted a new one: " + this.workUuid.toString());
        }
        return this.workUuid;
    }

    public resetWorkUuid() {
        this.workUuid = null;
    }
}

export class QuestionnaireCommentCommand extends CommentCommand {

    constructor(commandDto: WorkEvidenceCommandDto) {
        let effects: CommandEffect<any>[] = [];
        super(commandDto, effects);
    }
}

/**
 * Command to associate a contact with a piece of work.
 * Maps to com.ecco.webApi.evidence.EvidenceAssociatedContactCommandViewModel, for AssociatedContactCommand.java
 */
export class EvidenceAssociatedContactCommand extends BaseUpdateCommand {
    /** as per com.ecco.dom.commands.AssociatedContactCommand */
    public static discriminator = "associatedContact";

    /** The attendance status */
    attendanceStatus?: AttendanceStatus; // FIXME: This is always set - should be moved to ctor

    /**
     * The eventId that the work is for (eg 'rota visit')
     * Optional
     */
    eventId?: string;

    /**
     * The time the contact expects to spend on the work item in minutes.
     * This could be derived from eventId, but we want to rely on that
     * because its an 'ideal' value and might not reflect reality - which
     * a lone working system needs to do.
     */
    plannedDurationMins?: number;

    /**
     * The location of the contact at this (created) moment in time.
     */
    private location?: LocationDto;

    withLocation(location: GeolocationPosition | GeolocationPositionError | undefined): this {
        this.location = CommentCommandBuilder.fromLocation(location);
        return this;
    }

    constructor(
        private workUuid: Uuid,
        private serviceRecipientId: number,
        private contactId: number,
        private evidenceGroup: string,
        private taskName: string
    ) {
        super(
            `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup}/${taskName}/contact/${contactId}/`
        );
        assertNotNull(contactId);
    }

    public toDto(): AssociatedContactCommandDto {
        return {
            commandName: EvidenceAssociatedContactCommand.discriminator,
            uuid: this.getUuid().toString(),
            workUuid: this.workUuid.toString(),
            serviceRecipientId: this.serviceRecipientId,
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            contactId: this.contactId,
            attendanceStatus: this.attendanceStatus!!,
            eventId: this.eventId!!,
            location: this.location!! // TODO: Remove !! hacks
        };
    }

    public getEvidenceGroupName() {
        return this.evidenceGroup;
    }
}

/**
 * Command to change the answer to a question.
 * Maps to com.ecco.webApi.evidence.QuestionAnswerCommandViewModel
 */
export class QuestionAnswerCommand extends BaseUpdateCommand {
    public static discriminator = "questionanswer";

    private answerChange: StringChangeOptional;

    constructor(
        private operation: string,
        private workUuid: Uuid,
        private serviceRecipientId: number,
        private questionDefId: number,
        private evidenceGroup: string,
        private taskName: string
    ) {
        super(
            `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup}/${taskName}/questionanswer/${questionDefId}/`
        );
        assertNotNull(questionDefId);
    }

    public changeAnswer(from: string | null, to: string | null) {
        this.answerChange = asStringChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return this.answerChange != null;
    }

    public toDto(): QuestionAnswerCommandDto {
        return {
            commandName: QuestionAnswerCommand.discriminator,
            operation: this.operation,
            uuid: this.getUuid().toString(),
            workUuid: this.workUuid.toString(),
            serviceRecipientId: this.serviceRecipientId,
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            questionDefId: this.questionDefId,
            answerChange: this.answerChange
        };
    }

    public getEvidenceGroupName() {
        return this.evidenceGroup;
    }
}

/**
 * Command to add or remove an assocation between a goal on a referral and an activity type.
 */
export class ActivityInterestChangeCommand extends BaseUpdateCommand {
    public static discriminator = "activityInterestChange";

    /** operation should be either "CREATE", "UPDATE" or "DELETE" */
    constructor(
        private operation: string,
        private serviceRecipientId: number,
        private activityTypeId: number
    ) {
        super(`service-recipients/${serviceRecipientId}/tasks/activityInterest/`);
        assertNotNull(activityTypeId);
    }

    public override canMerge(candidate: Mergeable): boolean {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof ActivityInterestChangeCommand)) {
            return false;
        }

        const otherCommand = <ActivityInterestChangeCommand>candidate;

        return (
            this.getCommandTargetUri() == otherCommand.getCommandTargetUri() &&
            this.activityTypeId == otherCommand.activityTypeId &&
            ((this.operation == "add" && otherCommand.operation == "remove") ||
                (this.operation == "remove" && otherCommand.operation == "add"))
        );
    }

    public override merge(previousCommand: this): this {
        // If we can merge - then the result of a merge is that they cancel each other out
        return this;
    }

    public toDto(): ActivityInterestChangeDto {
        return {
            commandName: ActivityInterestChangeCommand.discriminator,
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: this.operation,
            activityTypeId: this.activityTypeId
        };
    }
}

// matches com.ecco.webApi.evidence.AreaUpdateCommandViewModel */
export class AreaUpdateCommand extends BaseUpdateCommand {
    public static discriminator = "areaUpdate";

    private levelChange: NumberChangeOptional;
    private triggerChange: StringChangeOptional;
    private controlChange: StringChangeOptional;

    //noinspection JSUnusedLocalSymbols
    constructor(
        private operation: string,
        private workUuid: Uuid,
        private serviceRecipientId: number,
        taskName: string,
        private areaDefId: number
    ) {
        super(
            `service-recipients/${serviceRecipientId}/evidence/threat/${taskName}/area/${areaDefId}/`
        );
    }

    public changeLevel(from: number, to: number) {
        this.levelChange = asNumberChange(from, to);
        return this;
    }

    public changeTrigger(from: string, to: string) {
        this.triggerChange = asStringChange(from, to);
        return this;
    }

    public changeControl(from: string, to: string) {
        this.controlChange = asStringChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return this.levelChange != null || this.triggerChange != null || this.controlChange != null;
    }

    public toDto(): AreaUpdateCommandDto {
        return {
            commandName: AreaUpdateCommand.discriminator,
            uuid: this.getUuid().toString(),
            workUuid: this.workUuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            areaDefId: this.areaDefId,
            levelChange: this.levelChange,
            triggerChange: this.triggerChange,
            controlChange: this.controlChange
        };
    }
}


// matches com.ecco.webApi.evidence.GoalUpdateCommandViewModel */
class BaseGoalUpdateCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "goalUpdate";

    private statusChange: NumberChangeOptional;

    /**
     * Associated action - link/chain icon
     * Ideally this would be 'isAssociated' but its a command with history now, and probably not worth being
     * part of GoalCommandExtractCommandViewModelJson.
     */
    private isRelevant?: boolean;

    /** Optional eventId to allow work to be linked to an event (where event is known)
     *  in the absence of a comment being saved (or at least before that comment may be saved at the
     *  end */
    private eventId?: string;

    /**
     * When completing a 'visit' task, the task needs to record its plannedDateTime
     * so the audit trail is clear on when the task was meant to be due, since we don't
     * collect
     */
    private plannedDateTime?: EccoDateTime;

    /** for when the status doesn't change because it's 5, but we want to record as a instance (vs edit of target date
     * or comment) */
    private forceStatusChange?: boolean;
    private targetDateChange: StringChangeOptional;
    private targetScheduleChange: StringChangeOptional;
    private expiryDateChange: StringChangeOptional;
    private goalNameChange: StringChangeOptional;
    private goalPlanChange: StringChangeOptional;
    private scoreChange: NumberChangeOptional;
    private statusChangeReason: NumberChangeOptional;
    private annotationChanges?: {[key: string]: StringChangeOptional};
    private likelihoodChange: NumberChangeOptional;
    private severityChange: NumberChangeOptional;
    private triggerChange: StringChangeOptional;
    private controlChange: StringChangeOptional;
    private hierarchyChange: NumberChangeOptional;
    private positionChange: StringChangeOptional;

    /** operation should be either "add", "remove" or "update"
     * actionInstanceUuid is needed for "remove" or "update" on base
     * workUuidQ argument can be null except for risk which currently uses this feature
     * evidenceGroup grouping for an arbitrary number of related history items (e.g. "needs", "threat",
     *     "reception enquiries", "restricted notes"), and most often encountered as very specific implementations
     *     found in EvidenceGroup.java - we are working towards this being more general purpose.
     */
    constructor(
        private operation: string,
        uuid: Uuid,
        private workUuid: Uuid,
        private serviceRecipientId: number,
        private evidenceGroup: string,
        private taskName: string,
        private actionDefId: number,
        private actionInstanceUuid: Uuid,
        private parentActionInstanceUuid?: Uuid
    ) {
        super(
            `service-recipients/${serviceRecipientId.toString()}/evidence/${evidenceGroup}/${taskName}/goals/${actionDefId.toString()}/`,
            uuid,
            BaseGoalUpdateCommand.discriminator
        );

        assertNotNull(workUuid, "BaseUpdateCommand.workUuid");
        assertNotNull(actionInstanceUuid, "BaseUpdateCommand.actionInstanceUuid");

        // "add-instance" is a HACK to allow us to do non-myPlan instances without having to do rest of
        // multi-instance support for needs/threat etc
        if (
            operation != "add" &&
            operation != "add-instance" &&
            operation != "remove" &&
            operation != "update"
        ) {
            throw new Error("Illegal param 'operation': " + operation);
        }
    }

    withEventId(eventId: string) {
        this.eventId = eventId;
        return this;
    }

    /** Add text annotation change data, but only if to != from */
    public changeAnnotation(key: string, from: string, to: string) {
        if (!this.annotationChanges) {
            this.annotationChanges = {};
        }
        this.annotationChanges[key] = asStringChange(from, to);
        return this;
    }

    /** Add comment change data, but only if to != from */
    public changeGoalName(from: string | null, to: string | null) {
        this.goalNameChange = asStringChange(from, to);
        return this;
    }

    /** Add comment change data, but only if to != from */
    public changeGoalPlan(from: string | null, to: string | null) {
        this.goalPlanChange = asStringChange(from, to);
        return this;
    }

    /** Add status change reason data, but only if to != from */
    public changeStatusChangeReason(from: number | null, to: number | null) {
        this.statusChangeReason = asNumberChange(from, to);
        return this;
    }

    /** Add score change data, but only if to != from */
    public changeScore(from: number | null, to: number | null) {
        this.scoreChange = asNumberChange(from, to);
        return this;
    }

    /** Add status change data, but only if to != from */
    public changeStatus(from: number | null, to: number | null) {
        this.statusChange = asNumberChange(from, to);
        return this;
    }

    /** Add comment change data, but only if to != from */
    public changeTargetDate(from: null | string, to: null | string) {
        this.targetDateChange = asStringChange(from, to);
        return this;
    }

    /**
     * Format is "days:Mo,Tu,We times:12:00,15:00"
     * days:* means every day.  Times:* means any time that day (e.g. yoga, meditation)
     */
    public changeTargetSchedule(from: null | string, to: null | string) {
        this.targetScheduleChange = asStringChange(from, to);
        return this;
    }

    /** Add comment change data, but only if to != from */
    public changeExpiryDate(from: null | string, to: null | string) {
        this.expiryDateChange = asStringChange(from, to);
        return this;
    }

    public changeHierarchy(from: number | null, to: number | null) {
        this.hierarchyChange = asNumberChange(from, to);
        return this;
    }

    public changePosition(from: string | null, to: string | null) {
        this.positionChange = asStringChange(from, to);
        return this;
    }

    public setForceStatusChange() {
        this.forceStatusChange = true;
        return this;
    }

    public changeLikelihood(from: number | null, to: number) {
        this.likelihoodChange = asNumberChange(from, to);
        return this;
    }

    public changeSeverity(from: number | null, to: number) {
        this.severityChange = asNumberChange(from, to);
        return this;
    }

    public changeHazard(from: string | null, to: string) {
        this.triggerChange = asStringChange(from, to);
        return this;
    }

    public changeIntervention(from: string | null, to: string) {
        this.controlChange = asStringChange(from, to);
        return this;
    }

    /** ensures a command is seen as hasChanges() == true so it does get sent even when no other changes */
    public setRelevant() {
        this.isRelevant = true;
    }

    public setPlannedDateTime(dateTime: EccoDateTime | null) {
        if (dateTime) {
            this.plannedDateTime = dateTime;
        }
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return (
            this.goalNameChange != null ||
            this.scoreChange != null ||
            this.targetDateChange != null ||
            this.targetScheduleChange != null ||
            this.expiryDateChange != null ||
            this.statusChange != null ||
            this.annotationChanges != null ||
            this.isRelevant ||
            this.statusChangeReason != null ||
            this.goalPlanChange != null ||
            this.likelihoodChange != null ||
            this.severityChange != null ||
            this.triggerChange != null ||
            this.controlChange != null ||
            this.hierarchyChange != null ||
            this.positionChange != null ||
            this.operation == "remove"
        );
    }

    public getServiceRecipientId() {
        return this.serviceRecipientId;
    }
    public getEvidenceGroupName() {
        return this.evidenceGroup;
    }
    public getTaskName() {
        return this.taskName;
    }
    public getActionDefId() {
        return this.actionDefId;
    }
    public getWorkUuid() {
        return this.workUuid;
    }
    public getOperation() {
        return this.operation;
    }
    public getStatusChange(): NumberChangeOptional {
        return this.statusChange;
    }
    public getTargetDateChange(): StringChangeOptional {
        return this.targetDateChange;
    }

    protected getCommandDto(): GoalUpdateCommandDto {
        return {
            commandName: BaseGoalUpdateCommand.discriminator, // must match ORM discriminator at server end
            uuid: this.uuid.toString(),
            workUuid: this.workUuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            operation: this.getOperation(),
            actionDefId: this.actionDefId,
            actionInstanceUuid: this.actionInstanceUuid.toString(),
            eventId: this.eventId,
            parentActionInstanceUuid: this.parentActionInstanceUuid
                ? this.parentActionInstanceUuid.toString()
                : undefined,
            positionChange: this.positionChange,
            hierarchyChange: this.hierarchyChange,
            statusChange: this.statusChange,
            isRelevant: this.isRelevant,
            forceStatusChange: this.forceStatusChange,
            targetDateChange: this.targetDateChange,
            targetScheduleChange: this.targetScheduleChange,
            expiryDateChange: this.expiryDateChange,
            goalNameChange: this.goalNameChange,
            goalPlanChange: this.goalPlanChange,
            scoreChange: this.scoreChange,
            statusChangeReason: this.statusChangeReason,
            plannedDateTime: this.plannedDateTime?.formatIso8601(),
            annotationChange: this.annotationChanges,
            likelihoodChange: this.likelihoodChange,
            severityChange: this.severityChange,
            triggerChange: this.triggerChange,
            controlChange: this.controlChange
        };
    }

    public override canMerge(candidate: Mergeable) {
        if (!(candidate instanceof BaseGoalUpdateCommand)) {
            return false;
        }

        const that = <BaseGoalUpdateCommand>candidate;

        return (
            this.getCommandTargetUri() == that.getCommandTargetUri() &&
            this.actionInstanceUuid.equals(that.actionInstanceUuid) &&
            this.workUuid.equals(that.workUuid)
        );
    }

    public override merge(previousCommand: this): this {
        // keep the last command only
        return this;
    }
}

export class MyPlanUpdateCommand extends BaseGoalUpdateCommand {

    /** operation should be either "add", "remove" or "update" */
    constructor(operation: string, uuid: Uuid, workUuid: Uuid, serviceRecipientId: number, taskName: string, actionDefId: number,
                actionInstanceUuid: Uuid) {
        super(operation, uuid, workUuid, serviceRecipientId, "my plan", taskName, actionDefId, actionInstanceUuid);
    }
}


export class GoalUpdateCommand extends BaseGoalUpdateCommand {
    /** operation should be either "add", "remove" or "update" */
    constructor(
        operation: "add" | "add-instance" | "remove" | "update",
        uuid: Uuid,
        workUuid: Uuid,
        serviceRecipientId: number,
        taskName: string,
        actionDefId: number,
        evidenceGroup: EvidenceGroup,
        actionInstanceUuid: Uuid,
        parentActionInstanceUuid?: Uuid
    ) {
        const evidenceGroupName = (evidenceGroup && evidenceGroup.name) || EvidenceGroup.needs.name;
        super(
            operation,
            uuid,
            workUuid,
            serviceRecipientId,
            evidenceGroupName,
            taskName,
            actionDefId,
            actionInstanceUuid,
            parentActionInstanceUuid
        );
    }
}

export enum EvidenceDiscriminator {SUPPORT, RISK, HR, FORM}

export class DeleteEvidenceRequestCommand extends BaseUpdateCommand {
    public static discriminator = "deleteReqEvidence"; // ?? as per DeleteEvidenceCommand.discriminator

    constructor(private discriminator: EvidenceDiscriminator, private serviceRecipientId: number,
                private workUuid: Uuid,
                private evidenceGroup: EvidenceGroup, private taskName: string,
                private reason: string, private revoke: boolean,
                private jsonViewModel: string) {
        super(`service-recipient/${serviceRecipientId}/evidence/${workUuid}/delete-request/`);
    }

    public toDto(): DeleteEvidenceRequestCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                discriminator: EvidenceDiscriminator[this.discriminator],
                serviceRecipientId: this.serviceRecipientId,
                workUuid: this.workUuid.toString(),
                evidenceGroup: this.evidenceGroup.name,
                evidenceTask: this.taskName,
                revoke: this.revoke,
                reason: this.reason,
                jsonViewModel: this.jsonViewModel
        });
    }
}

export class DeleteEvidenceCommand extends EvidenceCommand { // EvidenceCommand applies the modern effects
    public static discriminator = "deleteEvidence"; // ?? as per DeleteEvidenceCommand.discriminator

    private readonly serviceRecipientId: number;
    private workUuid: Uuid;
    private readonly evidenceGroup: EvidenceGroup;
    private readonly taskName: string;
    private readonly reason: string;
    private requestDeletionUuid: Uuid;
    private readonly jsonViewModel: string;

    constructor(uuid: Uuid, serviceRecipientId: number, workUuid: Uuid, evidenceGroup: EvidenceGroup,
                taskName:string, reason: string, requestDeletionUuid: Uuid, jsonViewModel: string) {
        let effects: CommandEffect<any>[] = [];
        // see AttachSignatureChange, which also 'effects' another domain object
        effects.push(new Delete<SupportWork>(workUuid.toString()));

        const uri = `service-recipient/${serviceRecipientId}/evidence/${workUuid}/delete/`;

        super(uuid, effects, uri, DeleteEvidenceCommand.discriminator);

        this.serviceRecipientId = serviceRecipientId;
        this.workUuid = workUuid;
        this.evidenceGroup = evidenceGroup;
        this.taskName = taskName;
        this.reason = reason;
        this.requestDeletionUuid = requestDeletionUuid;
        this.jsonViewModel = jsonViewModel;
    }

    public static create(serviceRecipientId: number, workUuid: Uuid, evidenceGroup: EvidenceGroup,
                         taskName: string, reason: string, requestDeletionUuid: string, jsonViewModel: string) {
        return new DeleteEvidenceCommand(Uuid.randomV4(), serviceRecipientId, workUuid, evidenceGroup,
                    taskName, reason, Uuid.parse(requestDeletionUuid), jsonViewModel);
    }

    public static fromDto(dto: DeleteEvidenceCommandDto): DeleteEvidenceCommand {
        return new DeleteEvidenceCommand(Uuid.parse(dto.uuid), dto.serviceRecipientId, Uuid.parse(dto.workUuid),
            EvidenceGroup.fromName(dto.evidenceGroup), dto.evidenceTask, dto.reason, Uuid.parse(dto.requestDeletionUuid),
            dto.jsonViewModel);
    }

    protected getCommandDto(): DeleteEvidenceCommandDto {
        return {
            commandName: DeleteEvidenceCommand.discriminator,
            commandUri: this.commandUri,
            uuid: this.uuid.toString(),
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            evidenceGroup: this.evidenceGroup.name,
            evidenceTask: this.taskName,
            workUuid: this.workUuid.toString(),
            requestDeletionUuid: this.requestDeletionUuid.toString(),
            jsonViewModel: this.jsonViewModel,
            reason: this.reason

        };
    }

}


export class UserAccessAuditCommand extends EvidenceCommand {
    // EvidenceCommand applies the modern effects
    public static discriminator = "userAccessAudit"; // ?? as per UserAccessAuditCommand.discriminator

    private readonly serviceRecipientId: number;
    private readonly taskName: string;
    private readonly level: UserAccessAuditLevel;
    private readonly source: SourceAudit;

    constructor(
        uuid: Uuid,
        serviceRecipientId: number,
        taskName: string,
        level: UserAccessAuditLevel,
        source: SourceAudit
    ) {
        let effects: CommandEffect<any>[] = [];
        // see AttachSignatureChange, which also 'effects' another domain object
        // TODO effects.push(new New<XX>());

        const uri = `service-recipients/${serviceRecipientId}/tasks/${taskName}/audit`;

        super(uuid, effects, uri, UserAccessAuditCommand.discriminator);

        this.serviceRecipientId = serviceRecipientId;
        this.taskName = taskName;
        this.level = level;
        this.source = source;
    }

    public static create(
        serviceRecipientId: number,
        taskName: string,
        level: UserAccessAuditLevel,
        source?: SourceAudit
    ) {
        return new UserAccessAuditCommand(
            Uuid.randomV4(),
            serviceRecipientId,
            taskName,
            level,
            source
        );
    }

    public static fromDto(dto: UserAccessAuditCommandDto): UserAccessAuditCommand {
        return new UserAccessAuditCommand(
            Uuid.parse(dto.uuid),
            dto.serviceRecipientId,
            dto.taskName,
            dto.level,
            dto.source
        );
    }

    protected getCommandDto(): UserAccessAuditCommandDto {
        return {
            commandName: DeleteEvidenceCommand.discriminator,
            commandUri: this.commandUri,
            uuid: this.uuid.toString(),
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            level: this.level,
            source: this.source
        };
    }
}