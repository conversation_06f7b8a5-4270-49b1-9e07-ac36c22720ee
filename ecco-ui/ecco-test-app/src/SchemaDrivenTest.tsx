import {HateoasResource, StringToObjectMap} from "@eccosolutions/ecco-common";
import {Button} from "@eccosolutions/ecco-mui";
import {TabsBuilder} from "ecco-components/navigation/TabsBuilder";
import {UsersList} from "ecco-components/user/UsersList";
import * as React from "react";
import {useState} from "react";
import {SchemaList} from "ecco-components/json-schema-form/SchemaList";
import {TasksList} from "ecco-components/tasks/TasksList";

export default function SchemaDrivenTest() {

    const actionRenderer = (dataRow: HateoasResource) => {
        return dataRow?.links.map(link => <Button key={link.rel}>{link.rel}</Button>);
    };

    // replaces a lot of controls/ReferralsListControl.ts except for the service/projects list
    const ReferralsTab = () => {
        const [page, setPage] = useState(0);
        const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});

        const statusGroup = filters.statusGroup || 'ongoing';

        return (
            <SchemaList
                src={`referrals/list/?statusGroup=${statusGroup}&page=${page}`}
                displayFields={[
                    "referralId",
                    "clientDisplayName",
                    "supportWorkerDisplayName",
                    "statusSummary"
                ]}
                filterFields={["statusGroup"]}
                actionRenderer={actionRenderer}
                onRowClick={links => alert(JSON.stringify(links))}
                filters={filters}
                onFilter={setFilters}
                page={page}
                onPage={setPage}
            />
        );
    };
    return (
        new TabsBuilder()
            .addTab("tasks", <TasksList />)
            .addTab("users", <UsersList />)
            // Moved to ecco-offline so use via welcome/staff-list .addTab("staff", <StaffList/>)
            .addTab("referrals", <ReferralsTab />)
            // incidents, see also SchedulerView.test.tsx
            .build()
    );
}