import {But<PERSON>} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useState} from "react";
import {ChangePasswordForm} from "ecco-components/user/ChangePasswordForm";

export const ModalFormsPage: FC = () => {
    const [show, setShow] = useState(false);

    return <div>
        <Button onClick={() => setShow(true)}>change password</Button>
        {show && <ChangePasswordForm onChange={() => setShow(false)}/>}
    </div>
}
