import * as React from 'react';
import {FC} from 'react';

import {
    ConfigurablePageComponentRegistry,
    EccoAPI,
    nullAuditHistoryIntegrations,
    ServicesContext,
    setGlobalEccoAPI,
    withSessionData
} from "ecco-components";
import {
    AddressedLocationAjaxRepository,
    AddressHistoryAjaxRepository,
    ApiClient,
    BuildingAjaxRepository,
    CalendarAjaxRepository,
    ChartAjaxRepository,
    ClientAjaxRepository,
    ContactsAjaxRepository,
    ContractAjaxRepository,
    FormEvidenceAjaxRepository,
    InvoicesAjaxRepository,
    NotificationAjaxRepository,
    QuestionnaireWorkAjaxRepository,
    ReferralAjaxRepository,
    RiskEvidenceAjaxRepository,
    ServiceRecipientAjaxRepository,
    SignatureAjaxRepository,
    SupportWorkAjaxRepository,
    TaskCommandAjaxRepository,
    UserAjaxRepository,
    WorkersAjaxRepository
} from "ecco-dto";
import {RotaAjaxRepository} from "ecco-rota";
import {CommandAjaxRepository} from "ecco-commands";
import {sessionDataFn} from "./services";
import {getEvidenceEffectsRepository} from "ecco-offline-data";
import {nullTaskIntegrations} from "ecco-components/EccoAPI";
import {FinanceAjaxRepository} from "ecco-dto";
import {IncidentAjaxRepository} from "ecco-incidents";
import {RepairAjaxRepository} from "ecco-repairs";
import {ManagedVoidAjaxRepository} from "ecco-managedvoids";

/** Shows a loading spinner while resolving sessionData then publishes access to all repositories
 * in EccoAPI via ServicesContext (for use via useServicesContext()) */
export const ServicesContextProvider: FC<{client: ApiClient}> = (props) => {
    const {client, children} = props;
    const supportWorkRepository = new SupportWorkAjaxRepository(client);
    const eccoAPI: EccoAPI = {
        baseURI: "TODO: it's for /offline|online/ in environment.ts",
        apiClient: client,
        pageComponentRegistry: new ConfigurablePageComponentRegistry(), // FIXME: Nothing will work without config like defaultPageComponentRegistry
        auditHistoryIntegrations: nullAuditHistoryIntegrations,
        taskIntegrations: nullTaskIntegrations,
        getAddressRepository: () => new AddressedLocationAjaxRepository(client),
        addressHistoryRepository: new AddressHistoryAjaxRepository(client),
        getBuildingRepository: () => new BuildingAjaxRepository(client),
        calendarRepository: new CalendarAjaxRepository(client),
        chartRepository: new ChartAjaxRepository(client),
        clientRepository: new ClientAjaxRepository(client),
        getCommandRepository: () => new CommandAjaxRepository(client),
        contactsRepository: new ContactsAjaxRepository(client),
        contractRepository: new ContractAjaxRepository(client),
        getEvidenceEffectsRepository: getEvidenceEffectsRepository, // FIXME: probably want only the online one for consistency with others here
        financeRepository: new FinanceAjaxRepository(client),
        formEvidenceRepository: new FormEvidenceAjaxRepository(client),
        incidentsRepository: new IncidentAjaxRepository(client),
        managedVoidsRepository: new ManagedVoidAjaxRepository(client),
        repairsRepository: new RepairAjaxRepository(client),
        notificationRepository: new NotificationAjaxRepository(client),
        invoicesRepository: new InvoicesAjaxRepository(client),
        referralRepository: () => new ReferralAjaxRepository(client),
        riskWorkRepository: new RiskEvidenceAjaxRepository(client),
        rotaRepository: new RotaAjaxRepository(client, sessionDataFn),
        getSignatureRepository: () => new SignatureAjaxRepository(client),
        supportWorkRepository,
        supportSmartStepsSnapshotRepository: supportWorkRepository,
        questionnaireSnapshotRepository: new QuestionnaireWorkAjaxRepository(client),
        userRepository: new UserAjaxRepository(client),
        workersRepository: new WorkersAjaxRepository(client),
        tasksRepository: new TaskCommandAjaxRepository(client),
        serviceRecipientRepository: new ServiceRecipientAjaxRepository(client),
        sessionData: null!
    };

    return withSessionData(sessionData => {
        eccoAPI.sessionData = sessionData;
        setGlobalEccoAPI(eccoAPI);
        return <ServicesContext.Provider value={eccoAPI}>
            {children}
        </ServicesContext.Provider>;
    });
};
