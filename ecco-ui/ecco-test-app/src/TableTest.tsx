import * as React from "react";
import {DataTable} from "ecco-components/table/DataTable";

export function TableTest() {
    const columns = ["name", "position", "office", "age", "date", "salary"];
    const data = [
        {
            name: "<PERSON>",
            position: "System Architect",
            office: "Edinburgh",
            age: "61",
            date: "2011/04/25",
            salary: "$320"
        },
        {
            name: "<PERSON>",
            position: "Accountant",
            office: "Tokyo",
            age: "63",
            date: "2011/07/25",
            salary: "$170"
        },
        {
            name: "<PERSON>",
            position: "Junior Technical Author",
            office: "San Francisco",
            age: "66",
            date: "2009/01/12",
            salary: "$86"
        },
        {
            name: "<PERSON><PERSON>",
            position: "Senior Javascript Developer",
            office: "Edinburgh",
            age: "22",
            date: "2012/03/29",
            salary: "$433"
        },
        {
            name: "<PERSON><PERSON>",
            position: "Accountant",
            office: "Tokyo",
            age: "33",
            date: "2008/11/28",
            salary: "$162"
        }
    ];

    return (
        <DataTable columns={columns} data={data} title={"title"} toolbar={data => <div></div>} />
    );
}
