// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CareTest should render correctly 1`] = `
<DocumentFragment>
  <div
    class="MuiPaper-root MuiCard-root makeStyles-card-1 MuiPaper-elevation1 MuiPaper-rounded"
  >
    <div
      class="MuiCardContent-root"
    >
      <p
        class="MuiTypography-root makeStyles-title-3 MuiTypography-body1 MuiTypography-colorTextSecondary MuiTypography-gutterBottom"
      >
        12th Dec, 8:00pm
      </p>
      <h6
        class="MuiTypography-root MuiTypography-h6"
      >
        <PERSON>
      </h6>
      <p
        class="MuiTypography-root makeStyles-pos-4 MuiTypography-body1 MuiTypography-colorTextSecondary"
      >
        Blah, CB3 3BB
      </p>
      <p
        class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom"
      />
    </div>
    <div
      class="MuiCardActions-root MuiCardActions-spacing"
    >
      <button
        class="MuiButtonBase-root MuiButton-root MuiButton-text"
        tabindex="0"
        type="button"
      >
        <span
          class="MuiButton-label"
        >
          visit
        </span>
        <span
          class="MuiTouchRipple-root"
        />
      </button>
      <button
        class="MuiButtonBase-root MuiButton-root MuiButton-text"
        tabindex="0"
        type="button"
      >
        <span
          class="MuiButton-label"
        >
          client details
        </span>
        <span
          class="MuiTouchRipple-root"
        />
      </button>
    </div>
  </div>
</DocumentFragment>
`;
