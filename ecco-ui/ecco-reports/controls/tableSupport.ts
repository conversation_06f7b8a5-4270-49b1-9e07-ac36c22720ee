import {EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";
import {SessionData} from "ecco-dto";
import {ColumnSelector, ColumnSpecification} from "ecco-dto";
import {ColumnRepresentationsMap, TableRepresentationBase} from "../ReportTable";

/** Using the json definition of "columns" as the selectionList, find the relevant definitions in the code */
export function representation<TRow>(
    definitions: ColumnRepresentationsMap<TRow>,
    selectionList: Array<ColumnSelector> | undefined
): TableRepresentationBase<TRow> {
    const columns = selectionList?.map(selector => getFieldRepresentation(definitions, selector));
    return new TableRepresentationBase<TRow>(columns || []);
}

export interface RowContext {
    getSessionData(): SessionData;
}

export class RowContextImpl implements RowContext {
    constructor(private sessionData: SessionData) {}
    public getSessionData() {
        return this.sessionData;
    }
}

export function getPathValue(object: any, ctx: RowContext, path: string[]): any {
    let result = object;
    path.forEach(key => {
        if (key == "lookupProjectFromId") {
            result = result && ctx.getSessionData().getProject(result as number)?.name;
        } else if (key == "lookupListDefFromId") {
            result =
                result &&
                ctx
                    .getSessionData()
                    .getListDefinitionEntryById(result as number)
                    .getDisplayName();
        } else {
            result = result && result[key];
        }
    });
    return result;
}

export type DataType =
    | "string"
    | "number"
    | "boolean"
    | "date"
    | "dateTime"
    | "time"
    | "array"
    | "link";
export interface ColumnRepresentation<TRow> {
    getHeading(): string;
    getType(): DataType;
    getCssClasses(row: TRow): string;
    /** represent the row using the given definition. If substitutePath is provided, then value represented is
     * selected by evaluating substitutePath[] as path elements within the row object. e.g. substitutePath could be
     * ["dateMap","date of death"] */
    represent(ctx: RowContext, row: TRow, substitutePath?: string[]): string | HrefData;
}

export interface HrefData {
    display: string;
    url?: string;
    click?: () => void;
}

export function hrefColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx?: RowContext) => HrefData
): ColumnRepresentation<TRow> {
    return {
        getType: () => "link",
        getHeading: () => heading,
        getCssClasses: () => "",
        // substitutePath can be applied to this definition when a "columns" item declares a "path" object ColumnSelector
        // see representation<TRow> above
        represent: (ctx, row) => {
            //var value: {display: string, url: string};
            // if (substitutePath) {
            //     value = getPathValue(row, substitutePath);
            // }
            let value = represent(row, ctx);
            return value || "-";
        }
    };
}

export function arrayColumn<TRow>(
    heading: string,
    represent: (row: TRow | null | undefined, ctx: RowContext) => any[] | null | undefined
): ColumnRepresentation<TRow> {
    return {
        getType: () => "array",
        getHeading: () => heading,
        getCssClasses: () => "",
        // substitutePath can be applied to this definition when a "columns" item declares a "path" object ColumnSelector
        // see representation<TRow> above
        represent: (ctx, row, substitutePath) => {
            let value: any[] | null | undefined;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath) as any[];
            } else {
                value = represent(row, ctx);
            }
            return value != null ? value.join(",") : "-";
        }
    };
}

export function textColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => string | null | undefined
): ColumnRepresentation<TRow> {
    return {
        getType: () => "string",
        getHeading: () => heading,
        getCssClasses: () => "",
        // substitutePath can be applied to this definition when a "columns" item declares a "path" object ColumnSelector
        // see representation<TRow> above
        represent: (ctx, row, substitutePath) => {
            let value: string | null | undefined;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value != null ? value : "-";
        }
    };
}

export function numberColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => number | null | undefined,
    showZero: boolean = false
): ColumnRepresentation<TRow> {
    return {
        getType: () => "number",
        getHeading: () => heading,
        getCssClasses: () => "text-right",
        represent: (ctx, row, substitutePath) => {
            let value: number | null | undefined;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value == null ? " " : !showZero && value == 0 ? "-" : value.toString(10);
        }
    };
}

export function numberFixedColumn<TRow>(
    heading: string,
    decimalPlaces: number,
    represent: (row: TRow, ctx: RowContext) => number | null | undefined
): ColumnRepresentation<TRow> {
    return {
        getType: () => "number",
        getHeading: () => heading,
        getCssClasses: () => "text-right",
        represent: (ctx, row, substitutePath) => {
            let value: number | null | undefined;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value == null ? " " : value == 0 ? "-" : value.toFixed(decimalPlaces);
        }
    };
}

export function fixedPrecisionNumberColumn<TRow>(
    heading: string,
    precision: number,
    represent: (row: TRow, ctx: RowContext) => number | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "number",
        getHeading: () => heading,
        getCssClasses: () => "text-right",
        represent: (ctx, row, substitutePath) => {
            let value: number | null;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value == null ? " " : value == 0 ? "-" : value.toFixed(precision);
        }
    };
}

const useIso = (sessionData: SessionData) => {
    return (
        sessionData.isEnabled("reports.breakdown.iso") ||
        sessionData.isEnabled("reports.breakdown.mui")
    );
};
// our format of 2023-08-16 14:07:19
const toExcel = (v: EccoDateTime): string | null => {
    // was formatIso8601, but in ecco-common the time.ts uses moment to format, so we can use a pattern to get what we want
    return v ? v.format("YYYY-MM-DD HH:mm:ss") : null;
};

export function dateColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => EccoDate | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "date",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row, substitutePath) => {
            let value: EccoDate | null;
            const fmt = (v: EccoDate): string =>
                useIso(ctx.getSessionData()) ? v.formatIso8601() : v.formatCSV();
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value != null ? fmt(value) : "-";
        }
    };
}

export function dateShortColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => EccoDate | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "date",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row, substitutePath) => {
            let value: EccoDate | null;
            if (substitutePath) {
                value = getPathValue(row, ctx, substitutePath);
            } else {
                value = represent(row, ctx);
            }
            return value != null ? value.formatShort() : "-";
        }
    };
}

/** Show the extracted ISO8601 UTC string (ending in Z) as a date time */
export function dateTimeColumnFromIsoUtc<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => string | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "dateTime",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row) => {
            const value = represent(row, ctx);
            const fmt = (v: string): string =>
                useIso(ctx.getSessionData())
                    ? EccoDateTime.parseIso8601Utc(v).formatIso8601()
                    : EccoDateTime.parseIso8601Utc(v).formatCSV();
            return value != null ? fmt(value) : "-";
        }
    };
}

/** Show the extracted ISO8601 zoneless string (no zone info) as a date time */
export function dateTimeColumnFromIsoZoneless<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => string | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "dateTime",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row) => {
            const value = represent(row, ctx);
            const fmt = (v: string): string | null =>
                useIso(ctx.getSessionData())
                    ? toExcel(EccoDateTime.parseIso8601(v))
                    : EccoDateTime.parseIso8601(v).formatCSV();
            return value != null ? fmt(value) || "-" : "-";
        }
    };
}

/**
 * @deprecated use dateTimeColumnFromIsoZoneless (or more specific dateTimeColumnFromIsoUtc where we really
 */
export function dateTimeColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => EccoDateTime | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "dateTime",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row) => {
            const value = represent(row, ctx);
            const fmt = (v: EccoDateTime): string | null =>
                useIso(ctx.getSessionData()) ? toExcel(v) : v.formatCSV();
            return value != null ? fmt(value) || "-" : "-";
        }
    };
}

export function timeColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => EccoTime | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "time",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row) => {
            const value = represent(row, ctx);
            return value != null ? value.formatHoursMinutes() : "-";
        }
    };
}

export function booleanColumn<TRow>(
    heading: string,
    represent: (row: TRow, ctx: RowContext) => boolean | null
): ColumnRepresentation<TRow> {
    return {
        getType: () => "boolean",
        getHeading: () => heading,
        getCssClasses: () => "text-center",
        represent: (ctx, row) => {
            const value = represent(row, ctx);
            return value != null ? (value ? "yes" : "no") : "-";
        }
    };
}

export function columnMap<TRow>(
    ...columns: ColumnRepresentation<TRow>[]
): ColumnRepresentationsMap<TRow> {
    const result: ColumnRepresentationsMap<TRow> = {};
    columns.forEach(col => {
        result[col.getHeading()] = col;
    });
    return result;
}

/** columns that are more dynamic.
 * eg in ClientOnly {"title": "tenancy start", "representation": "birthdate", "path":["client","dateMap","tenancy start"]},
 * or in CustomFormWork (see FormEvidenceSecretFields) {"title": "some date", "representation": "author", "path": ["form","3_date"]}
 */
export function expandedColumnRepresentation<TRow>(
    definitions: ColumnRepresentationsMap<TRow>,
    selector: ColumnSpecification
): ColumnRepresentation<TRow> {
    if (!selector.representation) {
        return definitions[selector.title];
    }

    // placeholders to avoid using an existing 'representation' (when one may not exist)
    // they are empty as we expect a 'path' to be present, which has overrides in textColumn, dateColumn etc
    const throwIfCalled = () => {
        throw Error();
    };
    definitions["_text"] = textColumn<TRow>("", throwIfCalled);
    definitions["_date"] = dateColumn<TRow>("", throwIfCalled);
    definitions["_dateTime"] = dateTimeColumn<TRow>("", throwIfCalled);
    definitions["_boolean"] = booleanColumn<TRow>("", throwIfCalled);
    definitions["_array"] = arrayColumn<TRow>("", throwIfCalled);

    return {
        getType: () => definitions[selector.representation].getType(),
        getHeading: () => selector.title,
        getCssClasses: row => definitions[selector.representation].getCssClasses(row),
        represent: (ctx: RowContext, row) => {
            const value = selector.path
                ? definitions[selector.representation].represent(ctx, row, selector.path)
                : definitions[selector.representation].represent(ctx, row);
            return value != null ? value : "-";
        }
    };
}
export function getFieldRepresentation<TRow>(
    definitions: ColumnRepresentationsMap<TRow>,
    selector: ColumnSelector
): ColumnRepresentation<TRow> {
    let result: ColumnRepresentation<TRow>;

    if (typeof selector === "string") {
        result = definitions[selector];
        if (!result) {
            throw new TypeError(
                `Cannot find representation for: '${selector}' in '${Object.keys(definitions)}'`
            );
        }
        return result;
    } else {
        // implies -> if (typeof selector === "object") {
        result = expandedColumnRepresentation(definitions, selector);
        if (!result) {
            throw new TypeError(
                `Cannot find representation for: '${JSON.stringify(selector)}' using '${Object.keys(
                    definitions
                )}'`
            );
        }
        return result;
    }
}


/**
 * This function allows a ColumnRepresentationMap<TRow> to inherit a column map of a different type such that a parent
 * can reuse a column map for a child property. For example, a report json defn can be 'source agency: company name'
 * where the code column definition uses a concatenation of the parent and child - so ReferralAggregate and Agency,
 * without specifying all the columns of Agency manually in the ReferralAggregate.
 *
 * This approach therefore automates the hard coded column definitions. For more dynamically generated column definition
 * ideas please see the options in the JIRA.
 *
 * NB The type, <TRow>, is determined from the Analyser and allows the column definitions in the code to be appropriately typed.
 * However, what is shown in the column can be anything (although we make use of 'textColumn' and 'numberColumn' etc to ensure
 * further typing). This join function allows us to return the same parent row type but passes the compile checking to a
 * nested path column map.
 *
 // * @param parentPropertyDefName the parent's column name to use as the prefix for the child properties
 * @param parentToChild function to get the child<YRow> from the parent<TRow>
 * @param childColumnMap the child column map to lookup the keys (report defn names) to add them to this column map
 * @param joinWith the string to use to join the parent with the child to produce one column name
 * @returns {ColumnRepresentationsMap<TRow>} column map of the child properties which will be added to the parent
 */
export function joinNestedPathColumnMaps<TRow, YRow>(
    parentPropertyDefName: string,
    parentToChild: (row: TRow) => YRow | null | undefined,
    childColumnMap: ColumnRepresentationsMap<YRow>,
    joinWith = ": "
): ColumnRepresentationsMap<TRow> {
    const result: ColumnRepresentationsMap<TRow> = {};
    if (!childColumnMap) {
        throw new Error("childColumnMap is empty for: " + parentPropertyDefName);
    }
    for (let key in childColumnMap) {
        let childPropertyDefName = key;
        if (childColumnMap.hasOwnProperty(childPropertyDefName)) {
            let fullPathDefName = parentPropertyDefName
                .concat(joinWith)
                .concat(childPropertyDefName);
            const y: YRow = {} as YRow;
            const arg = (row: TRow) => parentToChild(row) ?? y;
            const clss = (row: TRow) =>
                childColumnMap[childPropertyDefName].getCssClasses(arg(row));
            const typ = childColumnMap[childPropertyDefName].getType();
            result[fullPathDefName] = {
                getType: () => typ,
                getHeading: () => fullPathDefName,
                getCssClasses: clss,
                represent: (ctx: RowContext, row: TRow, _?: string[]) => {
                    return childColumnMap[childPropertyDefName].represent(
                        ctx,
                        parentToChild(row) ? parentToChild(row)! : y
                    );
                }
            };
        }
    }
    return result;
}

export function joinColumnMaps<TRow>(
    ...columnMaps: ColumnRepresentationsMap<TRow>[]
): ColumnRepresentationsMap<TRow> {
    const result: ColumnRepresentationsMap<TRow> = {};
    columnMaps.forEach(colMap => {
        for (let key in colMap) {
            if (colMap.hasOwnProperty(key)) {
                let col = colMap[key];
                result[col.getHeading()] = col;
            }
        }
    });
    return result;
}

/** Converts phraseWith1DigitAndAChar to "phrase with 1 digit and a char", but will
 * make a mess if you give it capitals e.g. HACT -> h a c t */
export function unCamelCase(camelCase: string): string {
    return camelCase == null || camelCase.indexOf(" ") > 0 || camelCase.match(/^\d+$/) // already has spaces, or just numbers
        ? camelCase
        : camelCase
              .split(/([A-Z0-9])/)
              .map(str => (str.match(/[A-Z0-9]/) ? " " + str.toLowerCase() : str))
              .join("");
}
