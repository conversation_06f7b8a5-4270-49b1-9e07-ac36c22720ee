import {EntityWithParent, SequenceAnalysis} from "./types";
import {ServiceRecipient} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import Sequence = LazyJS.Sequence;
import tableRepresentations = require("../tables/predefined-table-representations");

//*********************************
// Analysis: ServiceRecipientAnalysis

export class ServiceRecipientAnalysis<P> extends SequenceAnalysis<
    EntityWithParent<P, ServiceRecipient>
> {
    constructor(ctx: AnalysisContext, data: Sequence<EntityWithParent<P, ServiceRecipient>>) {
        super(ctx, data, (item: ServiceRecipient) => item.serviceRecipientId.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            ServiceRecipientCols: tableRepresentations.serviceRecipientColumns
        };
    }
}
