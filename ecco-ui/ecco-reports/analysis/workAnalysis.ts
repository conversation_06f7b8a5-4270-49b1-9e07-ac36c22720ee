import types = require("./types");
import tableRepresentations = require("../tables/predefined-table-representations");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {
    Accumulator,
    Analyser,
    CustomFormWorkWithRefToReferralAggregate,
    EntityWithParent,
    extractPair,
    Group,
    GroupedAnalysis,
    GroupFn,
    GroupWithVisits,
    ReferralAggregate,
    SequenceAnalysis,
    Transformer,
    WorkAnalysis,
    WorkGroupWithCount,
    WorkWithRefGroupWithCount,
    WorkWithRefToReferralAggregate,
    WorkWithRefToReferralAggregateSvh
} from "./types";
import {EccoDate, EccoDateTime, SparseArray, StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    baseOutcomeWorkColumns,
    filterWorkBySVHDate,
    flattenAllWork,
    flattenCustomFormWork,
    flattenSupportWork,
    groupByCommentType,
    groupBySourceGroup,
    groupBySourceTask,
    GroupedReferralAggregateAnalysisWithVisits,
    hoursSpent,
    minsInLastHour,
    svhWithWork,
    unGroup,
    VisitsAnalysisFromWorkAccumulator,
    visitWithReferralColumns,
    workCalcsPerGroup,
    workCountsBy,
    workCountsByWithCountOf,
    workUnGroupBy,
    WorkWithRefGroupWithSummary
} from "./workCommonAnalysis";
import {calendarOnlyColumns} from "./calendarAnalysis";
import {
    BaseOutcomeBasedWork,
    BaseWork,
    Client,
    EventResourceDto,
    EvidenceFlags,
    FormEvidence,
    QuestionAnswerSnapshotDto,
    ReferralSummaryDto,
    RiskFlags,
    RiskGroupEvidenceDto,
    RiskWorkEvidenceDto,
    SessionData,
    SingleValueHistoryDto,
    SupportFlags,
    SupportWork
} from "ecco-dto";
import {
    clientOnlyColumns,
    customFormWorkOnlyColumns,
    listDefIdLookup,
    referralAggregateReportItemColumns,
    referralReportItemColumns,
    referralSummaryColumns
} from "../tables/predefined-table-representations";
import {AnalysisContext} from "../chart-domain";
import {
    booleanColumn,
    columnMap,
    ColumnRepresentation,
    dateColumn,
    dateTimeColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {ColumnRepresentationsMap} from "../ReportTable";
import {flattenToSmartStepsAnalyser} from "./smartStepAnalysis";

function groupBySrId<T extends {serviceRecipientId: number}>(
    input: Sequence<T>
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => inputElement.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}

//*********************************
// exported properties

const svhOnlyColumns = columnMap(
    numberColumn<SingleValueHistoryDto>("id", row => row.id),
    numberColumn<SingleValueHistoryDto>("sr-id", row => row.serviceRecipientId),
    textColumn<SingleValueHistoryDto>("key", row => row.key),
    numberColumn<SingleValueHistoryDto>("value raw", row => row.value),
    textColumn<SingleValueHistoryDto>("value", (row, ctx) =>
        ctx.getSessionData().getListDefinitionEntryById(row.value).getName()
    ),
    dateColumn<SingleValueHistoryDto>("valid from", row =>
        EccoDateTime.parseIso8601(row.validFrom).toEccoDate()
    ),
    dateColumn<SingleValueHistoryDto>(
        "valid to",
        row => (row.validTo && EccoDateTime.parseIso8601(row.validTo).toEccoDate()) || null
    )
);

const baseWorkToSvh = joinNestedPathColumnMaps<
    WorkWithRefToReferralAggregateSvh,
    SingleValueHistoryDto
>("svh", row => row.svh, svhOnlyColumns);

const workWithRefColumns = columnMap(
    textColumn<WorkWithRefToReferralAggregate>(
        "r-id",
        row => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString()
    ),
    textColumn<WorkWithRefToReferralAggregate>(
        "client",
        row => row.reportItem.referral.clientDisplayName
    ),
    textColumn<WorkWithRefToReferralAggregate>("location", row =>
        listDefIdLookup(row.locationId || null, row.reportItem.sessionData!)
    )
);

const supportWorkOnlyColumns = columnMap(
    booleanColumn<SupportWork>(
        "risk required",
        row => row.riskManagementRequired || null
    )
    //      "associatedActions", "clientStatusId", "meetingStatusId", "riskManagementHandled", "actions", "attachments"
);

// named to indicate that we are assuming the data is support work, which at the moment has properties mapped to
// BaseOutcomeWork, but which only populates from support work - so we can avoid a large refactoring for now
// by generalising the type of the analysers
const fudgedSupportWorkOnlyColumns = columnMap(
    numberColumn<BaseOutcomeBasedWork>("travel time (mins)", row => row.minsTravel),
    numberColumn<BaseOutcomeBasedWork>("mileage to/from", row => row.mileageTo),
    numberColumn<BaseOutcomeBasedWork>("mileage during visit", row => row.mileageDuring)
);

export const supportWorkColumns = joinColumnMaps(baseOutcomeWorkColumns, supportWorkOnlyColumns);

// to signify that we expect the 'answers' here in the support work to be in this data set
// but in fact we have already mapped the questions to answers via getRecordRepresentationFromSources
// because we wanted them transposed
export const supportWorkWithAnswersColumns = joinColumnMaps(supportWorkColumns, supportWorkColumns);

//export const baseWorkColumnsWithParent =  joinColumnMaps(baseOutcomeWorkOnlyColumns, workWithRefColumns);

const supportWorkToEventOnlyColumns = joinNestedPathColumnMaps<
    BaseOutcomeBasedWork,
    EventResourceDto
>("event", row => row.event, calendarOnlyColumns);

const supportWorkToReferralColumns = joinNestedPathColumnMaps<
    WorkWithRefToReferralAggregate,
    ReferralAggregate
>("r", row => row.reportItem, referralAggregateReportItemColumns);

const supportWorkToClientColumns = joinNestedPathColumnMaps<WorkWithRefToReferralAggregate, Client>(
    "c",
    row => row.reportItem.client,
    clientOnlyColumns
);

export const supportWorkColumnsWithParent = joinColumnMaps(
    baseOutcomeWorkColumns,
    fudgedSupportWorkOnlyColumns,
    workWithRefColumns,
    supportWorkToEventOnlyColumns,
    supportWorkToReferralColumns,
    baseWorkToSvh
);

export const supportWorkColumnsWithReferralWithClient = joinColumnMaps(
    baseOutcomeWorkColumns,
    fudgedSupportWorkOnlyColumns,
    workWithRefColumns,
    supportWorkToEventOnlyColumns,
    supportWorkToReferralColumns,
    supportWorkToClientColumns,
    baseWorkToSvh
);

export const supportWorkToReferralSummaryColumns = joinNestedPathColumnMaps<
    SupportWork,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);
export const supportWorkColumnsWithReferral = joinColumnMaps(
    supportWorkColumns,
    supportWorkToReferralSummaryColumns
);

export const baseWorkToReferralSummaryColumns = joinNestedPathColumnMaps<
    EntityWithParent<ReferralSummaryDto, BaseOutcomeBasedWork>,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);
export const baseWorkColumnsWithParent = joinColumnMaps(
    baseOutcomeWorkColumns,
    baseWorkToReferralSummaryColumns
);

// see also SupportWorkOnlyAnalysis, and RiskWorkOnlyAnalysis
export class WorkAnyAnalysis extends SequenceAnalysis<BaseOutcomeBasedWork> {
    constructor(ctx: AnalysisContext, data: Sequence<BaseOutcomeBasedWork>) {
        super(ctx, data, (item: BaseOutcomeBasedWork) => item.id);
        this.derivativeAnalysers = {
            visitCountsByService: "visitSupportRiskCountsByServiceAnalyser",
            visitCountsByProject: "visitSupportRiskCountsByProjectAnalyser",
            visitCountsBySrId: "visitSupportRiskCountsBySrIdAnalyser",
            visitCountsBySrIdAndWorkType: "visitCountsBySrIdAndWorkTypeAnalyser", // this is type of support/risk
            workByCommentType: workByCommentTypeAnalyser
        };
        this.recordRepresentation = {
            WorkOnly: baseOutcomeWorkColumns,
            WorkWithReferral: baseWorkColumnsWithParent
        };
    }
}
const Un_GroupedAnalysisWithVisitsAnalyser: Analyser<
    GroupWithVisits<any>,
    Sequence<BaseOutcomeBasedWork>
> = function (ctx: AnalysisContext, input: Group<BaseOutcomeBasedWork>): WorkAnyAnalysis {
    return new WorkAnyAnalysis(ctx, input.elements);
};
types.analysersByName["Un_GroupedAnalysisWithVisitsAnalyser"] =
    Un_GroupedAnalysisWithVisitsAnalyser;

export class SupportWorkOnlyAnalysis extends SequenceAnalysis<SupportWork> {
    constructor(ctx: AnalysisContext, data: Sequence<SupportWork>) {
        super(ctx, data, (item: SupportWork) => item.id);
        this.derivativeAnalysers = {
            flattenToSmartSteps: "flattenToSmartStepsAnalyser",
            flattenToAssociatedActionsWithWork: "flattenToAssociatedActionsWithWorkAnalyser",

            // copied from WorkAnyAnalysis - support/risk independent
            visitCountsByService: "visitSupportRiskCountsByServiceAnalyser",
            visitCountsByProject: "visitSupportRiskCountsByProjectAnalyser",
            visitCountsBySrId: "visitSupportRiskCountsBySrIdAnalyser",
            workByCommentType: workByCommentTypeAnalyser,

            // replace some ReferralAggregate reports
            actionCountsBySrId: "actionCountsBySrIdAnalyser" // replaces 'actionDefFromReferrals'
        };
        this.recordRepresentation = {
            WorkOnly: supportWorkColumns,
            WorkWithReferral: supportWorkColumnsWithReferral
        };
    }
}

const riskWorkOnlyColumns =
    columnMap();
    // riskActions, flags, riskAreas, handledSupportWorkIds

const riskWorkToReferralSummaryOnlyColumns = joinNestedPathColumnMaps<
    RiskWorkEvidenceDto,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);

export const riskWorkColumns = joinColumnMaps(baseOutcomeWorkColumns, riskWorkOnlyColumns);
export const riskWorkColumnsWithReferral = joinColumnMaps(
    riskWorkColumns,
    riskWorkToReferralSummaryOnlyColumns
);

export class RiskWorkOnlyAnalysis extends SequenceAnalysis<RiskWorkEvidenceDto> {
    constructor(ctx: AnalysisContext, data: Sequence<RiskWorkEvidenceDto>) {
        super(ctx, data, (item: RiskWorkEvidenceDto) => item.id);
        this.derivativeAnalysers = {
            // mimicking SupportOnlyAnalysis
            flattenToRiskActions: "flattenToRiskActionAnalyser",
            flattenToRiskAreas: "flattenToRiskAreaAnalyser",
            flattenToRiskFlags: "flattenToRiskFlagAnalyser",

            // copied from WorkAnyAnalysis - support/risk independent
            visitCountsByService: "visitSupportRiskCountsByServiceAnalyser",
            visitCountsByProject: "visitSupportRiskCountsByProjectAnalyser",
            visitCountsBySrId: "visitSupportRiskCountsBySrIdAnalyser",
            workByCommentType: workByCommentTypeAnalyser
        };
        this.recordRepresentation = {
            RiskWorkOnly: riskWorkColumns,
            WorkWithReferral: riskWorkColumnsWithReferral
        };
    }
}

const customFormWorkToReferralColumns = joinNestedPathColumnMaps<
    CustomFormWorkWithRefToReferralAggregate,
    ReferralAggregate
>("r", row => row.reportItem, referralReportItemColumns);
export const customFormWorkColumnsWithParent = joinColumnMaps(
    customFormWorkToReferralColumns,
    customFormWorkOnlyColumns
);

//*********************************
// produce WorkWithRefGroupWithCount

function groupByRid(
    input: Sequence<WorkWithRefToReferralAggregate>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.reportItem.referral.referralId.toString())
        .pairs()
        .map(extractPair);
}

//*********************************
// Analyers for transformation of ReferralAggregate to WorkWithRefToReferralAggregate

export var supportWorkFromReferrals_JOIN_workByCommentTypeAnalyser: Transformer<
    ReferralAggregate,
    WorkWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(
        ctx,
        workCountsBy(flattenSupportWork(input), groupByCommentType, ctx)
    );
};

export const workCountColumns = columnMap(
    // choose one of these 'key' columns according to the analyser before this chart/table
    textColumn<WorkGroupWithCount<any>>("type", row => row.key),
    textColumn<WorkGroupWithCount<any>>("task", row => row.key),
    numberColumn<WorkGroupWithCount<any>>("count", row => row.count)
);

export class CustomFormWorkOnlyAnalysis extends SequenceAnalysis<FormEvidence<any>> {
    constructor(ctx: AnalysisContext, data: Sequence<FormEvidence<any>>) {
        super(ctx, data, (item: FormEvidence<any>) => item.id);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            CustomFormWorkOnly: customFormWorkOnlyColumns
        };
    }
}

export class CustomFormWorkWithParentRefAnalysis extends SequenceAnalysis<CustomFormWorkWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<CustomFormWorkWithRefToReferralAggregate>) {
        super(ctx, data, (item: CustomFormWorkWithRefToReferralAggregate) => item.id);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            CustomFormWork: customFormWorkColumnsWithParent
        };
    }
}
export var flattenToSupportWorkWithRefToAggregateAnalyser: Transformer<
    ReferralAggregate,
    WorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<WorkWithRefToReferralAggregate> {
    return new WorkWithParentRefAnalysis(ctx, flattenSupportWork(input));
};
export var flattenToAllWorkWithRefToAggregateAnalyser: Transformer<
    ReferralAggregate,
    WorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<WorkWithRefToReferralAggregate> {
    return new WorkWithParentRefAnalysis(ctx, flattenAllWork(input));
};

export var flattenToCustomFormWorkWithRefToAggregateAnalyser: Transformer<
    ReferralAggregate,
    CustomFormWorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<CustomFormWorkWithRefToReferralAggregate> {
    return new CustomFormWorkWithParentRefAnalysis(ctx, flattenCustomFormWork(input));
};

/** This deals with clicking on chart segment */
const wrapWorkWithParentRefSequenceAnalyser: Analyser<
    Group<WorkWithRefToReferralAggregate>,
    Sequence<WorkWithRefToReferralAggregate>
> = function (
    ctx: AnalysisContext,
    input: Group<WorkWithRefToReferralAggregate>
): WorkWithParentRefAnalysis {
    return new WorkWithParentRefAnalysis(ctx, input.elements);
};

export var unGroupWorkWithRefGroupWithCountAnalyser: Transformer<
    WorkWithRefGroupWithCount,
    WorkWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefGroupWithCount>
): SequenceAnalysis<WorkWithRefToReferralAggregate> {
    return new WorkWithParentRefAnalysis(ctx, workUnGroupBy(input, unGroup));
};

export class GroupedWorkWithParentRefAnalysis extends GroupedAnalysis<
    WorkWithRefToReferralAggregate,
    WorkWithRefGroupWithCount
> {
    constructor(ctx: AnalysisContext, data: Sequence<WorkWithRefGroupWithCount>) {
        super(ctx, data);
        this.recordRepresentation = {
            WorkCount: workCountColumns
        };
        this.derivativeAnalysers = {
            workGroupToVisitGroup: workGroupToVisitGroupAnalyser,
            //workGroupToVisitGroupByWorkType: workGroupToVisitGroupByWorkTypeAnalyser,
            // where is select all done
            unGroupWork: unGroupWorkWithRefGroupWithCountAnalyser // WIP
        };
        this.addOnClickAnalyser("ungroup", wrapWorkWithParentRefSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", WorkWithParentRefAnalysis);
    }
}
export var workWithRefAggByCommentTypeAnalyser: Transformer<
    WorkWithRefToReferralAggregate,
    WorkWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefToReferralAggregate>
): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(ctx, workCountsBy(input, groupByCommentType, ctx));
};
export var workWithRefAggByCommentTypeMinsAnalyser: Transformer<WorkWithRefToReferralAggregate, WorkWithRefGroupWithCount>
        = function(ctx: AnalysisContext, input: Sequence<WorkWithRefToReferralAggregate>): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(ctx, workCountsByWithCountOf(input, groupByCommentType, 'minsSpent', ctx));
};
export var workBySourceTaskAnalyser: Transformer<
    WorkWithRefToReferralAggregate,
    WorkWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefToReferralAggregate>
): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(ctx, workCountsBy(input, groupBySourceTask));
};
export var workBySourceGroupAnalyser: Transformer<
    WorkWithRefToReferralAggregate,
    WorkWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefToReferralAggregate>
): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(ctx, workCountsBy(input, groupBySourceGroup));
};
export class GroupedWorkAnalysis extends GroupedAnalysis<
    BaseOutcomeBasedWork,
    WorkGroupWithCount<BaseOutcomeBasedWork>
> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<WorkGroupWithCount<BaseOutcomeBasedWork>>
    ) {
        super(ctx, data);
        this.recordRepresentation = {
            WorkCount: workCountColumns
        };
        this.derivativeAnalysers = {
            //"workGroupToVisitGroup": workGroupToVisitGroupAnalyser,
            //"unGroupWork": unGroupWorkWithRefGroupWithCountAnalyser // WIP
        };
        this.addOnClickAnalyser("ungroup", wrapWorkSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", WorkAnyAnalysis);
    }
}
const wrapWorkSequenceAnalyser: Analyser<
    Group<BaseOutcomeBasedWork>,
    Sequence<BaseOutcomeBasedWork>
> = function (
    ctx: AnalysisContext,
    input: Group<BaseOutcomeBasedWork>
): WorkAnyAnalysis {
    return new WorkAnyAnalysis(ctx, input.elements);
};
export var workByCommentTypeAnalyser: Transformer<
    BaseOutcomeBasedWork,
    WorkGroupWithCount<BaseOutcomeBasedWork>
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseOutcomeBasedWork>
): SequenceAnalysis<WorkGroupWithCount<BaseOutcomeBasedWork>> {
    return new GroupedWorkAnalysis(ctx, workCountsBy(input, groupByCommentType, ctx));
};

//*********************************
// Analysis for types.SingleValueHistory

export function ensureWorkReferencesSvh(
    item: types.SingleValueHistoryWithWorkSummary,
    work: BaseWork
): WorkWithRefToReferralAggregateSvh {
    const result = <WorkWithRefToReferralAggregateSvh>work;
    result.svh = item;
    return result;
}
export function flattenToWorkWithSvh(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): Sequence<WorkWithRefToReferralAggregateSvh> {
    const allWork: Sequence<WorkWithRefToReferralAggregateSvh> = input
        .filter(p => p != null && p.work != null)
        .map(p => p.work.map(work => ensureWorkReferencesSvh(p, work)))
        .flatten<WorkWithRefToReferralAggregateSvh>();
    return allWork;
}

export var flattenToWorkWithRefToSvhAnalyser: Transformer<
    types.SingleValueHistoryWithWorkSummary,
    WorkWithRefToReferralAggregateSvh
> = function (
    ctx: AnalysisContext,
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): SequenceAnalysis<WorkWithRefToReferralAggregateSvh> {
    return new WorkWithParentRefAnalysis(ctx, flattenToWorkWithSvh(input));
};

//*********************************
// Analysis for types.SingleValueHistoryWithWorkSummary

// NB textColumn should be be able to reuse the columns from other table defs
// eg show all the usual 'referrals and client and address' fields
const svhWithWorkSummaryColumns = columnMap(
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "rid",
        row => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString()
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "cid",
        row => row.reportItem.referral.clientCode || row.reportItem.referral.clientId.toString()
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "last name",
        row => row.reportItem.client?.lastName
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "first name",
        row => row.reportItem.client?.firstName
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "postcode",
        row => row.reportItem.client?.address?.postcode
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("received", row =>
        EccoDate.parseIso8601(row.reportItem.referral.receivedDate || null)
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "phone",
        row => row.reportItem.client?.phoneNumber
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "mobile",
        row => row.reportItem.client?.mobileNumber
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("first contact", row =>
        EccoDate.parseIso8601(row.reportItem.referral.firstResponseMadeOn || null)
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("interview", row =>
        EccoDate.parseIso8601FromDateTime(row.reportItem.referral.decisionDate || null)
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "team",
        (row, ctx) =>
            ctx
                .getSessionData()
                .getServiceCategorisation(row.reportItem.referral.serviceAllocationId).projectName
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "worker",
        row => row.reportItem.referral.supportWorkerDisplayName
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("start", row =>
        EccoDate.parseIso8601(row.reportItem.referral.receivingServiceDate || null)
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("valid from", row =>
        EccoDateTime.parseIso8601(row.validFrom).toEccoDate()
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>("level", (row, ctx) =>
        row.valueRef
            ? ctx.getSessionData().getAnswerDisplayValue(row.valueRef, row.value?.toString())
            : null
    ),
    numberColumn<types.SingleValueHistoryWithWorkSummary>(
        "time (mins)",
        row => row.totalTimeSpentMins
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "time",
        row => `${hoursSpent(row.totalTimeSpentMins)}:${minsInLastHour(row.totalTimeSpentMins)}`
    ),
    numberColumn<types.SingleValueHistoryWithWorkSummary>("time left (mins)", (row, ctx) => {
        if (!row.valueRef) return null;
        const hoursAllowed = parseInt(
            ctx.getSessionData().getAnswerDisplayValue(row.valueRef, row.value?.toString())
        );
        const minsTaken = row.totalTimeSpentMins;
        return hoursAllowed * 60 - minsTaken;
    }),
    textColumn<types.SingleValueHistoryWithWorkSummary>("time left", (row, ctx) => {
        if (!row.valueRef) return null;
        const hoursAllowed = parseInt(
            ctx.getSessionData().getAnswerDisplayValue(row.valueRef, row.value?.toString())
        );
        const minsTaken = row.totalTimeSpentMins;
        const minsRemaining = hoursAllowed * 60 - minsTaken;
        return `${hoursSpent(minsRemaining)}:${minsInLastHour(minsRemaining)}`;
    }),
    dateColumn<types.SingleValueHistoryWithWorkSummary>(
        "valid to",
        row => (row.validTo && EccoDateTime.parseIso8601(row.validTo).toEccoDate()) || null
    ),
    dateColumn<types.SingleValueHistoryWithWorkSummary>("exited", row =>
        EccoDate.parseIso8601(row.reportItem.referral.exitedDate || null)
    ),
    textColumn<types.SingleValueHistoryWithWorkSummary>(
        "exit reason",
        row => row.reportItem.referral.exitReason
    )
);

const svhQuantisedWithWorkSummaryColumns = columnMap(
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "rid",
        row => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString()
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "cid",
        row => row.reportItem.referral.clientCode || row.reportItem.referral.clientId.toString()
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "last name",
        row => row.reportItem.client?.lastName
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "first name",
        row => row.reportItem.client?.firstName
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "postcode",
        row => row.reportItem.client?.address?.postcode
    ),
    dateColumn<QuantisedSingleValueHistoryWithWorkSummary>("received", row =>
        EccoDate.parseIso8601(row.reportItem.referral.receivedDate || null)
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "phone",
        row => row.reportItem.client?.phoneNumber
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "mobile",
        row => row.reportItem.client?.mobileNumber
    ),
    dateColumn<QuantisedSingleValueHistoryWithWorkSummary>("first contact", row =>
        EccoDate.parseIso8601(row.reportItem.referral.firstResponseMadeOn || null)
    ),
    dateColumn<QuantisedSingleValueHistoryWithWorkSummary>("interview", row =>
        EccoDate.parseIso8601FromDateTime(row.reportItem.referral.decisionDate || null)
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "team",
        (row, ctx) =>
            ctx
                .getSessionData()
                .getServiceCategorisation(row.reportItem.referral.serviceAllocationId).projectName
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "worker",
        row => row.reportItem.referral.supportWorkerDisplayName
    ),
    dateColumn<QuantisedSingleValueHistoryWithWorkSummary>("start", row =>
        EccoDate.parseIso8601(row.reportItem.referral.receivingServiceDate || null)
    ),

    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week1 time",
        row => row.week[0] && row.week[0].totalTimeSpentMins
    ),
    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week1 time diff",
        row => row.week[0] && row.week[0].totalTimeSpentMins
    ),

    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week1 level",
        (row, ctx) =>
            (row.week[0] &&
                row.week[0].svh.valueRef &&
                ctx
                    .getSessionData()
                    .getAnswerDisplayValue(
                        row.week[0].svh.valueRef,
                        row.week[0].svh.value!.toString()
                    )) ||
            null
    ),
    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week1 mins",
        row => row.week[0] && row.week[0].totalTimeSpentMins
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week2 level",
        (row, ctx) =>
            (row.week[1] &&
                row.week[1].svh.valueRef &&
                ctx
                    .getSessionData()
                    .getAnswerDisplayValue(
                        row.week[1].svh.valueRef,
                        row.week[1].svh.value!.toString()
                    )) ||
            null
    ),
    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week2 mins",
        row => row.week[1] && row.week[1].totalTimeSpentMins
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week3 level",
        (row, ctx) =>
            (row.week[2] &&
                row.week[2].svh.valueRef &&
                ctx
                    .getSessionData()
                    .getAnswerDisplayValue(
                        row.week[2].svh.valueRef,
                        row.week[2].svh.value!.toString()
                    )) ||
            null
    ),
    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week3 mins",
        row => row.week[2] && row.week[2].totalTimeSpentMins
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week4 level",
        (row, ctx) =>
            (row.week[3] &&
                row.week[3].svh.valueRef &&
                ctx
                    .getSessionData()
                    .getAnswerDisplayValue(
                        row.week[3].svh.valueRef,
                        row.week[3].svh.value!.toString()
                    )) ||
            null
    ),
    numberColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "week4 mins",
        row => row.week[3] && row.week[3].totalTimeSpentMins
    ),
    dateColumn<QuantisedSingleValueHistoryWithWorkSummary>("exited", row =>
        EccoDate.parseIso8601(row.reportItem.referral.exitedDate || null)
    ),
    textColumn<QuantisedSingleValueHistoryWithWorkSummary>(
        "exit reason",
        row => row.reportItem.referral.exitReason
    )
);

export class SingleValueHistoryWithWorkSummaryAnalysis extends SequenceAnalysis<types.SingleValueHistoryWithWorkSummary> {
    constructor(ctx: AnalysisContext, data: Sequence<types.SingleValueHistoryWithWorkSummary>) {
        super(ctx, data, (item: types.SingleValueHistoryWithWorkSummary) => item.id.toString());
        this.derivativeAnalysers = {
            svhCountsByKey: svhCountsByKeyAnalyser,
            svhCountsByValue: svhCountsByValueAnalyser,
            svhQuantisedWeekPivot: svhQuantisedWeekPivotAnalyser,
            workFromSvhWorkSummary: flattenToWorkWithRefToSvhAnalyser
        };
        this.recordRepresentation = {
            SVHWithWorkSummary: svhWithWorkSummaryColumns
        };
    }
}

export class PivotSingleValueHistoryWithWorkSummaryAnalysis extends SequenceAnalysis<QuantisedSingleValueHistoryWithWorkSummary> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<QuantisedSingleValueHistoryWithWorkSummary>,
        keyFn: (item: QuantisedSingleValueHistoryWithWorkSummary) => string
    ) {
        super(ctx, data, keyFn);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            SVHQuantisedWithWorkSummary: svhQuantisedWithWorkSummaryColumns
        };
    }
}

//*********************************
// Group SingleValueHistory quantised with dates
// this acts like a pivot table - taking some rows (per svh) into columns (cid -> svh1, svh2. svh3, svh4)
// where there is too much data to fit in the columns we have allowed additional rows to show with the excess

export var svhQuantisedWeekPivotAnalyser: Transformer<
    types.SingleValueHistoryWithWorkSummary,
    QuantisedSingleValueHistoryWithWorkSummary
> = function (
    ctx: AnalysisContext,
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): SequenceAnalysis<QuantisedSingleValueHistoryWithWorkSummary> {
    return new PivotSingleValueHistoryWithWorkSummaryAnalysis(
        ctx,
        pivotWeekCalcsPerGroup(input, groupByCid),
        (item: QuantisedSingleValueHistoryWithWorkSummary) => item.unique
    );
};

function groupByCid(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): Sequence<Group<types.SingleValueHistoryWithWorkSummary>> {
    return input
        .groupBy(inputElement => inputElement.reportItem.referral.clientId.toString())
        .pairs()
        .map(extractPair);
}

interface GroupSingleValueHistoryWithWorkSummary {
    (input: Sequence<types.SingleValueHistoryWithWorkSummary>): Sequence<
        Group<types.SingleValueHistoryWithWorkSummary>
    >;
}

function pivotWeekCalcsPerGroup(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>,
    groupFn: GroupSingleValueHistoryWithWorkSummary
): Sequence<QuantisedSingleValueHistoryWithWorkSummary> {
    return groupFn(input)
        .map((groupSvh: Group<types.SingleValueHistoryWithWorkSummary>) => {
            let allSvhForGroup: Sequence<types.SingleValueHistoryWithWorkSummary> =
                groupSvh.elements;
            let oneToMany: QuantisedSingleValueHistoryWithWorkSummary[] =
                new PivotSvhQuantiseByWeekAccumulator(groupSvh.key).reduce(allSvhForGroup);
            return oneToMany;
        })
        .flatten<QuantisedSingleValueHistoryWithWorkSummary>();
}

interface QuantisedSingleValueHistoryWithWorkSummary {
    unique: string; // concatenation of cid and row per cid
    reportItem: ReferralAggregate; // NB using ReferralAggregate.supportWork would not be correct since this is arbitrarily chosen from the week property
    week: SparseArray<{
        svh: types.SingleValueHistoryWithWorkSummary;
        totalTimeSpentMins: number;
        work: Sequence<BaseWork>;
    }>; // week number -> work[]
}
/** Converts a group of cid/week -> svh into a row with quantised svh as columns, like a pivot table */
export class PivotSvhQuantiseByWeekAccumulator
    implements
        Accumulator<
            QuantisedSingleValueHistoryWithWorkSummary[],
            types.SingleValueHistoryWithWorkSummary
        >
{
    private memo: StringToObjectMap<QuantisedSingleValueHistoryWithWorkSummary> = {}; //unique concat of cid and rows (to cater for overflow rows)

    // eg, cid
    constructor(private key: string) {}

    // NB the '...Summary' work time total in SingleValueHistoryWithWorkSummary is largely
    // meaningless here as the total time is for the svh - but the work could span more than one week
    private accumulate(
        prev: StringToObjectMap<QuantisedSingleValueHistoryWithWorkSummary>,
        svh: types.SingleValueHistoryWithWorkSummary
    ): StringToObjectMap<QuantisedSingleValueHistoryWithWorkSummary> {
        // ensure we have a row for this cid and svh week number
        const reportFromDate = EccoDate.parseIso8601(svh.reportItem.reportCriteria!.from)!;
        let svhFrom = EccoDateTime.parseIso8601(svh.validFrom).toEccoDate();
        let svhWeekNum = this.getWeekNumber(reportFromDate, svhFrom);
        let uniqueKey = this.extractUniqueKey(prev, svhWeekNum);
        if (!prev[uniqueKey]) {
            prev[uniqueKey] = {
                unique: uniqueKey,
                reportItem: svh.reportItem,
                week: {}
            };
        }

        // now build up the work in each week within the svh
        // the data structure [svh id -> relevant work] means we can partition the svh work into weeks
        // and be sure that the work will not overlap with another svh
        // NB we could have just ignored svh and grouped by weeks at the outset, but then we would
        // need to be careful to set the overflow rows according to an SVH clash as it currently does
        // and to ensure that the data in the overflow was relevant to the svh - see extractUniqueKey
        this.groupWorkByWeek(reportFromDate, svh.work).each(grp => {
            let totalMins = grp.elements.reduce((prev, work) => (prev += work.minsSpent), 0);
            prev[uniqueKey].week[parseInt(grp.key)] = {
                svh: svh,
                totalTimeSpentMins: totalMins,
                work: grp.elements
            };
        });

        return prev;
    }
    private groupWorkByWeek(
        reportFromDate: EccoDate,
        input: Sequence<BaseWork>
    ): Sequence<Group<BaseWork>> {
        return input
            .groupBy(work => {
                let workDate = EccoDateTime.parseIso8601(work.workDate).toEccoDate();
                return this.getWeekNumber(reportFromDate, workDate).toString();
            })
            .pairs()
            .map(extractPair);
    }
    /**
     * Ensure that we create a new row whenever there is an SVH for the same cid and week number of the svh.
     * This allows us to have an overflow row when the data doesn't fit.
     */
    private extractUniqueKey(
        prev: StringToObjectMap<QuantisedSingleValueHistoryWithWorkSummary>,
        weekNum: number
    ) {
        let rowIndex = 0; // assume only one row needed
        let uniqueIndex = this.key.concat(":").concat(rowIndex.toString());
        while (prev[uniqueIndex] && prev[uniqueIndex].week[weekNum]) {
            rowIndex++;
            uniqueIndex = this.key.concat(":").concat(rowIndex.toString());
        }
        return uniqueIndex;
    }
    // 0-based week number relative to the report date
    private getWeekNumber(reportFromDate: EccoDate, entityDate: EccoDate) {
        let diff = entityDate.toUtcJsDate().getTime() - reportFromDate.toUtcJsDate().getTime();
        let weekNum = Math.floor(diff / (1000 * 60 * 60 * 24 * 7)); // week in ms
        return weekNum;
    }
    reduce(
        allSvhForGroup: Sequence<types.SingleValueHistoryWithWorkSummary>
    ): QuantisedSingleValueHistoryWithWorkSummary[] {
        allSvhForGroup.reduce((prev, work) => this.accumulate(prev, work), this.memo);
        return this.flattenSparseArray();
    }
    // flatten each key into a row
    private flattenSparseArray(): QuantisedSingleValueHistoryWithWorkSummary[] {
        let result: QuantisedSingleValueHistoryWithWorkSummary[] = [];
        for (let key in this.memo) {
            result.push(this.memo[key]);
        }
        return result;
    }
}

//*********************************
// Group SingleValueHistory with Work summary

export class GroupedSingleValueHistoryWithWorkSummaryAnalysis extends SequenceAnalysis<
    Group<types.SingleValueHistoryWithWorkSummary>
> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<Group<types.SingleValueHistoryWithWorkSummary>>
    ) {
        super(ctx, data, (item: Group<types.SingleValueHistoryWithWorkSummary>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapSingleValueHistoryWithWorkSummary);
        this.addOnClickManyAnalysis("ungroup", SingleValueHistoryWithWorkSummaryAnalysis);
    }
}
const WrapSingleValueHistoryWithWorkSummary: Analyser<
    Group<types.SingleValueHistoryWithWorkSummary>,
    Sequence<types.SingleValueHistoryWithWorkSummary>
> = function (
    ctx: AnalysisContext,
    input: Group<types.SingleValueHistoryWithWorkSummary>
): SingleValueHistoryWithWorkSummaryAnalysis {
    return new SingleValueHistoryWithWorkSummaryAnalysis(ctx, input.elements);
};

function svhCountsBy(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>,
    groupFn: GroupFn<types.SingleValueHistoryWithWorkSummary>
): Sequence<Group<types.SingleValueHistoryWithWorkSummary>> {
    return groupFn(input).map(pair => {
        const input: Sequence<types.SingleValueHistoryWithWorkSummary> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

export function groupBySvhKey(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): Sequence<Group<types.SingleValueHistoryWithWorkSummary>> {
    return input
        .groupBy(inputElement => inputElement.key || "no key assigned")
        .pairs()
        .map(extractPair);
}

const svhCountsByKeyAnalyser: Analyser<
    Sequence<types.SingleValueHistoryWithWorkSummary>,
    Sequence<Group<types.SingleValueHistoryWithWorkSummary>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): GroupedSingleValueHistoryWithWorkSummaryAnalysis {
    return new GroupedSingleValueHistoryWithWorkSummaryAnalysis(
        ctx,
        svhCountsBy(input, groupBySvhKey)
    );
};

export function groupBySvhValue(
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): Sequence<Group<types.SingleValueHistoryWithWorkSummary>> {
    return input
        .groupBy(
            inputElement =>
                (inputElement.value && inputElement.value.toString()) || "no value assigned"
        )
        .pairs()
        .map(extractPair);
}

const svhCountsByValueAnalyser: Analyser<
    Sequence<types.SingleValueHistoryWithWorkSummary>,
    Sequence<Group<types.SingleValueHistoryWithWorkSummary>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<types.SingleValueHistoryWithWorkSummary>
): GroupedSingleValueHistoryWithWorkSummaryAnalysis {
    return new GroupedSingleValueHistoryWithWorkSummaryAnalysis(
        ctx,
        svhCountsBy(input, groupBySvhValue)
    );
};

//*********************************
// SingleValueHistory with Work summary

// Transform a stream of work (from all the referrals) to each svh (which means per referral per date & value)
export var workSummaryBySVHIdAnalyser: Transformer<
    WorkWithRefToReferralAggregate,
    types.SingleValueHistoryWithWorkSummary
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefToReferralAggregate>
): SequenceAnalysis<types.SingleValueHistoryWithWorkSummary> {
    return new SingleValueHistoryWithWorkSummaryAnalysis(
        ctx,
        convertBackToSVHWithWorkSummary(workCalcsPerGroup(input, workSummaryBySVHId))
    );
};

/**
 * Converts back to just a svh with accumulated stats.
 * Now includes the 'work' relevant for the svh, as this was expected.
 */
function convertBackToSVHWithWorkSummary(
    input: Sequence<WorkWithRefGroupWithSummary>
): Sequence<types.SingleValueHistoryWithWorkSummary> {
    return input.map(workGroup => {
        // although we have the group, we have no access to what it was
        // we extract it with the knowledge we have about the parent
        // as we want to retain this 'convertBackToSVHWithWork' attempt
        // as a record of how we can get more reuse in reporting
        // and hence how we can make it more automated
        // TODO use a parent method to get access back to the parent

        // the key is the id of the svh
        let keyId = parseInt(workGroup.key);

        // flatten all the work and svh in this group, and find by id
        let svh = workGroup.elements
            .map(work => {
                return work.reportItem.singleValueHistory;
            })
            .flatten<SingleValueHistoryDto>()
            .filter(s => {
                return s.id == keyId;
            })
            .first();

        // flatten all the work in this group, and find any reportItem
        let reportItem = workGroup.elements
            .flatten<WorkWithRefToReferralAggregate>()
            .map(work => work.reportItem)
            .first();

        // return our re-constructed object with the summary data
        let ret: types.SingleValueHistoryWithWorkSummary = {
            id: svh.id,
            serviceRecipientId: svh.serviceRecipientId,
            key: svh.key,
            value: svh.value,
            valueRef: svh.valueRef,
            validFrom: svh.validFrom,
            validTo: svh.validTo,
            totalTimeSpentMins: workGroup.totalTimeSpentMins,
            work: workGroup.elements,
            reportItem: reportItem
        };
        return ret;
    });
}

// this method allows us to avoid the accumulator being tied to SVH specifics
// because it produces something which can be reused to calculate values on work
function workSummaryBySVHId(
    input: Sequence<WorkWithRefToReferralAggregate>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    return convertToSVHGroupByWork(
        groupWorkBySVHId(filterWorkBySVHDate(filterMatchingWorkAndSVHSrId(svhWithWork(input))))
    );
}
function convertToSVHGroupByWork(
    input: Sequence<Group<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    return input.map(input => {
        return {
            key: input.key,
            elements: input.elements
                .map(svhWithWorkRA => svhWithWorkRA.work)
                .flatten<WorkWithRefToReferralAggregate>()
        };
    });
}
// group by id because we already have the data we need, this is just a grouping
// structure to do a calculation (and 'id' means we don't dilute the grouping)
function groupWorkBySVHId(
    input: Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>
): Sequence<Group<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>> {
    return (
        input
            .groupBy(svhWithWorkPair => svhWithWorkPair.svh.id.toString())
            // so far this will be the 'value' with an array of {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}
            .pairs()
            // pairs moves this structure into an array of ['value', {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}[]]
            .map(types.extractPair)
        // turns the pairs array into a Group {key: array[0], elements: array[1]}
        // which is key: 'value', elements: {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}
    );
}

// NB 'svhWithWork' probably only maps the same serviceRecipientId's anyway
// TODO verify this is always the case and remove this method and perhaps make
// the 'svhWithWork' name clearer
function filterMatchingWorkAndSVHSrId(
    input: Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>
): Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}> {
    return input
        .filter(svhWithWorkPair => {
            let match =
                svhWithWorkPair.svh.serviceRecipientId == svhWithWorkPair.work.serviceRecipientId;
            return match;
        })
        .filter(svhWithWorkPair => svhWithWorkPair != null);
}

//*********************************
// Analyers for WorkWithRefGroupWithCount

export var workByRidAnalyser: Transformer<
    WorkWithRefToReferralAggregate,
    WorkWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefToReferralAggregate>
): SequenceAnalysis<WorkWithRefGroupWithCount> {
    return new GroupedWorkWithParentRefAnalysis(ctx, workCountsBy(input, groupByRid));
};

// NB this is used with workByRid before it, and so the input is grouped by r-id.
// This is assumed in the GroupedReferralAggregateAnalysisWithVisits
function workGroupToVisitGroup(
    input: Sequence<WorkWithRefGroupWithCount>,
    ctx: AnalysisContext
): Sequence<types.ReferralAggregateGroupWithVisits> {
    return input.map(workGroup => {
        const allWork: Sequence<WorkWithRefToReferralAggregate> = workGroup.elements;
        return new VisitsAnalysisFromWorkAccumulator(workGroup.key, ctx).reduce(
            allWork
        );
    });
}

export var workGroupToVisitGroupAnalyser: Transformer<
    WorkWithRefGroupWithCount,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<WorkWithRefGroupWithCount>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        workGroupToVisitGroup(input, ctx)
    );
};

export class WorkWithParentRefAnalysis extends SequenceAnalysis<WorkWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<WorkWithRefToReferralAggregate>) {
        super(ctx, data, (item: WorkWithRefToReferralAggregate) => item.id);
        this.derivativeAnalysers = {
            workByCommentType: workWithRefAggByCommentTypeAnalyser,
            workByCommentTypeMins: workWithRefAggByCommentTypeMinsAnalyser,
            workBySourceTask: workBySourceTaskAnalyser,
            workBySourceGroup: workBySourceGroupAnalyser,
            workByRid: workByRidAnalyser,
            workSummaryBySVHId: workSummaryBySVHIdAnalyser,
            flattenToAssociatedActionsWithWork: "flattenToAssociatedActionsWithWorkAnalyser"
        };
        this.recordRepresentation = {
            Work: supportWorkColumnsWithParent,
            WorkWithClient: supportWorkColumnsWithReferralWithClient
        };
    }
}

export class GroupedVisitsByWorkTypeAnalysis<T extends BaseWork> extends GroupedAnalysis<
    T,
    types.GroupWithWorkTypeVisits<T>
> {
    constructor(ctx: AnalysisContext, data: Sequence<types.GroupWithWorkTypeVisits<T>>) {
        super(ctx, data);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            VisitsByType: visitsByTypeColumns
        };
        this.addOnClickAnalyser(
            "ungroup",
            (ctx: AnalysisContext, input: types.GroupWithWorkTypeVisits<T>) => {
                return new WorkAnyAnalysis(ctx, input.elements);
            }
        );
        //this.addOnClickAnalyser("ungroup", wrapActionDefSequenceWithOriginalAnalyser);
        //this.addOnClickManyAnalysis("ungroup", ActionDef_withRefTo_ReferralAggregate_Analysis);
        //this.addOnClickManyAnalysis("ungroup", "ReferralAggregateAnalysis");
        // show all the work for this r-id
        // this.addOnClickAnalyser("single", (ctx: AnalysisContext, input: types.GroupWithWorkTypeVisits<T>) => {
        //     return new WorkAnyAnalysis(ctx, input.elements);
        // });
    }
}

const visitByTypeGroupColumns =
    columnMap();
    // use only one of these key representations
    //textColumn<types.GroupWithWorkTypeVisits<BaseWork>>("r-id", (row) => row.key)

    //textColumn<types.ReferralAggregateGroupWithWorkTypeVisits>("c-id", (row) => extractClientFrom_RAGWV_groupedByRid(row)),
    //textColumn<types.ReferralAggregateGroupWithWorkTypeVisits>("client name", (row) => extractClientNameFrom_RAGWV_groupedByRid(row)),
    //textColumn<types.ReferralAggregateGroupWithWorkTypeVisits>("worker", (row) => extractWorkerNameFrom_RAGWV_groupedByRid(row))
const visitByTypeToTotalVisits = joinNestedPathColumnMaps<
    types.GroupWithWorkTypeVisits<BaseWork>,
    types.Group<BaseWork> & WorkAnalysis
>("total", row => row.total, visitWithReferralColumns);
const visitByTypeToSupportVisits = joinNestedPathColumnMaps<
    types.GroupWithWorkTypeVisits<SupportWork>,
    types.Group<SupportWork> & WorkAnalysis
>("support", row => row.support, visitWithReferralColumns);
const visitByTypeToThreatVisits = joinNestedPathColumnMaps<
    types.GroupWithWorkTypeVisits<RiskWorkEvidenceDto>,
    types.Group<RiskWorkEvidenceDto> & WorkAnalysis
>("risk", row => row.threat, visitWithReferralColumns);
// NB ASSUMES the group is by referral
const visitByTypeToReferralColumns = joinNestedPathColumnMaps<types.GroupWithWorkTypeVisits<BaseWork>, ReferralSummaryDto>("r",
        (row) => row.elements.first().referralSummary, referralSummaryColumns);
const visitsByTypeColumns = joinColumnMaps(
    visitByTypeGroupColumns,
    visitByTypeToReferralColumns,
    visitByTypeToTotalVisits,
    visitByTypeToSupportVisits,
    visitByTypeToThreatVisits
);

//*********************************
// Grouped Analysis: EvidenceFlags


const riskFlagsCountsByValueAnalyser: Analyser<
    Sequence<RiskFlags>,
    Sequence<Group<RiskFlags>>
> = function (ctx: AnalysisContext, input: Sequence<RiskFlags>): GroupedRiskFlagsAnalysis {
    return new GroupedRiskFlagsAnalysis(
        ctx,
        evidenceFlagsCountsBy(input, groupByEvidenceFlagsValue)
    );
};
class GroupedRiskFlagsAnalysis extends SequenceAnalysis<Group<RiskFlags>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<RiskFlags>>) {
        super(ctx, data, (item: Group<RiskFlags>) => item.key);
        this.addOnClickAnalyser(
            "ungroup",
            (ctx: AnalysisContext, input: Group<RiskFlags>) =>
                new RiskFlagsAnalysis(ctx, input.elements)
        );
        this.addOnClickManyAnalysis("ungroup", RiskFlagsAnalysis);
    }
}


const evidenceFlagsCountsByValueAnalyser: Analyser<
    Sequence<EvidenceFlags>,
    Sequence<Group<EvidenceFlags>>
> = function (ctx: AnalysisContext, input: Sequence<EvidenceFlags>): GroupedEvidenceFlagsAnalysis {
    return new GroupedEvidenceFlagsAnalysis(
        ctx,
        evidenceFlagsCountsBy(input, groupByEvidenceFlagsValue)
    );
};

export function groupByEvidenceFlagsValue<T extends {value: boolean}>(
    input: Sequence<T>
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => (inputElement.value ? "on" : "off"))
        .pairs()
        .map(extractPair);
}

class GroupedEvidenceFlagsAnalysis extends SequenceAnalysis<Group<EvidenceFlags>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<EvidenceFlags>>) {
        super(ctx, data, (item: Group<EvidenceFlags>) => item.key);
        const analyser: Analyser<Group<EvidenceFlags>, any> = WrapEvidenceFlagsSequenceAnalyser;
        this.addOnClickAnalyser("ungroup", analyser);
        this.addOnClickManyAnalysis("ungroup", EvidenceFlagsAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapEvidenceFlagsSequenceAnalyser: Analyser<
    Group<EvidenceFlags>,
    Sequence<EvidenceFlags>
> = function (ctx: AnalysisContext, input: Group<EvidenceFlags>): EvidenceFlagsAnalysis {
    return new EvidenceFlagsAnalysis(ctx, input.elements);
};

function evidenceFlagsCountsBy<T>(input: Sequence<T>, groupFn: GroupFn<T>): Sequence<Group<T>> {
    return groupFn(input).map(pair => {
        let input: Sequence<T> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

//*********************************
// EvidenceFlags

/**
 * shows the flag information across many pieces of work
 */
const evidenceFlagsOnlyColumns = columnMap(
    numberColumn<EvidenceFlags>("id", row => row.id),
    numberColumn<EvidenceFlags>("f-id", row => row.flagId),
    numberColumn<EvidenceFlags>("sr-id", row => row.serviceRecipientId),
    dateTimeColumn<EvidenceFlags>("work date", row => EccoDateTime.parseIso8601(row.workDate)),
    // 'comment' is the latest comment, but we don't want to confuse them
    textColumn<EvidenceFlags>("comment", (row) => (row as SupportFlags).latestWorkComment),
    textColumn<EvidenceFlags>("name", (row, ctx) =>
        ctx.getSessionData().getListDefinitionEntryById(row.flagId).getName()
    ),
    textColumn<EvidenceFlags>("value", row => (row.value ? "on" : "off"))
);

const evidenceFlagsToReferralSummaryColumns = joinNestedPathColumnMaps<
    EvidenceFlags,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);
const evidenceFlagsToClientColumns = joinNestedPathColumnMaps<EvidenceFlags, Client>(
    "c",
    row => row.client,
    clientOnlyColumns
);

const evidenceFlagsToWorkColumns = joinNestedPathColumnMaps<SupportFlags, SupportWork>("w",
    (row) => row.work, supportWorkWithAnswersColumns);

const evidenceFlagsWithReferralSummaryColumns = joinColumnMaps(
    evidenceFlagsOnlyColumns,
    evidenceFlagsToReferralSummaryColumns,
    evidenceFlagsToWorkColumns,
    evidenceFlagsToClientColumns
);

const riskFlagsToWorkColumns = joinNestedPathColumnMaps<RiskFlags, RiskWorkEvidenceDto>(
    "w",
    row => row.work,
    riskWorkColumns
);
const riskFlagsColumns = joinColumnMaps(evidenceFlagsOnlyColumns, riskFlagsToWorkColumns);
const riskFlagsWithReferralSummaryColumns = joinColumnMaps(
    riskFlagsColumns,
    evidenceFlagsToReferralSummaryColumns,
    evidenceFlagsToClientColumns
);

export class RiskFlagsAnalysis extends SequenceAnalysis<RiskFlags> {
    constructor(ctx: AnalysisContext, data: Sequence<RiskFlags>) {
        super(ctx, data, (item: RiskFlags) => item.id.toString());
        this.derivativeAnalysers = {
            riskFlagsCountsByValue: riskFlagsCountsByValueAnalyser
        };
        this.recordRepresentation = {
            RiskFlagsOnly: riskFlagsColumns,
            RiskFlagsWithReferralSummary: riskFlagsWithReferralSummaryColumns
        };
    }
}

// COPY
function transposedQnAnswerWorkQnNameColumns(
    qnIds: number[],
    sessionData: SessionData,
    answers: (row: EvidenceFlags) => QuestionAnswerSnapshotDto[]
): ColumnRepresentation<EvidenceFlags>[] {
    //textColumn<TransposedQnAnswerWorkWithRefToReferralAggregate>("rid", (row) => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString())
    return qnIds.map(qnId =>
        textColumn<EvidenceFlags>(sessionData.getQuestionById(qnId).name, (row, ctx) =>
            tableRepresentations.questionAnswerDisplayValueLookup(
                qnId,
                answers(row)
                    .filter(a => a.questionId == qnId)
                    .map(a => a.answer)
                    .pop(),
                ctx.getSessionData()
            )
        )
    );
}

export class EvidenceFlagsAnalysis extends SequenceAnalysis<EvidenceFlags> {
    constructor(ctx: AnalysisContext, data: Sequence<EvidenceFlags>) {
        super(ctx, data, (item: EvidenceFlags) => item.id.toString());
        this.derivativeAnalysers = {
            flagsCountsByValue: evidenceFlagsCountsByValueAnalyser
        };
        this.recordRepresentation = {
            flagsOnly: evidenceFlagsOnlyColumns,
            flagsWithReferralSummary: evidenceFlagsWithReferralSummaryColumns,
            flagsWithReferralSummaryLatestQnr: evidenceFlagsWithReferralSummaryColumns
        };
    }

    // COPY of getRecordRepresentationFromSources from questionnaireAnalysis.ts
    // sources are from the definition of "columnSourceDefIds": ["questionGroupId"]
    override getRecordRepresentationFromSources(
        name: string,
        sources: string[]
    ): ColumnRepresentationsMap<EvidenceFlags> {
        // dynamic columns are triggered using this representation name
        if (!sources || sources.length == 0) {
            return {};
        }
        const questionIds = sources
            .map(qgIdStr =>
                this.ctx
                    .getSessionData()
                    .getQuestionGroupById(parseInt(qgIdStr))!
                    .questions.map(q => q.id)
            )
            .reduce((r, x) => r.concat(x), []); // flatMap;

        // get the answers from the questionnaire snapshot, or the flag's work item
        const ans: (row: EvidenceFlags) => QuestionAnswerSnapshotDto[] = row => [];
        const qns: (row: EvidenceFlags) => QuestionAnswerSnapshotDto[] = row =>
            (row as SupportFlags).questionnaireWork?.answers || [];
        const qa: (row: EvidenceFlags) => QuestionAnswerSnapshotDto[] = row =>
            name.includes("LatestQnr") ? qns(row) : ans(row);
        let columns: ColumnRepresentation<EvidenceFlags>[];
        columns = transposedQnAnswerWorkQnNameColumns(
            questionIds,
            this.ctx.getSessionData(),
            (row: EvidenceFlags) => []
        );
        return columnMap(...columns);
    }
}


//*********************************
// RiskRatings

// Grouped Analysis
const riskRatingsCountsByLevelAnalyser: Analyser<
    Sequence<RiskGroupEvidenceDto>,
    Sequence<Group<RiskGroupEvidenceDto>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<RiskGroupEvidenceDto>
): GroupedRiskRatingsAnalysis {
    return new GroupedRiskRatingsAnalysis(ctx, riskRatingsCountsBy(input, groupByRiskRatingsLevel));
};

export function groupByRiskRatingsLevel(
    input: Sequence<RiskGroupEvidenceDto>
): Sequence<Group<RiskGroupEvidenceDto>> {
    return input
        .groupBy(inputElement => inputElement.level.toString())
        .pairs()
        .map(extractPair);
}

class GroupedRiskRatingsAnalysis extends SequenceAnalysis<Group<RiskGroupEvidenceDto>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<RiskGroupEvidenceDto>>) {
        super(ctx, data, (item: Group<RiskGroupEvidenceDto>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapRiskRatingsSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", RiskRatingsAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapRiskRatingsSequenceAnalyser: Analyser<
    Group<RiskGroupEvidenceDto>,
    Sequence<RiskGroupEvidenceDto>
> = function (ctx: AnalysisContext, input: Group<RiskGroupEvidenceDto>): RiskRatingsAnalysis {
    return new RiskRatingsAnalysis(ctx, input.elements);
};

function riskRatingsCountsBy(
    input: Sequence<RiskGroupEvidenceDto>,
    groupFn: GroupFn<RiskGroupEvidenceDto>
): Sequence<Group<RiskGroupEvidenceDto>> {
    return groupFn(input).map(pair => {
        let input: Sequence<RiskGroupEvidenceDto> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

// Single Analysis
const riskRatingsOnlyColumns = columnMap(
    numberColumn<RiskGroupEvidenceDto>("id", row => row.id),
    numberColumn<RiskGroupEvidenceDto>("sr-id", row => row.serviceRecipientId),
    numberColumn<RiskGroupEvidenceDto>("sa-id", row => row.serviceAllocationId),
    dateTimeColumn<RiskGroupEvidenceDto>("work date", row =>
        EccoDateTime.parseIso8601(row.workDate || null)
    ),
    numberColumn<RiskGroupEvidenceDto>("ra-id", row => row.riskAreaId),
    textColumn<RiskGroupEvidenceDto>("name", (row, ctx) => row.riskAreaName),
    textColumn<RiskGroupEvidenceDto>("trigger", (row, ctx) => row.trigger),
    textColumn<RiskGroupEvidenceDto>("levelMeasure", (row, ctx) => row.levelMeasure),
    numberColumn<RiskGroupEvidenceDto>("level", row => row.level)
);

const riskRatingsToReferralSummaryColumns = joinNestedPathColumnMaps<
    RiskGroupEvidenceDto,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);

const riskRatingsWithReferralSummaryColumns = joinColumnMaps(
    riskRatingsOnlyColumns,
    riskRatingsToReferralSummaryColumns
);

export class RiskRatingsAnalysis extends SequenceAnalysis<RiskGroupEvidenceDto> {
    constructor(ctx: AnalysisContext, data: Sequence<RiskGroupEvidenceDto>) {
        super(ctx, data, (item: RiskGroupEvidenceDto) => item.id.toString());
        this.derivativeAnalysers = {
            riskRatingsCountsByLevel: riskRatingsCountsByLevelAnalyser
        };
        this.recordRepresentation = {
            RiskRatingsOnly: riskRatingsOnlyColumns,
            RiskRatingsWithReferralSummary: riskRatingsWithReferralSummaryColumns
        };
    }
}
