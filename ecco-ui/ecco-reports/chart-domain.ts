import {SessionData, RelatedEntitiesKey} from "ecco-dto";
import {ChartDefinitionDto, AnalyserConfig} from "ecco-dto";
import {StageType, SeriesType} from "./charts-dto";
import {ReportCriteriaDtoFactory} from "./ReportCriteriaDtoFactory";
import * as dto from "ecco-dto";
import ReportStageDto = dto.ReportStageDto;
import SelectionCriteriaDto = dto.SelectionCriteriaDto;
import SeriesDefinitionDto = dto.SeriesDefinitionDto;
import domain = require("./chart-domain");
import {EccoDate} from "@eccosolutions/ecco-common";
import {
    ReferralStatusLookupById,
    ReferralStatus,
    ReferralStatusHandler
} from "./referralStatusHandler";
import {SequenceAnalysis} from "./analysis/types";
import {ReportCapabilityLookup} from "./ReportCapabilityLookup";
import {ReportCriteriaDto} from "ecco-dto";

export interface DataSource<T> {
    getData(): Promise<SequenceAnalysis<T>>;
}

export enum ReportEntity {
    Referral = "Referral",
    SupportWork = "SupportWork",
    ActivityAttendance = "ActivityAttendance",
    Questionnaire = "Questionnaire",
    ServiceRecipient = "ServiceRecipient",
    GroupedWorkAnalysis = "GroupedWorkAnalysis",
    ReferralsByMonth = "ReferralsByMonth",
    Client = "Client",
    Agency = "Agency",
    ServiceRecipientCommand = "ServiceRecipientCommand",
    QuestionnaireMultiSnapshot = "QuestionnaireMultiSnapshot",
    TaskStatus = "TaskStatus",
    RiskWork = "RiskWork",
    ReferralSummary = "ReferralSummary",
    User = "User",
    Review = "Review",
    RiskFlags = "RiskFlags",
    CustomFormSnapshot = "CustomFormSnapshot",
    ReferralFull = "ReferralFull",
    SmartStepMultiSnapshot = "SmartStepMultiSnapshot",
    RotaDemand = "RotaDemand",
    RotaAgreement = "RotaAgreement",
    RotaSchedule = "RotaSchedule",
    ServiceType = "ServiceType",
    AddressHistory = "AddressHistory",
    ReferralsByService = "ReferralsByService",
    TasksByMonth = "TasksByMonth",
    SmartStepSingleSnapshot = "SmartStepSingleSnapshot",
    SupportWorkSnapshot = "SupportWorkSnapshot",
    SupportRiskWork = "SupportRiskWork",
    Professional = "Professional",
    RiskWorkSnapshot = "RiskWorkSnapshot",
    Building = "Building",
    ReferralsBySource = "ReferralsBySource",
    ReferralsByEthnicity = "ReferralsByEthnicity",
    ReferralsBySexualOrientation = "ReferralsBySexualOrientation",
    ReferralsByDisability = "ReferralsByDisability",
    QuestionnaireSnapshot = "QuestionnaireSnapshot", // equivalent to QuestionnaireMultiSnapshot
    QuestionnaireSingleSnapshot = "QuestionnaireSingleSnapshot",
    AnswersByQuestion = "AnswersByQuestion",
    AssociatedContact = "AssociatedContact",
    RiskRatings = "RiskRatings",
    FinanceCharge = "FinanceCharge",
    FinanceReceipt = "FinanceReceipt",
    SupportFlags = "SupportFlags",
    EventResource = "EventResource",
    CustomFormWork = "CustomFormWork",
    Repair = "Repair",
    Occupancy = "Occupancy"
}

export class SeriesDefinition {
    constructor(private data: SeriesDefinitionDto) {
        if (data.renderMode && SeriesType[`${this.data.renderMode}` as SeriesType] == null) {
            throw new Error("Invalid seriesType: " + data.renderMode);
        }
    }

    public getSeriesType(): SeriesType {
        return SeriesType[`${this.data.renderMode}` as SeriesType];
    }
}

/**
 * The 'selectionCriteria' defined at the top of a report definition file
 * gets instantly transformed into this domain object when a ChartDefinition is created
 * See reportDataSourceFactory.ts getDataSource and ChartControl.ts
 */
export class SelectionCriteria {
    private selectionCriteria: dto.SelectionCriteriaDto;

    constructor(selectionCriteria: dto.SelectionCriteriaDto) {
        let clone = SelectionCriteria.cloneDto(selectionCriteria);
        clone = SelectionCriteria.processLegacySelectionCriteria(clone);
        clone = SelectionCriteria.applyDefaultCriteria(clone);
        clone = SelectionCriteria.ensureValidSelectionCriteria(clone);
        this.selectionCriteria = clone;
    }

    public clone(): SelectionCriteria {
        return new SelectionCriteria(this.selectionCriteria);
    }
    private static cloneDto(selectionCriteria: dto.SelectionCriteriaDto): dto.SelectionCriteriaDto {
        return JSON.parse(JSON.stringify(selectionCriteria));
    }

    /** /referral/ -> use status to determine how dates are used to filter
     *  /supportWork/ -> filter on date of support work
     *  groupActivityDate -> all referrals, but filter groupActivities to date range
     */
    //public getSelectionPropertyPath() { return this.dto.definition.selectionCriteria.selectionPropertyPath; }

    //public getHactSessionData() { return this.dto.definition.selectionCriteria.hactSessionData; }

    public getDto() {
        return this.selectionCriteria;
    }

    /** Get the derived report criteria suitable for submitting with a data query or using for filtering */
    public getReportCriteriaDto(): ReportCriteriaDto {
        return ReportCriteriaDtoFactory.getReportCriteriaDto(this);
    }

    public getReportCapability() {
        return ReportCapabilityLookup.getInstance().getReportCapability(
            this.getDto().selectionRootEntity
        );
    }

    /** Get the allowable editable properties of the report */
    public getReportCriteriaAllowableBuilder() {
        const capability = this.getReportCapability().allowableCriteria(this);
        if (this.clone().selectionCriteria.referralStatus == "allNoDates") {
            capability.withFrom(false).withTo(false).withReferralStatus(false);
        }
        return capability;
    }

    public describeCriteria(sessionData: SessionData) {
        // describes from report criteria
        let currentReportCriteria = this.getReportCriteriaDto();
        let currentSelectionCriteria = this;

        if (!currentReportCriteria) {
            return "";
        }

        const descs: string[] = [];

        // lookup the ReportCapability
        let reportCapability = this.getReportCapability();
        // Describe the entity (and property if this is not already covered in the referral status).
        // We don't specify the 'what on' in the ReportCriteriaDto because
        // its implicit in where the webapi is directed but we still want to
        // specify to the user what the report is operating on.
        if (!reportCapability) {
            descs.push("on <unknown>");
        } else {
            descs.push(
                "on ".concat(
                    reportCapability.describeEntityAndPath(
                        currentReportCriteria,
                        currentSelectionCriteria
                    )
                )
            );
        }

        if (currentReportCriteria.serviceId) {
            const name = sessionData.getServiceName(currentReportCriteria.serviceId);
            descs.push("service: " + name);
        }
        if (currentReportCriteria.projectId) {
            const name = sessionData.getProjectName(currentReportCriteria.projectId);
            descs.push("project: " + name);
        }
        if (currentReportCriteria.geographicAreaIdSelected) {
            const isWithin = currentReportCriteria.geographicAreaIds
                ? currentReportCriteria.geographicAreaIds.length > 0
                : false;
            const geoArea = sessionData
                .getListDefinitionEntryById(currentReportCriteria.geographicAreaIdSelected)
                .getFullName();
            if (isWithin) {
                descs.push("within geo area: " + geoArea);
            } else {
                descs.push("geo area: " + geoArea);
            }
        }
        if (currentReportCriteria.userId) {
            descs.push("for userId: " + currentReportCriteria.userId);
        }
        if (currentReportCriteria.from) {
            descs.push("from: " + currentReportCriteria.from);
        }
        if (currentReportCriteria.to) {
            let asAtOrToLabel = currentReportCriteria.from ? "to: " : "at: ";
            descs.push(asAtOrToLabel.concat(currentReportCriteria.to));
        }
        return descs.length > 0 ? descs.join(", ") : "all";
    }

    /** fetches the invited/attending/attended etc info */
    public fetchActivityInvolvement() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("activityInvolvement") >= 0;
    }

    /** NOTE: Fetches the activity interest according to the master referral */
    public fetchActivityInterest() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("activityInterest") >= 0;
    }

    public fetchClientDetail() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("client") >= 0;
    }

    /** Switches from fetching ReferralSummary to fetching Referral */
    public fetchReferralFull() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("referralFull") >= 0;
    }

    public fetchServiceRecipient() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("serviceRecipient") >= 0;
    }

    public fetchReferral() {
        return (
            this.selectionCriteria.fetchRelatedEntities.indexOf("referral") >= 0 &&
            this.selectionCriteria.fetchRelatedEntities.indexOf("referralFull") < 0
        );
    }

    public fetchStaff() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("staff") >= 0;
    }

    public fetchReferralEvents() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("referralEvents") >= 0;
    }

    public fetchScheduleAptsConfirmedAtEnd() {
        return (
            this.selectionCriteria.fetchRelatedEntities.indexOf("scheduleAptsConfirmedAtEnd") >= 0
        );
    }

    public fetchRiskWork() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("riskWork") >= 0;
    }

    public fetchSingleValueHistory() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("singleValueHistory") >= 0;
    }

    public fetchSingleValueQuestionnaireHistory() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("singleValueQnrHistory") >= 0;
    }

    public fetchTaskAudit() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("taskAudit") >= 0;
    }

    public fetchFinanceReceipts() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("receipts") >= 0;
    }

    public fetchSupportWork() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("supportWork") >= 0;
    }

    public fetchSupportRiskWork() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("supportRiskWork") >= 0;
    }

    public fetchSupportWorkEvents() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("supportWorkEvents") >= 0;
    }

    public fetchQuestionnaireWork() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("questionnaireWork") >= 0;
    }

    public fetchQuestionnaireSnapshotAtEnd() {
        return (
            this.selectionCriteria.fetchRelatedEntities.indexOf("questionnaireSnapshotAtEnd") >= 0
        );
    }

    // NB LATEST work only
    public fetchCustomFormWork() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("customFormWork") >= 0;
    }

    public fetchServiceRecipientCommandAtEnd() {
        return (
            this.selectionCriteria.fetchRelatedEntities.indexOf(
                "serviceRecipientAuditsSnapshotAtEnd"
            ) >= 0
        );
    }

    /** returns the relationships which is 'primary' terminology in the system */
    public fetchRelatedReferrals() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("relatedReferrals") >= 0;
    }

    public fetchAclPermissions() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("aclPermissions") >= 0;
    }

    public fetchChildRecipientIds() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("childRecipientIds") >= 0;
    }

    public fetchAssociatedContacts() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("associatedContacts") >= 0;
    }

    public fetchFinanceCharges() {
        return this.selectionCriteria.fetchRelatedEntities.indexOf("financeCharges") >= 0;
    }

    public fetchHactSessionData() {
        return (
            this.selectionCriteria.hactSessionData != null && this.selectionCriteria.hactSessionData
        );
    }

    /** Test if we should show next/prev */
    public isRelativeRangeBased(): boolean {
        return this.selectionCriteria.relativeStartIndex != null;
    }

    public nextInRange() {
        this.selectionCriteria.relativeStartIndex!++;
        this.selectionCriteria.relativeEndIndex!++;
        return this;
    }

    public prevInRange() {
        this.selectionCriteria.relativeStartIndex!--;
        this.selectionCriteria.relativeEndIndex!--;
        return this;
    }

    /** Search for all that have one of geoAreaIds as Referral.srcGeographicArea */
    public setGeoAreaIds(geoAreaIdSelected?: number, geoAreaIds?: Array<number>) {
        this.selectionCriteria.geographicAreaIdSelected = geoAreaIdSelected;
        this.selectionCriteria.geographicAreaIds = geoAreaIds;
    }

    public setReferralStatus(statusKey: string | undefined, limitToReceived: boolean) {
        this.selectionCriteria.referralStatus = statusKey;
        this.selectionCriteria.newReferralsOnly = limitToReceived;
    }

    public setEntityStatus(statusKey: string | undefined) {
        this.selectionCriteria.entityStatus = statusKey;
    }

    public setServiceProject(serviceId: number, projectId: number) {
        this.selectionCriteria.serviceId = serviceId;
        this.selectionCriteria.projectId = projectId;
    }

    public setUserId(userId: number) {
        this.selectionCriteria.userId = userId;
    }

    public clearFromDate() {
        this.selectionCriteria.absoluteFromDate = undefined;
    }

    public setDateRange(from: EccoDate, to: EccoDate) {
        this.selectionCriteria.absoluteFromDate = from ? from.formatIso8601() : undefined; // from can be optional in 'as at' dates
        this.selectionCriteria.absoluteToDate = to && to.formatIso8601();
        // once we set absolute dates, we shouldn't try to use relative ranges
        // set as 'null' so the 'prev' and 'next' don't show - see ChartControl.CriteriaControl if (criterion.isRelativeRangeBased())
        // we need to clear these indexes to allow our absolute dates to get used - see CriteriaFactory.getDateRange
        this.selectionCriteria.relativeStartIndex = undefined;
        this.selectionCriteria.relativeEndIndex = undefined;
    }

    public setDatesOf(fromDto: SelectionCriteriaDto) {
        this.selectionCriteria.selectorType = fromDto.selectorType; // needed for CriteriaFactory to calculate the dates
        this.selectionCriteria.absoluteFromDate = fromDto.absoluteFromDate;
        this.selectionCriteria.absoluteToDate = fromDto.absoluteToDate;
        this.selectionCriteria.relativeStartDate = fromDto.relativeStartDate;
        this.selectionCriteria.relativeStartIndex = fromDto.relativeStartIndex;
        this.selectionCriteria.relativeEndIndex = fromDto.relativeEndIndex;
    }

    public clearDateSelectorType() {
        // also see 'asAt knowledge' below
        this.selectionCriteria.selectorType = undefined;
        this.selectionCriteria.relativeStartIndex = undefined;
        this.selectionCriteria.relativeEndIndex = undefined;
    }

    public setPropertyPath(propertyPath: string | undefined) {
        // TODO: Change with to set (with implies immutable and chainable)
        this.selectionCriteria.selectionPropertyPath = propertyPath;
    }

    /**
     * Get the entity to apply the report criteria to.
     * If we say its a Referral, but really we are a Questionnaire or SupportWork, then manipulate the selectionCriteria
     * to be the correct root report entity, and add a 'referral' to the fetchRelatedEntities.
     */
    private static processLegacySelectionCriteria(
        clone: dto.SelectionCriteriaDto
    ): dto.SelectionCriteriaDto {
        // this stops us seeing defaults in subsequent stages when there is a 'selectionCriteriaSource' (a previousDataSource)
        // its not harmful because the criteria is largely ignored since the previousDataSource is processed for the stage
        // rather than using the selectionCriteria
        if (clone.selectionCriteriaSource) {
            return clone;
        }

        // 'Referral' was the root in the early days even when we actually applied
        // the report to evidence instead. So here we convert the incoming dto to
        // the correct entity and fetch criteria
        let rootEntity = clone.selectionRootEntity;

        // remove the selectionPropertyPath from GroupedWorkAnalysis
        // (this is not the groupBy parameter). Its not obeyed as its hard coded
        // and fails at ReferralPredicates.applyReportCriteria which is called from:
        // ReportController.findGroupedWorkAnalysis
        //          -> applySupportWorkReferralCriteriaModifier -> applyReportCriteria
        if (rootEntity == ReportEntity[ReportEntity.GroupedWorkAnalysis]) {
            let isWorkDate =
                clone.selectionPropertyPath &&
                clone.selectionPropertyPath.match(/workDate/) != null;
            if (isWorkDate) {
                clone.selectionPropertyPath = null;
            }
        }

        // remove the selectionPropertyPath from ReferralsByMonth
        // (this is not the dateField property). Its not obeyed as its hard coded
        // and fails at ReferralPredicates.applyReportCriteria which is called from:
        // ReportController.findReferralsByMonth
        //      -> referralPredicate -> applyReportCriteria
        if (rootEntity == ReportEntity[ReportEntity.ReferralsByMonth]) {
            // which is called from: ReportController.findReferralsByMonth
            //          -> applySupportWorkReferralCriteriaModifier -> applyReportCriteria
            let isReceivedDate =
                clone.selectionPropertyPath &&
                clone.selectionPropertyPath.match(/receivedDate/) != null;
            if (isReceivedDate) {
                clone.selectionPropertyPath = null;
            }
        }

        // NB here we flip any Referral reports that are asking for support/qnr work
        // into the correct report base, which then uses RelatedWorkThenReferralQueryDataSource (because fetchRelatedEntities is referral)
        if (rootEntity == ReportEntity[ReportEntity.Referral]) {
            // correct the root entity and relatedEntities
            // if its really SupportWork with fetching Referral
            let isSupportWorkBased =
                clone.selectionPropertyPath &&
                clone.selectionPropertyPath.match(/supportWork/) != null;
            // if its really QuestionnaireWork with fetching Referral
            let isQuestionnaireWorkBased =
                clone.selectionPropertyPath &&
                clone.selectionPropertyPath.match(/questionnaireWork/) != null;

            if (isSupportWorkBased) {
                clone.selectionRootEntity = ReportEntity[ReportEntity.SupportWork];
                // assume that anything with 'supportWork' as the selection property is legacy
                // since we don't need the rootEntity prefixed (at the moment)
                clone.selectionPropertyPath = null;
            }
            if (isQuestionnaireWorkBased) {
                clone.selectionRootEntity = ReportEntity[ReportEntity.Questionnaire];
                // assume that anything with 'questionnaireWork' as the selection property is legacy
                // since we don't need the rootEntity prefixed (at the moment)
                clone.selectionPropertyPath = null;
            }

            if (isSupportWorkBased || isQuestionnaireWorkBased) {
                // we add 'referral' to the fetch criteria since it was expected that we loaded referrals
                // and we use this in ReportDataSourceFactory to use the legacy data source still in use
                let applyFetchRelatedEntities: RelatedEntitiesKey[] = ["referral"];
                if (clone.fetchRelatedEntities !== undefined) {
                    // NB we retain the original relatedEntities for the legacy data source
                    clone.fetchRelatedEntities =
                        clone.fetchRelatedEntities.concat(applyFetchRelatedEntities);
                } else {
                    clone.fetchRelatedEntities = applyFetchRelatedEntities;
                }
            }
        }

        // Be explicit about default criteria so that it looks and behaves the same as a chosen criteria:
        //   ie - 'referral status received' needs to display and prepopulate the edit form when there is nothing chosen
        //   ie - ReferralPredicate adds a date range to referrals received, but not exactly the same as status received
        // NB this MUST BE AFTER processLegacySelectionCriteria, since it has now processed the rootEntity
        // So, if we are a referral based entity, then set a default referral status
        // assuming we haven't provided a status, or a specific property path
        let capability = ReportCapabilityLookup.getInstance().getReportCapability(
            clone.selectionRootEntity
        );
        if (capability.isReferralBasedReport()) {
            this.transferSelectionPropertyPathToReferralStatus(
                clone,
                ReferralStatus.Exited,
                /referral\.exitedDate/
            );
            this.transferSelectionPropertyPathToReferralStatus(
                clone,
                ReferralStatus.Created,
                /referral\.created/
            );
            this.transferSelectionPropertyPathToReferralStatus(
                clone,
                ReferralStatus.Received,
                /referral\.receivedDate/
            );
            // some further guesses, but easy to change the defn if more cause errors
            this.transferSelectionPropertyPathToReferralStatus(
                clone,
                ReferralStatus.AcceptedService,
                /referral\.decisionMadeOn/
            );
            this.transferSelectionPropertyPathToReferralStatus(
                clone,
                ReferralStatus.Signposted,
                /referral\.decisionMadeOn/
            );
            // default is received with no status and path, so make this explicit (if no newer 'entityStatus' is defined)
            if (!clone.referralStatus && !clone.selectionPropertyPath && !clone.entityStatus) {
                clone.referralStatus = ReferralStatusLookupById[ReferralStatus.Received];
            }
        }

        if (clone.selectionPropertyPath == "date of death") {
            clone.selectionPropertyPath = "dateOfDeath";
        }

        // we want to clean the 'from' date in case we are an 'atEnd' report, but we don't persist 'from' dates
        //if (ReferralReportCapability.referralStatusAtEndDate(clone.referralStatus)) {

        // we don't need 'byReferralStatus...' in our date-based selectorType now we override the logic for atEnd dates in ReportCriteriaDtoFactory
        // but if the user switches between date-based and not, its useful to keep in the report defn - which is only used client-side
        // however with b053929 (partly below) we don't need the selectorType - since the selectorType gets modified to a valid value
        if (ReferralStatusHandler.referralStatusAtEndDate(clone.referralStatus)) {
            // 'asAt' knowledge
            // allow the selectorType and relative indexes, so we have prev/next operating on the 'as at' end date
            // prevent 'prev/next' by uncommenting the below, but we should be consistent and also do 'entityStatus'
            // NB now the ByStatusAtEnd.getDateRange avoids any absolute 'from' date selected (because selectorType was null before eac7d348)
            // (all are required to be uncommented else the /prev/next appears but we can't change them - since the date selectorType is null)
            //clone.selectorType = null;
            //clone.relativeStartIndex = null;
            //clone.relativeEndIndex = null;
            //clone.entityStatus = "liveAtEnd";
        }
        // the 'byReferralStatusWeekly' is now identical client side to 'byStartOfWeekMonday' (and its only a client side property)
        if (clone.selectorType == "byReferralStatusWeekly") {
            clone.selectorType = "byStartOfWeekMonday";
        }

        // we don't need 'byReferralStatus' in our date-based selectorType
        // and in fact, specifying it can cause errors since 79da856 removed it as a date option
        // where it was always used as an 'atEnd' - which is now captured, but our error is caused from
        // 'view activity demand' report which specifies allNoDates. An empty selectorType simply passes through.
        if (clone.selectorType == "byReferralStatus") {
            clone.selectorType = null;
        }

        return clone;
    }

    private static transferSelectionPropertyPathToReferralStatus(
        clone: dto.SelectionCriteriaDto,
        referralStatus: ReferralStatus,
        path: RegExp
    ) {
        // specific property paths are used by the server on referral reports, but not with a referral status
        // so if there is a referral status, take that and ignore/clear the selectionPropertyPath
        // else if the property matches, make sure we set a referral status
        let isPropertyPath =
            clone.selectionPropertyPath && clone.selectionPropertyPath.match(path) != null;
        if (clone.referralStatus) {
            clone.selectionPropertyPath = null;
        } else {
            if (isPropertyPath) {
                clone.referralStatus = ReferralStatusLookupById[referralStatus];
            }
        }
    }

    /**
     * When transitioning from SelectionCriteria to ReportCriteria, we have made some assumptions which we shouldn't.
     * This method is about resolving those oddities. Such oddities occur when running a definition differs to running an
     * an existing edited report. For instance, the hact management report can't be defined with 'liveAtEnd' since there
     * is no dates defined in the SelectionCriteria to pass through to the selectionCriteriaSource, which leaves the hactAnalysis
     * causing a NPE on the 'to' date. But, if we edit an existing report to be 'liveAtEnd' the ChartCriteraForm.tsx updates
     * the date (setDateRange) in the SelectionCriteria to an absolute date of today and all is well. Here we correct that
     * default date assumption.
     */
    private static applyDefaultCriteria(clone: SelectionCriteriaDto): SelectionCriteriaDto {
        if (!SelectionCriteria.hasDatesDefined(clone)) {
            if (clone.entityStatus == "liveAtEnd" || clone.referralStatus == "liveAtEnd") {
                clone.absoluteToDate = EccoDate.todayLocalTime().formatIso8601(); // as per ByStatusAtEnd
            }
        }
        return clone;
    }

    private static hasDatesDefined(dto: SelectionCriteriaDto): boolean {
        return (
            !!dto.selectorType || // needed for CriteriaFactory to calculate the dates
            !!dto.absoluteFromDate ||
            !!dto.absoluteToDate ||
            !!dto.relativeStartDate ||
            !!dto.relativeStartIndex ||
            !!dto.relativeEndIndex
        );
    }

    /**
     * Verify if there is any post-processing to do
     */
    private static ensureValidSelectionCriteria(
        clone: dto.SelectionCriteriaDto
    ): dto.SelectionCriteriaDto {
        // ensure that we have an array, even if its blank
        if (!clone.fetchRelatedEntities) {
            clone.fetchRelatedEntities = [];
        }
        return clone;
    }
}

export interface KeySelector {
    /** returns true if the key is a match for this selector */
    matches(key: string): boolean;
}
/**
 * Array of selected values
 */
export class ArrayKeySelector implements KeySelector {
    constructor(private keys: string[]) {}

    matches(key: string): boolean {
        return this.keys.indexOf(key) >= 0;
    }
    toString() {
        return this.keys.join(", ");
    }
}
/**
 * Cell of selected value
 */
export class TableCellKeySelector implements KeySelector {
    constructor(private columnIndex: number, private rowIndex: number) {}

    matches(key: string): boolean {
        throw new Error("to implement - a match on rowIndex and columnIndex");
        //return (this.rowIndex == rowIndex) && (this.columnIndex == columnIndex);
    }
    toString() {
        return this.rowIndex.toString() + ":" + this.columnIndex.toString();
    }
}
/**
 * All data
 */
export class AllKeySelector implements KeySelector {
    matches(key: string): boolean {
        return true;
    }
    toString() {
        return "all";
    }
}

export class ReportStage {
    protected dto: ReportStageDto;

    /** stageIndex = index within the report definition */
    constructor(
        private chartDefinition: ChartDefinition,
        dto: ReportStageDto,
        private stageIndex: number
    ) {
        this.dto = JSON.parse(JSON.stringify(dto)); // defensive clone as we may allow mutation
    }

    public getStageType(): StageType {
        return StageType[`${this.dto.stageType}` as StageType];
    }

    public getDescription() {
        return this.dto.description;
    }

    /** Get the analyser to apply when a record is selected. If none specified in config, returns "ungroup" as the
     *  default */
    public getSelectionAnalyser(): string {
        return this.dto.selectionAnalyser || "ungroup";
    }

    /** Get the analyser to apply when many records are selected. */
    public getSelectionAnalyserMany(): string | undefined {
        return this.dto.selectionAnalyserMany;
    }

    /** Get the analyser to apply when 'select all' is selected. If none specified in config, returns "ungroup" as the
     *  default */
    public getSelectionAnalyserAll(): string | undefined {
        return this.dto.selectionAnalyserAll;
    }

    /**
     * Get any pre-defined selectionKey defined in the definition
     * (or use all data if 'selectionKey' is present but no value specified)
     */
    public getSelectionKey(): KeySelector | null {
        if (this.dto.selectionKeys == null) {
            return null;
        } else if (this.dto.selectionKeys.length == 0) {
            return new AllKeySelector();
        } else {
            return new ArrayKeySelector(this.dto.selectionKeys);
        }
    }

    public getStageIndex() {
        return this.stageIndex;
    }

    public showSelectAll(): boolean {
        if (this.dto.canSkip != null) {
            return this.dto.canSkip;
        }

        return this.dto.description.match(/by\ project|by\ service|by\ worker$/) != null;
    }

    public canClickThrough(): boolean {
        return this.dto.canClickThrough === null || this.dto.canClickThrough === undefined
            ? true
            : this.dto.canClickThrough;
    }
}

export class AnalyserReportStage extends ReportStage {
    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
    }
    public getAnalyserConfig(): AnalyserConfig | undefined {
        return this.dto.analyserConfig;
    }
    public getAnalyserType() {
        return this.dto.analyserConfig && this.dto.analyserConfig.analyserType;
    }
}

/**
 * configure as:
 * {
 *   "description": "breakdown of referrals",
 *   "stageType": "AUDIT"
 * }
 */
export class AuditReportStage extends ReportStage {
    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
    }
}

/**
 * configure as:
 * {
 *   "description": "show some events...",
 *   "stageType": "CALENDAREVENT"
 * }
 */
export class CalendarEventReportStage extends ReportStage {
    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
    }
}

export class BadgeClickEvent<TDatum> {
    private indexes: number[] = [];
    private selectedDatums: TDatum[] | undefined;

    constructor() {}

    public selectSingleItem(index: number, datum: TDatum) {
        this.indexes = [index];
        this.selectedDatums = [datum];
        return this;
    }

    public getDatums(): TDatum[] | undefined {
        return this.selectedDatums;
    }
}

export class BadgeReportStage extends ReportStage {
    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
    }
    public getIconCssClass() {
        return this.dto.badgeRepresentation?.badgeIconCssClasses;
    }
    /** We (re-)use a TableRepresentation to allow us to specify badge data as a table column as would be the case
     * if we represented our analysis in a single row of a table */
    public getRecordRepresentationName() {
        return this.dto.badgeRepresentation?.recordRepresentationClassName;
    }
    public getBadgeRenderType() {
        return this.dto.badgeRepresentation?.renderType;
    }
    public getMainIndicatorValue() {
        return this.dto.badgeRepresentation?.mainIndicatorValue;
    }
    public getUpIndicatorValue() {
        return this.dto.badgeRepresentation?.upIndicatorValue;
    }
    public getDownIndicatorValue() {
        return this.dto.badgeRepresentation?.downIndicatorValue;
    }
    public getTargetValue() {
        return this.dto.badgeRepresentation?.targetValue;
    }
}

export class ChartReportStage extends ReportStage {
    private seriesDefs: SeriesDefinition[] | undefined;

    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
        this.seriesDefs = dto.seriesDefs && dto.seriesDefs.map(def => new SeriesDefinition(def));
    }

    public getSeriesDefinitions() {
        return this.dto.seriesDefs;
    }
    public isPieChart() {
        return this.dto.seriesDefs
            ? this.dto.seriesDefs[0].renderMode == SeriesType[SeriesType.PIE]
            : false;
    }
}

export class TableReportStage extends ReportStage {
    private seriesDefs: SeriesDefinition[] | undefined;

    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
        this.seriesDefs = dto.seriesDefs && dto.seriesDefs.map(def => new SeriesDefinition(def));
    }

    /**
     * 'className' provides us the columns to choose from in 'columns'
     */
    public getTableRepresentationName() {
        return this.dto.tableRepresentation?.className;
    }
    public getTableColumns() {
        return this.dto.tableRepresentation?.columns;
    }

    // used if we want to override the columns from a fixed definition - eg questionGroupId
    // (mostly used in conjunction with TransposedTransposedQnAnswerWorkAnalysis)
    public getTableDynamicColumnsSources() {
        return this.dto.tableRepresentation?.columnSourceDefIds;
    }

    public getTableRenderType() {
        return this.dto.tableRepresentation?.renderType;
    }
}

export class MatrixReportStage extends ReportStage {
    constructor(chartDefinition: ChartDefinition, dto: ReportStageDto, stageIndex: number) {
        super(chartDefinition, dto, stageIndex);
    }
}

/**
 * Context which each report component has access to for decision making.
 * AnalysisContext starts life in ReportCapability.getDataSource - eg from ReportDataSourceAggregatorDefault
 * which refers to ChartDefinition where we can get most things, report criteria etc.
 *
 * ChartDefinition creates each stage in the constructor, which means that each component
 * doesn't end up knowing its own context - since the current stage is unknown.
 * So, AnalysisContext should probably be ChartContext. However, it's used throughout the code,
 * so we simply extend via AnalysisStageContext and provide that instead. Each component can
 * then choose to cast as AnalysisStageContext if it needs to.
 */
export interface AnalysisContext {
    getSessionData(): SessionData;
    getReportFrom(): EccoDate | null;
    getReportTo(): EccoDate | null;
    hasColumn(name: string): boolean;
    hasColumnStart(startsWith: string): boolean;
    getServiceId(): number | undefined | null;
    getQuestionnaireEvidenceGroups(): string[];
}

export interface AnalysisStageContext extends AnalysisContext {
    getCurrentStage(): ReportStage;
}

// currently AnalysisContext is ChartDefinition, so make explicit to help us see the current flow better
export class ChartDefinition implements AnalysisContext {
    private selectionCriteria: SelectionCriteria[] = [];
    private reportStages: ReportStage[];

    constructor(private dto: ChartDefinitionDto, private cfg: SessionData) {
        this.reportStages = dto.definition.stages
            .filter(stage => this.stageIsRelevant(stage)) // filter before .map so indexes are continguous
            .map((stage, index) => {
                switch (StageType[`${stage.stageType}` as StageType]) {
                    case StageType.ANALYSER:
                        return new AnalyserReportStage(this, stage, index);
                    case StageType.AUDIT:
                        return new AuditReportStage(this, stage, index);
                    case StageType.CALENDAREVENT:
                        return new CalendarEventReportStage(this, stage, index);
                    case StageType.BADGE:
                        return new BadgeReportStage(this, stage, index);
                    case StageType.CHART:
                        return new ChartReportStage(this, stage, index);
                    case StageType.TABLE:
                        return new TableReportStage(this, stage, index);
                    case StageType.MATRIX:
                        return new MatrixReportStage(this, stage, index);
                    default:
                        throw new Error("unknown representation: " + stage.stageType);
                }
            });
        let selectionCriteria = dto.definition.selectionMultiCriteria
            ? dto.definition.selectionMultiCriteria
            : [dto.definition.selectionCriteria];
        selectionCriteria.forEach(criterion => {
            if (criterion) {
                let selectionCriteria = new domain.SelectionCriteria(criterion);
                this.selectionCriteria.push(selectionCriteria);
            }
        });
    }

    public getSessionData() {
        return this.cfg;
    }
    public getReportFrom() {
        return EccoDate.parseIso8601(this.selectionCriteria[0].getReportCriteriaDto().from);
    }
    public getReportTo() {
        return EccoDate.parseIso8601(this.selectionCriteria[0].getReportCriteriaDto().to);
    }
    public hasColumn(name: string): boolean {
        return (
            this.dto.definition.stages
                .filter(s => !!s.tableRepresentation)
                .map(stage => stage.tableRepresentation!.columns)
                .reduce((r, x) => r.concat(x), []) // flatMap
                .filter(column => column == name).length > 0
        );
    }
    public hasColumnStart(startWith: string): boolean {
        return (
            this.dto.definition.stages
                .filter(s => !!s.tableRepresentation)
                .map(stage => stage.tableRepresentation!.columns)
                .reduce((r, x) => r.concat(x), []) // flatMap
                .filter(column => column.toString().indexOf(startWith) > -1).length > 0
        );
    }
    public getServiceId(): number | undefined | null {
        return this.selectionCriteria[0].getDto().serviceId;
    }
    getQuestionnaireEvidenceGroups(): string[] {
        const qg = this.selectionCriteria[0].getDto().questionnaireEvidenceGroup;
        return qg instanceof Array ? qg : [qg!];
    }

    private stageIsRelevant(stage: ReportStageDto): boolean {
        // For projects we exclude if there are no projects defined
        if (
            stage.activeFor == "project" ||
            (stage.analyserConfig && stage.analyserConfig.analyserType.indexOf("ByProject") >= 0) ||
            stage.description.indexOf("by project") >= 0
        ) {
            return this.cfg.hasProjects();
        }
        return true;
    }

    public clone(): ChartDefinition {
        return new ChartDefinition(this.cloneDto(), this.getSessionData());
    }

    private cloneDto(): ChartDefinitionDto {
        return JSON.parse(JSON.stringify(this.dto));
    }

    public getName() {
        return this.dto.name;
    }
    public getFriendlyName() {
        return this.dto.friendlyName;
    }
    public getDescription() {
        return this.dto.definition.description;
    }

    public getSelectionCriteria(index: number) {
        return this.selectionCriteria[index];
    }
    public getAllSelectionCriteria() {
        return this.selectionCriteria;
    }
    public setSelectionCriteria(criterion: SelectionCriteria, index: number) {
        return (this.selectionCriteria[index] = criterion);
    }

    public getReportStage(stageIndex: number) {
        return this.reportStages[stageIndex];
    }

    public withCriteria(criteria: domain.SelectionCriteria[]): ChartDefinition {
        const clone = this.cloneDto();
        clone.definition.selectionCriteria = null;
        clone.definition.selectionMultiCriteria = [];
        criteria.forEach((criterion, index) => {
            clone.definition.selectionMultiCriteria![index] = criterion.getDto();
        });
        return new ChartDefinition(clone, this.cfg);
    }
}
