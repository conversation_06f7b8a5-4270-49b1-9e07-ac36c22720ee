import {ReportEntity} from "./chart-domain";
import {
    ReportEntityCapability,
    ReferralReportCapability,
    SupportWorkReportCapability,
    GroupedWorkReportCapability,
    QuestionnaireReportCapability,
    ActivityAttendanceReportCapability,
    ServiceRecipientCommandReportCapability,
    AgencyReportCapability,
    RotaDamandReportCapability,
    ClientReportCapability,
    ReferralsByMonthReportCapability,
    TasksByMonthReportCapability,
    TaskStatusReportCapability,
    RiskWorkReportCapability,
    RiskFlagsReportCapability,
    ReferralSummaryReportCapability,
    UserReportCapability,
    ReviewReportCapability,
    CustomFormSnapshotLatestReportCapability,
    ReferralFullReportCapability,
    SmartStepMultiSnapshotReportCapability,
    SmartStepSingleSnapshotReportCapability,
    RotaAgreementReportCapability,
    RotaScheduleReportCapability,
    ServiceTypeReportCapability,
    AddressHistoryReportCapability,
    ReferralsGroupedByServiceReportCapability,
    ReferralsGroupedBySourceReportCapability,
    AnswersGroupedByQuestionReportCapability,
    SupportWorkSnapshotReportCapability,
    RiskWorkSnapshotReportCapability,
    SupportRiskWorkReportCapability,
    ProfessionalReportCapability,
    BuildingReportCapability,
    ReferralsGroupedByEthnicityReportCapability,
    ReferralsGroupedBySexualOrientationReportCapability,
    ReferralsGroupedByDisabilityReportCapability,
    QuestionnaireMultiSnapshotReportCapability,
    QuestionnaireSingleSnapshotReportCapability,
    QuestionnaireLegacyMultiSnapshotReportCapability,
    AssociatedContactsReportCapability,
    RiskRatingsReportCapability,
    FinanceChargeReportCapability,
    FinanceReceiptReportCapability,
    SupportFlagsReportCapability,
    EventResourceReportCapability,
    RepairReportCapability,
    OccupancyReportCapability
} from "./reportCapabilities";

export class ReportCapabilityLookup {
    private static _instance = new ReportCapabilityLookup();

    private reportCapabilitiesRepository: ReportEntityCapability[] = [];
    constructor() {
        if (ReportCapabilityLookup._instance) {
            throw new Error("Error: Instantiation failed: Use getInstance() instead of new.");
        }
        this.reportCapabilitiesRepository.push(new ReferralFullReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralSummaryReportCapability());
        this.reportCapabilitiesRepository.push(new SupportWorkReportCapability());
        this.reportCapabilitiesRepository.push(new RiskWorkReportCapability());
        this.reportCapabilitiesRepository.push(new SupportRiskWorkReportCapability());
        this.reportCapabilitiesRepository.push(new SupportFlagsReportCapability());
        this.reportCapabilitiesRepository.push(new RiskFlagsReportCapability());
        this.reportCapabilitiesRepository.push(new RiskRatingsReportCapability());
        this.reportCapabilitiesRepository.push(new GroupedWorkReportCapability());
        this.reportCapabilitiesRepository.push(new QuestionnaireReportCapability());
        this.reportCapabilitiesRepository.push(
            new QuestionnaireLegacyMultiSnapshotReportCapability()
        );
        this.reportCapabilitiesRepository.push(new QuestionnaireMultiSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new QuestionnaireSingleSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new SmartStepMultiSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new SmartStepSingleSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new ActivityAttendanceReportCapability());
        this.reportCapabilitiesRepository.push(new ServiceRecipientCommandReportCapability());
        this.reportCapabilitiesRepository.push(new AgencyReportCapability());
        this.reportCapabilitiesRepository.push(new ProfessionalReportCapability());
        this.reportCapabilitiesRepository.push(new AssociatedContactsReportCapability());
        this.reportCapabilitiesRepository.push(new RotaAgreementReportCapability());
        this.reportCapabilitiesRepository.push(new RotaDamandReportCapability());
        this.reportCapabilitiesRepository.push(new RotaScheduleReportCapability());
        this.reportCapabilitiesRepository.push(new EventResourceReportCapability());
        this.reportCapabilitiesRepository.push(new ClientReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralsByMonthReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralsGroupedByServiceReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralsGroupedBySourceReportCapability());
        this.reportCapabilitiesRepository.push(new ReferralsGroupedByEthnicityReportCapability());
        this.reportCapabilitiesRepository.push(
            new ReferralsGroupedBySexualOrientationReportCapability()
        );
        this.reportCapabilitiesRepository.push(new ReferralsGroupedByDisabilityReportCapability());
        this.reportCapabilitiesRepository.push(new AnswersGroupedByQuestionReportCapability());
        this.reportCapabilitiesRepository.push(new TasksByMonthReportCapability());
        this.reportCapabilitiesRepository.push(new TaskStatusReportCapability());
        this.reportCapabilitiesRepository.push(new UserReportCapability());
        this.reportCapabilitiesRepository.push(new ReviewReportCapability());
        this.reportCapabilitiesRepository.push(new CustomFormSnapshotLatestReportCapability());
        this.reportCapabilitiesRepository.push(new ServiceTypeReportCapability());
        this.reportCapabilitiesRepository.push(new AddressHistoryReportCapability());
        this.reportCapabilitiesRepository.push(new FinanceChargeReportCapability());
        this.reportCapabilitiesRepository.push(new FinanceReceiptReportCapability());
        this.reportCapabilitiesRepository.push(new OccupancyReportCapability());
        this.reportCapabilitiesRepository.push(new SupportWorkSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new RiskWorkSnapshotReportCapability());
        this.reportCapabilitiesRepository.push(new BuildingReportCapability());
        this.reportCapabilitiesRepository.push(new RepairReportCapability());
        ReportCapabilityLookup._instance = this;
    }

    public static getInstance(): ReportCapabilityLookup {
        return ReportCapabilityLookup._instance;
    }

    getReportCapability(reportEntity: string): ReportEntityCapability {
        return this.reportCapabilitiesRepository
            .filter(r => ReportEntity[r.getReportEntity()] == reportEntity)
            .pop()!;
    }
    getReportEntities(): ReportEntity[] {
        return this.reportCapabilitiesRepository.map(repCap => repCap.getReportEntity());
    }
}
