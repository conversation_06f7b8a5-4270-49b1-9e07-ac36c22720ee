import {default as MomentUtils} from "@date-io/moment";
import {EccoDate} from "@eccosolutions/ecco-common";
import {DatePicker as MuiDatePicker, KeyboardDatePicker, MuiPickersUtilsProvider} from "@material-ui/pickers";
import * as moment from "moment";
import * as React from "react";
import {ReactNode, useState} from "react";
import * as ReactDOM from "react-dom";
import {Button, createStyles, makeStyles, Theme} from "@material-ui/core";

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            margin: 0 // theme.spacing(),
        },
        navDate: {
            width: 92
        },
        navButton: {
            minWidth: 32
        }
    })
);

/** Catch error with partial date when entering dates on keyboard */
export function eccoDateToIso(date?: EccoDate | null | undefined) {
    return date ? date.formatIso8601CatchingInvalidAsNull() : null;
}

type Variant = "standard" | "filled" | "outlined";

interface DatePickerEccoDateProps {
    id?: string | undefined;
    className?: string | undefined;
    name?: string | undefined;
    label?: ReactNode | undefined;
    shrinkLabel?: boolean | undefined;
    format?: string | undefined;
    minDate?: EccoDate | null | undefined;
    maxDate?: EccoDate | null | undefined;
    onChange: (value: EccoDate | null) => void;
    value: EccoDate | null;
    disabled?: boolean | undefined;
    required?: boolean | undefined;
    variant?: Variant | undefined;
    size?: "small" | "medium" | undefined;
    noKeyboard?: boolean | undefined; // true to force date picker
    open?: boolean | undefined;
    showTodayButton?: boolean | undefined;
    error?: boolean | undefined;
    helperText?: string | undefined;
}


export function eccoDateAsElement(props: DatePickerEccoDateProps): Element {
    const WithState = () => {
        const [date, setDate] = useState(props.value);
        return <DatePickerEccoDate
            {...props}
            size="small"
            noKeyboard={true}
            variant="outlined"
            value={date}
            onChange={date => {
                setDate(date);
                props.onChange(date);
            }
        }/>;
    }

    const element = document.createElement("span");
    ReactDOM.render(<WithState/>, element);
    return element;
}

export function DatePickerEccoDate(props: DatePickerEccoDateProps) {
    return (
        <DatePickerMoment
            {...props}
            // returns null if not a valid date
            value={eccoDateToIso(props.value)}
            minDate={props.minDate ? props.minDate.formatIso8601CatchingInvalidAsNull() : undefined}
            maxDate={props.maxDate ? props.maxDate.formatIso8601CatchingInvalidAsNull() : undefined}
            onChange={m => props.onChange(m ? EccoDate.fromMoment(m) : null)}
        />
    );
}

export function DatePicker(props: {
    className?: string | undefined;
    name?: string | undefined;
    label: ReactNode;
    shrinkLabel?: boolean | undefined;
    format?: string | undefined;
    minDate?: string | undefined;
    maxDate?: string | undefined;
    onChange: (value: string | null) => void;
    value: string | null;
    disabled?: boolean | undefined;
    required?: boolean | undefined;
    variant?: Variant | undefined;
    size?: "small" | "medium" | undefined;
    noKeyboard?: boolean | undefined; // true to force date picker
    open?: boolean | undefined;
    showTodayButton?: boolean | undefined;
    error?: boolean | undefined;
    helperText?: string | undefined;
}) {
    return (
        <DatePickerMoment {...props} onChange={m => props.onChange(m ? m.toISOString() : null)} />
    );
}

/**
 * This should become internal only to anti-corrupt the spread of Moment which is useful internally for formatting
 * @deprecated
 */
export function DatePickerMoment(props: {
    id?: string | undefined;
    className?: string | undefined;
    name?: string | undefined;
    label?: ReactNode | undefined;
    shrinkLabel?: boolean | undefined;
    format?: string | undefined;
    minDate?: moment.Moment | string | null | undefined;
    maxDate?: moment.Moment | string | null | undefined;
    onChange: (value: moment.Moment | null) => void;
    value: moment.Moment | string | null;
    disabled?: boolean | undefined;
    required?: boolean | undefined;
    variant?: Variant | undefined;
    size?: "small" | "medium" | undefined;
    noKeyboard?: boolean | undefined; // true to force date picker
    open?: boolean | undefined;
    showTodayButton?: boolean | undefined;
    error?: boolean | undefined;
    helperText?: string | undefined;
}) {
    const classes = useStyles();
    const Picker = props.noKeyboard ? MuiDatePicker : KeyboardDatePicker;
    const addedProps = props.noKeyboard
        ? {}
        : {KeyboardButtonProps: {color: "primary" as "primary"}};

    // NB 'red box' validation is expected to be provided via the 'error' property - see calcValidateionState

    return (
        <MuiPickersUtilsProvider utils={MomentUtils}>
            <Picker
                autoOk={true}
                showTodayButton={props.showTodayButton}
                clearable={true}
                className={props.className ?? classes.root}
                format={props.format || "DD/MM/YYYY"}
                id={"date-picker-inline-" + props.name}
                label={props.label}
                InputLabelProps={{shrink: props.shrinkLabel}}
                name={props.name}
                value={props.value}
                minDate={props.minDate}
                maxDate={props.maxDate}
                onChange={(value: moment.Moment | null, valueStr?: string | null | undefined) => {
                    props.onChange(value && value.isValid() ? value : null);
                }}
                disabled={props.disabled}
                required={props.required}
                inputVariant={props.variant}
                size={props.size}
                error={props.error}
                helperText={props.helperText}
                {...addedProps}
            />
        </MuiPickersUtilsProvider>
    );
}

export function DateNavEccoDate(props: {
    date: EccoDate | null;
    name?: string | undefined;
    onChange: (date: EccoDate | null) => void;
    week?: boolean | undefined;
}) {
    const step = props.week ? 7 : 1; // Specifying a prop name without any value is the same as week={true}
    const date = props.date || EccoDate.todayLocalTime();
    const onChange = props.onChange;
    const classes = useStyles();
    return (
        <>
            <Button className={classes.navButton} onClick={() => onChange(date.subtractDays(step))}>
                &lt;
            </Button>
            <DatePickerEccoDate
                name={props.name}
                className={classes.navDate}
                value={date}
                onChange={onChange}
                format="D MMM YYYY"
                variant="standard"
                noKeyboard={true}
            />
            <Button className={classes.navButton} onClick={() => onChange(date.addDays(step))}>
                &gt;
            </Button>
        </>
    );
}