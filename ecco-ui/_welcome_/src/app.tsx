import {AsyncSessionData, handleLazy} from "ecco-components";
import * as React from "react";
import * as ReactDOM from "react-dom";
import {BrowserRouter, <PERSON>} from "react-router-dom";
import {initApiClient, sessionDataFn} from "./services"; // Should be pointing at build/offline/router
import {ServicesContextProvider} from "./ServicesContextProvider";
import * as serviceWorker from "./serviceWorker";
import {SettingAppBar, WelcomePage} from "ecco-offline";
import {Route, Switch} from "react-router";
import TestAppBar from "ecco-offline/build/welcome/TestAppBar";
import AdminAppBar from "ecco-offline/build/service-config/AdminAppBar";
import {EccoTheme} from "ecco-components-core";
import {ApiClient} from "ecco-dto";
import {Sr2View} from "ecco-components/service-recipient/Sr2View";
import {useParams} from "react-router";

interface PageProps {
    readonly appPath: string;
}

const Sr2ViewWrapper = () => {
    const {srId} = useParams<{srId: string}>();
    const srIdInt = parseInt(srId);
    return <Sr2View basepath={`/test-sr2/${srId}/`} srId={srIdInt} />;
};

const Page = ({appPath}: PageProps) => {
    console.debug(`BrowserRouter basename=${appPath}`);
    return handleLazy(
        <BrowserRouter basename={appPath}>
            <Switch>
                {/* Pick up routes that are context relative (e.g. buildings) */}
                <Route path="/test">
                    <TestAppBar />
                </Route>
                <Route path="/settings">
                    <SettingAppBar />
                </Route>
                <Route path="/admin">
                    <AdminAppBar />
                </Route>
                <Route exact path={`/test-sr2/`}>
                    <>
                        goto <Link to={`/test-sr2/200008/`}>/test-sr2/200008/</Link> for testdomcare
                        client file
                    </>
                </Route>
                <Route path={"/test-sr2/:srId/"}>
                    <Sr2ViewWrapper />
                </Route>
                <Route path={["/nav/r/welcome", "/nav/w/welcome", "/"]}>
                    <WelcomePage />
                </Route>
            </Switch>
        </BrowserRouter>
    );
};

export interface AppProps {
    readonly appPath: string;
    readonly apiClient: ApiClient;
}

const App = ({appPath, apiClient}: AppProps) => (
    <AsyncSessionData promiseFn={sessionDataFn}>
        <ServicesContextProvider client={apiClient}>
            <EccoTheme prefix="app">
                <Page appPath={appPath} />
            </EccoTheme>
        </ServicesContextProvider>
    </AsyncSessionData>
);

export interface AppOptions {
    readonly appPath: string;
    readonly remoteRoot: string;
}

export function start({appPath, remoteRoot}: AppOptions): void {
    const apiClient = initApiClient(remoteRoot);

    ReactDOM.render(
        <App appPath={appPath} apiClient={apiClient} />,
        document.getElementById("appbar") as HTMLElement
    );

    // If you want your app to work offline and load faster, you can change
    // unregister() to register() below. Note this comes with some pitfalls.
    // Learn more about service workers: https://create-react-app.dev/docs/making-a-progressive-web-app/
    serviceWorker.unregister(); // No offline for this one - it's dev only
}
