<!DOCTYPE html>
<!--suppress HtmlUnknownTarget, JSUnresolvedLibraryURL -->
<html lang="en" class="v3">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="<%= PUBLIC_URL %>favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Auditable management for care and support"
    />
    <link rel="x-ecco-public-url" href="<%= PUBLIC_URL %>" />
    <link rel="apple-touch-icon" href="<%= PUBLIC_URL %>ecco-e-192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="<%= PUBLIC_URL %>manifest.json">
    <link rel="shortcut icon" href="<%= PUBLIC_URL %>favicon.ico">
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
          integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
      <link rel="stylesheet"
            href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css"
            integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu"
            crossorigin="anonymous">
    <link href="<%= PUBLIC_URL %>common.css" rel="stylesheet">
    <link href="<%= PUBLIC_URL %>evidence.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"
            integrity="sha384-rY/jv8mMhqDabXSo+UCggqKtdmBfd3qC2/KvyTDNQ6PcUJXaxK1tMepoQda4g5vB"
            crossorigin="anonymous"></script> <!-- TODO: Remove both of these when we've migrated tabs and pickers -->
    <script src="https://code.jquery.com/ui/1.10.3/jquery-ui.min.js"
            integrity="sha384-NdBrHQkGhjPzZhn0Cev/LAH+zOvmTXmdqQ0tijTFgAEejVFblAdSsen/W5dPlN8v"
            crossorigin="anonymous"></script> <!-- TODO: Remove both of these when we've migrated tabs and pickers -->
    <script src="https://cdn.jsdelivr.net/npm/lazy.js@0.5.1/lazy.min.js"
            integrity="sha384-VY4hQibSrgNe6UaBHvZA7O+KC/iTHxla7ma5vwV7wdoYcWBCv2kOluHyc0QQj3eP"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/urijs@1.19.2/src/URI.min.js"
            integrity="sha384-iQrom+XLiuEnCavfJX2m7jVE5dvkbHluj3pV3/Aav5DZBHwBBljGupbEUcpX0BAn"
            crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!--
      Notice the use of <%= PUBLIC_URL %> in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      // NB external sites from localhost REQUIRE samesite=none, ie those with 365 configured.

      Unlike "/favicon.ico" or "favicon.ico", "<%= PUBLIC_URL %>favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>ecco - Care and Support</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      (function() { // Use a function to prevent vars leaking to global scope
        window.notAvailOffline = {
          default: () => {
            console.error("not available offline")
          }
        }
      }());
    </script>
    <div id="appbar"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
