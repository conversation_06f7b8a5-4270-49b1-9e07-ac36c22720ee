import {FC} from "react";
import * as React from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {CommandQueue, CommandSource} from "ecco-commands";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useCommandSourceStateHolder, usePromise} from "ecco-components";
import {FinanceReceiptCommand} from "./finance-commands";
import {ModalCommandForm, useCommandSourceRegistration} from "ecco-components";
import {dropdownList, numberInput, textArea} from "ecco-components-core";
import {useServicesContext} from "ecco-components";
import {LoadingSpinner} from "ecco-components";
import {CHARGENAME_LISTDEF, listDefToIdName, SessionData} from "ecco-dto";

export interface FinanceReceiptInit extends FinanceReceiptState {
    receiptId: number | null;
    serviceRecipientId: number;
}
export interface FinanceReceiptState {
    typeDefId: number | null;
    amount: number | null;
    received: EccoDate | null;
    description: string | null;
}

export const FinanceReceiptCommandForm: FC<{init: FinanceReceiptInit}> = props => {
    const {init} = props;
    const {stateHolderMemo, stateHolder, dispatchHolder} = useCommandSourceStateHolder(init);
    const {sessionData} = useServicesContext();

    const cmdSrc: CommandSource = {
        emitChangesTo: function (cmdQ: CommandQueue): void {
            const cmd = new FinanceReceiptCommand(
                init.receiptId ? "update" : "add",
                init.serviceRecipientId,
                init.receiptId
            );
            cmd.changeTypeDefId(init.receiptId, stateHolderMemo.state.typeDefId);
            cmd.changeAmount(init.amount, stateHolderMemo.state.amount);
            cmd.changeReceivedDate(init.received, stateHolderMemo.state.received);
            cmd.changeDescription(init.description, stateHolderMemo.state.description);
            cmdQ.addCommand(cmd);
        },
        getErrors(): string[] {
            const errors: string[] = [];
            if (!stateHolderMemo.state?.amount) {
                errors.push("amount is required");
            }
            if (!stateHolderMemo.state?.received) {
                errors.push("received is required");
            }
            return errors;
        }
    };
    useCommandSourceRegistration(cmdSrc);

    return stateHolder ? (
        <FinanceReceipt data={stateHolder} setData={dispatchHolder} sessionData={sessionData} />
    ) : null;
};

export const FinanceReceipt: FC<{
    data: FinanceReceiptState;
    setData: (s: FinanceReceiptState) => void;
    sessionData: SessionData;
}> = props => {
    const {data, setData} = props;

    return (
        <Grid container direction="row" justify="center" alignItems="center">
            <Grid item md={8} sm={10} xs={12}>
                {dropdownList(
                    "type",
                    setData,
                    data,
                    "typeDefId",
                    props.sessionData
                        .getListDefinitionEntriesByListName(CHARGENAME_LISTDEF)
                        .map(ld => listDefToIdName(ld))
                    // {helperText: "status can only be set for events before today"},
                )}
            </Grid>
            <Grid item md={8} sm={10} xs={12}>
                {numberInput(
                    "amount",
                    "amount £",
                    setData,
                    data,
                    false,
                    2,
                    undefined,
                    undefined,
                    true
                )}
            </Grid>
            <Grid item md={8} sm={10} xs={12}>
                <DatePickerEccoDate
                    showTodayButton={true}
                    name="received"
                    label="received"
                    value={data.received}
                    onChange={dte => setData({...data, received: dte})}
                    required={true}
                    error={!data.received}
                />
            </Grid>
            <Grid item md={8} sm={10} xs={12}>
                {textArea("description", "description", setData, data)}
            </Grid>
        </Grid>
    );
};

export function useFinanceReceiptById(receiptId: number | null) {
    const {financeRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () => (receiptId ? financeRepository.findByReceiptId(receiptId) : Promise.resolve(null)),
        [receiptId]
    );
    return {receipt: resolved, error, loading, reload};
}

export const FinanceReceiptModal: FC<{
    serviceRecipientId?: number | undefined;
    receiptId?: number | undefined;
    setShow: (show: boolean) => void;
    afterSave?: (() => void) | undefined;
}> = props => {
    const {receipt, loading} = useFinanceReceiptById(props.receiptId || null);
    if (loading) {
        return <LoadingSpinner />;
    }
    if (!(receipt || props.serviceRecipientId)) {
        throw new Error("developer error: receiptId or srId currently required"); // we could implement 'choosing a client'
    }
    const srId = (receipt?.serviceRecipientId || props.serviceRecipientId)!;
    const init: FinanceReceiptInit = {
        receiptId: props.receiptId || null,
        serviceRecipientId: srId,
        received: EccoDate.parseIso8601(receipt?.receivedDate || null),
        typeDefId: receipt?.typeDefId || null,
        amount: receipt?.amount || null,
        description: receipt?.description || null
    };
    return (
        <ModalCommandForm
            show={true}
            setShow={props.setShow}
            afterSave={props.afterSave}
            title={"receipt"}
            action={"update"}
        >
            <FinanceReceiptCommandForm init={init} />
        </ModalCommandForm>
    );
};
