import {EccoDate} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Mergeable, NumberChangeOptional, StringChangeOptional, UpdateCommandDto} from "ecco-dto";
import {Command} from "ecco-commands";

export interface FinanceReceiptCommandDto extends UpdateCommandDto {
    receiptId: number;
    serviceRecipientId: number;
    typeDefId: NumberChangeOptional;
    amount: NumberChangeOptional;
    /** ISO8601 format yyyy-MM-dd */
    receivedDate: StringChangeOptional;
    description: StringChangeOptional;
}

export class FinanceReceiptCommand extends Command {
    public static discriminator = "finReceipt";

    private amountChange: NumberChangeOptional;
    private typeDefIdChange: NumberChangeOptional;
    private receivedDateChange: StringChangeOptional;
    private descriptionChange: StringChangeOptional;

    constructor(
        private operation: "add" | "update" | "remove",
        private serviceRecipientId: number,
        private receiptId: number | null
    ) {
        super(
            Uuid.randomV4(),
            [],
            `finance/receipts/serviceRecipient/${serviceRecipientId}/command`,
            FinanceReceiptCommand.discriminator
        );
    }

    public override canMerge(_candidate: Mergeable) {
        return false;
    }

    public changeReceivedDate(from: EccoDate | null, to: EccoDate | null) {
        this.receivedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeAmount(from: number | null, to: number | null) {
        this.amountChange = this.asNumberChange(from, to);
        return this;
    }

    public changeTypeDefId(from: number | null, to: number | null) {
        this.typeDefIdChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDescription(from: string | null, to: string | null) {
        this.descriptionChange = this.asStringChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return (
            this.amountChange != null ||
            this.receivedDateChange != null ||
            this.typeDefIdChange != null ||
            this.descriptionChange != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    public getCommandDto(): FinanceReceiptCommandDto {
        return {
            operation: this.operation,
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            receiptId: this.receiptId,
            serviceRecipientId: this.serviceRecipientId,
            typeDefId: this.typeDefIdChange,
            receivedDate: this.receivedDateChange,
            description: this.descriptionChange,
            amount: this.amountChange
        } as FinanceReceiptCommandDto;
    }
}
