import {Configuration} from "webpack";
import esbuildLoaders from "./webpack/loaders/esbuild";
import babelLoader from "./webpack/loaders/babel";
import {devtool} from "./webpack/devtool";
import {Options, resolveBuildOptions, ResolvedOptions} from "./options/options";
import {cssLoaders} from "./webpack/loaders/css";
import {fileLoader} from "./webpack/loaders/file";
import {output} from "./webpack/output";
import {optimization} from "./webpack/optimization";
import {resolve} from "./webpack/resolve";
import {plugins} from "./webpack/plugins/plugins";
import {devServer} from "./webpack/dev-server";
import {WebpackEnv} from "./webpack/env";
import {WebpackArguments} from "./webpack/arguments";
import {entry} from "./webpack/entry";

export type WebpackConfig = (
    webpackEnv: WebpackEnv | undefined,
    args: WebpackArguments
) => Promise<Configuration>;

export function webpackConfig(options: Options): WebpackConfig {
    return async (webpackEnv, args) =>
        webpackConfigFromResolvedOptions(await resolveBuildOptions(options, webpackEnv, args));
}

async function webpackConfigFromResolvedOptions(options: ResolvedOptions): Promise<Configuration> {
    process.env["NODE_ENV"] = options.mode;
    process.env["BABEL_ENV"] = options.mode;

    return {
        mode: options.mode,
        bail: options.mode === "production",
        devtool: devtool(options),
        entry: entry(options),
        output: output(options),
        optimization: optimization(options),
        resolve: resolve(options),
        module: {
            strictExportPresence: true,
            rules: [
                {
                    oneOf: [
                        ...esbuildLoaders(options),
                        babelLoader(options),
                        ...cssLoaders(options),
                        fileLoader(options)
                        // ** STOP ** Are you adding a new loader?
                        // Make sure to add the new loader(s) before fileLoader().
                    ]
                }
            ]
        },
        externals: options.externals,
        plugins: plugins(options),
        ...(options.target === "single-page-app"
            ? {
                  devServer: devServer(options)
              }
            : {}),
        performance: {
            maxEntrypointSize: 512 * 1024,
            maxAssetSize: 1024 * 1024
        }
    };
}
