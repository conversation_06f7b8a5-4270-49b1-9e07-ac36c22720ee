import {ResolvedOptions} from "../../options/options";
import {CleanWebpackPlugin} from "clean-webpack-plugin";
import {Configuration} from "webpack";
import {htmlWebpackPlugin} from "./html";
import {definePlugin} from "./define";
import {miniCssExtractPlugins} from "./mini-css-extract";
import {ignorePlugin} from "./ignore";
import {bundleAnalyzerPlugins} from "./bundle-analyzer";
import {copyPlugin} from "./copy";
import {serviceWorkerPlugins} from "./service-worker";

export function plugins(options: ResolvedOptions): NonNullable<Configuration["plugins"]> {
    return [
        new CleanWebpackPlugin(),
        ...(options.target === "single-page-app" ? [htmlWebpackPlugin(options)] : []),
        definePlugin(options),
        ...(options.target === "single-page-app" ? miniCssExtractPlugins(options) : []),
        ignorePlugin(),
        ...bundleAnalyzerPlugins(options),
        ...(options.target === "single-page-app"
            ? [copyPlugin(options), ...serviceWorkerPlugins(options)]
            : [])
    ];
}
