import {sep} from "node:path";
import {RuleSetRule} from "webpack";
import {ResolvedOptions} from "../../options/options";
import {esbuildTsTransformOptions, esbuildTsxTransformOptions} from "../../esbuild";

export default function esbuildLoaders(options: ResolvedOptions): RuleSetRule[] {
    const loader = require.resolve("esbuild-loader");

    return [
        // Process application JS with esbuild.
        {
            test: /\.[cm]?[tj]s$/,
            include: [`${options.paths.src}${sep}`],
            loader,
            options: esbuildTsTransformOptions,
            type: "javascript/auto"
        },
        // Process application JSX with esbuild.
        {
            test: /\.[cm]?[tj]sx$/,
            include: [`${options.paths.src}${sep}`],
            loader,
            options: esbuildTsxTransformOptions,
            type: "javascript/auto"
        },
        ...(options.target === "single-page-app" &&
        typeof options.serviceWorker === "object" &&
        typeof options.serviceWorker.implementation === "object"
            ? [
                  // Process Service Worker JS with esbuild.
                  {
                      test: /\.[cm]?[tj]s$/,
                      include: [`${options.serviceWorker.implementation.src}${sep}`],
                      loader,
                      options: esbuildTsTransformOptions,
                      type: "javascript/auto"
                  }
              ]
            : [])
    ];
}
