{"private": true, "name": "@eccosolutions/ecco-mui", "version": "0.0.0", "license": "UNLICENSED", "main": "./index.js", "typings": "./types/index.d.ts", "scripts": {"analyse": "webpack --json | webpack-bundle-size-analyzer", "clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && webpack", "lint": "eslint --ext .ts,.tsx .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Nothing to do", "test-sequential": "echo Nothing to do"}, "dependencies": {"@date-io/core": "^1.3.6", "@date-io/moment": "1.3.13", "@eccosolutions/ecco-common": "2.0.0", "@material-ui/core": "^4.11.3", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "~4.0.0-alpha.57", "@material-ui/pickers": "3.2.8", "immutability-helper": "3.0.0", "moment": "2.24.0", "mui-datatables": "3.7.8", "prop-types": "^15.7.2", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "recharts": "^1.8.6"}, "devDependencies": {"@types/mui-datatables": "^3.4.0", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "@types/recharts": "^1.8.9", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^5.3.3", "typescript": "5.8.3", "url-loader": "4.1.1", "webpack": "^5.101.0", "webpack-bundle-size-analyzer": "^3.1.0", "webpack-cli": "^6.0.1"}}