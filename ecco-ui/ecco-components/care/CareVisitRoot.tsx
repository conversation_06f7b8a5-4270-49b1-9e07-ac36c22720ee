import * as React from 'react';
import {FC, useReducer} from "react";
import {CareVisitContext, CareVisitContextProps, CareVisitState} from "./careVisitState";
import {careVisitInitialState, CareVisitReducer, careVisitReducer} from "./careVisitReducer";
import {useServicesContext} from "../ServicesContext";


interface CareVisitContextProviderProps {
    initState?: Partial<CareVisitState> | undefined;
    reducer: CareVisitReducer;
}
/**
 *  Inspired by https://medium.com/simply/state-management-with-react-hooks-and-context-api-at-10-lines-of-code-baf6be8302c
 */
const CareVisitContextProvider: FC<CareVisitContextProviderProps> = ({ reducer, initState, children }) => {
    const apis = useServicesContext();
    // @ts-ignore
    const [state, dispatch] = useReducer<CareVisitReducer>(reducer, {
        ...careVisitInitialState(apis.getCommandRepository()),
        ...initState
    });
    // NB Be aware that the 'value' parameter is '{state, dispatch}' which fixes typescript issues - see https://stackoverflow.com/questions/54577865/react-createcontext-issue-in-typescript
    // This is described (badly) at https://reactjs.org/docs/hooks-faq.html#how-to-avoid-passing-callbacks-down when it says:
    //  "use two different context types — the dispatch context never changes, so components that read it don’t need to rerender unless they also need the application state."
    const value: CareVisitContextProps = {state, dispatch};
    return (
        <CareVisitContext.Provider value={value}>
            {children}
        </CareVisitContext.Provider>
    );
};

// USAGE - MAIN APP
// =====
interface CareVisitRootProps {
    careVisitInitState?: Partial<CareVisitState> | undefined;
}

/** Create state/dispatch root for the provided initial state such that we can const {state, dispatch} = useCareVisitContext() */
export const CareVisitRoot: FC<CareVisitRootProps> = ({careVisitInitState, children }) =>
        <CareVisitContextProvider reducer={careVisitReducer} initState={careVisitInitState}>
            {children}
        </CareVisitContextProvider>;

CareVisitRoot.displayName = "CareVisitRoot";