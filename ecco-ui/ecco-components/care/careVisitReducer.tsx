import * as React from "react";
import {CareTask, CareVisitActionType, CareVisitState} from "./careVisitState";
import {showErrorAsAlert} from "ecco-offline-data";
import {
    EvidenceAssociatedContactCommand,
    BaseUpdateCommand,
    CommandQueue,
    CommandRepository,
    CommentCommand,
    SupportCommentCommand
} from "ecco-commands";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Dispatch} from "react";
import {showNotification} from "ecco-components-core";
import {AttendanceStatus, EvidenceDef, SessionData} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {getTemporaryStartedAt, getTemporaryStoppedAt} from "./CareVisitOverview";


// STATE
// =====
// See careVisitState.tsx for CareVisitProps/CareVisitState, CareTaskState and reducer actions in CareVisitActionType for the CareVisitContextProps

// could be a type instead or we could use Partial, or use ' | null' syntax
//const careItemInitialTask: CareTask = { id: "", taskText: "", taskTime: "", completed: undefined, listDefId: undefined };
//const careItemInitialProps: CareItemProps = { successMethods: [], failureReasons: [], showEditIcon: null };
export const careVisitInitialState: (
    commandRepository: CommandRepository
) => Partial<CareVisitState> = commandRepository => ({
    cmdRepo: commandRepository,
    cmdQueue: new CommandQueue(commandRepository),
    visitDateTime: "",
    displayName: "",
    address: "",
    visitType: "",
    loneWorkerUpdateFailed: false,
    tasks: []
    // tasksLoad: () => Promise.reject(),
    // taskUpdateCmd: () => null!,
    // loneWorkerUpdate: () => Promise.reject()
});
/*
 * alternative styles...
enum ActionType {
    Increment = 'increment',
    Decrement = 'decrement',
}
interface CareVisitAction {
    type: ActionType;
    payload: {
        count: number;
    };
}
*/

function changeTaskOutcome(
    cmdQueue: CommandQueue,
    state: CareVisitState,
    origItem: CareTask,
    newItems: CareTask[],
    action: {
        task: CareTask; // expects to be the new task data - which is state.showTask when 'changeTaskOutcome'
        value: number | null; // new listDefId
        dispatch: Dispatch<CareVisitActionType>;
    }
) {
    // async save - ideally we make the state change and re-attempt etc if cmd failure
    //  - could be like ServiceAgreementsView which uses a version counter and useEffect to reload
    //  - could be like loneWorkerUpdate which triggers onChange then calls dispatch with result (see CareVisitOverview)
    //  - could find other online option that doesn't refer to redux, eg https://stackoverflow.com/questions/53146795/react-usereducer-async-data-fetch
    //  - we go for re-using the dispatch passed in
    cmdQueue.addCommand(state.taskUpdateCmd(action.task, action.value, origItem.taskTime)!!);
    const newItem = {...origItem, listDefId: action.value};
    const i = newItems.findIndex(t => t.taskInstanceId == action.task.taskInstanceId);
    newItems[i] = newItem;
    cmdQueue.size() > 0 &&
        cmdQueue.flushCommands(false).catch(reason => {
            action.dispatch({type: "commandFailed", reason});
        });
}

function stop(
    cmdQueue: CommandQueue,
    state: CareVisitState,
    dispatch: Dispatch<CareVisitActionType>
): CareVisitState {
    const ended = EccoDateTime.nowLocalTime();
    const newState = {
        ...state,
        timerStoppedAt: ended
    };
    state
        .loneWorkerUpdate(newState)
        .then(() => {
            cmdQueue.size() > 0 &&
                cmdQueue.flushCommands(false).catch(reason => {
                    dispatch({type: "commandFailed", reason});
                });
        })
        .catch(() => {
            dispatch({type: "loneWorkerUpdateFailed"});
        });
    return newState;
}

function sendCommand(cmd: BaseUpdateCommand | CommentCommand, message: string): Promise<void> {
    const commandQueue = new CommandQueue();
    commandQueue.addCommand(cmd);
    return commandQueue
        .flushCommands(false)
        .then(() => showNotification("info", message))
        .catch(e => showErrorAsAlert(e));
}

export const ROTA_VISIT = "rotaVisit";

function commentUpdateCmd(
    sessionData: SessionData,
    srId: number,
    workUuid: string,
    state: CareVisitState
): SupportCommentCommand {
    const evidenceDef = EvidenceDef.fromTaskName(sessionData, null, ROTA_VISIT);
    return (
        SupportCommentCommand.create(
            true,
            Uuid.parse(workUuid),
            srId,
            evidenceDef.getEvidenceGroup(),
            ROTA_VISIT
        )
            .changeComment(null, state.commentForm.comment)
            .changeLocationId(null, state.locationId)
            // incomplete 'stopReasonId' saves to the event's eventStatusId
            .changeEventStatusId(null, state.eventStatusId)
            // timerStartedAt comes from the eventStatus snapshot
            // and is transformed to the workDate
            .changeWorkDate(null, state.timerStartedAt?.formatIso8601())
            .plannedStart(state.plannedDateTime ? state.plannedDateTime.formatIso8601() : undefined)
            .plannedMinsSpent(state.plannedMins)
            .changeMinsSpent(
                0,
                state.timerStartedAt && state.timerStoppedAt
                    ? Math.floor(
                          state.timerStoppedAt.subtractDateTime(state.timerStartedAt).inMinutes()
                      )
                    : null
            )
            .build()
    );
}

export function generateLoneWorkerUpdate(
    sessionData: SessionData,
    serviceRecipientId: number,
    eventId: string,
    workUuid: string
) {
    return (state: CareVisitState) => {
        const loneCmd = createLoneWorkerCommand(
            sessionData,
            serviceRecipientId,
            eventId,
            workUuid,
            state
        );
        const msg =
            loneCmd.attendanceStatus == AttendanceStatus.START ? "start saved" : "end saved";
        const loneQ = sendCommand(loneCmd, msg);
        if (loneCmd.attendanceStatus == AttendanceStatus.END) {
            const minsCmd = commentUpdateCmd(sessionData, serviceRecipientId, workUuid, state);
            return loneQ.then(() => sendCommand(minsCmd, "end saved"));
        } else {
            return loneQ;
        }
    };
}

function createLoneWorkerCommand(
    sessionData: SessionData,
    serviceRecipientId: number,
    eventId: string,
    workUuid: string,
    state: CareVisitState
): EvidenceAssociatedContactCommand {
    const start = state.timerStartedAt;
    const stop = state.timerStoppedAt;
    console.info(`start: ${start?.formatDateTimePretty()}`);
    stop && console.info(`stop: ${stop.formatDateTimePretty()}`);

    // create the command - which creates the work item on 'start' - see EvidenceAssociatedContactCommandHandler and LoneWorkingCommandAPITests
    const evidenceDef = EvidenceDef.fromTaskName(sessionData, null, ROTA_VISIT);
    const cmd = new EvidenceAssociatedContactCommand(
        Uuid.parse(workUuid),
        serviceRecipientId,
        // current user's contact - although this is overridden in EACCHandler result.contactId
        sessionData.getDto().individualUserSummary.individualId,
        evidenceDef.getEvidenceGroup().name,
        ROTA_VISIT
    );

    cmd.attendanceStatus = stop ? AttendanceStatus.END : AttendanceStatus.START;
    cmd.eventId = eventId;
    cmd.withLocation(sessionData.getGpsIfEnabled());
    return cmd;
}

export type CareVisitReducer = React.Reducer<CareVisitState, CareVisitActionType>;
export const careVisitReducer: CareVisitReducer = (
    state: CareVisitState,
    action: CareVisitActionType
): CareVisitState => {
    // <-- Error here if you add a type bug don't add to switch(action.type) cases

    console.info("careVisitReducer: %o", action);
    const getItem = (items: CareTask[], instanceId: string) =>
        items.find(t => t.taskInstanceId === instanceId);

    switch (action.type) {
        case "hideVisit":
            return {
                ...state,
                showVisit: false
            };
        case "commandFailed":
            showErrorAsAlert(action.reason);
            // NB some reload action?
            return state;
        case "start": {
            const started = EccoDateTime.nowLocalTime();
            const newState = {
                ...state,
                timerStartedAt: started
            };
            state.loneWorkerUpdate(newState).catch(() => {
                action.dispatch({type: "loneWorkerUpdateFailed"});
            });
            return newState;
        }
        case "stop": {
            // TODO: DEV-1918 set state to take us to comment form while showing timer still active, but save button can be "complete visit"
            return stop(state.cmdQueue, state, action.dispatch);
        }
        case "showStopOptions":
            return {
                ...state,
                showStopOptions: action.show
            };
        case "loneWorkerUpdateFailed":
            return {
                ...state,
                loneWorkerUpdateFailed: true
            };
        case "showVisit":
            // if we are the initial load, load the work and tasks
            if (state.tasks == null) {
                state.workLoad().then(work => {

                    const timerStartedAt =
                        getTemporaryStartedAt(undefined, state.work) || undefined;
                    const timerStoppedAt =
                        getTemporaryStoppedAt(undefined, state.work) || undefined;

                    return state.tasksLoad().then(tasks =>
                        action.dispatch({
                            type: "setState",
                            setter: state => ({
                                ...state,
                                work,
                                tasks,
                                timerStartedAt,
                                timerStoppedAt,
                                locationId: work?.locationId || null,
                                commentForm: {
                                    ...state.commentForm,
                                    comment: work ? work.comment : ""
                                }
                            })
                        })
                    );
                });
            }

            // if we have saved a task, just re-load the tasks

            return {
                ...state,
                showVisit: true
            };

        case "setState": // e.g. finished loading/reloading tasks or work list
            return action.setter(state);
        case "showVisitComment":
            return {
                ...state,
                showCommentForm: action.show
            };
        case "changeVisitComment":
            return {
                ...state,
                commentForm: action.commentForm,
                showCommentForm: false
            };
        case "changeVisitLocation":
            return {
                ...state,
                locationId: action.locationId
            };
        case "changeTaskOutcome":
            const newItems = state.tasks!.slice(0);
            const item = getItem(newItems, action.task.taskInstanceId);
            if (item) {
                changeTaskOutcome(new CommandQueue(state.cmdRepo), state, item, newItems, action);
            }
            return {
                ...state,
                tasks: newItems
            };
        case "stopWithOptions": {
            const newItems = state.tasks!.slice(0);

            // the cmdQueue is added to whilst being submitted so can repeat a flush
            // for now, we keep each cmdQueue separate in this code block
            // but we should be sorting this out - perhaps so that state is only updated
            // on success - eg state.workLoad() has a dispatch of 'setState'

            const incompleteTasks = newItems.filter(t => !t.listDefId);
            incompleteTasks.forEach(item => {
                if (item) {
                    changeTaskOutcome(new CommandQueue(state.cmdRepo), state, item, newItems, {
                        task: item,
                        value: action.stopReasonId,
                        dispatch: action.dispatch
                    });
                }
            });

            const newState = {
                ...state,
                tasks: newItems,
                // incomplete 'stopReasonId' saves to the event's eventStatusId
                eventStatusId: action.stopReasonId,
                showStopOptions: false
            };

            return {
                ...stop(new CommandQueue(state.cmdRepo), newState, action.dispatch)
            };
        }
        case "showClientDetails":
            return {
                ...state,
                showClientDetails: true
            };
        case "hideClientDetails":
            return {
                ...state,
                showClientDetails: false
            };
        case "showClientFile":
            return {
                ...state,
                showClientFile: true
            };
        case "hideClientFile":
            return {
                ...state,
                showClientFile: false
            };
        default:
            return state;
    }
};