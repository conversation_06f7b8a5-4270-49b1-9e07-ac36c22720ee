import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Observable} from "rxjs";

/** A CardData can asynchronously render itself */
export interface CardData {
    getKey(): string;
    getPriority(): EccoDateTime;
}


export function compareCards(a: CardData, b: CardData) {
    return a.getPriority().compare(b.getPriority());
}

export class CardGroup<T extends CardData> implements CardData {

    private static instanceCount = 0;

    /** Combines with instanceCount to ensure that independent instances are able to be disambiguated by using
     *  this.key when components such as React components use a key property.
     */
    public key: string;

    constructor(public title: string, public cards: Observable<T>, private priority?: EccoDateTime) {
        this.key = title + (++CardGroup.instanceCount).toString();
        console.debug(`new CardGroup key=${this.key}`)
    }

    /** Combines with instanceCount to ensure that independent instances are able to be disambiguated by using
     *  this.key when components such as React components use a key property.
     */
    getKey() {
        return this.key
    }

    getPriority() {
        return this.priority || EccoDateTime.nowLocalTime();
    }

    public compareCards(a: CardData, b: CardData) {
        return compareCards(a, b);
    }
}

export interface CardSource {
    getCards(): Observable<CardData>;
}
