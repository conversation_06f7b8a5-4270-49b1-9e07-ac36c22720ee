import {useReducer} from "react";

type Action<T> = {type: "add", item: T} | {type: "reset"}

export const useAppendableArray = <T extends any>(): [T[], (it: T) => void, () => void] => {
    const [state, dispatch] = useReducer(
        (prev: T[], action: Action<T>) => action.type == "add" ? [...prev, action.item] : [],
        []
    );
    const add = (it: T) => dispatch({type: "add", item: it});
    const reset = () => dispatch({type: "reset"});
    return [state, add, reset];
};
