import {useEffect} from "react";
import {HateoasResource, Result} from "@eccosolutions/ecco-common";
import {Notifications} from "ecco-components-core";
import {commandSentResultBus} from "ecco-commands";


function possiblyNotifyResult(result?: HateoasResource | undefined) {
    if (result && (result as Result).message) {
        Notifications.add("result", (result as Result).message);
    }
}

/** Have this component add/remove handler for reload events */
export function useResultNotification() {
    useEffect(() => {
        commandSentResultBus.addHandler(possiblyNotifyResult);
        return () => {
            commandSentResultBus.removeHandler(possiblyNotifyResult);
        }
    }, []);
}
