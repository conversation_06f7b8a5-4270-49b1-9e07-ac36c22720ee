import {mount} from "cypress/react";
import * as React from "react";
import {sessionData} from "../../__tests__/testUtils";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {
    AddressHistoryDto,
    Building,
    BuildingAjaxRepository,
    BuildingRepository,
    ReferralSummaryDto,
    ServiceRecipientRepository,
    ServiceRecipientAjaxRepository,
    ManagedVoidDto,
    OccupancyFilter
} from "ecco-dto";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {OccupancyList} from "../../buildings/OccupancyList";
import {EccoDate} from "@eccosolutions/ecco-common";

describe("OccupancyList tests", () => {
    it("render page", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <OccupancyList applicationRootPath={"context-path"} />
            </TestServicesContextProvider>
        );
        cy.viewport(1250, 750);
    });
});

const buildingRepository = getFailAllMethodsMock<BuildingRepository>(BuildingAjaxRepository);
buildingRepository.findOccupancy = (
    from: EccoDate,
    to: EccoDate,
    filter: OccupancyFilter,
    search: string | null,
    page?: number,
    buildingIds?: number[]
) => Promise.resolve(addressHistoryDtos);

buildingRepository.getCachedBuildingsMap = () => Promise.resolve(buildings.concat(units));

const srRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
srRepository.findManyServiceRecipientByIds = (ids: number[]) =>
    Promise.resolve([
        managedVoid_200905 as any as ManagedVoidDto,
        referral_200299 as any as ReferralSummaryDto,
        referral_200300 as any as ReferralSummaryDto,
        referral_200416 as any as ReferralSummaryDto
    ]);

const overrides = {
    getBuildingRepository: () => buildingRepository,
    serviceRecipientRepository: srRepository,
    sessionData: sessionData
} as EccoAPI;

// from /housing with a mix of our own 'createUnoccupiedEntry'
const addressHistoryDtos: AddressHistoryDto[] = [
    {
        id: 1336,
        serviceRecipientId: 200905,
        validFrom: "2025-08-20T00:00:00",
        validTo: "2025-08-23T00:00:00",
        days: 3,
        buildingId: 1925,
        addressId: 1924
    },
    {
        id: 1335,
        serviceRecipientId: 200299,
        validFrom: "2024-08-20T00:00:00",
        validTo: null,
        days: 330,
        buildingId: 1925,
        addressId: 1924
    },
    {
        id: null,
        serviceRecipientId: null,
        validFrom: "2021-09-01T00:00:00",
        validTo: "2024-08-19T00:00:00",
        days: 1000,
        //"contactId": 111639,
        buildingId: 1925,
        addressId: 1924
    },
    {
        id: 1334,
        serviceRecipientId: 200299,
        validFrom: "2021-09-01T00:00:00",
        validTo: "2024-08-19T00:00:00",
        days: 1000,
        //"contactId": 111639,
        buildingId: 1377,
        addressId: 1330
    },
    {
        id: 1333,
        serviceRecipientId: 200299,
        validFrom: "1970-01-01T00:00:00",
        validTo: "2021-09-01T00:00:00",
        days: 4000,
        //"contactId": 111639,
        buildingId: null,
        addressId: 1224
    }
];

const buildings: Building[] = [
    {
        buildingId: 1001,
        name: "Jolly Place",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200018,
        calendarId: "d379cef8-b8a0-4784-9720-5bd0867a6a8b",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1001&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200018/"
            }
        ]
    },
    {
        buildingId: 1084,
        name: "Butterfly House",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200235,
        calendarId: "29c422ee-8f7c-4cc0-8bd2-9595b6cd44bb",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 1330,
            disabled: false, //
            address: ["21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {
            landlord: "ECCO Homes Group",
            tenancies: "Assured shorthold"
        },
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1084&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200235/"
            }
        ]
    },
    {
        buildingId: 1640,
        name: "Rowbuck Lane",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200396,
        calendarId: "345e2a51-16c6-4a54-b052-125748c437e6",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1640&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200396/"
            }
        ]
    },
    {
        buildingId: 1696,
        name: "Jasper House",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200417,
        calendarId: "8c5958f2-a643-47fd-a7ff-fca9d4dcf86a",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1706,
        address: {
            addressId: 1706,
            disabled: false, //
            address: ["11, The Street", null, null],
            town: null,
            county: null,
            postcode: "WA2 1LU"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1696&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200417/"
            }
        ]
    },
    {
        buildingId: 1711,
        name: "Jasper Court",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200418,
        calendarId: "f039825d-b25c-4684-885c-785fd5ebb29d",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1711&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200418/"
            }
        ]
    },
    {
        buildingId: 1765,
        name: "Hebden Court",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200434,
        calendarId: "ac330ac6-c5a6-4a1e-94ec-9dcee6dc5ef4",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1765&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200434/"
            }
        ]
    },
    {
        buildingId: 1803,
        name: "Bucktree Lane",
        disabled: false,
        externalRef: null,
        resourceTypeId: 132,
        resourceTypeName: "building",
        serviceRecipientId: 200439,
        calendarId: "2a66ed35-7d39-42d4-8e0c-6105bccd9d34",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1803&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200439/"
            }
        ]
    }
];

const units = [
    {
        buildingId: 1377,
        name: "Flat 1",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200312,
        calendarId: "588a8ea5-573a-433c-a528-4b2cf16bf491",
        //disabled: false,
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2001,
            disabled: false, //
            address: ["Flat 1, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1377&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200312/"
            }
        ]
    },
    {
        buildingId: 1430,
        name: "Flat 2",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200336,
        calendarId: "f504090a-4879-4d9f-ad5b-496b07481401",
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2003,
            disabled: false, //
            address: ["Flat 2, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1430&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200336/"
            }
        ]
    },
    {
        buildingId: 1432,
        name: "Flat 3",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200337,
        calendarId: "4a7ca627-6736-4541-8367-70b0508d7a39",
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2004,
            disabled: false, //
            address: ["Flat 3, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1432&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200337/"
            }
        ]
    },
    {
        buildingId: 1434,
        name: "Flat 4",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200338,
        calendarId: "268edc6e-5765-426c-a258-f5638b8b2f7a",
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2005,
            disabled: false, //
            address: ["Flat 4, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1434&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200338/"
            }
        ]
    },
    {
        buildingId: 1436,
        name: "Flat 5",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200339,
        calendarId: "19cbf238-c30f-477b-aa85-d290a25cb2dd",
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2006,
            disabled: false, //
            address: ["Flat 5, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1436&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200339/"
            }
        ]
    },
    {
        buildingId: 1438,
        name: "Staff Office",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200340,
        calendarId: "14754587-f0be-4c87-86fe-64b8158c29db",
        parentId: 1084,
        parentName: "Butterfly House",
        serviceAllocationId: -100,
        locationId: 1330,
        address: {
            addressId: 2000,
            disabled: false, //
            address: ["Staff Office, 21 Hebden Avenue", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK6 1EH"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1438&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200340/"
            }
        ]
    },
    {
        buildingId: 1925,
        name: "JH1",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200472,
        calendarId: "0c96ad22-92a3-4d8e-a00f-a26b93d72d08",
        parentId: 1696,
        parentName: "Jasper House",
        serviceAllocationId: -100,
        locationId: 1924,
        address: {
            addressId: 1924,
            disabled: false, //
            address: ["JH1, 11, The Street", null, null],
            town: null,
            county: null,
            postcode: "WA2 1LU"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1925&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200472/"
            }
        ]
    },
    {
        buildingId: 1928,
        name: "JH2",
        disabled: false,
        externalRef: null,
        resourceTypeId: 134,
        resourceTypeName: "room",
        serviceRecipientId: 200473,
        calendarId: "c2c7e662-cc70-4c64-8725-2bdd8b3902a8",
        parentId: 1696,
        parentName: "Jasper House",
        serviceAllocationId: -100,
        locationId: 1927,
        address: {
            addressId: 1927,
            disabled: false, //
            address: ["JH2, 11, The Street", null, null],
            town: null,
            county: null,
            postcode: "WA2 1LU"
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/workers:all/view?demandFilter=buildings:1928&loadResource=true&loadDemand=true"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/housing/api/rota/agreements/serviceRecipient/200473/"
            }
        ]
    }
];

const referral_200300 = {
    statusMessageKey: "status.incomplete",
    displayName: "Jon Snow",
    contactId: 111645,
    calendarId: "9f042666-5580-4883-b830-0b060cb1027c",
    addressCommaSep: "21 Hebden Avenue, Stockport, SK6 1EH",
    prefix: "r",
    parentId: 296,
    serviceRecipientId: 200300,
    serviceAllocationId: 1302,
    serviceIdAcl: 111550,
    projectIdAcl: 111551,
    _readOnly: false,
    referralId: 296,
    clientId: 270,
    clientCode: "270",
    clientDisplayName: "Jon Snow",
    clientFirstName: "Jon",
    firstName: "Jon",
    clientLastName: "Snow",
    lastName: "Snow",
    appropriateReferralState: "UNSET",
    acceptOnServiceState: "UNSET",
    selfReferral: true,
    receivedDate: "2021-08-01",
    interviewDna: 0,
    daysAttending: 0,
    signpostedBack: false,
    fundingAccepted: false,
    waitingListScore: 0,
    acceptedOnService: false,
    acceptedReferral: false,
    pending: false,
    finalDecision: false,
    referralDecision: false,
    meetingDays: {
        sunday: false,
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false
    },
    signposted: false,
    exited: false
};

const managedVoid_200905 = {
    statusMessageKey: "status.started",
    displayName: "-display-",
    contactId: 111639,
    //calendarId: "d8eee607-5659-44c2-95ec-8f0cef393b70",
    //"addressCommaSep": "21 Hebden Avenue, Stockport, SK6 1EH",
    prefix: "mv",
    parentId: null,
    serviceRecipientId: 200905,
    serviceAllocationId: 1302
};

const referral_200299 = {
    statusMessageKey: "status.started",
    displayName: "Victoria Beckham",
    contactId: 111639,
    calendarId: "d8eee607-5659-44c2-95ec-8f0cef393b70",
    //"addressCommaSep": "21 Hebden Avenue, Stockport, SK6 1EH",
    prefix: "r",
    parentId: 295,
    serviceRecipientId: 200299,
    serviceAllocationId: 1302,
    //"serviceIdAcl": 111550,
    //"projectIdAcl": 111551,
    _readOnly: false,
    referralId: 295,
    clientId: 269,
    clientCode: "269",
    clientDisplayName: "Victoria Beckham",
    //"clientFirstName": "Victoria",
    firstName: "Victoria",
    //"clientLastName": "Beckham",
    lastName: "Beckham",
    referrerAgencyId: 110649,
    referrerIndividualId: 110650,
    appropriateReferralState: "ACCEPTED",
    acceptOnServiceState: "ACCEPTED",
    decisionDate: "2021-08-04T12:00:00.000",
    decisionReferralMadeOn: "2021-08-03",
    decisionMadeOn: "2021-08-05",
    supportWorkerId: 101702,
    selfReferral: false,
    receivedDate: "2021-08-02",
    agreement1AgreementDate: "2021-08-24T23:00:00.000",
    agreement1AgreementStatus: true,
    agreement1SignedId: "302e3c32-adc6-4ed0-ba02-bc9879c04353",
    firstResponseMadeOn: "2021-08-25",
    firstOfferedInterviewDate: "2021-08-03T00:00:00.000",
    interviewDna: 0,
    interviewSetupComments: "Social worker will also attend",
    receivingServiceDate: "2025-02-03",
    daysAttending: 0,
    interviewer1ContactId: 109601,
    interviewer2ContactId: 111384,
    interviewLocation: "Parent's address",
    signpostedBack: false,
    fundingAccepted: false,
    waitingListScore: 0,
    //"acceptedOnService": true,
    //"acceptedReferral": true,
    receivingServiceLocalDate: "2025-02-03",
    pending: false,
    finalDecision: true,
    referralDecision: true,
    meetingDays: {
        sunday: false,
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false
    },
    signposted: false,
    exited: false
};

const referral_200416 = {
    statusMessageKey: "status.incomplete",
    displayName: "Jo Test",
    contactId: 112304,
    calendarId: "bf0ad208-7bf0-4575-9945-30c32455be68",
    //"addressCommaSep": "21 Hebden Avenue, Stockport, SK6 1EH",
    prefix: "r",
    parentId: 363,
    serviceRecipientId: 200416,
    serviceAllocationId: 19,
    //"serviceIdAcl": 106033,
    //"projectIdAcl": 104335,
    _readOnly: false,
    referralId: 363,
    clientId: 284,
    clientCode: "284",
    clientDisplayName: "Jo Test",
    //"clientFirstName": "Jo",
    firstName: "Jo",
    //"clientLastName": "Test",
    lastName: "Test",
    appropriateReferralState: "UNSET",
    acceptOnServiceState: "UNSET",
    selfReferral: false,
    interviewDna: 0,
    daysAttending: 0,
    requestedDelete: true,
    signpostedBack: false,
    fundingAccepted: false,
    waitingListScore: 0,
    //"acceptedOnService": false,
    //"acceptedReferral": false,
    pending: false,
    finalDecision: false,
    referralDecision: false,
    meetingDays: {
        sunday: false,
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false
    },
    signposted: false,
    exited: false
};
