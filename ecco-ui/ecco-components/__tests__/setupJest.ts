// see https://www.swarmonline.com/tips-for-testing-react-with-jest-enzyme/
import {configure} from "enzyme";
import Adapter = require("enzyme-adapter-react-16");

configure({adapter: new Adapter()});

window.alert = data => console.error(data);

window.applicationProperties = {applicationRootPath: "/jest-test/"} as ApplicationProperties;

/** See https://github.com/jestjs/jest/issues/11698#issuecomment-922351139 */
function fail(reason = "fail was called in a test."): never {
    throw new Error(reason);
}
window.fail = fail;