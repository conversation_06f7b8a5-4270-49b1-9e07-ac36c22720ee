import * as React from 'react';
import {shallow, mount} from 'enzyme';
import {DateAndTime} from "../DateAndTime";

jest.mock("bowser", () => {
        // debugger;
        return ({
            firefox: true
        });
    }
);

describe('DateAndTime', () => {
    it('should render correctly', () => {
        const tree = shallow(
            <DateAndTime propertyKey="instant" label="date/time" stateSetter={() => {}} state={{instant: "2018-12-13T12:23:00"}} disabled={false} required={true}/>
        );
        expect(tree).toMatchSnapshot();
    });
    it('typing should set date/time', () => {
        let theState = {instant: null as null | string};
        const handler = (state: {instant: string | null}) => theState = state;
        const tree = mount(
            <DateAndTime propertyKey="instant" label="date/time" stateSetter={handler} state={{instant: "2018-12-13T12:23:00"}} disabled={false} required={true}/>
        );
        // Simulated DOM doesn't give us real input into type="date" just reflects value we set
        tree.find('input[name="instant_date"]').simulate('change', {target: {value: '2015-05-12'}});
        tree.find('input[name="instant_time"]').simulate('change', {target: {value: '12:18'}});
        expect(tree).toMatchSnapshot();
        expect(theState).toEqual({instant: "2015-05-12T12:18:00.000"});
    });

    it('should return null when I set only date', () => {
        let theState = {instant: null as null | string};
        const handler = (state: {instant: string | null}) => theState = state;
        const tree = mount(
            <DateAndTime propertyKey="instant" label="date/time" stateSetter={handler} state={{instant: "2018-12-13T12:23:00"}} disabled={false} required={true}/>
        );
        tree.find('input[name="instant_date"]').simulate('change', {target: {value: '12/05/2015'}});
        expect(tree).toMatchSnapshot();
        expect(theState).toEqual({instant: null});
    });
});
