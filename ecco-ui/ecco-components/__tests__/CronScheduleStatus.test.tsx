import * as React from 'react';
import {shallow} from 'enzyme';
import {CronScheduleStatus} from "../agreements/CronScheduleStatus";
import {ScheduleData} from "ecco-rota";

jest.mock("bowser", () => {
        // debugger;
        return ({
            firefox: true
        });
    }
);

describe('CronScheduleStatus', () => {
    it('should show single time with <PERSON><PERSON>,Thur<PERSON> every 2 weeks', () => {
        const tree = shallow(
            <CronScheduleStatus
                instanceState={ScheduleData.fromTargetSchedule(null, "start:2021-05-31 days:tues,thurs times:10:00 week:2")}
                onChange={() => {}}
                readOnly={false}
                title="test"
            />
        );
        expect(tree).toMatchSnapshot();
    });
    it('should show multiple times daily with end date weekly', () => {
        const tree = shallow(
            <CronScheduleStatus
                instanceState={ScheduleData.fromTargetSchedule(null, "start:2021-06-21 times:10:00,22:15 end:2022-06-30")}
                onChange={() => {}}
                readOnly={false}
                title="test 2"
            />
        );
        expect(tree).toMatchSnapshot();
    });

});
