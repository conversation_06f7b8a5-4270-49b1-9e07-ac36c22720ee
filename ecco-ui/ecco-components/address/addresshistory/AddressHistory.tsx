import * as React from "react";
import {FC, useState} from "react";
import {Grid, IconButton, Table, TableCell, TableHead, TableRow} from "@eccosolutions/ecco-mui";
import {EccoDateTime, WebApiError} from "@eccosolutions/ecco-common";
import DeleteIcon from "@material-ui/icons/Delete";
import {AddressHistoryCommand, CommandQueue} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {useAddressHistories} from "../../data/entityLoadHooks";
import {showNotification} from "ecco-components-core";
import {LoadingSpinner} from "../../Loading";
import {EccoModal, Footer} from "ecco-components-core";
import {EccoV3Modal} from "ecco-components-core";
import {AddressDisplay} from "../AddressLocationForm";


interface AddressHistoryProps {
    serviceRecipientId?: number | undefined;
    contactId?: number | undefined;
}
export const AddressHistory: FC<AddressHistoryProps> = props => {

    const {serviceRecipientId, contactId} = props;

    const {history, loading, reload} = useAddressHistories(serviceRecipientId, contactId);

    const [deletingItem, setDeletingItem] = useState<number>();

    if (loading || (history === undefined)) return <LoadingSpinner/>;

    const DeletingItemModal = deletingItem && <DeleteItemModal onChange={(confirm) => {
        if (!confirm) {
            setDeletingItem(undefined);
            return;
        } else {
            const cmd = new AddressHistoryCommand(Uuid.randomV4(), "remove", serviceRecipientId!, deletingItem); // NOTE: This needs a contactId variant if srId is null
            const commandQueue = new CommandQueue();
            commandQueue.addCommand(cmd);
            return commandQueue
                .flushCommands(false)
                .then(reload)
                .then(() => {
                    showNotification("info", "deleted");
                    setDeletingItem(undefined);
                })
                .catch((error: WebApiError) => {
                    showNotification("error", error.getMessage() || error.reason);
                    /*showErrorAsAlert(e)*/
                });
        }
    }}/>;

    return history.length == 0 ? (
        <Grid container>
            <Grid item xs={12}>
                {"no history recorded"}
            </Grid>
        </Grid>
    ) : (
        <Grid container>
            <Grid item={true} xs={12}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell scope="row" padding="none" style={{fontWeight: "bold"}}>
                                {"valid from"}
                            </TableCell>
                            <TableCell scope="row" padding="none" style={{fontWeight: "bold"}}>
                                {"valid to"}
                            </TableCell>
                            <TableCell scope="row" padding="none" style={{fontWeight: "bold"}}>
                                {"address"}
                            </TableCell>
                            <TableCell scope="row" padding="none">
                                {<span>&nbsp;</span>}
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    {history.map(h => {
                        const when = EccoDateTime.parseIso8601(h.validFrom)!!
                            .toEccoDate()
                            .formatPretty();
                        const whenTo = EccoDateTime.parseIso8601(h.validTo || null)
                            ?.toEccoDate()
                            .formatPretty();
                        const bldgId = h.buildingId || null;
                        const adrId = h.addressId || null;
                        return (
                            <TableRow key={h.id.toString()}>
                                <TableCell scope="row" padding="none">
                                    {h.validFrom.startsWith("1970") ? " " : when}
                                </TableCell>
                                <TableCell scope="row" padding="none">
                                    {whenTo}
                                </TableCell>
                                <TableCell scope="row" padding="none">
                                    <AddressDisplay
                                        buildingLocationId={bldgId}
                                        addressLocationId={adrId}
                                        displayAddress={null}
                                    />
                                </TableCell>
                                <TableCell scope="row" padding="none">
                                    <IconButton onClick={() => setDeletingItem(h.id)}>
                                        <DeleteIcon />
                                    </IconButton>
                                    {DeletingItemModal}
                                </TableCell>
                            </TableRow>
                        );
                    })}
                </Table>
            </Grid>
        </Grid>
    );
}


export interface ModalProps {
    title: string;
    show: boolean;
    close: () => void;
}
export const Modal: FC<ModalProps> = props => {
    const footer = <Footer
        onCancel={() => props.close()}
        onSave={() => props.close()}
        saveEnabled={false}
        action={"close"}
    />;
    return <EccoModal
        title={props.title}
        footer={footer}
        show={props.show}
        onEscapeKeyDown={props.close}
    >
        {props.children}
    </EccoModal>;
};

export const AddressHistoryModalLink: FC<AddressHistoryProps> = ({serviceRecipientId, contactId}) => {
    const [active, setActive] = useState(false);
    return active
        ? <AddressHistoryModal
            serviceRecipientId={serviceRecipientId}
            contactId={contactId}
            show={true}
            close={() => setActive(false)}
        />
        : <a onClick={() => setActive(true)}>history</a>
}

interface ModalAddressHistoryProps extends Omit<ModalProps, 'title'>, AddressHistoryProps {}
export const AddressHistoryModal: FC<ModalAddressHistoryProps> = props => {
    return <Modal title={"address history"} show={props.show} close={props.close}>
        <AddressHistory serviceRecipientId={props.serviceRecipientId} contactId={props.contactId}/>
    </Modal>
};


interface AddressHistoryDeleteProps {
    onChange: (confirm: boolean) => void;
}
const DeleteItemModal: FC<AddressHistoryDeleteProps> = props => {
    return (
        <EccoV3Modal
            title="delete address history"
            show={true}
            onCancel={() => props.onChange(false)}
            onSave={() => {
                props.onChange(true);
            }}
            action="delete"
            saveEnabled={true}
        >
            <Grid container>
                <Grid item xs={12}>
                    {"delete this history item?"}
                </Grid>
            </Grid>
        </EccoV3Modal>
    );
}
