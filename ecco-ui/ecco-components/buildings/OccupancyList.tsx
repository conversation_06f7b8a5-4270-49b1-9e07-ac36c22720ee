import * as React from "react";
import {ReactElement, useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    TablePagination,
    IconButton
} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancy, SortOption} from "../data/entityLoadHooks";
import {Building, fullAddress, OccupancyFilter, ServiceRecipient} from "ecco-dto";
/*import {link} from "../MUIComponentUtils";*/
import {useDebounce} from "ecco-components-core";
import {SearchFilter, OccupancyCheckboxFilter} from "./SearchFilter";
import {OpenInNewIcon, WrenchIcon} from "@eccosolutions/ecco-mui-controls";
import PersonIcon from "@material-ui/icons/Person";
import BusinessIcon from "@material-ui/icons/Business";

interface OccupancyRecord {
    id: number;
    serviceRecipient?: ServiceRecipient;
    serviceRecipientId: number;
    validFrom: string;
    validTo?: string;
}


interface OccupancyHistorySectionProps {
    occupancy?: Array<OccupancyRecord>;
    applicationRootPath: string;
    occupancyFilter?: OccupancyCheckboxFilter;
    search?: string;
}

/**
 * Convert checkbox filter to backend API filter format
 */
const convertToApiFilter = (checkboxFilter: OccupancyCheckboxFilter): OccupancyFilter => {
    if (checkboxFilter.occupied && checkboxFilter.void) {
        return "all";
    } else if (checkboxFilter.occupied && !checkboxFilter.void) {
        return "occupied";
    } else if (!checkboxFilter.occupied && checkboxFilter.void) {
        return "void";
    } else {
        // If neither is selected, default to showing all
        return "all";
    }
};

const srUrl = (srId: number, applicationRootPath: string) =>
    new URL(`${applicationRootPath}nav/r/main/sr2/${srId}/`, location.href).href;

const ExternalLinkSrId: React.FC<{
    srId: number | undefined;
    displayIcon: ReactElement;
    display: ReactElement;
    applicationRootPath: string;
}> = ({srId, displayIcon, display, applicationRootPath}) => {
    return (
        <Typography component={"span"}>
            <span style={{verticalAlign: "middle"}}>{displayIcon}</span>
            <span style={{fontSize: "small"}}>
                {display}
                {srId && (
                    <IconButton
                        size="small"
                        href={srId ? srUrl(srId, applicationRootPath) : "#"}
                        target="_blank"
                    >
                        <OpenInNewIcon fontSize="small" />
                    </IconButton>
                )}
            </span>
        </Typography>
        /*<Typography
            variant="subtitle2"
            component="h3"
            gutterBottom
            style={{color: "success.main" || "inherit", fontWeight: "bold"}}
        >*/

        /* NB this makes a blue link */
        /*
        <Button
                    component={Link}
                    color="primary"
                    style={{textTransform: 'none'}}
                    href={srId ? srUrl(srId, applicationRootPath) : "#"}
                    target="_blank"
                    /!*variant="body2"*!/
            >{displayName}
        </Button>
        */

        /*WORK srId
                ? link(`${displayName}`, () => window.open(srUrl(srId, applicationRootPath), "_blank"))
                : <Typography>void</Typography>*/
        /*</Typography>*/
    );
};

/**
 * Helper function to format building display name with address and parent building
 */

const BuildingName: React.FC<{building: Building; applicationRootPath: string}> = ({
    building,
    applicationRootPath
}) => {
    const name = `${building.parentName ? building.parentName + ", " : ""} ${
        building.name || `[b-id ${building.buildingId}]`
    }`;
    const Icon = <BusinessIcon style={{color: "#36720A"}} />; // see AppBarBase ${greenAppBar}
    const displayName = <span style={{fontWeight: "bold"}}>{name}</span>;
    return (
        <>
            <ExternalLinkSrId
                srId={building.serviceRecipientId}
                applicationRootPath={applicationRootPath}
                displayIcon={Icon}
                display={displayName}
            />
            <Typography style={{fontSize: "small"}}>{fullAddress(building.address)}</Typography>
            <Typography
                style={{fontSize: "small"}}
            >{`[b-id ${building.buildingId}] [a-id ${building.locationId}]`}</Typography>
        </>
    );
};

/**
 * Component for rendering a single occupancy record
 */
const OccupancyItem: React.FC<{occupancy: OccupancyRecord; applicationRootPath: string}> = ({
    occupancy,
    applicationRootPath
}) => {
    /*const srUrl = new URL(
        `${applicationRootPath}nav/r/main/sr2/${occupancy.serviceRecipientId}/`,
        location.href
    ).href;*/

    // TODO programatically replace color !! when have internet to remember this

    // ICON
    const ManagedVoidIcon = <WrenchIcon style={{transform: "scale(0.8)", color: "#36720A"}} />; // see AppBarBase ${greenAppBar}
    const ClientIcon = <PersonIcon style={{color: "#2994ed"}} />;
    const VoidIcon = <PersonIcon />;

    const type = occupancy.serviceRecipient?.prefix;
    const unmanagedVoid = type === undefined;
    const Icon: JSX.Element =
        type === "mv" ? ManagedVoidIcon : unmanagedVoid ? VoidIcon : ClientIcon;

    // NAME
    const managedVoidName = (futureDated: boolean) => {
        return futureDated ? (
            <span style={{fontStyle: "italic", color: "#36720A"}}>void (planned)</span>
        ) : (
            <span>void (planned)</span>
        );
    }; // see AppBarBase ${greenAppBar}
    const clientName = (occupiedNow: boolean, name: string) => {
        return occupiedNow ? (
            <span style={{fontStyle: "italic", color: "#2994ed"}}>{name}</span>
        ) : (
            <span>{name}</span>
        );
    }; // see AppBarBase ${blueAppBar}
    const voidName = (occupiedNow: boolean) => {
        return occupiedNow ? (
            <span style={{fontStyle: "italic"}}>void (unplanned)</span>
        ) : (
            <span>void (unplanned)</span>
        );
    };
    const today = new Date().getTime();
    const fromDate = new Date(occupancy.validFrom).getTime();
    const occupiedCurrently =
        fromDate <= today &&
        (occupancy.validTo == null || new Date(occupancy.validTo).getTime() > today); // to is exclusive
    const futureDated = fromDate > today;

    const displayName =
        type === "mv"
            ? managedVoidName(futureDated)
            : unmanagedVoid
              ? voidName(futureDated)
              : clientName(futureDated, occupancy.serviceRecipient?.displayName || "");

    const Name = (
        <ExternalLinkSrId
            srId={occupancy.serviceRecipientId}
            displayIcon={Icon}
            display={displayName}
            applicationRootPath={applicationRootPath}
        />
    );

    const When = (() => {
        const fromDate = new Date(occupancy.validFrom);
        // difference in fromDate to today
        const fromStartDays = Math.ceil(
            (fromDate.getTime() - new Date().getTime()) / (1000 * 3600 * 24)
        );
        const toDate = occupancy.validTo ? new Date(occupancy.validTo) : new Date();

        // Calculate the number of days
        const timeDiff = toDate.getTime() - fromDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        // Format duration in a friendly way
        const formatDuration = (days: number) => {
            if (days === 1) return "1 day";
            if (days < 30) return `${days} days`;
            if (days < 365) {
                const months = Math.floor(days / 30);
                return `${months} month${months === 1 ? "" : "s"}`;
                /*const remainingDays = days % 30;
                if (months === 1 && remainingDays === 0) return "1 month";
                if (remainingDays === 0) return `${months} months`;
                if (months === 1) return `1 month, ${remainingDays} day${remainingDays === 1 ? '' : 's'}`;
                return `${months} months, ${remainingDays} day${remainingDays === 1 ? '' : 's'}`;*/
            } else {
                const years = Math.floor(days / 365);
                return `${years} year${years === 1 ? "" : "s"}`;
                /*const remainingDays = days % 365;
                if (years === 1 && remainingDays === 0) return "1 year";
                if (remainingDays === 0) return `${years} years`;
                if (years === 1) return `1 year, ${remainingDays} day${remainingDays === 1 ? '' : 's'}`;
                return `${years} years, ${remainingDays} day${remainingDays === 1 ? '' : 's'}`;*/
            }
        };

        /*const dateOptions: Intl.DateTimeFormatOptions = {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric"
        };*/

        return (
            <span>
                {/*from {fromDate.toLocaleDateString('en-US', dateOptions)}*/}
                {occupancy.serviceRecipient?.prefix == "mv" && fromStartDays > 0 ? (
                    <>
                        {`in ${formatDuration(fromStartDays)}`}{" "}
                        {occupancy.validTo && <>{`for ${formatDuration(daysDiff)}`}</>}
                    </>
                ) : (
                    daysDiff > 0 && (
                        <> {`${occupancy.validTo ? "for" : "at"} ${formatDuration(daysDiff)}`}</>
                    )
                )}
                {` [${fromDate.toLocaleDateString()} - ${
                    occupancy.validTo
                        ? toDate.toLocaleDateString()
                        : "" /* today may not be the case for future occupancy */
                }]`}
            </span>
        );
    })();

    const Line = (
        <>
            {Name}
            {When}
        </>
    );

    return (
        <>
            <ListItem>
                <ListItemText primary={Line} /*secondary={}*/ />
            </ListItem>
        </>
    );
};

/**
 * Component for displaying current occupancy for a building
 */
const RecentOccupancySection: React.FC<OccupancyHistorySectionProps> = ({
    occupancy,
    applicationRootPath
}) => {
    if (!occupancy || occupancy.length === 0) {
        return (
            <Typography variant="body2" color="textSecondary">
                -
            </Typography>
        );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    /*const occupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is in current period
        return validFrom <= today && (!validTo || validTo >= today);
    });*/
    const occupancies = occupancy.slice(0, 5);

    if (occupancies.length === 0) {
        return null;
    }

    return (
        <List dense>
            {occupancies.map((occupancy, index) => (
                <React.Fragment key={occupancy.id}>
                    <OccupancyItem
                        occupancy={occupancy}
                        applicationRootPath={applicationRootPath}
                    />
                    {/*{index < occupancies.length - 1 && <Divider />}*/}
                </React.Fragment>
            ))}
        </List>
    );
};

/**
 * Component for displaying past and future occupancy in an accordion
 */
const OccupancyHistoryAccordion: React.FC<OccupancyHistorySectionProps> = ({
    occupancy,
    applicationRootPath
}) => {
    if (!occupancy || occupancy.length === 0) {
        return null;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Categorize occupancy records
    /*const pastOccupancies = occupancyHistory.filter(occupancy => {
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is past
        return validTo && validTo < today;
    });*/
    const pastOccupancies = occupancy.slice(5);

    // Don't show accordion if no past or future occupancies
    if (pastOccupancies.length === 0) {
        return null;
    }

    return (
        <Accordion>
            <AccordionSummary>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                            {pastOccupancies.length > 0 && `${pastOccupancies.length} previous `}
                        </Typography>
                    </Grid>
                    {/*<Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                            {futureOccupancies.length > 0 &&
                                `${futureOccupancies.length} future`}
                        </Typography>
                    </Grid>*/}
                </Grid>
            </AccordionSummary>
            <AccordionDetails>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        {pastOccupancies.length > 0 && (
                            <List dense>
                                {pastOccupancies.map((occupancy, index) => (
                                    <React.Fragment key={occupancy.id}>
                                        <OccupancyItem
                                            occupancy={occupancy}
                                            applicationRootPath={applicationRootPath}
                                        />
                                        {/*{index < pastOccupancies.length - 1 && <Divider />}*/}
                                    </React.Fragment>
                                ))}
                            </List>
                        )}
                    </Grid>
                    {/*<Grid item xs={12} md={6}>
                        {futureOccupancies.length > 0 && (
                            <List dense>
                                {futureOccupancies.map((occupancy, index) => (
                                    <React.Fragment key={occupancy.id}>
                                        <OccupancyItem
                                            occupancy={occupancy}
                                            applicationRootPath={applicationRootPath}
                                        />
                                        {index < futureOccupancies.length - 1 && <Divider />}
                                    </React.Fragment>
                                ))}
                            </List>
                        )}
                    </Grid>*/}
                </Grid>
            </AccordionDetails>
        </Accordion>
    );
};

/**
 * Component for displaying occupancy lists
 * Use with: const {applicationRootPath} = window.applicationProperties;
 */
export const OccupancyList: React.FC<{
    applicationRootPath: string;
    parentBuildingSrId?: number | undefined;
}> = ({applicationRootPath, parentBuildingSrId}) => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime().subtractMonths(12));
    // 'to' 3 months time, so can see upcoming managed voids, but do to the end of the month so it matches the reporting
    const [to, setTo] = useState<EccoDate>(
        EccoDate.todayLocalTime().addMonths(4).withDate(1).subtractMonths(1)
    );
    const [occupancyFilter, setOccupancyFilter] = useState<OccupancyCheckboxFilter>({
        occupied: true,
        void: true
    });
    const [sortOption, setSortOption] = useState<SortOption>("name");

    // Separate states for input value and debounced search
    const [searchInput, setSearchInput] = useState<string>("");
    const debouncedSearch = useDebounce(searchInput, 500);

    //useAppBarOptions(""); // not needed with proper AppBarBase defaultAppBar

    const pageSize = 20; // Match the buildingsSize from the hook

    const handleChangePage = (event: unknown, newPage: number) => {
        setPageNumber(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        // For now, we keep the page size fixed at 20 as per the hook implementation
        // This handler is required by TablePagination but we don't use it
    };

    const {bldgsWithOccupancy, totalCount, error, loading} = useBuildingsWithOccupancy(
        from,
        to,
        convertToApiFilter(occupancyFilter),
        debouncedSearch,
        pageNumber,
        parentBuildingSrId,
        sortOption
    );

    const isSearch = debouncedSearch.trim() !== "";

    const bldgs = bldgsWithOccupancy || [];

    return (
        <>
            <OccupancyListDisplay
                applicationRootPath={applicationRootPath}
                loading={loading}
                error={error}
                bldgs={bldgs}
                occupancyFilter={occupancyFilter}
                setOccupancyFilter={setOccupancyFilter}
                debouncedSearch={debouncedSearch}
                isSearch={isSearch}
                searchInput={searchInput}
                setSearchInput={setSearchInput}
                sortOption={sortOption}
                setSortOption={setSortOption}
            />
            <Pagination
                searchInProgress={loading}
                totalCount={totalCount}
                pageNumber={pageNumber}
                pageSize={pageSize}
                handleChangePage={handleChangePage}
                handleChangeRowsPerPage={handleChangeRowsPerPage}
            />
        </>
    );
};

const Pagination: React.FC<{
    searchInProgress: boolean;
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    handleChangePage: (event: unknown, newPage: number) => void;
    handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void;
}> = ({
    searchInProgress,
    totalCount,
    pageNumber,
    pageSize,
    handleChangePage,
    handleChangeRowsPerPage
}) => {
    return (
        <>
            {/* Only show pagination when not searching */}
            {!searchInProgress && (
                <Box display="flex" justifyContent="center" mt={2}>
                    <TablePagination
                        rowsPerPageOptions={[25]}
                        component="div"
                        count={totalCount}
                        rowsPerPage={pageSize}
                        page={pageNumber}
                        backIconButtonProps={{
                            "aria-label": "Previous Page"
                        }}
                        nextIconButtonProps={{
                            "aria-label": "Next Page"
                        }}
                        onChangePage={handleChangePage}
                        onChangeRowsPerPage={handleChangeRowsPerPage}
                    />
                </Box>
            )}
        </>
    );
};

const OccupancyListDisplay: React.FC<{
    applicationRootPath: string;
    loading: boolean;
    error: any;
    bldgs: Building[];
    occupancyFilter: OccupancyCheckboxFilter;
    setOccupancyFilter: (occupancyFilter: OccupancyCheckboxFilter) => void;
    debouncedSearch: string;
    isSearch: boolean;
    searchInput: string;
    setSearchInput: (searchInput: string) => void;
    sortOption: SortOption;
    setSortOption: (sortOption: SortOption) => void;
}> = ({
    applicationRootPath,
    loading,
    error,
    bldgs,
    occupancyFilter,
    setOccupancyFilter,
    searchInput,
    setSearchInput,
    debouncedSearch,
    isSearch,
    sortOption,
    setSortOption
}) => {
    const LoadingDisplay = loading && (
        <Box display="flex" justifyContent="center" p={2}>
            <CircularProgress />
        </Box>
    );

    const ErrorDisplay = error && (
        <Box p={2}>
            <Typography color="error">error loading data: {error.message}</Typography>
        </Box>
    );

    return (
        <Grid container justify="center">
            <Grid item xs={12} md={8}>
                <Box p={2} pb={1}>
                    <SearchFilter
                        searchInput={searchInput}
                        onSearchInputChange={setSearchInput}
                        occupancyFilter={occupancyFilter}
                        onOccupancyFilterChange={setOccupancyFilter}
                        sortOption={sortOption}
                        onSortOptionChange={setSortOption}
                    />
                </Box>

                {LoadingDisplay}
                {ErrorDisplay}

                {!loading && !error && (
                    <>
                        <Box p={2} pt={0}>
                            {isSearch && (
                                <Typography variant="body2" color="textSecondary" gutterBottom>
                                    showing {bldgs.length} of {bldgs.length} buildings
                                </Typography>
                            )}
                            {bldgs.length === 0 && isSearch ? (
                                <Typography
                                    variant="body1"
                                    color="textSecondary"
                                    style={{textAlign: "center", padding: "2rem"}}
                                >
                                    no buildings found matching "{debouncedSearch}"
                                </Typography>
                            ) : bldgs.length === 0 ? (
                                <Box p={2}>
                                    <Typography>no units found</Typography>
                                </Box>
                            ) : (
                                bldgs.map(building => (
                                    <Box key={building.buildingId} mb={2}>
                                        <Card elevation={2}>
                                            <CardContent>
                                                <BuildingName
                                                    building={building}
                                                    applicationRootPath={applicationRootPath}
                                                />
                                                <RecentOccupancySection
                                                    occupancy={building.occupancy}
                                                    applicationRootPath={applicationRootPath}
                                                />
                                                <OccupancyHistoryAccordion
                                                    occupancy={building.occupancy}
                                                    applicationRootPath={applicationRootPath}
                                                />
                                            </CardContent>
                                        </Card>
                                    </Box>
                                ))
                            )}
                        </Box>
                    </>
                )}
            </Grid>
        </Grid>
    );
};