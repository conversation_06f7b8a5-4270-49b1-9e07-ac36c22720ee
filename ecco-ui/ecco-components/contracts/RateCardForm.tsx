import {EccoDate, EccoDateTime, EccoTime, SelectListOption} from "@eccosolutions/ecco-common";
import {Grid, Typography, Box} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate, TimePickerEccoTime} from "@eccosolutions/ecco-mui-controls";
import {CommandQueue, RateCardCommand} from "ecco-commands";
import {CHARGENAME_LISTDEF, RateCardDto} from "ecco-dto";
import * as React from "react";
import {FC} from "react";
import {useServicesContext} from "../ServicesContext";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {dropdownList, textInput} from "ecco-components-core";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoAPI} from "../EccoAPI";

type Props = {
    rateCardId?: number | undefined;
    contractId: number;
    show: boolean;
    setShow: (show: boolean) => void;
};

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

type State = {
    origRateCard?: RateCardDto | undefined;
    startDate?: EccoDate | null | undefined;
    endDate?: EccoDate | null | undefined;
    name?: string | null | undefined;
    chargeNameId?: number | null | undefined;
    matchingPartsOfWeek?: string | undefined;
    matchingStartTime: EccoTime | null;
    matchingEndTime: EccoTime | null;
};

class RateCardForm extends CommandSubform<Props & LocalProps, State> {
    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {
            matchingStartTime: null,
            matchingEndTime: null
        };
    }

    override componentDidMount() {
        super.componentDidMount();
        const {rateCardId, services} = this.props;
        if (rateCardId) {
            services.contractRepository.findRateCardById(rateCardId).then(rc => {
                this.setState({
                    origRateCard: rc,
                    startDate: EccoDateTime.parseIso8601(rc.startDateTime)?.toEccoDate(),
                    endDate: EccoDateTime.parseIso8601(rc.endDateTime)?.toEccoDate(),
                    name: rc.name,
                    chargeNameId: rc.chargeNameId,
                    matchingPartsOfWeek: rc.matchingPartsOfWeek,
                    matchingStartTime: EccoTime.parseIso8601(rc.matchingStartTime),
                    matchingEndTime: EccoTime.parseIso8601(rc.matchingEndTime)
                });
            });
        }
    }

    getErrors(): string[] {
        const errors: string[] = [];
        // NOTE: Done in order they appear on the form

        // if (!this.state.matchingPartsOfWeek) {
        //     errors.push("part of week required");
        // }
        return errors;
    }

    emitChangesTo(cmdQ: CommandQueue) {
        const {origRateCard, startDate, endDate} = this.state;

        let cmd: RateCardCommand = new RateCardCommand(
            origRateCard ? "update" : "add",
            Uuid.randomV4(),
            undefined,
            origRateCard?.rateCardId
        );
        if (origRateCard) {
            const origStart = EccoDateTime.parseIso8601(origRateCard.startDateTime)?.toEccoDate();
            const origEnd = EccoDateTime.parseIso8601(origRateCard.endDateTime)?.toEccoDate();
            const origMatchingStart = EccoTime.parseIso8601(origRateCard.matchingStartTime);
            const origMatchingEnd = EccoTime.parseIso8601(origRateCard.matchingEndTime);
            cmd.changeStartDate(origStart, startDate || origStart)
                .changeEndDate(origEnd, endDate || origEnd)
                .changeName(origRateCard.name, this.state.name!!)
                .changeChargeNameId(origRateCard.chargeNameId, this.state.chargeNameId!!)
                .changeMatchingStartTime(origMatchingStart, this.state.matchingStartTime || null)
                .changeMatchingEndTime(origMatchingEnd, this.state.matchingEndTime || null);
        } else {
            cmd.changeStartDate(null, startDate!!)
                .changeEndDate(null, endDate!!)
                .changeName(null, this.state.name!!)
                .changeChargeNameId(null, this.state.chargeNameId!!)
                .changeContracts(null, [this.props.contractId])
                .changeMatchingStartTime(null, this.state.matchingStartTime || null)
                .changeMatchingEndTime(null, this.state.matchingEndTime || null);
        }
        cmd.changeMatchingPartsOfWeek(
            origRateCard?.matchingPartsOfWeek || null,
            this.state.matchingPartsOfWeek || null
        );

        if (cmd.hasChanges()) {
            cmdQ.addCommand(cmd);
        }
    }

    private partsOfWeek: SelectListOption[] = [
        {id: "WEEKDAY", name: "weekday", disabled: false},
        {id: "WEEKEND", name: "weekend", disabled: false},
        {id: "WEEKDAY_WEEKEND", name: "weekday & weekend", disabled: false},
        {id: "BANKHOLIDAY", name: "bank holiday", disabled: false}
    ];

    override render() {
        const {startDate, endDate} = this.state;
        return (
            <Grid container direction="column" justify="center" alignItems="center">
                <Grid container>
                    <Grid item sm={6} xs={12}>
                        <DatePickerEccoDate
                            name="start date"
                            label="start date"
                            onChange={startDate => this.setState({startDate})}
                            maxDate={endDate}
                            value={startDate!}
                        />
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        <DatePickerEccoDate
                            name="end date"
                            label="end date"
                            onChange={endDate => this.setState({endDate})}
                            minDate={startDate}
                            value={endDate!}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        {textInput(
                            "name",
                            "name",
                            newState => this.setState({name: newState.name}),
                            this.state
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        {dropdownList(
                            "charge",
                            newState => this.setState({chargeNameId: newState.chargeNameId}),
                            this.state,
                            "chargeNameId",
                            this.props.services.sessionData
                                ?.getListDefinitionEntriesByListName(CHARGENAME_LISTDEF)
                                .map(l => l.getDto()) || []
                        )}
                    </Grid>

                    {/* Matching Criteria Section - not for finance charges (not appropriate to group on) */}
                    <Grid item xs={12}>
                        <Box
                            style={{
                                marginTop: 16,
                                marginBottom: 8,
                                border: "1px solid #e0e0e0",
                                borderRadius: 4,
                                padding: 16
                            }}
                        >
                            <Typography
                                variant="h6"
                                component="h3"
                                style={{
                                    marginBottom: 16,
                                    fontWeight: "bold"
                                }}
                            >
                                Matching Criteria
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    {dropdownList(
                                        "part of week",
                                        newState =>
                                            this.setState({
                                                matchingPartsOfWeek: newState.matchingPartsOfWeek
                                            }),
                                        this.state,
                                        "matchingPartsOfWeek",
                                        this.partsOfWeek
                                    )}
                                </Grid>
                                <Grid item sm={6} xs={12}>
                                    <TimePickerEccoTime
                                        label="start time"
                                        time={this.state.matchingStartTime}
                                        onTimeChange={
                                            time =>
                                                this.setState({
                                                    matchingStartTime: time
                                                }) /*FIXME: ENSURE ISO8601 */
                                        }
                                    />
                                </Grid>
                                <Grid item sm={6} xs={12}>
                                    <TimePickerEccoTime
                                        label="end time"
                                        time={this.state.matchingEndTime}
                                        onTimeChange={
                                            time =>
                                                this.setState({
                                                    matchingEndTime: time
                                                }) /*FIXME: ENSURE ISO8601 */
                                        }
                                    />
                                </Grid>
                            </Grid>
                        </Box>
                    </Grid>
                </Grid>
            </Grid>
        );
    }
}

export const RateCardFormModal: FC<Props> = props => {
    const eccoAPI = useServicesContext();

    return props.show ? (
        <ModalCommandForm
            show={true}
            setShow={props.setShow}
            title={"rate card"}
            action={props.rateCardId ? "update" : "save"}
            maxWidth="md"
        >
            {form => <RateCardForm {...props} services={eccoAPI} commandForm={form} />}
        </ModalCommandForm>
    ) : null;
};
