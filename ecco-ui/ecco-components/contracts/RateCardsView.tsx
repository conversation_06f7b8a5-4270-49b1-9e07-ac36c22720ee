import {EccoDateTime, SelectListOption} from "@eccosolutions/ecco-common";

import {
    createStyles,
    Fab,
    Grid,
    Hidden,
    IconButton,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableRow,
    Theme
} from "@eccosolutions/ecco-mui";
import AddIcon from "@material-ui/icons/Add";
import EditIcon from "@material-ui/icons/Edit";
import ExpandMore from "@material-ui/icons/ExpandMore";

import {RateCardDto, RateCardEntryDto} from "ecco-dto";
import * as React from "react";
import {Reducer, useEffect, useReducer, useState} from "react";

import {useToggleList} from "../hooks/useToggleList";
import {useServicesContext} from "../ServicesContext";
import {RateCardEntriesTable} from "./RateCardEntriesTable";
import {RateCardEntryFormModal} from "./RateCardEntryForm";
import {RateCardFormModal} from "./RateCardForm";

// styles:
// https://material-ui.com/customization/components/#2-dynamic-variation-for-a-one-time-situation
// also typescript workarounds: https://material-ui.com/guides/typescript/
// also see "const useStyles = makeStyles((theme: Theme) =>", eg DateRangePicker.tsx
// also see 9d8d2697

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        table: {
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        },
        muted: {
            opacity: 0.5
        },
        inverseIcon: {
            // color: "white",
            // backgroundColor: "blue"
        },
        extendedIcon: {
            marginRight: theme.spacing(1),
        },
    })
);


function RateCardTableRow(props: {
    rateCard: RateCardDto;
    onClickExpander: () => void;
    expanded: boolean;
    dispatchEdit: DispatchEdit;
    chargeTypesFixedTemporal: SelectListOption[];
    reloadRateCardEntry: (rateCardEntryId: number) => void;
}) {
    const styles = useStyles();
    const {onClickExpander, expanded, rateCard} = props;
    const services = useServicesContext();

    function openNewRateCardEntry() {
        props.dispatchEdit({type: "editRateCardEntry", rateCard});
    }

    const startDateTime = EccoDateTime.parseIso8601(rateCard.startDateTime);
    const endDateTime = EccoDateTime.parseIso8601(rateCard.endDateTime);

    return (
        <>
            <TableRow
                className={
                    endDateTime?.earlierThan(EccoDateTime.nowLocalTime()) ? styles.muted : ""
                }
            >
                <TableCell colSpan={1}>rc-id {rateCard.rateCardId}</TableCell>
                <TableCell colSpan={9}>
                    {startDateTime?.toEccoDate().formatShort() +
                        (endDateTime
                            ? " to " + endDateTime.toEccoDate().formatShort()
                            : " onwards")}
                    <br />
                    <small>
                        {rateCard.name ?? ""}{" "}
                        {rateCard.chargeNameId &&
                            services.sessionData
                                .getListDefinitionEntryById(rateCard.chargeNameId)
                                .getDisplayName()}
                    </small>
                </TableCell>
                <TableCell colSpan={1}>
                    <IconButton
                        color="primary"
                        aria-label="edit"
                        size="small"
                        onClick={() => props.dispatchEdit({type: "editRateCard", rateCard})}
                    >
                        <EditIcon />
                    </IconButton>
                    <IconButton color="primary" onClick={openNewRateCardEntry}>
                        <AddIcon />
                    </IconButton>
                </TableCell>
                <TableCell colSpan={1}>
                    <IconButton onClick={onClickExpander}>
                        <ExpandMore />
                    </IconButton>
                </TableCell>
            </TableRow>
            {expanded && (
                <TableRow>
                    <TableCell colSpan={12}>
                        <RateCardEntriesTable
                            rateCard={rateCard}
                            chargeTypesFixedTemporal={props.chargeTypesFixedTemporal}
                            editRateCardEntryCallback={(_onSave, rateCard, rateCardEntry) =>
                                props.dispatchEdit({
                                    type: "editRateCardEntry",
                                    rateCard,
                                    rateCardEntry
                                })
                            }
                            reloadRateCardEntry={props.reloadRateCardEntry}
                        />
                    </TableCell>
                </TableRow>
            )}
        </>
    );
}

interface RateCardsProps {
    contractId: number
}

interface EditState {
    rateCard?: RateCardDto | null | undefined;
    rateCardEntry?: RateCardEntryDto | null | undefined;
    dialog: "rateCard" | "rateCardEntry" | null;
    version: number;
}
interface EditAction {
    type: "close" | "editRateCard" | "editRateCardEntry" | "reload";
    rateCard?: RateCardDto | undefined;
    rateCardEntry?: RateCardEntryDto | undefined;
}

type RateCardReducer = Reducer<EditState, EditAction>;
type DispatchEdit = (action: EditAction) => void;

export function RateCardsView(props: RateCardsProps) {
    const classes = useStyles();
    const [rows, setRows] = useState<RateCardDto[]>([]);
    const [expandedRows, toggleRow] = useToggleList();

    const [state, dispatchEdit] = useReducer<RateCardReducer>(
        (prevState, action) => {
            if (action.type == "close" || action.type == "reload") {
                return {
                    rateCard: null,
                    rateCardEntry: null,
                    dialog: null,
                    version: prevState.version + 1
                };
            } else if (action.type == "editRateCard") {
                return {rateCard: action.rateCard, dialog: "rateCard", version: prevState.version};
            } else if (action.type == "editRateCardEntry") {
                return {
                    rateCardEntry: action.rateCardEntry,
                    rateCard: action.rateCard,
                    dialog: "rateCardEntry",
                    version: prevState.version
                };
            }
            return prevState;
        },
        {rateCard: null, rateCardEntry: null, dialog: null, version: 0}
    );

    const services = useServicesContext();

    const chargeTypesFixedTemporal: SelectListOption[] = [
        {id: "FIXED", name: "fixed", disabled: false},
        {id: "TEMPORAL", name: "temporal", disabled: false},
        {id: "FIXED_TEMPORAL", name: "fixed & temporal", disabled: false}
    ];

    const orderRateCardEntries = (cards: RateCardDto[]) => {
        // sort by list def name
        cards.forEach(c => {
            c.rateCardEntries.sort((a, b) => {
                // sort by name
                const nameA = services.sessionData
                    .getListDefinitionEntryById(a.matchingChargeCategoryId)
                    .getName();
                const nameB = services.sessionData
                    .getListDefinitionEntryById(b.matchingChargeCategoryId)
                    .getName();
                if (nameA > nameB) {
                    return 1;
                }
                if (nameA > nameB) {
                    return -1;
                }

                // sort by durationMins
                return a.units > b.units ? 1 : -1;
            });
        });

        // sort by childId
        // TODO - don't need to sort by childId for now, since there is no overlap in the current spec

        setRows(cards);
    };

    useEffect(() => {
        services.contractRepository
            .findRateCardsForContract(props.contractId)
            .then(rc => orderRateCardEntries(rc));
    }, [state.version, props.contractId]);

    function isExpanded(index: number): boolean {
        const foundIndex = expandedRows.indexOf(index);
        return foundIndex > -1;
    }

    // noinspection JSUnusedLocalSymbols
    // function reloadRateCard(_rateCardId?: number) {
    //     // reload all for now
    //     services.contractRepository.findRateCardsForContract(props.contractId).then(rc => setRows(rc));
    // }
    // noinspection JSUnusedLocalSymbols
    function reloadRateCardEntry(_rateCardEntryId: number) {
        // reload all for now
        services.contractRepository
            .findRateCardsForContract(props.contractId)
            .then(rc => setRows(rc));
    }
    function newRateCard() {
        dispatchEdit({type: "editRateCard"});
    }

    const styles = useStyles();
    return (
        <>
            <Grid container>
                <Grid item xs={6} implementation="css" component={Hidden} />
                <Grid item xs={6} style={{textAlign: "right"}}>
                    {/* see also https://material-ui.com/components/icons/*/}
                    <Fab
                        variant="extended"
                        // color="primary"
                        size="small"
                        onClick={newRateCard}
                    >
                        new card
                        <AddIcon className={styles.extendedIcon} />
                    </Fab>
                </Grid>
                <Grid container justify={"center"}>
                    <Grid item xs={12}>
                        <div className={classes.tableWrapper}>
                            <Table className={classes.table}>
                                <TableBody>
                                    {rows &&
                                        rows.map((row, index: number) => (
                                            <RateCardTableRow
                                                key={row.rateCardId}
                                                rateCard={row}
                                                dispatchEdit={dispatchEdit}
                                                chargeTypesFixedTemporal={chargeTypesFixedTemporal}
                                                reloadRateCardEntry={rateCardEntryId =>
                                                    reloadRateCardEntry(rateCardEntryId)
                                                }
                                                onClickExpander={() => toggleRow(index)}
                                                expanded={isExpanded(index)}
                                            />
                                        ))}
                                </TableBody>
                            </Table>
                        </div>
                    </Grid>
                </Grid>
            </Grid>
            {state.dialog == "rateCard" && (
                <RateCardFormModal
                    contractId={props.contractId}
                    rateCardId={state.rateCard?.rateCardId}
                    show={true}
                    setShow={() => dispatchEdit({type: "close"})}
                />
            )}
            {state.dialog == "rateCardEntry" && (
                <RateCardEntryFormModal
                    rateCard={state.rateCard!}
                    rateCardEntry={state.rateCardEntry!}
                    chargeTypesFixedTemporal={chargeTypesFixedTemporal}
                    setShow={() => dispatchEdit({type: "close"})}
                />
            )}
        </>
    );
}

export default RateCardsView;
