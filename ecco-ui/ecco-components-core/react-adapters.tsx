/*
 * Utils for adapting DOM <-> React
 */

import * as React from "react";
import {Component, Fragment, ReactNode} from "react";
import {render} from "react-dom";

interface Props {
    content: Node;
    useLi?: boolean | undefined;
    afterNextPaint?: (() => void) | undefined;
}
/** Use for mounting non-React DOM content such as from JQuery
 * see https://reactjs.org/docs/integrating-with-other-libraries.html */
export class DomElementContainer extends Component<Props> {
    private el: Node | null = null;

    override componentDidMount(): void {
        this.el!.appendChild(this.props.content);
        this.doCallBack(); // catch the first render
    }

    /**
     * Call back the afterNextPaint after the component is fully rendered.
     * See https://stackoverflow.com/a/34999925
     * Also https://github.com/facebook/react/issues/2659#issuecomment-66165159
     * Essentially the requestAnimationFrame calls us too early - before its fully painted.
     * NB requestAnimationFrame requests the browser to execute the code during the next repaint cycle.
     * So using setTimout allows the react code to flush through before we then get the callback after fully painted.
     * We find various breakpoints in 'raw' useEffects etc don't get called after the final render.
     */
    override componentDidUpdate(
        prevProps: Readonly<{content: Node}>,
        prevState: Readonly<{}>,
        snapshot?: any
    ) {
        this.doCallBack();
    }

    private doCallBack() {
        const afterNextPaint = this.props.afterNextPaint;
        if (afterNextPaint) {
            setTimeout(function () {
                requestAnimationFrame(() => {
                    afterNextPaint();
                });
            });
        }
    }

    override render() {
        return this.props.useLi ? (
            <li ref={el => (this.el = el)} />
        ) : (
            <div ref={el => (this.el = el)} />
        );
    }
}

/**
 * Put React content in non-react code
 * Also see similar areas: mountWithServices, showInCommandForm, showInModalDom
 */
export function reactElementContainer(content: ReactNode, mountPoint: HTMLElement) {
    render(<Fragment>{content}</Fragment>, mountPoint);
}
