const {defineConfig} = require("cypress");
const webpackConfig = require("./webpack.config.dev.js");
const createBundler = require("@bahmutov/cypress-esbuild-preprocessor");

module.exports = defineConfig({
    component: {
        devServer: {
            framework: "react",
            bundler: "webpack",
            webpackConfig
        },
        setupNodeEvents: on => {
            on("file:preprocessor", createBundler());
        },
        specPattern: "cypress/component/**/*.test.{js,ts,jsx,tsx}",
        video: false
    }
});
