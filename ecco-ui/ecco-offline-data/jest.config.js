// This should be specified in Configurations -> Templates -> Jest as the config file
module.exports = {
    testEnvironment: "jsdom",
    transform: {
        "^.+\\.ts$": [
            // Note: No tsx here
            "ts-jest",
            {
                tsconfig: "__tests__/tsconfig.json"
            }
        ]
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(ts)$",
    setupFilesAfterEnv: ["./__tests__/setupJest.ts"],
    moduleFileExtensions: ["ts", "js", "json", "node"],
    moduleNameMapper: {
        bowser: "<rootDir>/__mocks__/bowser"
        // "services": "<rootDir>/__mocks__/null",
        // "punycode": "<rootDir>/__mocks__/null",
        // "IPv6": "<rootDir>/__mocks__/null",
        // "SecondLevelDomains": "<rootDir>/__mocks__/null"
    },
    modulePaths: ["<rootDir>"],
    // collectCoverage: true,
    // mapCoverage: true
};
