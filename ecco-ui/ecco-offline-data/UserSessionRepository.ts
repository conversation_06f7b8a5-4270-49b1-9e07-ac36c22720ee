import {UserSessionDto} from "./UserSessionDto";

/**
 * Required methods for offline user management.
 */
export interface UserSessionRepository {
    /** Throws AuthenticationException if not logged in (no session found) */
    findUserSession(): Promise<UserSessionDto>;

    login(username: string, credentialsKey: string, userDeviceId: string): Promise<UserSessionDto>;
    logout(): Promise<void>
}
