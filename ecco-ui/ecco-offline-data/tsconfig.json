{
  "extends": "../tsconfig.ecco-module.json",
  "compilerOptions": {
    "lib": ["ES2017", "ES2018.Promise", "DOM", "DOM.Iterable"],
    "baseUrl": "",
    "outDir": "./build-tsc",
    "declarationDir": "./build-tsc"
  },
  "references": [
    { "path": "../ecco-commands/tsconfig.json" },
    { "path": "../ecco-dto/tsconfig.json" }
  ],
  "include": ["**/*.ts"],
  "exclude": ["debug", "dist", "build-tsc", "__tests__"] // Technically shouldn't need to specify debug and dist as they don't have *.tsx? in
}
