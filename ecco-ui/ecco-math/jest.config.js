// This should be specified in Configurations -> Templates -> Jest as the config file
module.exports = {
    transform: {
        "^.+\\.tsx?$": [
            "ts-jest",
            {
                tsconfig: "tsconfig.json"
            }
        ]
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(tsx?)$",
    setupFiles: [
        // "./setupJestTests.ts"
    ],
    moduleFileExtensions: ["ts", "tsx", "js", "json", "node"],
    moduleNameMapper: {},
    modulePaths: ["<rootDir>"],
    // collectCoverage: true,
    // mapCoverage: true
};
