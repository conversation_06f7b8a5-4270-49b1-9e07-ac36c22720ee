import {HateoasResource} from "@eccosolutions/ecco-common";
import {InvoiceLineDto} from "../invoicing/invoicing-dto";
import {ReferralSummaryDto} from "../referral-dto";
import {Building as BuildingDto} from "../building-dto";
import {ServiceRecipient} from "../service-recipient-dto";

export const CHARGENAME_LISTDEF = "chargeNames";

export interface FinanceChargeDto extends InvoiceLineDto {
    // for client side reporting
    // receipts in the period of the charge
    receiptTotal?: number | undefined;
    // due, where +ve means outstanding
    dueAmount?: number | undefined;
    serviceRecipient?: ServiceRecipient | undefined; // occupied service recipient (referral / managed void)
    building?: BuildingDto | undefined;
    // for client side reporting
}

export interface FinanceReceiptDto extends HateoasResource {
    receiptId: number;
    serviceRecipientId: number;
    typeDefId: number;
    amount: number;
    /** ISO8601 format yyyy-MM-dd */
    receivedDate: string;
    description: string;

    // for client side reporting
    referralSummary?: ReferralSummaryDto | undefined;
}
