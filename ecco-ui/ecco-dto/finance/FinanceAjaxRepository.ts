import {FinanceRepository} from "./FinanceRepository";
import {FinanceChargeDto, FinanceReceiptDto} from "./finance-dto";
import {ApiClient} from "../web-api";

export class FinanceAjaxRepository implements FinanceRepository {
    constructor(private apiClient: ApiClient) {}

    public findByReceiptId(receiptId: number): Promise<FinanceReceiptDto> {
        const apiPath = `finance/receipts/${receiptId}/`;
        return this.apiClient.get<FinanceReceiptDto>(apiPath);
    }

    public findChargesBySrId(srId: number): Promise<FinanceChargeDto[]> {
        const apiPath = `finance/service-recipients/${srId}/charges/`;
        const options = {query: {fromDate: "1970-01-01"}};
        return this.apiClient.get<FinanceChargeDto[]>(apiPath, options);
    }

    public findReceiptsBySrId(srId: number): Promise<FinanceReceiptDto[]> {
        const apiPath = `finance/service-recipients/${srId}/receipts/`;
        return this.apiClient.get<FinanceReceiptDto[]>(apiPath);
    }
}
