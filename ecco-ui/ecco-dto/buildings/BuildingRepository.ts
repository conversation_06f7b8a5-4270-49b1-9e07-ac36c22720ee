import {Building, OccupancyDto} from "../building-dto";
import {EccoDate, NumberToObjectMap} from "@eccosolutions/ecco-common";

export type OccupancyFilter = "all" | "occupied" | "void";

export interface BuildingRepository {
    findOneBuilding(buildingId: number): Promise<Building>;

    findOneBuildingBySrId(srId: number): Promise<Building>;

    findAllBuildingsInIds(ids: number[]): Promise<Building[]>;

    findAllBuildingCareRuns(buildingId: number): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildings(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
              }
            | undefined
    ): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildingsForUser(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
              }
            | undefined
    ): Promise<Building[]>;

    findAllBuildingsOfLocationId(addressLocationId: number): Promise<Building[]>;

    getCachedBuildingsMap(refresh?: boolean | undefined): Promise<NumberToObjectMap<Building>>;

    findOccupancy(
        from: EccoDate,
        to: EccoDate,
        filter: OccupancyFilter,
        search: string | null,
        page?: number,
        buildingIds?: number[]
    ): Promise<OccupancyDto[]>;
}
