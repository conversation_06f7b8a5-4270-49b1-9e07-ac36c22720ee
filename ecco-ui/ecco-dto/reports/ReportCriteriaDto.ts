export type IdSourceKey = "id" | "serviceRecipientId" | "clientId";

/** Optional entities that can be required for a report */
export type RelatedEntitiesKey =
    | "activityInvolvement"
    | "activityInterest"
    | "referral"
    | "client"
    | "referralFull"
    | "referralEvents"
    | "riskWork"
    | "singleValueHistory"
    | "singleValueQnrHistory"
    | "supportWork"
    | "supportRiskWork"
    | "supportWorkEvents"
    | "taskAudit"
    | "questionnaireWork"
    | "questionnaireSnapshotAtEnd"
    | "serviceRecipientAuditsSnapshotAtEnd"
    | "customFormWork"
    | "childRecipientIds"
    | "relatedReferrals"
    | "review"
    | "riskFlags"
    | "aclPermissions"
    | "scheduleAptsConfirmedAtEnd"
    | "staff"
    | "associatedContacts"
    | "receipts"
    | "financeCharges"
    | "serviceRecipient";

/**
 * This interface is based on the Java class com.ecco.webApi.controllers.ReportCriteriaDto
 *
 * The SelectionCriteria is translated (via ReportCriteriaDtoFactory.getReportCriteriaDto) in order to
 * send domain-specific requests to the server, eg /reports/referrals. This means we don't need to
 * specify 'selectionRootEntity' here.
 *
 * DOCUMENTATION on similar fields - see charts-dto/SelectionCriteria
 */
export interface ReportCriteriaDto {
    /**
     * GENERIC CRITERIA (non-referral)
     */

    /** ISO-8601 date: yyyy-MM-dd */
    from: string | null;

    /** ISO-8601 date: yyyy-MM-dd */
    to: string | null;

    /**
     * The property that the report dates are applicable for.
     */
    selectionPropertyPath?: string | null | undefined;

    /**
     * The client-side list of fetchRelatedEntities (extra data to be loaded).
     * eg riskWork, or 'childRecipientIds' for /reports/referrals/.
     */
    optionalData?: RelatedEntitiesKey[] | undefined;

    /**
     * Way of passing various data/flags
     */
    options?: string[] | undefined;

    /**
     * Status of the entity - similar to referralStatus, except meaningful for the entity
     * eg liveAsEnd for a review, means get all the reviews latest status (don't use dates)
     */
    entityStatus?: string | undefined;

    /**
     * FILTERS on the criteria.
     * NB these perhaps shouldn't be hard-coded, and could be:
     *  - [operand, filterPropertyPath, filterPropertyValue]
     */
    /** Filter the report by a particular srId */
    serviceRecipientFilter: string | null;

    /** Filter the report by a specific userId */
    userId: number | null;
    username: string | null;

    /** Filter the evidence group name to retrieve for questionnaires involved in this report */
    questionnaireEvidenceGroup?: string | null | undefined;
    questionnaireEvidenceGroupArr: string[] | null;

    /** Filter the command name to retrieve for audits involved in this report */
    commandNameArr?: string[] | undefined;

    /** The task definition id to restrict to. */
    taskDefName?: string | undefined;

    /** The evidence group name to retrieve for support involved in this report (eg supportStaffNotes).
     * If not provided, then 'needs' is assumed.
     */
    supportEvidenceGroup?: string | undefined;

    /** The evidence group name to retrieve for custom forms involved in this report (eg customForm1) */
    customFormEvidenceGroup?: string | undefined;

    buildingIds?: number[] | undefined;

    /**
     * REFERRAL CRITERIA
     */

    /** The service ID. */
    // REVIEW strings are used as conversion of number to Long not quite right from js to Java
    serviceId?: number | null | undefined;

    /** The project ID. */
    projectId?: number | null | undefined;

    companyId?: number | undefined;

    clientGroupId?: number | undefined;

    serviceGroupId?: number | undefined;

    /** If set, filters for is or is not a child referral. null means get children and parents */
    isChild?: boolean | null | undefined;

    /** The item selected */
    geographicAreaIdSelected?: number | null | undefined;

    /** For a match on all referrals whose srcGeographicArea's matches an id. This allows us to include all the children
     * of a parent recursively - so all districts and wards within a county. */
    geographicAreaIds?: Array<number> | null | undefined;

    /**
     * Status of the referrals (live / closed etc)
     * See ecco-dto.ts for the presented list client-side
     */
    referralStatus?: string | null | undefined;

    /** To differentiate between the meaning of the status - eg:
     * 1) get referrals who were 'live' during the period (which could bring back the entire database)
     * OR with this setting set:
     * 2) get referrals who were 'live' during the period who were also received during the period */
    newReferralsOnly?: boolean | null | undefined;

    includeRelated?: boolean | undefined;
}
