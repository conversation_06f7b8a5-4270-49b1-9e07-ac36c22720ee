import {Person} from "./contact-dto";
import {ServiceRecipient} from "./service-recipient-dto";
import {ConsentSummaryFields} from "./referral-dto";
import {ClientDetailAbstractSecretFields} from "./client-dto";
import {HateoasResource} from "@eccosolutions/ecco-common";


/**
 * This interface must match the Java class com.ecco.webApi.contacts.WorkerViewModel. */
export interface StaffListRowDto extends HateoasResource {
    /** The worker ID. */
    workerId: number;
    firstName: string;
    lastName: string;
}

/**
 * This interface must match the Java class com.ecco.webApi.contacts.WorkerViewModel. */
export interface StaffDto extends ClientDetailAbstractSecretFields, Person {
    /** The worker ID. */
    workerId: number;

    /** firstname lastname separated by a space, where both exist, otherwise whichever exists */
    displayName: string;

    /** The UUID of this person's calendar */
    calendarId: string;

    primaryLocationId: number;

    primaryLocationName: string; // just handy

    /** now dbsNumber */
    crbNumber: string;
    /** A custom identifier used by the organisation to uniquely identify the worker.
     *
     * This might, for example, be an identifier originating from a legacy system. */
    code: string;

    jobs: StaffJobDto[];
}

export interface StaffJobDto extends ConsentSummaryFields {
    id: number;

    workerId: number;

    code: string;

    contractedWeeklyHours: number;

    /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
    startDate: string;

    /** ISO8601 yyyy-MM-dd date that the person started employment/contract - can be null */
    endDate: string;

    serviceRecipient: ServiceRecipient;
}