import {SignpostedIdFields} from "../referral-dto";
import {AcceptedState} from "../dto";
import {ServiceRecipient, ServiceRecipientStatusFields} from "../service-recipient-dto";
import {Agency} from "../contact-dto";
import {FormEvidence} from "../evidence-dto";

export interface ManagedVoidDto
    extends SignpostedIdFields,
        ServiceRecipientStatusFields,
        ServiceRecipient {
    repairId: number;
    serviceRecipientId: number;
    displayName: string;
    serviceAllocationId: number;
    statusMessage: string; // statusMessageKey populated from messages

    /** ISO8601 format yyyy-MM-dd */
    receivedDate: string;

    signpostedExitComment: string;
    decisionMadeOn: string;
    acceptOnServiceState: AcceptedState;

    // source
    selfReferral?: boolean;
    referrerAgencyId?: number;
    referrerIndividualId?: number;
    source?: string;
    sourceAgency?: Agency;

    // startOnService
    supportWorkerId: number;
    receivingServiceDate?: string;
    supportWorkerDisplayName: string; // client-only

    // exit
    exitedDate: string;
    exitReasonId: number;

    // client only
    // client only
}
