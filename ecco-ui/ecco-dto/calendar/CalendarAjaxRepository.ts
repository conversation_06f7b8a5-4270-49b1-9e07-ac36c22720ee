import {EventResourceDto} from "../calendar-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {CalendarRepository} from "./CalendarRepository";
import {ApiClient} from "../web-api";

export class CalendarAjaxRepository implements CalendarRepository {
    constructor(private apiClient: ApiClient) {}

    /**
     * Calls EventController#findCalendarEvents
     * @param contactIds
     * @param start - local date
     * @param end - local date
     * @returns {Promise<EventResourceDto[]>}
     */
    public fetchCalendarsByContactIds(
        contactIds: number[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]> {
        const path = "calendar/event/search"; // see EventController.java
        const query: Record<string, string> = {
            start: start.formatIso8601(),
            end: end.formatIso8601()
        };
        if (contactIds.length > 0) {
            query["contactId"] = contactIds.join(",");
        }
        return this.apiClient.get<EventResourceDto[]>(path, {query});
    }

    public fetchEventsByCalendarId(
        calendarIds: string[],
        start: EccoDateTime,
        end: EccoDateTime
    ): Promise<EventResourceDto[]> {
        const path = "calendar/event/search"; // see EventController.java
        const query: Record<string, string> = {
            startTime: start.formatIso8601(),
            endTime: end.formatIso8601()
        };
        if (calendarIds.length > 0) {
            query["calendarId"] = calendarIds.join(",");
        }
        return this.apiClient.get<EventResourceDto[]>(path, {query});
    }

    public fetchEventsByCalendarIdAndDate(
        calendarIds: string[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]> {
        const path = "calendar/event/calendarIds"; // see EventController.java
        const query: Record<string, string> = {
            start: start.formatIso8601(),
            end: end.formatIso8601()
        };
        if (calendarIds.length > 0) {
            query["calendarIds"] = calendarIds.join(",");
        }
        return this.apiClient.get<EventResourceDto[]>(path, {query});
    }

    /**
     * Calls EventController#findEventsById
     */
    public fetchEventsById(eventIds: string[]): Promise<EventResourceDto[]> {
        const path = "calendar/event/ids/";
        const query: Record<string, string> = {
            eventIds: eventIds.join(",")
        };
        return this.apiClient.get<EventResourceDto[]>(path, {query});
    }

    /** Fetch nearby (-3 days -> +2 wks) calendar events for specifiedcalendar - see EventController /nearby */
    public nearby(calendarId: string, nearby?: EccoDate | undefined): Promise<EventResourceDto[]> {
        const path = "calendar/event/nearby";
        const query: Record<string, string> = {
            calendarId: calendarId,
            date: (nearby || EccoDate.todayLocalTime()).formatIso8601()
        };

        return this.apiClient.get<EventResourceDto[]>(path, {query});
    }
}
