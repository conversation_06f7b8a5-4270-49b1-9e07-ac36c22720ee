import {ServiceRecipient} from "../service-recipient-dto";
import {
    BaseServiceRecipientCommandDto,
    EvidenceAttachment,
    FlagArea,
    FlagEvidenceDto
} from "../evidence-dto";
import {SessionData} from "../session-data/feature-config-domain";
import {ServiceType} from "../service-config-domain";
import {ReportCriteriaDto} from "../reports/ReportCriteriaDto";

export interface ServiceRecipientRepository {
    findOneServiceRecipientById(serviceRecipientId: number): Promise<ServiceRecipient>;

    findManyServiceRecipientByIds(serviceRecipientIds: number[]): Promise<ServiceRecipient[]>;

    findServiceRecipientTaskCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number,
        taskName: string
    ): Promise<BaseServiceRecipientCommandDto[]>;

    findServiceRecipientCommandsBySearch(
        criteria: ReportCriteriaDto,
        pageNumber: number | null
    ): Promise<BaseServiceRecipientCommandDto[]>;

    findServiceRecipientCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number | null
    ): Promise<BaseServiceRecipientCommandDto[]>;

    findServiceRecipientDraftCommands(
        serviceRecipientId: number,
        taskName: string,
        userId: number
    ): Promise<BaseServiceRecipientCommandDto[]>;

    findServiceRecipientAttachments(serviceRecipientId: number): Promise<EvidenceAttachment[]>;

    fetchStatusAreaEvidenceFlags(
        sessionData: SessionData,
        serviceRecipientId: number,
        serviceType: ServiceType
    ): Promise<FlagArea[]>;

    findExternalFlagsBySourceAndRef(
        externalSourceName: string,
        externalRef: string
    ): Promise<FlagEvidenceDto[]>;
}
