import {ServiceRecipient} from "../service-recipient-dto";
import {ServiceRecipientRepository} from "./ServiceRecipientRepository";
import {ApiClient} from "../web-api";
import {
    BaseServiceRecipientCommandDto,
    EvidenceAttachment,
    EvidenceDef,
    FlagArea,
    FlagEvidenceDto
} from "../evidence-dto";
import {QuestionnaireWorkAjaxRepository} from "../evidence/questionnaire/QuestionnaireWorkAjaxRepository";
import {SessionData} from "../session-data/feature-config-domain";
import {ServiceType} from "../service-config-domain";
import {ReportCriteriaDto} from "../reports/ReportCriteriaDto";

export class ServiceRecipientAjaxRepository implements ServiceRecipientRepository {
    private questionnaireRepository: QuestionnaireWorkAjaxRepository;

    constructor(private apiClient: ApiClient) {
        this.questionnaireRepository = new QuestionnaireWorkAjaxRepository(apiClient);
    }

    // See ServiceRecipientController.findOne
    // This normally expects a referral - see
    // This does try a referral first, then defaults to standard ServiceRecipient properties
    public findOneServiceRecipientById(serviceRecipientId: number): Promise<ServiceRecipient> {
        return this.apiClient.get<ServiceRecipient>(`service-recipient/${serviceRecipientId}/`);
    }

    // See ServiceRecipientController.findMany
    // This doesn't assert access
    // This doesn't try to take part in caching because many at once is probably better for masses of data
    // RENAME noAcl
    public findManyServiceRecipientByIds(
        serviceRecipientIds: number[]
    ): Promise<ServiceRecipient[]> {
        return this.apiClient.get<ServiceRecipient[]>(`service-recipients/byIds/noAcl/`, {
            query: {ids: serviceRecipientIds.join(",")}
        });
    }

    public findServiceRecipientDraftCommands(
        serviceRecipientId: number,
        taskName: string,
        userId: number
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `service-recipients/${serviceRecipientId}/commands/${taskName}/`;
        const query = {
            draft: true.toString()
        };
        return this.apiClient.get<BaseServiceRecipientCommandDto[]>(path, {query});
    }

    public findServiceRecipientTaskCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number,
        taskName: string
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `service-recipients/${serviceRecipientId}/commands/${taskName}/`;
        const query = {
            page: pageNumber != null ? pageNumber.toString() : undefined
        };
        return this.apiClient.get<BaseServiceRecipientCommandDto[]>(path, {query});
    }

    // paged and ordered by created DESC
    // see also ReportAjaxRepository -
    public findServiceRecipientCommandsByCreated(
        serviceRecipientId: number,
        pageNumber: number | null
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `service-recipients/${serviceRecipientId}/commands/`;
        const query = {
            page: pageNumber != null ? pageNumber.toString() : undefined
        };
        return this.apiClient.get<BaseServiceRecipientCommandDto[]>(path, {query});
    }

    // TODO move/replace findServiceRecipientCommandsByCreated in ecco-reports
    //  however, for now it could be awkward with the branches we are maintaining around report changes
    public findServiceRecipientCommandsBySearch(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `reports/service-recipients/commands/page/${page}/?order=desc`;
        return this.apiClient.postWithReAuth<BaseServiceRecipientCommandDto[]>(path, reportDto);
    }

    public findServiceRecipientAttachments(
        serviceRecipientId: number
    ): Promise<EvidenceAttachment[]> {
        const path = `service-recipients/${serviceRecipientId}/evidence/attachments/`;
        return this.apiClient.get<EvidenceAttachment[]>(path);
    }

    public findExternalFlagsBySourceAndRef(
        externalSourceName: string,
        externalRef: string
    ): Promise<FlagEvidenceDto[]> {
        return this.apiClient.get<FlagEvidenceDto[]>(
            encodeURI(`clients/flags/fromExternal/${externalSourceName}/${externalRef}`)
        );
    }

    public fetchStatusAreaEvidenceFlags(
        sessionData: SessionData,
        serviceRecipientId: number,
        serviceType: ServiceType
    ): Promise<FlagArea[]> {
        let flagSnapshotsQ: Promise<FlagArea[]> = Promise.resolve([]);

        const evidencePagesForFlagStatusArea = this.getFlagEvidencePagesForStatusArea(serviceType);

        evidencePagesForFlagStatusArea.map(td => {
            const def = EvidenceDef.fromTaskName(sessionData, serviceType, td.getName());
            const taskNameType = EvidenceDef.taskEvidenceType(sessionData, td.getName());

            // we've commented support showing on StatusPanel/grabsheet in 9d2e5076
            // because it presumably never worked - it's referring to getQuestionnaireSnapshotRepository()
            // TODO ?? getSupportSmartStepsSnapshotRepository()
            /*if (taskNameType == "EVIDENCE_SUPPORT") {
                flagSnapshotsQ = flagSnapshotsQ.then(snapshots =>
                    services
                        .getQuestionnaireSnapshotRepository()
                        .findLatestFlagsByServiceRecipientIdAndEvidenceGroupKey(
                            serviceRecipientId,
                            def.getEvidenceGroup()
                        )
                        .then(snapshotsMore => {
                            const snapshotWithDef: FlagArea = {
                                label: td.getDisplayName() || "",
                                evidenceDef: def,
                                flagSnapshots: snapshotsMore
                            };
                            return snapshots.concat(snapshotWithDef);
                        })
                );
            }*/
            if (taskNameType == "EVIDENCE_QUESTIONNAIRE") {
                flagSnapshotsQ = flagSnapshotsQ.then(snapshots =>
                    this.questionnaireRepository
                        .findLatestFlagsByServiceRecipientIdAndEvidenceGroupKey(
                            serviceRecipientId,
                            def.getEvidenceGroup()
                        )
                        .then(snapshotsMore => {
                            const snapshotWithDef: FlagArea = {
                                label: td.getDisplayName() || "",
                                evidenceDef: def,
                                flagSnapshots: snapshotsMore
                            };
                            return snapshots.concat(snapshotWithDef);
                        })
                );
            }
        });

        return flagSnapshotsQ;
    }

    private getFlagEvidencePagesForStatusArea(serviceType: ServiceType) {
        return serviceType
            .getTaskDefinitionEntries()
            .filter(td => td.hasSetting("showFlagsOn", "statusArea"))
            .filter(td => td.hasSetting("showFlags", "y"));
    }
}
