import {CipherTextDto} from "@eccosolutions/ecco-crypto";
import {Individual} from "./contact-dto";
import {AclEntryDto} from "./acl-dto";

/** Data-transfer object representing a logged-in User Session. */
export interface UserSession {
    username: string;
    credentialsKey: string;
    userDeviceId: string;
}

/** Data-transfer object representing a User-Device. */
export interface UserDevice {
    /** A string that uniquely identifies the user. */
    username: string;

    /** A UUID that uniquely identifies the User-Device. */
    userDeviceId: string;

    /** The salt used in conjunction with the user's password to derive the
     * user's Credentials Key. */
    credentialsKeySalt: string;

    /** The encrypted User-Device key */
    userDeviceKeyCipherMessage: CipherTextDto;
}


/** Data-transfer object representing a summary of a user.
 * TODO see contact-dto PersonUserSummary which could probably be merged on both server and client sides
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.UserViewModel. */
export interface User {
    /**
     * The user ID.
     */
    userId: number;

    /**
     * username
     */
    username: string;

    /**
     * enabled
     */
    enabled: boolean;

    mfaRequired: boolean;
    mfaValidated: boolean;

    /**
     * created / registered
     */
    registered: string;

    /**
     * lastLoggedIn
     */
    lastLoggedIn: string;

    /**
     * The contact of the user (name, address etc)
     */
    individual: Individual;

    /**
     * The 'rights' or roles the user belongs to
     */
    groups: string[];

    /**
     * client-side generated list of acls for the user
     */
    acls?: AclEntryDto[] | undefined;
}
