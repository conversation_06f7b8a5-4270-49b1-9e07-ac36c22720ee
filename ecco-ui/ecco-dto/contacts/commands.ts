import {BooleanChange, StringChangeOptional, UpdateCommandDto} from "../command-dto";


export interface ContactChangeDto extends UpdateCommandDto {
    contactId: number;

    companyName?: StringChangeOptional | undefined; // May want to split these out

    jobTitle?: StringChangeOptional | undefined;
    title?: StringChangeOptional | undefined;
    firstName?: StringChangeOptional | undefined;
    lastName?: StringChangeOptional | undefined;
    mobileNumber?: StringChangeOptional | undefined;
    phoneNumber?: StringChangeOptional | undefined;
    messageBody?: string | undefined;

    deleted?: BooleanChange | undefined;
}
