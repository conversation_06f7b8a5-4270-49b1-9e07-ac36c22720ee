import {HateoasResource, StringToObjectMap, StringToStringMap} from "@eccosolutions/ecco-common";
import {Address, AddressHistoryDto} from "./contact-dto";

export const BLDG_SYMBOL = "@";

export interface ChargeCategoryCombination {
    chargeCategoryId?: number | undefined;
    chargeNameId?: number | undefined;
}

/** FixedContainerViewModel */
export interface Building extends HateoasResource {
    buildingId: number;
    disabled: boolean;
    /** Parent building id */
    parentId?: number | undefined;
    /** So we can easily say where a shift is for example */
    parentName?: string | undefined;
    serviceRecipientId: number;
    serviceAllocationId: number;
    name: string | null;
    externalRef: string;
    resourceTypeId: number | undefined;
    resourceTypeName: string;
    calendarId: string;
    address?: Address | undefined;
    locationId: number;
    chargeCategoryCombinations?: ChargeCategoryCombination[] | undefined;

    choicesMap?: StringToObjectMap<{id: number; name: string}> | undefined;
    textMap?: StringToStringMap | undefined;
    dateMap?: StringToStringMap | undefined;

    // client-side
    occupancy?: OccupancyDto[] | undefined;
    // client-side
}

export interface OccupancyDto extends AddressHistoryDto {
    /** days valid, to today if validTo is null */
    days: number;
    building?: Building;
}
