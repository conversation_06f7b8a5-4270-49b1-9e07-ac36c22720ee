import {EccoDateTime, Slice} from "@eccosolutions/ecco-common";
import {EvidenceGroup, SupportSmartStepsSnapshotDto} from "../../evidence-dto";
import {RiskFlagsSnapshotDto, RiskGroupEvidenceDto, RiskWorkEvidenceDto} from "../../evidence-risk-dto";
import {RiskEvidenceRepository} from "./RiskEvidenceRepository";
import {ApiClient} from "../../web-api";

export class RiskEvidenceAjaxRepository implements RiskEvidenceRepository {
    constructor(private apiClient: ApiClient) {}

    public findRiskAreaEvidenceByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<RiskGroupEvidenceDto[]> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/riskarea/`;
        return this.apiClient.get<RiskGroupEvidenceDto[]>(apiPath);
    }

    public findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.threat
    ): Promise<RiskFlagsSnapshotDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/snapshots/latest/`;
        return this.apiClient.get<RiskFlagsSnapshotDto>(apiPath);
    }

    public findRiskFlagsSnapshotByClientId(clientId: number): Promise<RiskFlagsSnapshotDto> {
        const apiPath = `clients/${clientId}/evidence/${EvidenceGroup.threat.name}/snapshots/latest/`;
        return this.apiClient.get<RiskFlagsSnapshotDto>(apiPath);
    }

    /**
     * When working online we just want to the latest for the page we are on
     */
    /*public findRiskSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number
    ): Promise<SupportSmartStepsSnapshotDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/threat/snapshots/latest/`;
        return this.apiClient.get<SupportSmartStepsSnapshotDto>(apiPath);
    }*/

    public findRiskSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
        serviceRecipientId: number,
        workDate: EccoDateTime
    ): Promise<SupportSmartStepsSnapshotDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/threat/snapshots/`;
        // the workDate timezone is assumed to be UTC - server-side actionInstanceSnapshotQuery assumes its UTC also
        const query = {
            query: {
                workDate: `${workDate.formatIso8601()}Z`
            }
        };
        return this.apiClient.get<SupportSmartStepsSnapshotDto>(apiPath, query);
    }

    public findRiskWorkByServiceRecipientId(
        serviceRecipientId: number,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined,
        serviceIds?: number[] | undefined
    ): Promise<RiskWorkEvidenceDto[]> {
        let apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.threat.name}/`;
        if (attachmentsOnly) {
            apiPath = apiPath + "attachments/";
        }

        const query = {
            page: pageNumber != null ? pageNumber.toString() : undefined,
            serviceIds: serviceIds ? serviceIds.toString() : undefined
        };

        return this.apiClient
            .get<Slice<RiskWorkEvidenceDto>>(apiPath, {query})
            .then(slice => slice.content);
    }
    public findRiskWorkByClientId(
        clientId: number,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined
    ): Promise<RiskWorkEvidenceDto[]> {
        let apiPath = `clients/${clientId}/evidence/${EvidenceGroup.threat.name}/`;

        if (attachmentsOnly) {
            apiPath = apiPath + "attachments/";
        }

        const options = !pageNumber ? undefined : {query: {page: pageNumber.toString()}};

        return this.apiClient
            .get<Slice<RiskWorkEvidenceDto>>(apiPath, options)
            .then(slice => slice.content);
    }

    /** Just one of findEvidenceByServiceRecipientId */
    public findOneRiskWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        workUuid: string
    ): Promise<RiskWorkEvidenceDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/uuid/${workUuid}/`;
        return this.apiClient.get<RiskWorkEvidenceDto>(apiPath);
    }

    public deleteByWorkUuidWithServiceRecipientId(workUuid: string, serviceRecipientId: number) {
        return this.apiClient.del(`evidence/${EvidenceGroup.threat.name}/${workUuid}/`, {
            serviceRecipientId
        });
    }
}
