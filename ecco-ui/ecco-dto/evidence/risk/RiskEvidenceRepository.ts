import {EvidenceGroup, SupportSmartStepsSnapshotDto} from "../../evidence-dto";
import {RiskFlagsSnapshotDto, RiskGroupEvidenceDto, RiskWorkEvidenceDto} from "../../evidence-risk-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";

export interface RiskEvidenceRepository {
    findRiskWorkByServiceRecipientId(
        serviceRecipientId: number,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined,
        serviceIds?: number[] | undefined
    ): Promise<RiskWorkEvidenceDto[]>;
    findRiskAreaEvidenceByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<RiskGroupEvidenceDto[]>;
    findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroup?: EvidenceGroup | undefined
    ): Promise<RiskFlagsSnapshotDto>;
    findRiskFlagsSnapshotByClientId(clientId: number): Promise<RiskFlagsSnapshotDto>;
    findRiskSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
        serviceRecipientId: number,
        workDate: EccoDateTime
    ): Promise<SupportSmartStepsSnapshotDto>;
    findRiskWorkByClientId(
        clientId: number,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined
    ): Promise<RiskWorkEvidenceDto[]>;
    // @see findRiskEvidenceByServiceRecipientId but one workUuid
    findOneRiskWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        workUuid: string
    ): Promise<RiskWorkEvidenceDto>;
}
