import {QuestionnaireWorkRepository} from "./QuestionnaireWorkRepository";
import {ApiClient} from "../../web-api";
import {EvidenceGroup, FlagEvidenceDto} from "../../evidence-dto";
import {QuestionnaireAnswersSnapshotDto, QuestionnaireWorkDto} from "../questionnaire-dto";

export class QuestionnaireWorkAjaxRepository implements QuestionnaireWorkRepository {
    constructor(private apiClient: ApiClient) {}

    /**
     * Used by HACT to get the history across the whole client's referrals, whilst
     * ignoring the client in the order by newest first: workDate DESC, created DESC
     */
    // see QuestionnaireEvidenceControler.java
    findQuestionnaireEvidenceByClientIdAndEvidenceGroupKey(
        clientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<QuestionnaireWorkDto[]> {
        if (!evidenceGroup) {
            throw new Error("evidenceGroup must be specified");
        }
        const apiPath = `clients/${clientId.toString()}/evidence/questionnaire/${
            evidenceGroup.name
        }/`;

        return this.apiClient.get<QuestionnaireWorkDto[]>(apiPath);
    }

    /**
     * Used for radar charts and support history and ReportDataSourceFactory
     * NB ordered by newest first: workDate DESC, created DESC
     */
    findQuestionnaireWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        attachmentsOnly = false
    ): Promise<QuestionnaireWorkDto[]> {
        let apiPath = `service-recipients/${serviceRecipientId.toString()}/evidence/questionnaire/`;
        if (evidenceGroup) {
            apiPath = apiPath + evidenceGroup.name + "/";
        }
        if (attachmentsOnly) {
            apiPath = apiPath + "attachments/";
        }

        return this.apiClient.get<QuestionnaireWorkDto[]>(apiPath);
    }

    /** Used for questionnaire pages */
    findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<QuestionnaireAnswersSnapshotDto> {
        if (!evidenceGroup) {
            throw new Error("evidenceGroup must be specified");
        }
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/questionnaire/snapshots/${evidenceGroup.name}/`;

        return this.apiClient.get<QuestionnaireAnswersSnapshotDto>(apiPath);
    }

    /** Used for questionnaire pages */
    findLatestFlagsByServiceRecipientIdAndEvidenceGroupKey(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FlagEvidenceDto[]> {
        if (!evidenceGroup) {
            throw new Error("evidenceGroup must be specified");
        }
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/questionnaire/snapshots/${evidenceGroup.name}/flags/`;

        return this.apiClient.get<FlagEvidenceDto[]>(apiPath);
    }
}
