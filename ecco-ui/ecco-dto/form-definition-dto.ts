
import {JSONSchema7} from "json-schema"
import type {UiSchema} from "@rjsf/core";

/** Matches Java class: com.ecco.webApi.forms.FormDefinitionToViewModel */
export interface FormDefinition {
    uuid: string;

    /** Name of this form definition instance */
    name: string;

    orderby: number;

    deleted: boolean;

    definition: {
        /** Meta data to allow for variations or migrating schemas */
        meta: MetaDataSchema;

        /**
         * Top level property starting the schema as expected by meta
         * Currently set as the only one, schema with layout */
        schema: JsonSchemaWithLayout;
    }
}

export interface MetaDataSchema {
    /** Some identifier that we can use to know the format we saved. eg 'json-schema-form-layout' */
    label: string;
    /** Some version identifier */
    version: string;

    /**
     * The link to the formbuilder that generated this schema
     */
    formbuilder?: string | undefined;
}

export interface JsonSchemaWithLayout {
    schema: JSONSchema7;
    uiSchema: UiSchema;
}
