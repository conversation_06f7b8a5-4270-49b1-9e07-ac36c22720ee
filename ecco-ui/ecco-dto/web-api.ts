import {
    bus,
    delay,
    Encrypted,
    FORBIDDEN,
    HateoasResource,
    LinkDto,
    NOT_FOUND,
    Slice,
    WebApiError
} from "@eccosolutions/ecco-common";

import lru = require("tiny-lru");

/** Key to set in sessionStorage if we have recently changed user or logged in, so that we can fetch user info without
 * caching. */
export const RECENT_USER_CHANGE = "recentUserChange";

// @ts-ignore - because tiny-lru.d.ts doesn't match code
const cache = lru(20, 3e3)

export function withCache<T>(key: string, provider: () => T): T {
    let result = cache.get(key);
    if (!result) {
        result = provider();
        cache.set(key, result);
    }
    return result;
}

/** true if not MSIE (IE<=10) or Trident (IE11+).  IE11 is also not working so just don't trust MS */
const _browserCachesAjaxNicely = navigator.appVersion.indexOf("MSIE") == -1
    && navigator.appVersion.indexOf("Trident") == -1;

/** Used to determine if we should cache agressively */
function browserCachesAjaxNicely() {
    return _browserCachesAjaxNicely;
}

export function adminModeEnabled() {
    return sessionStorage['admin-mode'] == 'y';
}

type ToStringable = {toString: () => string}

const ONE_MINUTE = 60 * 1000;
const targetInterval = 65; // millisecs - keeps rate < 1000/min

/** Must delay til this time if we are not already passed it, and add targetInterval for each request we do.
 * We can execute immediately if it is the past.
 * We start out with 6 seconds worth of budget, assuming we don't move page and reset that budget more frequently
 * than that. */
let nextTimeslotMs = Date.now() - ONE_MINUTE / 10;

/**
 * Use as apiClient.get(..args..).catch(handle404AsNullResult) to turn an throw exception as 404 into a null result.
 */
export function handle404AsNullResult(error: any) {
    // NB We did have error: WebApiError | unknown, and if (error instanceof WebApiError)
    //  which is described in DEV-2216 and observable via DEV-2239.
    //  It seemed to be triggered by DEV-2164, but could be fixed by the compilerOptions lib in tsconfig's to 2018 in DEV-2227
    // if 404 then we handle it, else re-throw FIXME: NOT_FOUND should become an error and server should return 204 instead
    if (error.statusCode === NOT_FOUND) {
        // NOTE: to get rid of console error, select "Hide network messages" in debug settings
        return null;
    }
    throw error;
}

function handleAjaxResponse<T>(promise: Promise<Response>, notFoundAsNull = false): Promise<T> {
    return promise
        .catch(reason => {
            console.log("Fetch failed: ", reason);
            throw reason;
        })
        .then(response => {
            return (notFoundAsNull && response.status == 404) || // NOTE: to get rid of console error, select "Hide network messages" in debug settings
                response.type == "opaqueredirect"
                ? Promise.resolve({response, body: null! as T}) // because normally we don't allow null so I'm not changing the typing
                : response
                      .json()
                      .catch(error => {
                          if (response.ok) {
                              // i.e. 200-299
                              console.log(
                                  "Perhaps you need to return valid JSON or a 404 response, not an empty string!"
                              );
                              return null! as T; // :D yes, that really does work ... but we need to remove this and do 404 instead.
                          }
                          throw new WebApiError(response.status, error.toString());
                      })
                      .then((body: T) => ({response, body}));
        })
        .then(({response, body}) => {
            if (response.ok || response.type == "opaqueredirect") return body;
            throw new WebApiError(response.status, body);
        })
}

export function isHateoasResource<T>(o: T | HateoasResource): o is HateoasResource {
    return typeof (o as HateoasResource).links != "undefined"
}

export function getRelation(resource: HateoasResource, relation: string): LinkDto | undefined {
    return resource.links.find(link => link.rel == relation);
}

/**
 * Get all the links for a 'ref' within a resource, and from all resources provided
 */
export function getRelations(resources: HateoasResource[], relation: string): LinkDto[] {
    return resources
        .map(r => r.links)
        .reduce((r, x) => r.concat(x), []) // flatMap
        .filter(link => link.rel == relation);
}

export function fetchAsJson<T>(url: URL, requestInfo?: RequestInit | undefined): Promise<T> {
    // console.debug("fetchAsJson: %o ", url, requestInfo);

    return handleAjaxResponse<T>(fetch(url.toString(), requestInfo));
}

function addSearchQueryParams(
    path: URL,
    params?: Record<string, string | undefined> | undefined
): URL {
    if (params) {
        for (const name in params) {
            // noinspection JSUnfilteredForInLoop - other props would be an error
            if (params[name] !== undefined) {
                path.searchParams.set(name, params[name] as string);
            }
        }
    }
    return path;
}

/** Return a promise for success or otherwise of logging in. Promise should .reject() if result is not success */
export function formLogin(baseWebApiUrl: string, credentials: Credentials): Promise<void> {
    /*
     curl 'http://localhost:8080/ecco-war/nav/secure/j_acegi_security_check'
     -H 'Content-Type: application/x-www-form-urlencoded'
     --data 'j_username=sysadmin&j_password=sunny1'
     */
    // strip api/ onwards, including api/portal - so we get the context
    const pathPre = baseWebApiUrl.substring(0, baseWebApiUrl.indexOf("api"));
    const path = `${pathPre}nav/secure/j_acegi_security_check`;
    const url = new URL(path, baseWebApiUrl);

    const formData = new URLSearchParams(); // fetch() does x-www-form-urlencoded if we supply this as body
    formData.append("j_username", credentials.username);
    formData.append("j_password", credentials.password);

    const requestInfo: RequestInit = {
        method: "POST",
        body: formData,
        redirect: "manual",
        credentials: "include", // FIXME: Credentials and mode don't seem to be changing anything
        mode: "cors" // Notes: same-origin means same SCHEME AND host AND PORT.
        // Same site can be different host within same eTLD+1 see https://web.dev/same-site-same-origin/
    };

    sessionStorage.setItem(RECENT_USER_CHANGE, credentials.username);
    return fetchAsJson<void>(url, requestInfo).catch((e: WebApiError) => {
        if (e.statusCode != 302) {
            // Allow 302 through as that's success for this login
            throw e;
        }
    });
}

function throttle(): Promise<void> {
    const now = Date.now();
    const msUntilAllowed = nextTimeslotMs - now;

    // If earlier than our current window, then reset to minus 1 minute
    if (msUntilAllowed < -ONE_MINUTE) {
        nextTimeslotMs = now - ONE_MINUTE
    }

    nextTimeslotMs += targetInterval; // Increment forwards towards or beyond `now`

    if (msUntilAllowed > 0) {
        console.debug(`throttle: ${msUntilAllowed}ms until allowed`);
    }
    return msUntilAllowed <= 0 ? Promise.resolve() : delay(msUntilAllowed);
}

export function tapResult<T>(after: () => void) {
    return (r: T) => {
        after();
        return r;
    };
}

export function tapError(after: () => void) {
    return (err: any) => {
        after();
        throw err;
    };
}

export interface ProgressState {
    /** Count done out of total */
    count: number
    total: number
}

class RequestTracker {
    private count = 0;
    private total = 0;
    public readonly bus = bus<ProgressState>();

    startedRequest() {
        this.total++
        this.emitEvent();
    }

    completedRequest() {
        this.count++
        this.emitEvent();
    }

    getProgress() {
        return {count: this.count, total: this.total};
    }

    trackPromise<T>(p: Promise<T>) {
        this.startedRequest();
        const after = () => this.completedRequest()
        return p.then(tapResult(after))
            .catch(tapError(after))
    }

    private emitEvent() {
        this.bus.fire(this.getProgress())
    }
}

export class ApiClient {
    /** Fully resolved URL including scheme and port */
    private readonly baseWebApiUrl: string;

    private readonly tracker = new RequestTracker();

    /** Constructs a new Web API Client to communicate with the Web API at
     * the specified URL on the current scheme, host and port.
     *
     * If cachePeriodSecs is specified and is >= 0, then the caching
     * behaviour of the Web API is overridden so that responses will be
     * cached for the specified number of seconds.
     *
     * If cachePeriodSecs is not specified, or if it is < 0, then the
     * default caching behaviour of the Web API is used. */
    constructor(
        webApiAbsPath: string,
        public readonly getCredentials: () => Promise<Credentials>,
        private readonly suppressLogin: () => boolean,
        private readonly options: {
            attemptReAuth?: boolean | undefined;
            cachePeriodSecs?: number | undefined;
            credentials?: Credentials | undefined;
        } = {},
        private readonly login: (
            baseWebApiUrl: string,
            credentials: Credentials
        ) => Promise<void> = formLogin
    ) {
        const url = new URL(webApiAbsPath, location.href);
        this.baseWebApiUrl = url.toString();
    }

    getWebApiUrl = (): string => this.baseWebApiUrl;

    getRequestTracker() {
        return this.tracker;
    }

    /** Creates a new Web API Client that communicates with the Web API at the
     * same URL as this Web API Client, but with the specified caching
     * behaviour.
     *
     * If cachePeriodSecs is specified and is >= 0, then the caching
     * behaviour of the Web API is overridden so that responses will be
     * cached for the specified number of seconds.
     *
     * If cachePeriodSecs is not specified, or if it is < 0, then the
     * default caching behaviour of the Web API is used. */
    public withCachePeriod(cachePeriodSecs: number) {
        const options = {...this.options, cachePeriodSecs};
        return new ApiClient(this.baseWebApiUrl, this.getCredentials, this.suppressLogin, options);
    }

    private addCacheQueryParams(path: URL): URL {
        if (typeof this.options.cachePeriodSecs == "number" && this.options.cachePeriodSecs >= 0) {
            path.searchParams.set("cachePeriodSecs", this.options.cachePeriodSecs.toString());
        }
        return path;
    }

    public getCached<T>(path: string | ToStringable): Promise<T> {
        const pathStr = typeof path === "string" ? path : path.toString();
        return withCache(pathStr, () => this.get<T>(path));
    }

    public get<T>(
        path: string | ToStringable,
        options?: RequestOptions | undefined,
        requestBody?: Object | null | undefined
    ): Promise<T> {
        const pathStr = typeof path === "string" ? path : path.toString();

        let url: URL;
        if (pathStr.indexOf("data") === 0) {
            url = new URL(pathStr);
        } else {
            url = new URL(pathStr, this.baseWebApiUrl);
            url = this.addCacheQueryParams(url);
            url = addSearchQueryParams(url, options && options.query);
        }
        const requestInfo = addCredentials(
            {
                method: "GET",
                cache:
                    !options?.forceReload && browserCachesAjaxNicely() && !adminModeEnabled()
                        ? "default"
                        : "reload",
                ...(requestBody == null ? null : {body: JSON.stringify(requestBody)}),
                headers: {}
            },
            {credentials: this.options.credentials, ...options}
        );

        // NB consider why Promises.all doesn't get throttled
        const throttled = throttle().then(() => {
            const response: Promise<T> = fetchAsJson(url, requestInfo);
            return (options && options.attemptReAuth) ||
                (this.options && this.options.attemptReAuth)
                ? response.catch(e => this.handleAuthFailure(e, () => this.get<T>(path, options)))
                : response;
        });
        return this.tracker.trackPromise(throttled);
    }

    public secureGetSlice<T extends Encrypted<any, any> | Encrypted<any, any>[]>(
        userDeviceId: string,
        path: string,
        options?: RequestOptions | undefined
    ): Promise<T> {
        return this.secureGet(userDeviceId, path, options).then(slice => {
            const convToSlice = <Slice<T>>(<any>slice);
            return <T>(<any>convToSlice.content);
        });
    }
    public secureGet<T extends Encrypted<any, any> | Encrypted<any, any>[]>(
        userDeviceId: string,
        path: string | ToStringable,
        options?: RequestOptions | undefined
    ): Promise<T> {
        const pathStr = typeof path === "string" ? path : path.toString();

        const securePath = new URL(`secure/query/${escape(userDeviceId)}/api/`, this.baseWebApiUrl);
        let fullPath = new URL(pathStr, securePath);
        // const requestInfo = requestOptionsToAjaxSettings(options);
        fullPath = addSearchQueryParams(fullPath, options && options.query);

        return fetchAsJson(fullPath); //, requestInfo);
    }

    /**
     *
     * @param path
     * @param requestBody - if FormData, then post will result in multipart/form-data, else we encode as JSON
     * @param attemptReAuth if true, show login dialog if we get unauthorized error
     * @param options
     */
    public post<T>(
        path: string | ToStringable,
        requestBody: {} | FormData | null,
        attemptReAuth = true,
        options?: RequestOptions | undefined
    ): Promise<T> {
        const pathStr = typeof path === "string" ? path : path.toString();
        const requestInfo = addCredentials(
            {
                method: "POST",
                body: requestBody instanceof FormData ? requestBody : JSON.stringify(requestBody),
                headers: {
                    ...(requestBody instanceof FormData ? {} : {"Content-Type": "application/json"})
                }
            },
            {credentials: this.options.credentials, ...options}
        );

        let url = new URL(pathStr, this.baseWebApiUrl);
        url = addSearchQueryParams(url, options && options.query);
        const response = fetchAsJson<T>(url, requestInfo);

        return attemptReAuth
            ? response.catch(e =>
                  this.handleAuthFailure(e, () => this.postWithReAuth<T>(path, requestBody))
              )
            : response;
    }

    /**
     * Post method and if it fails invoke reauthentication by showing a login dialog
     */
    public postWithReAuth<T>(
        path: string | ToStringable,
        requestBody: Object | null,
        options?: RequestOptions | undefined
    ): Promise<T> {
        return this.post<T>(path, requestBody, true, options);
    }

    // 'delete' is a reserved word in javascript pre-ES5, use del
    public del<T>(
        path: string | ToStringable,
        requestBody: Object,
        options?: RequestOptions | undefined
    ): Promise<T> {
        const pathStr = typeof path === "string" ? path : path.toString();

        const requestInfo = addCredentials(
            {
                method: "DELETE",
                body: JSON.stringify(requestBody),
                headers: {
                    "Content-Type": "application/json"
                }
            },
            {credentials: this.options.credentials, ...options}
        );
        let url = new URL(pathStr, this.baseWebApiUrl);
        url = addSearchQueryParams(url, options && options.query);
        return fetchAsJson(url, requestInfo);
    }

    /** If 403, then attempt re-login and call onAuthenticated if we succeed */
    public handleAuthFailure<T>(
        webApiError: WebApiError,
        onAuthenticated: () => Promise<T>
    ): Promise<T> {
        if (
            webApiError.statusCode == FORBIDDEN &&
            !this.suppressLogin() &&
            this.getCredentials &&
            // Only try to login if we get unauthenticated rather than unauthorized (which is insufficient rights)
            webApiError.message.indexOf("unauthenticated") >= 0
        ) {
            return this.getCredentials()
                .then(credentials => this.login(this.baseWebApiUrl, credentials))
                .then(() => onAuthenticated());
        }
        throw webApiError;
    }

    /**
     * Get all the links for a 'ref' within a resource, and from all resources provided
     * Specify T as | null when using with allowNotFound
     */
    public fetchRelations<T = HateoasResource>(
        resources: HateoasResource[],
        relation: string,
        allowNotFound = true
    ): Promise<T[]> {
        const links = getRelations(resources, relation);
        if (!links || links.length == 0) {
            if (allowNotFound) {
                return Promise.resolve([] as T[]);
            }
            throw Error(`Cannot find relations ${relation} in resource`);
        }

        return links
            .map(l => withCache(l.href, () => this.get<T>(l.href)))
            .reduce(
                (chainedQ: Promise<T[]>, currQ: Promise<T>): Promise<T[]> =>
                    Promise.all([chainedQ, currQ]).then(([chained, curr]) =>
                        Promise.resolve(chained.concat(curr))
                    ),
                Promise.resolve([])
            );
    }

    /**
     * Specify T as | null when using with allowNotFound
     */
    public fetchRelation<T = HateoasResource>(
        resource: HateoasResource,
        relation: string,
        allowNotFound?: true | undefined
    ): Promise<T> {
        const link = getRelation(resource, relation);
        if (!link) {
            if (allowNotFound) {
                return Promise.resolve(null!);
            }
            throw Error(`Cannot find relation ${relation} in ${JSON.stringify(resource.links)}`);
        }
        return withCache(link.href, () => this.get(link.href));
    }

    public promiseOfArray<R, T>(items: R[], promiseFactory: (item: R) => Promise<T>): Promise<T[]> {
        return items
            .map(promiseFactory)
            .reduce(
                (chainedQ: Promise<T[]>, currQ: Promise<T>): Promise<T[]> =>
                    Promise.all([chainedQ, currQ]).then(([chained, curr]) =>
                        Promise.resolve(chained.concat(curr))
                    ),
                Promise.resolve([])
            );
    }

    public flattenPromises<T>(promises: Promise<T[]>[]): Promise<T[]> {
        return Promise.all(promises).then(data => {
            return data.reduce((r, x) => r.concat(x), []); // flatMap
        });
    }
}

/** Optional parameters for Web API requests. */
export interface RequestOptions {
    /** Credentials for HTTP Basic authentication. */
    credentials?: Credentials | undefined;

    /** Force reload of this resource ignoring anything already cached */
    forceReload?: boolean | undefined;

    /** If false a 403 error ripples through.  If true, getCredentials is called to get credentials */
    attemptReAuth?: boolean | undefined;

    /** Optional query/search params for the URL */
    query?: Record<string, string | undefined> | undefined;
}

/** Credentials for HTTP Basic authentication. */
export interface Credentials {
    /** Username for HTTP Basic authentication. */
    username: string;

    /** Password for HTTP Basic authentication. */
    password: string;
}


/**
 * see com.ecco.webApi.viewModels.ResourceList.java
 */
export interface ResourceList<T extends HateoasResource> {
    data: T[];
    links: LinkDto[];
    numPages: number;
    pageSize: number
    numItems: number
}


/**
 * Validate credentials and return value for Authorization header
 */
function credentialsToBasicAuth(credentials: Credentials) {
    if (credentials.username == null) {
        throw new Error("Credentials.username must not be null.");
    }

    if (credentials.password == null) {
        throw new Error("Credentials.password must not be null.");
    }

    if (credentials.username.indexOf(":") != -1) {
        throw new Error("Credentials.username must not contain the character ':'.");
    }

    const userPass = String(credentials.username) + ":" + credentials.password;
    const userPassBase64 = btoa(userPass);
    return "Basic " + userPassBase64;
}

function addCredentials(
    requestInit: RequestInit,
    requestOptions?: RequestOptions | undefined
): RequestInit | undefined {
    const headers = new Headers(requestInit.headers);

    if (requestOptions && requestOptions.credentials) {
        const authHeader = credentialsToBasicAuth(requestOptions.credentials);
        headers.append("Authorization", authHeader);
    }

    const result: RequestInit = {
        ...requestInit,
        credentials: "include", // include - because we want cross-origin
        redirect: "manual",
        headers
    };
    console.debug("addCredentials: result = ", result);
    return result;
}

export function stringifyPossibleError(e: unknown) {
    return JSON.stringify(e, Object.getOwnPropertyNames(e));
}
