import {mount} from "cypress/react";
import * as React from "react";
import {CommentEntry} from "../../CommentEntry";
import {CommentEntryInit, CommentEntryState} from "../../CommentEntryRoot";
import {sessionData, testCommentEntryInit} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components/EccoAPI";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {FC, useState} from "react";
import {Paper, Grid} from "@eccosolutions/ecco-mui";

const CommentEntryTestLayout: FC = () => {
    // create/hold the props
    const init: CommentEntryInit = testCommentEntryInit();
    const [state, setState] = useState<CommentEntryState>(init.initState);
    const stateSetter = (update: Partial<CommentEntryState>) => {
        setState({...state, ...update});
    };

    return (
        <Grid container>
            <Grid item>
                <CommentEntry init={init} state={state} stateSetter={stateSetter} />
            </Grid>
            <Grid item>
                <Paper elevation={2}>
                    <p>state: {JSON.stringify(state)}</p>
                </Paper>
                <Paper elevation={2}>
                    <p>config: {JSON.stringify(sessionData.getDto())}</p>
                </Paper>
            </Grid>
        </Grid>
    );
};

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("CommentEntry tests", () => {
    it("comment", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommentEntryTestLayout />
            </TestServicesContextProvider>
        );

        cy.contains("my comment");
    });
});
