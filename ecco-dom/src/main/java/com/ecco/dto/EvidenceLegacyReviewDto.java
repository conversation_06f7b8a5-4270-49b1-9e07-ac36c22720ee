package com.ecco.dto;

import java.io.Serializable;
import java.util.List;

import com.ecco.dom.EvidenceSupportWork;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;

import com.ecco.dom.Review;

public class EvidenceLegacyReviewDto implements Serializable {

    private List<Review> reviews;

    // a new date for the review
    @DateTimeFormat(style="S-")
    private DateTime customReviewDate;

    // a date from a selection of review dates in the calendar
    @DateTimeFormat(style="S-")
    private DateTime reviewDate;

    private boolean lastReviewCompleted;

    @DateTimeFormat(style="S-")
    private DateTime lastReviewDate;

    // this is model data only
    private List<LocalDate> futureReviewDates;
    private List<EvidenceSupportWork> threatsOutstanding;

    public Review lastIncompleteReview() {
        // NB the ordering of ReviewFilter is 'startDate desc', so its possible we don't get the right
        // reviewId if a review is being done before another - until we force the process
        if (reviews != null && !reviews.isEmpty() && !reviews.get(0).isComplete()) {
            return reviews.get(0);
        }
        return null;
    }

    public LocalDate nextReviewDate() {
        if (futureReviewDates != null && !futureReviewDates.isEmpty()) {
            return futureReviewDates.get(0);
        }
        return null;
    }

    public void setReviews(List<Review> reviews) {
        this.reviews = reviews;
    }
    public List<Review> getReviews() {
        return reviews;
    }
    public DateTime getCustomReviewDate() {
        return customReviewDate;
    }
    public void setCustomReviewDate(DateTime customReviewDate) {
        this.customReviewDate = customReviewDate;
    }
    public DateTime getReviewDate() {
        return reviewDate;
    }
    public void setReviewDate(DateTime reviewDate) {
        this.reviewDate = reviewDate;
    }
    public boolean isLastReviewCompleted() {
        return lastReviewCompleted;
    }
    public void setLastReviewCompleted(boolean lastReviewCompleted) {
        this.lastReviewCompleted = lastReviewCompleted;
    }
    public List<LocalDate> getFutureReviewDates() {
        return futureReviewDates;
    }
    public void setFutureReviewDates(List<LocalDate> futureReviewDates) {
        this.futureReviewDates = futureReviewDates;
    }
    public List<EvidenceSupportWork> getThreatsOutstanding() {
        return threatsOutstanding;
    }
    public void setThreatsOutstanding(List<EvidenceSupportWork> threatsOutstanding) {
        this.threatsOutstanding = threatsOutstanding;
    }
    public DateTime getLastReviewDate() {
        return lastReviewDate;
    }
    public void setLastReviewDate(DateTime lastReviewDate) {
        this.lastReviewDate = lastReviewDate;
    }

}
