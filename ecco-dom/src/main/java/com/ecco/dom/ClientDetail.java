package com.ecco.dom;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.config.dom.ExternalSystem;
import com.ecco.dto.ClientDefinition;
import com.ecco.event.ClientCreatedEvent;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.HibTypeNames;
import com.ecco.dom.contacts.Address;
import lombok.Getter;
import lombok.NoArgsConstructor;

import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;

import javax.persistence.*;

import java.util.Set;

@Getter
@javax.persistence.Entity
@Table(name = "clientdetails")
@NoArgsConstructor
@Configurable
public class ClientDetail extends ClientDetailAbstract implements Created {

    private static final long serialVersionUID = 3L;

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime created;

    private String externalClientRef; // external client reference to another system

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "externalClientSource", insertable = false, updatable = false)
    @Fetch(FetchMode.SELECT)
    private ExternalSystem externalClientSource; // ... and which 'another system' that actually is.

    @Nullable
    @SuppressWarnings({"FieldCanBeLocal", "unused"})
    @Column(name = "externalClientSource", updatable = false)
    private String externalClientSourceName;

    @Type(type= HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String housingBenefit;

    @OneToMany(mappedBy = "client")
    private Set<Referral> referrals;
    @Nullable
    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name = "residenceId", insertable = false, updatable = false)
    private FixedContainer residence;

    @Column
    @Setter
    private Integer residenceId;

    /**
     * Used to create a client in an external system.
     * NB We provide it here, but QLWebServiceClientConnector.updateClient doesn't currently understand: code / nationality / maritalStatus
     * NB We also only supply the address first line and postcode to keep things simple, yet accurate.
     * Also used to load local matches as it passes through the same interface as externals.
     * Also see ClientViewModelDefinitionAdapter.
     */
    public static ClientDefinition toClientDefinition(ClientDetail input) {
        ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create()
                .localClientId(input.getId())
                .localContactId(input.getContact().getId())
                .firstName(input.getContact().getFirstName())
                .lastName(input.getContact().getLastName())
                .genderKey(input.getGender() != null ? input.getGender().getBusinessKey() : null)
                .genderAtBirthKey(input.getGenderAtBirth() != null ? input.getGenderAtBirth().getBusinessKey() : null)
                .birthDate(input.getBirthDate() != null ? input.getBirthDate().toLocalDate() : null)
                .code(input.getCode())
                .ni(input.getNi())
                .firstLanguageKey(input.getFirstLanguage() != null ? input.getFirstLanguage().getBusinessKey() : null)
                .ethnicOriginKey(input.getEthnicOrigin() != null ? input.getEthnicOrigin().getBusinessKey() : null)
                .nationalityKey(input.getNationality() != null ? input.getNationality().getBusinessKey() : null)
                .maritalStatusKey(input.getMaritalStatus() != null ? input.getMaritalStatus().getBusinessKey() : null)
                .religionKey(input.getReligion() != null ? input.getReligion().getBusinessKey() : null)
                .disabilityKey(input.getDisability() != null ? input.getDisability().getBusinessKey() : null)
                .sexualOrientationKey(input.getSexuality() != null ? input.getSexuality().getBusinessKey() : null)
                // NB address is below
                .phoneNumber(input.getContact().getPhoneNumber())
                .mobileNumber(input.getContact().getMobileNumber())
                .email(input.getContact().getEmail())
                .assignedLocationId(input.getResidenceId())
                .assignedLocationName(input.getResidence() != null ? input.getResidence().getName() : null)
                .externalClientRef(input.getExternalClientRef())
                .externalClientSource(input.getExternalClientSourceName());

        Address address = input.getContact().getAddress();
        if (address != null) {
            String[] lines = {address.getLine1(), address.getLine2()}; // only first two lines - see QLClientQueryAdapter.constructAddressArray
            builder = builder
                    .postCode(address.getPostCode())
                    .address(lines);
                    // we keep to first line and post code for accuracy and simplicity
                    //.town()
                    //.county()
        }
        return builder.build();
    }

    {
        // This class is instantiated by Hibernate, so not a managed Spring bean.
        injectServices();
    }

    public Object readResolve() {
        injectServices();
        return this;
    }
    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    public ClientDetail(Individual individual) {
        this.setContact(individual);
    }


    @PostPersist
    void notifyClientCreated() {
        messageBus.publishBeforeTxEnd(new ClientCreatedEvent(this, this));
    }

    @Override
    public void setCreated(DateTime created) {
        if (this.created == null) {
            this.created = created;
        }
    }

    /** JSP-settable properties that require getters/etter */
    public void setHousingBenefit(String housingBenefit) {
        this.housingBenefit = housingBenefit;
    }
    public String getHousingBenefit() {
        return this.housingBenefit;
    }

    public void setExternalClientRef(String externalClientRef) {
        this.externalClientRef = externalClientRef;
    }

    public void setExternalClientSource(ExternalSystem externalSystem) {
        this.externalClientSource = externalSystem;
    }

    public void setReferrals(Set<Referral> referrals) {
        this.referrals = referrals;
    }

    public void setExternalClientSourceName(String externalClientSourceName) {
        this.externalClientSourceName = externalClientSourceName;
    }
}
