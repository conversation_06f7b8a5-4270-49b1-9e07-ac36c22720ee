package com.ecco.dom.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import javax.persistence.*;
import java.util.UUID;

/**
 * Commands around creating service recipients.
 */
@Entity
@DiscriminatorValue("createSvcRec") // mimic deleteSvcRec
public class CreateServiceRecipientCommand extends ServiceRecipientCommand {

    // NB we opt not for a database column, because we probably don't need to index on it
    // private String prefix;

    public CreateServiceRecipientCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                         long userId, @NonNull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
        // this.prefix = prefix
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected CreateServiceRecipientCommand() {
        super();
    }

}
