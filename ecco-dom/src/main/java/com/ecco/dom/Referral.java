package com.ecco.dom;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.entity.CodedEntity;
import com.ecco.infrastructure.hibernate.AntiProxyUtils;
import com.ecco.security.SecurityUtil;
import com.ecco.service.ReviewService;
import com.ecco.serviceConfig.dom.GroupSupportActivityType;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusUpdate;
import com.ecco.servicerecipient.ServiceRecipientSource;
import com.querydsl.core.annotations.QueryInit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.*;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.Period;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQueries;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.*;

import static lombok.AccessLevel.NONE;

@Setter
@Getter
@Entity
@Table(name = "referrals")
@NamedQueries({
        @NamedQuery(name = Referral.BULK_UPDATE_SUPPORT_WORKER_QUERY, query = "update Referral set supportWorker = :newContact where supportWorker = :oldContact"),
        @NamedQuery(name = Referral.BULK_UPDATE_REFERRER_QUERY, query = "update Referral set referrer = :newContact where referrer = :oldContact"),
        @NamedQuery(name = Referral.BULK_UPDATE_INTERVIEWER1_QUERY, query = "update Referral set interviewer1 = :newContact where interviewer1 = :oldContact"),
        @NamedQuery(name = Referral.BULK_UPDATE_INTERVIEWER2_QUERY, query = "update Referral set interviewer2 = :newContact where interviewer2 = :oldContact")
})
@NamedNativeQueries(@NamedNativeQuery(name = Referral.BULK_UPDATE_CONTACTS_NATIVE_QUERY, query = "update svcrec_contacts set contactId = :newContactId where contactId = :oldContactId",
        resultClass = Referral.class))
@NamedEntityGraph(name = "referralForReferralsList",
        attributeNodes = @NamedAttributeNode(value = "agency", subgraph = "agencyWithName"),
        subgraphs = @NamedSubgraph(name = "agencyWithName", attributeNodes = @NamedAttributeNode("companyName")))
@Configurable
@Slf4j
@SuppressWarnings({"serial", "JpaAttributeTypeInspection"})
public class Referral extends RequestDeleteEntityImpl<Long> implements EvidenceCapable, Created, CodedEntity, ServiceRecipientCaseStatusUpdate, ServiceRecipientSource {
    private static final String IS_PRIMARY_CHILD_REFERRAL = "isPrimaryChildReferral";
    public static final String BULK_UPDATE_SUPPORT_WORKER_QUERY = "referral.bulkUpdateSupportWorker";
    public static final String BULK_UPDATE_REFERRER_QUERY = "referral.bulkUpdateReferrer";
    public static final String BULK_UPDATE_INTERVIEWER1_QUERY = "referral.bulkUpdateInterviewer1";
    public static final String BULK_UPDATE_INTERVIEWER2_QUERY = "referral.bulkUpdateInterviewer2";
    public static final String BULK_UPDATE_CONTACTS_NATIVE_QUERY = "referral.bulkUpdateContacts";

    {
        // This class is instantiated by Hibernate, so not a managed Spring bean.
        injectServices();
    }

    public Object readResolve() {
        injectServices();
        return this;
    }

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient MessageBus<ApplicationEvent> messageBus;

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient MessageSourceAccessor messagesSourceAccessor;


    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient ReviewService reviewService;

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient ServiceCategorisationRepository serviceCategorisationRepository;

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    private transient ServiceRepository serviceRepository;

    @Id
    @Column(name = "id", nullable = false) // oracle doesn't like using unique=true
    @GeneratedValue(generator = "referralsTableGenerator")
    @TableGenerator(
            name = "referralsTableGenerator", initialValue = 1, pkColumnValue = "referrals",
            allocationSize = 1, table = "hibernate_sequences")
    private Long id = null;

    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.REMOVE})
    @JoinColumn(name = "serviceRecipientId", nullable = false)
    @QueryInit("*.*.*") // *.serviceAllocation used because it won't accept _super
    private ReferralServiceRecipient serviceRecipient;

    // alternative id
    private String code;

    // common info
    // cascade to the client, since some details on the 'details of referral' can update the client code (eg doctors details, keycode - as seen in referralDetails_....jsp)
    // however, the better fix is to save the client and referral on the referralAspectFlow.xml since this causes problems saving referrals after creating or editing a client
    //@ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH}, fetch = FetchType.EAGER)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "clientId")
    @NotFound(action = NotFoundAction.EXCEPTION)
    @QueryInit("*.*")
    private ClientDetail client;

    @ManyToMany(fetch = FetchType.LAZY)
    @BatchSize(size = 10)
    @JoinTable(name = "svcrec_contacts", joinColumns = @JoinColumn(name = "serviceRecipientId"), inverseJoinColumns = @JoinColumn(name = "contactId"))
    private Set<ContactImpl> contacts = new HashSet<>();

    @Column(name = "relationshipToPrimaryId")
    Integer relationshipToPrimaryReferralId;

    /**
     * The relationship of this referral to its primary file.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "relationshipToPrimaryId", insertable = false, updatable = false)
    private ListDefinitionEntry relationshipToPrimaryReferral;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supportWorkerId")
    private Individual supportWorker;

    /* performance dates obtainable
     * referral received -> created
     *     created -> decisionReferralMadeOn (where appropriate - NB this is only populated at 'appropriate referral' stage)
     *          NB acceptedReferral (and receivingService) make up the status and do not have much weight, nor properties client-side (see ReferralSummaryToViewModel)
     *     decisionReferralMadeOn -> decisionDate (where an 'interview' is appropriate)
     *     decisionDate (where an 'interview' is appropriate) OR decisionMadeOn (NB this is populated at 'appropriate service' stage, or any signpost)
     *  -> receivingService
     *
     * statuses obtainable - see also ReferralStatusCommonPredicates
     *     if (acceptedReferral && !decision && !acceptedOnService) then referral acceptance was made
     *     if (!acceptedReferral && decision && !acceptedOnService) then referral signpost was made
     *     if (acceptedReferral && decision && !acceptedOnService) then final signpost was made
     *     if (acceptedReferral && decision && acceptedOnService) then final acceptance was made
     *
     * statuses overall 'signposted'
     *     This logic is similar to, and consistent with, ReferralSummaryView.getStatusMessageKey and ReferralToViewModel AcceptState[]
     *      (because decisionMadeOn is always set alongside decision in Referral.java)
     *     if (decisionMadeOn && !acceptOnService) then it was signposted (NB decisionMadeOn can be when accepted on service but we exclude it)
     *     if !(decisionMadeOn && !acceptOnService) then it was NOT signposted
     * NB 'signposted' is not intended to retain any other status over time - as per ReferraulStatusCommonPredicates 'if we accept then reject, we assume it was a mistake'.
     * However, it does not overwrite the acceptedReferral or decisionReferralMadeOn date should that wish to be queried.
     */
    @DateTimeFormat(style = "S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime receivedDate;


    /**
     * The geographical source of a referral.
     * This is designed to be from a number of hierarchical lists, this value is the innermost value.
     * See ECCO-1400.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "srcGeographicArea")
    private ListDefinitionEntry srcGeographicArea;

    @Column
    private boolean selfReferral;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "sourceTypeId")
//    private ListDefinitionEntry referrerSourceType; // designed as the advert the referrer saw to be pointed to the service

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agencyId")
    private Agency agency; // possible duplication in that the referrer is associated with the agency - but easier this way

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "referrerId")
    private Individual referrer;

    @Override
    public void setReferrerAgency(Agency agency) {
        this.agency = agency;
    }
    @Override
    public void setReferrerIndividual(Individual ind) {
        this.referrer = ind;
    }

    @Lob
    private String referralReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pendingStatusId")
    private ListDefinitionEntry pendingStatus;
    public static String PENDINGSTATUS_LISTNAME = "pendingStatus";

    /**
     * accepted the referral as appropriate
     */
    @Column
    private boolean acceptedReferral;

    @Override
    public LocalDate getReceivedDate() {
        return receivedDate != null ? this.receivedDate.toLocalDate() : null;
    }

    @Override
    public Boolean isAcceptedReferral() {
        return acceptedReferral;
    }

    @DateTimeFormat
    @Column(name="decisionReferralMadeOn")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime decisionReferralMadeOnDT;

    public void setDecisionReferralMadeOn(DateTime decisionReferralMadeOn) {
        this.decisionReferralMadeOnDT = decisionReferralMadeOn;
    }
    public LocalDate getDecisionReferralMadeOn() {
        return DateTimeUtils.convertFromUtcToUsersLocalDate(decisionReferralMadeOnDT);
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "deliveredById")
    private Agency deliveredBy;

    @DateTimeFormat(style = "S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime deliveredByStartDate;

    // interview/assessment stuff
    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime firstResponseMadeOn; // typically when setting up an interview [a 'users date', so local date]

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime firstOfferedInterviewDate; // designed to retain the first date offered

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "interviewer1Id")
    private Individual interviewer1;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "interviewer2Id")
    private Individual interviewer2;

    @Column(name="location")
    private String interviewLocation;
    private String interviewersOther;
    @Lob
    private String interviewSetupComments;

    /**
     * Date a decision will be made - this is the interview date
     */
    @DateTimeFormat
    @Column(name = "decisionDate")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime interviewDate;

    private int interviewDna = 0;
    @Lob
    private String interviewDnaComments;
    @Embedded
    @javax.persistence.AttributeOverrides({
            @AttributeOverride(name = "sunday", column = @Column(name = "sunday")),
            @AttributeOverride(name = "monday", column = @Column(name = "monday")),
            @AttributeOverride(name = "tuesday", column = @Column(name = "tuesday")),
            @AttributeOverride(name = "wednesday", column = @Column(name = "wednesday")),
            @AttributeOverride(name = "thursday", column = @Column(name = "thursday")),
            @AttributeOverride(name = "friday", column = @Column(name = "friday")),
            @AttributeOverride(name = "saturday", column = @Column(name = "saturday")),
    })
    private DaysOfWeek meetingDays = new DaysOfWeek(); // the days in the week the client is regularly seen

    @DateTimeFormat(style = "S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime eligibleFundingDate; // a date when funding can be claimed

    //@DateTimeFormat(style="S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentLocalDate")
    private LocalDate fundingReviewDate;

    private boolean acceptedFunding;
    private String fundingPaymentRef;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fundingSourceId2") // easy separation from fundingSourceId which points to contacts table
    private FundingSource fundingSource;

    private BigDecimal fundingAmount;

    private BigDecimal fundingHoursOfSupport;

    @DateTimeFormat(style = "S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime decisionFundingDate;

    /**
     * Whether the client is accepted on the service or signposted
     * NB This is not when a referral is appropriate, and not to do with the interview date ('decisionDate')
     *
     * @see #receivedDate for how this is used
     */
    @Column(name = "decision")
    private boolean finalDecision;

    /**
     * when the final decision was made (either accept on service, or signposted)
     */
    @DateTimeFormat
    @Column(name="decisionMadeOn")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime decisionMadeOnDT;

    public void setDecisionMadeOn(DateTime decisionMadeOn) {
        this.decisionMadeOnDT = decisionMadeOn;
    }
    public LocalDate getDecisionMadeOn() {
        return DateTimeUtils.convertFromUtcToUsersLocalDate(decisionMadeOnDT);
    }

    /**
     * will then be waiting to receive the service
     */
    @Column
    private boolean acceptedOnService;

    @Column
    private boolean signpostedBack;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "signpostedreasonid")
    private ListDefinitionEntry signpostReason;
    public static String SIGNPOSTREASON_LISTNAME = "signpostReason";

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "signpostedCommentId")
    private SignpostComment signpostedComment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "signpostedAgencyId")
    private Agency signpostedAgency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "signpostedServiceId")
    private Service signpostedService;

    /**
     * represents the referral which signposted to this one (internal transfer)
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "signpostedParentReferralId", updatable = false)
    private Referral signpostedParentReferral;

    private int waitingListScore = 0;

    @DateTimeFormat(style = "S-")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime contractStartDate;

    // these fields are duplicated under the projects
    // but we would still want to have an overall start/finish
    // as its useful for reporting not to get overly complex
    // and could be the case that projects aren't accurate (eg they move out for a week, but are still on the service)
    @DateTimeFormat(style = "S-")
    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime receivingServiceDate; // TODO: LocalDate

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime dataProtectionSigned;
    @Column
    private Boolean dataProtectionStatus;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime consentSigned;
    @Column
    private Boolean consentStatus;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreementSigned;
    @Column
    private Boolean agreementStatus;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement2Signed;
    @Column
    private Boolean agreement2Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement3Signed;
    @Column
    private Boolean agreement3Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement4Signed;
    @Column
    private Boolean agreement4Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement5Signed;
    @Column
    private Boolean agreement5Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement6Signed;
    @Column
    private Boolean agreement6Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement7Signed;
    @Column
    private Boolean agreement7Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement8Signed;
    @Column
    private Boolean agreement8Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement9Signed;
    @Column
    private Boolean agreement9Status;

    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime agreement10Signed;
    @Column
    private Boolean agreement10Status;

    // exiting - this is not a signpost
    @DateTimeFormat(style = "S-")
    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime exited;

    @Transient
    private DateTime previous_Exited;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "exitreasonid")
    private ListDefinitionEntry exitReason;
    public static String EXITREASON_LISTNAME = "exitReason";

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "exitCommentId")
    private ExitComment exitComment;

    /**
     * A primaryReferral is one which acts like any other referral in the system. However, the secondary referrals (those with a primary)
     * are hidden from the referral list and reports - for the purpose of associating family with the primary client for support but not being
     * included in statistics. We use the existence of this in ReferralListFilter to determine whether this referral should be shown.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "primaryReferralId")
    private Referral primaryReferral;

    /**
     * A parent indicates:
     *  - parent file has referred to this one - via an 'allocate to' task
     *      - see ReferralTaskAllocateServiceCommandHandler.java ReferralTaskAllocateControl.ts (not using 'openParentReferralInstead')
     *      - where the handler uses ReferralFromViewModel
     *  - parent file can be opened over this one - where relationships are concerned (using 'openParentReferralInstead')
     *      - see ReferralTaskJoinCommandHandler.java and RelationshipNodeContext.ts
     *
     * A parentReferral was used to associate child referrals created through some central processing with its parent.
     * In that, the parent and the children were 'proper' referrals but this allowed us to save evidence against the parent.
     * NB Only evidence was recorded in this way if serviceType.childService = true.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parentReferralId")
    // NB no longer takes part in ACL security
    private Referral parentReferral;

    // NB possibly go for referral-specific classes, but Referral is quite embedded at the moment...
    // the only issue is reporting from stuff in the map

    // 'customData' captures fields in a Lob and avoids us defining many fields for different services (where we don't need to report on them)
    // however 'null' appeared in our text fields not empty ""
    // this is because a HashMap returns null for a non-indexed key
    // the same is true for String fields, but webflow knows its a string, our hashmap contains Object's - so null is 'converted'
    // it would be solved if StringTrimmerEditor ever invoked its getAsText method
    // but that doesn't happen for reasons best left https://jira.springsource.org/browse/SWF-1361 (also see http://forum.springsource.org/archive/index.php/t-60704.html)
    // therefore there are several approaches...
    // 1) hack the conversion process to detect some special case of this map (eg the field name)
    // 2) register a converter between Object to String class - but this would apply to all 'Object's (not more specific cases though)
    // 3) apply custom binding in AbstractMvc as Rossen suggests (webflow binds at the binding stage, mvc binds at the controller stage)
    // 4) create a custom binder - but this requires all fields registering eg http://forum.springsource.org/showthread.php?59042-Custom-Converter-Example
    // 5) apply a custom map to specifically override the get method and determine to return blank for string's
    // 6) create two customData's one for strings, and one for objects
    // some useful breakpoints...org.springframework.webflow.mvc.view.BindingModel line 228/231, and GenericConversionService line 243
    // we opt for 6
    // code for 5) if this is a preferred approach
    @Lob
    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, String> textMap = new HashMap<>();

    @Lob
    @Column(name = "objectMap")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Object> customObjectData = new HashMap<>();

//    @Lob
//    @Column(length=16777216) // to trigger longblob for MySQL
//    private HashMap<String,Object> customObjectData = new HashMap<>();

    @Lob
    @Column(name = "dateMap2")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToLocalDateMap")
    private HashMap<String, LocalDate> dateMap = new HashMap<>();

    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Integer> choicesMap = new HashMap<>();

    @Lob
    private String comments;

    // NOTE: Don't want to delete GSAT so no cascade. See http://stackoverflow.com/a/14605189/1998186
    @ManyToMany(fetch = FetchType.LAZY)
    @BatchSize(size = 20)
    @JoinTable(name = "referral_to_gsat",
            joinColumns = @JoinColumn(name = "referralId"),
            inverseJoinColumns = @JoinColumn(name = "groupsupportactivitytypeId"))
    private List<GroupSupportActivityType> activityInterest;

    public static int daysAttending(DaysOfWeek days) {
        if (days == null) {
            return 0;
        }
        return days.daysAttending();
    }

    @PrePersist
    protected void createServiceRecipientIfNull() {
        // null check as we may have called this earlier to get access to serviceRecipient
        if (this.serviceRecipient == null) {
            this.serviceRecipient = new ReferralServiceRecipient();
            this.serviceRecipient.setReferral(this);
        }
    }

    public Service legacyLoadService() {
        // try to move away from getServiceAllocation()...
        // try to cache, or use AntiProxyUtils.identifier etc (but not in id)
        return serviceRepository.findOneByServiceCategorisationId(serviceRecipient.getServiceAllocationId());
    }

    public int loadConfigServiceTypeId() {
        return serviceRecipient.loadConfigServiceTypeId();
    }

    public Integer getAgeAtReferralReceivedDate() {
        DateTime birthDate = DateTimeUtils.convertToDateTime(client.getBirthDate(), true, DateTimeZone.UTC);
        if (birthDate == null) {
            return -1; // provides a detectable default for when age not specified. i.e. defaults to very young
        }
        Period period = new Period(birthDate, receivedDate);
        return period.getYears();
    }

    public UUID getConsentSignatureId() {
        return serviceRecipient.getConsentSignatureId();
    }

    public UUID getAgreementSignatureId() {
        return serviceRecipient.getAgreementSignatureId();
    }

    public UUID getAgreement2SignatureId() {
        return serviceRecipient.getAgreement2SignatureId();
    }

    public UUID getAgreement3SignatureId() {
        return serviceRecipient.getAgreement3SignatureId();
    }

    @Override
    public DateTime getCreated() {
        return serviceRecipient.getCreated();
    }

    public int getCurrentTaskDefinition() {
        return serviceRecipient == null ? 0 : serviceRecipient.getCurrentTaskIndex();
    }

    public UUID getDataProtectionSignatureId() {
        return serviceRecipient.getDataProtectionSignatureId();
    }

    public LocalDate getDeliveredByStartDateAsLocalDate() {
        return deliveredByStartDate == null ? null : deliveredByStartDate.toLocalDate();
    }

    public String getIdCode() {
        if (StringUtils.isEmpty(code)) {
            if (isNewEntity()) {
                return "";
            }
            String idCode = String.valueOf(getId());
            String lastReferralID = messagesSourceAccessor.getMessage("lastReferralID");
            if (StringUtils.isNotEmpty(lastReferralID)) {
                long lastClientIDnum = Long.parseLong(lastReferralID);
                long thisClientIDnum = lastClientIDnum + getId();
                idCode = String.valueOf(thisClientIDnum);
            }
            return idCode;
        } else {
            return code;
        }
    }

    public boolean getIsPrimaryChildReferral() {
        return Boolean.TRUE.equals(getCustomObjectData().get(IS_PRIMARY_CHILD_REFERRAL));
    }

    @Override
    public LocalDate getReceivingServiceLocalDate() {
        return receivingServiceDate == null ? null : receivingServiceDate.toLocalDate();
    }

    @Override
    public LocalDate getExitedDate() {
        return exited == null ? null : exited.toLocalDate();
    }

    public Long getReferralId() {
        return id;
    }

    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipient.getId();
    }

    @Override
    public String getClientDisplayName() {
        return client.getDisplayName();
    }

    @Override
    public Referral getParentEvidenceCapable() {
        return parentReferral;
    }

    public Long getParentReferralId() {
        return AntiProxyUtils.identifier(parentReferral);
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @Override
    public boolean isPending() {
        return pendingStatus != null;
    }

    public LocalDate getNextDueSlaDate() {
        return serviceRecipient.getNextSlaDueDate();
    }

    public Long getNextDueSlaTaskId() {
        return serviceRecipient.getNextDueSlaTaskId();
    }

    @Override
    public void setCreated(DateTime created) {
        createServiceRecipientIfNull();
        this.serviceRecipient.setCreated(created);
    }

    public void setCurrentTaskDefinition(int currentTaskDefinition) {
        serviceRecipient.setCurrentTaskIndex(currentTaskDefinition);
    }

    public void setIsPrimaryChildReferral(Boolean to) {
        if (to) {
            getCustomObjectData().put(IS_PRIMARY_CHILD_REFERRAL, Boolean.TRUE);
        } else {
            getCustomObjectData().remove(IS_PRIMARY_CHILD_REFERRAL);
        }
    }

    public void setServiceAllocation(ServiceCategorisation serviceAllocation) {
        Assert.notNull(serviceAllocation, "allocation cannot be null");
        createServiceRecipientIfNull();
        serviceRecipient.setServiceAllocation(serviceAllocation);
    }

    @PostLoad
    @PostPersist
    protected void storePreviousExited() {
        this.previous_Exited = exited;
    }

    // used on data import (mimic referralCloseFlow.jsp)
    public void withExitComment(String comment) {
        this.exitComment = new ExitComment();
        this.exitComment.setAuthor(SecurityUtil.getAuthenticatedUser().getContact());
        this.exitComment.setReferral(this);
        this.exitComment.setComment(comment);
    }

    // used on data import (mimic referralCloseFlow.jsp)
    public void withSignpostComment(@Nullable String comment) {
        if (comment == null) {
            this.setSignpostedComment(null);
        } else {
            this.signpostedComment = new SignpostComment();
            this.signpostedComment.setAuthor(SecurityUtil.getAuthenticatedUser().getContact());
            this.signpostedComment.setReferral(this);
            this.signpostedComment.setComment(comment);
        }
    }

    @Override
    public Integer countSiblings() {
        // can't use the simpler repository method since its not in scope (could use findAllServiceRecipientIdsFromClientId)
        return this.getClient().getReferrals().size();
    }

    @Override
    public Identified getGrandParent() {
        return this.getClient();
    }

    /**
     * finalDecision is a maintained property, but silently ignored since its derivable from decisionDate != null
     * TODO change finalDecision into an int of the status - for simplicity
     */
    @Override
    public boolean isFinalDecision() {
        return decisionMadeOnDT != null;
    }

    @Override
    public Boolean isReferralDecision() {
        return decisionReferralMadeOnDT != null;
    }

    public List<LocalDate> getFutureReviews() {
        return reviewService.getNextReviewDates(this.getServiceRecipientId());
    }
}
