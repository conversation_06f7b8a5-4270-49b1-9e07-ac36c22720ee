/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.git-properties")
}

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    implementation("org.liquibase:liquibase-core")
    implementation("com.h2database:h2")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation(project(":ecco-int-api-default"))
    implementation("org.apache.commons:commons-lang3")
    implementation(project(":ecco-int-core"))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

group = "com.ecco.integration"
description = "ecco-int-homemaster"
