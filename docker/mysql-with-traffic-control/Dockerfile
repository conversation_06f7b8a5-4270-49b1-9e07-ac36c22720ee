# Dockerfile
FROM mysql:8.0.41-debian

# Add MySQL APT repository key
RUN apt-key adv --keyserver keyserver.ubuntu.com --recv-keys B7B3B788A8D3785C

# Install iproute2 for the `tc` command (NOTE ; instead of & because update still complains about key)
RUN apt-get update; apt-get install -y iproute2 && rm -rf /var/lib/apt/lists/*

# Copy the latency script into the container
COPY set_latency.sh /usr/local/bin/set_latency.sh
RUN chmod +x /usr/local/bin/set_latency.sh

# Override the entrypoint to include the latency script
ENTRYPOINT ["/usr/local/bin/set_latency.sh"]
CMD ["mysqld"]