package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.ActionDefRepository;
import com.ecco.serviceConfig.repositories.ActionGroupDefRepository;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class ActionDefCommandHand<PERSON> extends BaseCommandHandler<ActionDefCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @NonNull
    private final ActionDefRepository actionDefRepository;
    @NonNull
    private final ActionGroupDefRepository actionGroupDefRepository;

    @Autowired
    public ActionDefCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                   ActionDefRepository actionDefRepository,
                                   ActionGroupDefRepository actionGroupDefRepository) {
        super(objectMapper, configCommandRepository, ActionDefCommandViewModel.class);
        this.actionDefRepository = actionDefRepository;
        this.actionGroupDefRepository = actionGroupDefRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull ActionDefCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                this.addActionDef(auth, viewModel);
                break;

            case BaseCommandViewModel.OPERATION_UPDATE:
                this.updateActionDef(auth, viewModel);
                break;

            case BaseCommandViewModel.OPERATION_REMOVE:
                this.hideActionDef(auth, viewModel);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addActionDef(Authentication auth, ActionDefCommandViewModel cmdVM) {
        Action actionDef = createActionDef(cmdVM);
        actionDef.setUuid(cmdVM.actionDefUuid);
        this.applyChanges(actionDef, cmdVM);
        ActionGroup actionGroupDef = this.actionGroupDefRepository.findOneByUuid(cmdVM.actionGroupDefUuid);
        actionGroupDef.addAction(actionDef);
        this.actionGroupDefRepository.save(actionGroupDef);
    }

    private Action createActionDef(ActionDefCommandViewModel cmdVM) {
        return new Action();
    }

    private void updateActionDef(Authentication auth,  ActionDefCommandViewModel cmdVM) {
        if (!cmdVM.hasChanges()) {
            return;
        }
        Action actionDef = this.actionDefRepository.findOneByUuid(cmdVM.actionDefUuid);
        this.applyChanges(actionDef, cmdVM);
        this.actionDefRepository.save(actionDef);
    }

    private void applyChanges(Action actionDef, ActionDefCommandViewModel cmdVM) {
        if (!cmdVM.hasChanges()) {
            return;
        }
        if (cmdVM.nameChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.nameChange, actionDef.getName(), "name");
            actionDef.setName(cmdVM.nameChange.to);
        }
        if (cmdVM.orderByChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.orderByChange, actionDef.getOrderby(), "orderby");
            actionDef.setOrderby(cmdVM.orderByChange.to);
        }
        if (cmdVM.disabledChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.disabledChange, actionDef.isDisabled(), "disabled");
            actionDef.setDisabled(cmdVM.disabledChange.to);
        }
    }

    private void hideActionDef(Authentication auth,  ActionDefCommandViewModel cmdVM) {
        Action actionDef = this.actionDefRepository.findOneByUuid(cmdVM.actionDefUuid);
        actionDef.setDisabled(true);
        this.actionDefRepository.save(actionDef);
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull ActionDefCommandViewModel viewModel, long userId) {
        return new ActionDefCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
