package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.dom.EvidenceGuidance;
import com.ecco.serviceConfig.repositories.MarkdownRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;

@RestController
public class MarkdownController extends BaseWebApiController {

    private final MarkdownRepository markdownRepository;

    @Autowired
    public MarkdownController(MarkdownRepository markdownRepository) {
        this.markdownRepository = markdownRepository;
    }

    // There was no 'get' as such for evidenceGuidance since it was obtained
    // by the jsp by direct dereference - see evidence/group/questions.jsp
    // but 15.05 screens do uses it? or is that 'outcome.guidance.guidanceAsHtml'?
    @GetJson("/markdown/id/{id}")
    public Map<String, String> getGuidanceAsHtml(@PathVariable long id) {
        log.info("id = {}", id);

        EvidenceGuidance guidance = markdownRepository.findById(id).orElse(null);
        String guidanceAsHtml = guidance == null ? null : guidance.getGuidanceAsHtml();
        return Collections.singletonMap("markdown", guidanceAsHtml);
    }

}
