package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.QuestionAnswerChoice;
import com.ecco.serviceConfig.dom.QuestionnaireCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.QuestionAnswerChoiceRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;

@Component
public class QuestionChoiceCommand<PERSON>and<PERSON> extends BaseCommandHandler<QuestionChoiceCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final QuestionAnswerChoiceRepository questionChoiceRepository;

    @Autowired
    public QuestionChoiceCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                        QuestionAnswerChoiceRepository questionChoiceRepository) {
        super(objectMapper, configCommandRepository, QuestionChoiceCommandViewModel.class);
        this.questionChoiceRepository = questionChoiceRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull QuestionChoiceCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case QuestionnaireBaseCommandViewModel.OPERATION_ADD:
                this.addQuestionChoice(auth, viewModel);
                break;

            case QuestionnaireBaseCommandViewModel.OPERATION_UPDATE:
                this.updateQuestionChoice(auth, viewModel);
                break;

//            case QuestionnaireBaseCommandViewModel.OPERATION_REMOVE:
//                this.disableQuestionChoice(auth, viewModel);
//                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addQuestionChoice(Authentication auth, QuestionChoiceCommandViewModel cmdVM) {
        QuestionAnswerChoice choice = new QuestionAnswerChoice();
        this.applyChanges(choice, cmdVM);

        // only a value change on adding - complex otherwise, and just disable and add another
        if (cmdVM.valueChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.valueChange, choice.getValue(), "value");
            choice.setValue(cmdVM.valueChange.to);
        }

        choice.setValue(""); // required on save, but then update to unique, generated value
        this.questionChoiceRepository.save(choice);
        choice.setValue(choice.getId().toString());
        this.questionChoiceRepository.addToQuestion(cmdVM.questionId, choice.getId());
    }

    private void updateQuestionChoice(Authentication auth,  QuestionChoiceCommandViewModel cmdVM) {
        QuestionAnswerChoice choice = this.questionChoiceRepository.findOne(cmdVM.id.longValue());
        this.applyChanges(choice, cmdVM);
        this.questionChoiceRepository.save(choice);
    }

    private void applyChanges(QuestionAnswerChoice choice, QuestionChoiceCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.nameChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.nameChange, choice.getDisplayValue(), "name");
                choice.setDisplayValue(cmdVM.nameChange.to);
            }
            if (cmdVM.disableChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.disableChange, choice.isDisabled(), "disabled");
                choice.setDisabled(cmdVM.disableChange.to);
            }
        }
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull QuestionChoiceCommandViewModel viewModel, long userId) {
        return new QuestionnaireCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
