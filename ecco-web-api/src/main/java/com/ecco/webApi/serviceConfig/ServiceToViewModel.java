package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Service;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToProjectViewModel;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.util.function.Function;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * WebApi version of ServiceViewModel.
 * NB there is also a domain version of this.
 */
@RequiredArgsConstructor
public final class ServiceToViewModel implements Function<Service, ServiceViewModel> {

    private final ServiceCategorisationToProjectViewModel serviceCategorisationToProjectViewModel;

    @NonNull
    @Override
    public ServiceViewModel apply(@Nullable Service input) {
        if (input == null) {
            throw new NullPointerException("input Service must not be null");
        }

        ServiceViewModel result = new ServiceViewModel();
        result.id = input.getId() == null ? null : input.getId().intValue();
        result.name = input.getName();
        result.serviceTypeId = input.getServiceTypeId();
        result.projects = input.getCategorisations().stream().map(serviceCategorisationToProjectViewModel).filter(Objects::nonNull).collect(toList());
        result.parameters = input.getParameters();
        result.disabled = input.isDisabled();

        return result;
    }

}