package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

/**
 * Base command for configuration operations on questionnaires
 */
public class ServiceTypeChangeCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public String operation;
    @Nullable
    public Integer id;
    @Nullable
    public ChangeViewModel<String> nameChange;
    @Nullable
    public ChangeViewModel<Boolean> childChange;

    public boolean hasChanges() {
        return nameChange != null || childChange != null;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected ServiceTypeChangeCommandViewModel() {
        super();
    }

    public ServiceTypeChangeCommandViewModel(@NonNull String operation, Integer id) {
        super(UriComponentsBuilder
                .fromUriString("service-config/servicetype/")
                .toUriString());
        this.operation = operation;
        this.id = id;
    }

}
