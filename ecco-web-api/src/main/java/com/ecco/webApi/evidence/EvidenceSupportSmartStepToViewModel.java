package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceAction;
import com.ecco.dom.EvidenceSupportAction;
import java.util.function.Function;
import org.joda.time.DateTimeZone;

import org.jspecify.annotations.Nullable;

public class EvidenceSupportSmartStepToViewModel implements Function<EvidenceSupportAction, EvidenceSmartStepViewModel> {

    // also see SmartStepSummaryToViewModel.applyCommonBase(viewModel, input);
    public static void applyCommonBase(BaseActionViewModel viewModel, EvidenceAction input) {
        viewModel.id = input.getId();
        viewModel.workId = input.getWorkId();
        viewModel.workDate = input.getWorkDate().toLocalDateTime();
        viewModel.name = input.getAction().getName();
        viewModel.goalName = input.getGoalName();
        viewModel.goalPlan = input.getGoalPlan();
        viewModel.actionInstanceUuid = input.getActionInstanceUuid();
        viewModel.parentActionInstanceUuid = input.getParentActionInstanceUuid();
        viewModel.hierarchy = input.getHierarchy();
        viewModel.position = input.getPosition();
        viewModel.actionId = input.getActionId();
        //TODO viewModel.actionGroupId = input.getAction().getRisk().getId();
        //TODO viewModel.outcomeId = input.getAction().getRisk().getOutcome().getId();
        viewModel.status = input.getStatus();
        viewModel.score = input.getScore();
        viewModel.statusChange = input.isStatusChange();
        viewModel.statusChangeReasonId = input.getStatusChangeReason() == null ? null : input.getStatusChangeReason().getId();
        // FIXME: The nasty workaround below should be fixed by using localdate only
        viewModel.targetDateTime = input.getTarget() == null
                ? null
                : input.getTarget().withZone(DateTimeZone.UTC).toLocalDateTime();
        viewModel.expiryDate = input.getExpiryDate() == null
                ? null
                : input.getExpiryDate().withZone(DateTimeZone.UTC).toLocalDate();
    }

    @Override
    @Nullable
    public EvidenceSmartStepViewModel apply(@Nullable EvidenceSupportAction input) {
        if (input == null) {
            throw new NullPointerException("input Referral must not be null");
        }

        EvidenceSmartStepViewModel viewModel = new EvidenceSmartStepViewModel();
        EvidenceSupportSmartStepToViewModel.applyCommonBase(viewModel, input);
        viewModel.targetSchedule = input.getTargetSchedule();
        return viewModel;
    }

}
