package com.ecco.webApi.evidence;

import com.ecco.evidence.dom.Signature;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

public final class SignatureToViewModel implements Function<Signature, SignatureViewModel> {
    @Nullable
    @Override
    public SignatureViewModel apply(Signature input) {
        SignatureViewModel viewModel = new SignatureViewModel();

        viewModel.id = input.getId();
        viewModel.svgXml = input.getSvgXml();
        viewModel.signedDate = input.getDate().toLocalDateTime();
        viewModel.signedFor = input.getIndividual().getDisplayName();
        return viewModel;
    }
}
