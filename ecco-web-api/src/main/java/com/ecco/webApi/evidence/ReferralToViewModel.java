package com.ecco.webApi.evidence;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.*;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView.Support;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.webApi.contacts.AgencyToViewModel;
import com.ecco.webApi.contacts.ContactToViewModel;
import com.ecco.webApi.contacts.IndividualToViewModel;
import com.ecco.webApi.featureConfig.ListDefinitionEntryIdToViewModel;
import com.ecco.webApi.viewModels.ServiceRecipientSourceViewModel;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import org.jspecify.annotations.Nullable;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

@RequiredArgsConstructor
public final class ReferralToViewModel implements Function<Referral, ReferralViewModel> {

    public static class LocalDateToString implements Function<LocalDate, String> {

        DateTimeFormatter formatter = DateTimeFormat.forPattern("dd/MM/yyyy");

        @Override
        public String apply(LocalDate source) {
            return source == null ? null : formatter.print(source);
        }

    }

    private boolean withContacts = false;

    private boolean withReviewDates = false;

    private ServicesProjectsDto restrictedServicesProjectsDto;
    private ListDefinitionEntryIdToViewModel choicesMapToViewModel;
    private final AgencyToViewModel agencyToViewModel;
    private final IndividualToViewModel individualToViewModel;
    private final ContactToViewModel contactToViewModel;
    private final LocalDateToString localDateToString = new LocalDateToString();

    public ReferralToViewModel(ListDefinitionRepository listDefinitionRepository) {
        this.agencyToViewModel = new AgencyToViewModel();
        this.individualToViewModel = new IndividualToViewModel();
        this.contactToViewModel = new ContactToViewModel(agencyToViewModel, individualToViewModel);
        this.choicesMapToViewModel = new ListDefinitionEntryIdToViewModel(listDefinitionRepository);
    }

    @Nullable
    @Override
    public ReferralViewModel apply(@Nullable Referral input) {
        if (input == null) {
            throw new NullPointerException("input Referral must not be null");
        }

        ReferralViewModel result = new ReferralViewModel();
        result.referralId = input.getId();
        result.mapFrom(input.getServiceRecipient());
        result.created = input.getServiceRecipient().getCreated();

        result._readOnly = restrictedServicesProjectsDto != null && !restrictedServicesProjectsDto.canAccess(
                input.getServiceRecipient().getPermissionServiceId(),
                input.getServiceRecipient().getPermissionProjectId());

        result.currentTaskDefinitionIndex = Integer.toString(input.getCurrentTaskDefinition());

        result.srcGeographicAreaId = input.getSrcGeographicArea() == null ? null : input.getSrcGeographicArea().getId();

        result.latestClientStatusId = input.getServiceRecipient().getLatestClientStatusId();

        result.latestClientStatusDateTime = input.getServiceRecipient().getLatestClientStatusDateTime();

        result.latestClientStatusOkayDateTime = input.getServiceRecipient().getLatestClientStatusOkayDateTime();

        result.referralCode = input.getCode();
        result.textMap = input.getTextMap();

        if (input.getDateMap() != null) {
            Map<String, String> view = Maps.transformValues(input.getDateMap(), localDateToString::apply);
            result.dateMap = new HashMap<>(view);
        }

        if (input.getChoicesMap() != null) {
            result.choicesMap = Maps.transformValues(input.getChoicesMap(), choicesMapToViewModel::apply);
        }

        if (input.getClient() != null) {
            result.clientId = input.getClient().getId();
            result.clientCode = input.getClient().getCode();
            result.clientDisplayName = input.getClient().getDisplayName();
            result.displayName = result.clientDisplayName;
            result.contactId = input.getClient().getContact().getId();

            // NB introduced for api tests only, not mapped in the ts
            // firstName and lastName don't exist on the dto.ts Referral
            // but the api tests require matching properties for items on the ReferralSummary
            result.firstName = input.getClient().getContact().getFirstName();
            result.lastName = input.getClient().getContact().getLastName();
            // NB introduced for ui-tests only, not mapped in the ts
            result.clientFirstName = input.getClient().getContact().getFirstName();
            result.clientLastName = input.getClient().getContact().getLastName();
        }
        else {
            result.clientDisplayName = "[none - partial record]";
        }

        result.referralReason = input.getReferralReason();

        if (input.getPrimaryReferral() != null) {
            result.primaryReferralId = input.getPrimaryReferral().getId();
            if (result.primaryRelationshipId != null) {
                result.primaryRelationshipId = input.getRelationshipToPrimaryReferralId();
            }
        }

        if (input.getParentEvidenceCapable() != null) {
            result.parentReferralId = input.getParentEvidenceCapable().getId();
            result.parentServiceRecipientId = input.getParentEvidenceCapable().getServiceRecipient().getId();
        }
        result.isPrimaryChildReferral = input.getIsPrimaryChildReferral();

        ServiceRecipientSourceViewModel.sourceOfReferral(input.isSelfReferral(), input.getAgency(), input.getReferrer(), result, this.agencyToViewModel);

//        if (input.getCurrentAccommodation() != null) {
//            result.accommodationCategory = input.getCurrentAccommodation().getName();
//        }

        if (input.getFundingSource() != null) {
            result.fundingSource = input.getFundingSource().getName();
            result.fundingSourceId = input.getFundingSource().getId();
        }
        result.fundingPaymentRef = input.getFundingPaymentRef();
        result.fundingAmount = input.getFundingAmount();

        if (input.getDecisionFundingDate() != null) {
            result.fundingDecisionDate = input.getDecisionFundingDate().toLocalDate();
        }
        result.fundingReviewDate = input.getFundingReviewDate();

        result.fundingHoursOfSupport = input.getFundingHoursOfSupport();

        result.fundingAccepted = input.isAcceptedFunding();

        Agency delivererAgency = input.getDeliveredBy();
        if (delivererAgency != null) {
            result.delivererAgencyName = delivererAgency.getName();
            result.deliveredById = delivererAgency.getId();
        }

        // TODO ? Use the locale setting of the authenticated Web API user.
        result.deliveredByStartDate = input.getDeliveredByStartDateAsLocalDate();

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        result.receivedDate = input.getReceivedDate();

        if (input.getConsentSigned() != null) {
            result.consentAgreementStatus = input.getConsentStatus();
            result.consentAgreementDate = input.getConsentSigned().toLocalDateTime();
            result.consentSignedId = input.getConsentSignatureId();
        }

        if (input.getDataProtectionSigned() != null) {
            result.dataProtectionAgreementStatus = input.getDataProtectionStatus();
            result.dataProtectionAgreementDate = input.getDataProtectionSigned().toLocalDateTime(); // UTC time
            result.dataProtectionSignedId = input.getDataProtectionSignatureId();
        }

        if (input.getAgreementSigned() != null) {
            result.agreement1AgreementStatus = input.getAgreementStatus();
            result.agreement1AgreementDate = input.getAgreementSigned().toLocalDateTime();
            result.agreement1SignedId = input.getAgreementSignatureId();
        }
        if (input.getAgreement2Signed() != null) {
            result.agreement2AgreementStatus = input.getAgreement2Status();
            result.agreement2AgreementDate = input.getAgreement2Signed().toLocalDateTime();
            result.agreement2SignedId = input.getAgreement2SignatureId();
        }
        if (input.getAgreement3Signed() != null) {
            result.agreement3AgreementStatus = input.getAgreement3Status();
            result.agreement3AgreementDate = input.getAgreement3Signed().toLocalDateTime();
            result.agreement3SignedId = input.getAgreement3SignatureId();
        }

        // system date - needs translating into user time zone, but only date based for now
        if (input.getFirstResponseMadeOn() != null) {
            result.firstResponseMadeOn = input.getFirstResponseMadeOn().toLocalDate();
        }

        // user date - display as-is
        if (input.getFirstOfferedInterviewDate() != null) {
            result.firstOfferedInterviewDate = input.getFirstOfferedInterviewDate().toLocalDateTime();
        }

        final Individual interviewer1Contact = input.getInterviewer1();
        if (interviewer1Contact != null) {
            result.interviewer1WorkerDisplayName = interviewer1Contact.getDisplayName();
            result.interviewer1ContactId = interviewer1Contact.getId();
        }

        final Individual interviewer2Contact = input.getInterviewer2();
        if (interviewer2Contact != null) {
            result.interviewer2WorkerDisplayName = interviewer2Contact.getDisplayName();
            result.interviewer2ContactId = interviewer2Contact.getId();
        }

        result.interviewLocation = input.getInterviewLocation();

        // user date - display as-is
        if (input.getInterviewDate() != null) {
            result.decisionDate = input.getInterviewDate().toLocalDateTime();
        }

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // system date - needs translating into user time zone, but only date based for now
        result.decisionReferralMadeOn = input.getDecisionReferralMadeOn();

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // system date - needs translating into user time zone, but only date based for now
        result.decisionMadeOn = input.getDecisionMadeOn();

        // user date - display as-is
        if (input.getReceivingServiceDate() != null) {
            result.receivingServiceDate = input.getReceivingServiceDate().toLocalDate();
        }

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // user date - display as-is
        if (input.getExited() != null) {
            result.exitedDate = input.getExited().toLocalDate();
            if (input.getExitReason() != null) {
                result.exitReasonId = input.getExitReason().getId();
                result.exitReason = input.getExitReason().getName();
            }
            if (input.getExitComment() != null) {
                result.exitComment = input.getExitComment().getComment();
            }
        }

        AcceptState[] states = Support.acceptedStates(input.isFinalDecision(), input.getDecisionReferralMadeOn() != null, input.isAcceptedReferral(), input.isAcceptedOnService());
        result.appropriateReferralState = states[0];
        result.acceptOnServiceState = states[1];

        if (input.getSignpostedAgency() != null) {
            result.signpostedAgencyId = input.getSignpostedAgency().getId();
            result.signpostedAgencyName = input.getSignpostedAgency().getName();
        }

        if (input.getSignpostedComment() != null) {
            result.signpostedCommentId = input.getSignpostedComment().getId();
            result.signpostedComment = input.getSignpostedComment().getComment();
        }

        result.signpostedBack = input.isSignpostedBack();

        if (input.getSignpostReason() != null) {
            result.signpostedReason = input.getSignpostReason().getName();
            result.signpostedReasonId = input.getSignpostReason().getId();
        }

        final Individual supportWorker = input.getSupportWorker();
        if (supportWorker != null) {
            result.supportWorkerId = supportWorker.getId();
            result.supportWorkerDisplayName = supportWorker.getDisplayName();
        }

        result.statusMessageKey = Support.getStatusMessageKey(input);
        result.daysAttending = Referral.daysAttending(input.getMeetingDays());
        result.requestedDelete = input.isRequestedDelete();

        if (withContacts) {
            result.contacts = input.getContacts().stream()
                .map(contactToViewModel::apply)
                .collect(toList());
        }

        if (withReviewDates) {
            result.reviewDates = input.getFutureReviews();
        }

        result.firstOfferedInterviewDate = input.getFirstOfferedInterviewDate() == null ? null : input.getFirstOfferedInterviewDate().toLocalDateTime();
        result.firstResponseMadeOn = input.getFirstResponseMadeOn() == null ? null : input.getFirstResponseMadeOn().toLocalDate();
        result.interviewDnaComments = input.getInterviewDnaComments();
        result.interviewSetupComments = input.getInterviewSetupComments();
        result.interviewDna = input.getInterviewDna();

        if (input.getPendingStatus() != null) {
            result.pendingStatusId = input.getPendingStatus().getId();
        }

        result.nextDueSlaDate = input.getNextDueSlaDate();
        result.nextDueSlaTaskId = input.getNextDueSlaTaskId();

        result.waitingListScore = input.getWaitingListScore();

        return result;
    }

    public ReferralToViewModel withContacts() {
        withContacts = true;
        return this;
    }

    public ReferralToViewModel withReviewDates() {
        withReviewDates = true;
        return this;
    }

    /**
     * Calculate _readOnly for this referral, else false is assumed.
     */
    public ReferralToViewModel withRestrictions(ServicesProjectsDto servicesProjectsDto) {
        this.restrictedServicesProjectsDto = servicesProjectsDto;
        return this;
    }

}
