package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;
import java.util.Map;
import java.util.UUID;

public abstract class BaseGoalUpdateCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public String operation;

    public long actionDefId;

    @Nullable
    public String eventId;

    public UUID workUuid;
    @Nullable
    public ChangeViewModel<Integer> statusChange;

    @Nullable
    public Boolean forceStatusChange;

    /**
     * The expiry date, that this action is due by.
     * When targetScheduleChange exists or is already set, this implies the first
     * due date of that schedule.
     */
    @Nullable
    public ChangeViewModel<LocalDate> expiryDateChange;

    /**
     * The next date (soon to be date-localtime), that this action is due by.
     * When targetScheduleChange exists or is already set, this implies the first
     * due date of that schedule.
     */
    @Nullable
    public ChangeViewModel<LocalDate> targetDateChange;

    /**
     * A CRON (-like) expression that represents how to calculate the
     * next targetDate when the current one is "achieved".  The existence of this
     * schedule means that the entry is recorded as "achieved and still relevant".
     * Format is "days:Mo,Tu,We times:12:00,15:00 end:date"
     * days:* means every day.  Times:* means any time that day (e.g. yoga, meditation)
     * end is the iso date of the end of the schedule, inclusive
     * See Schedule.java and CronScheduleStatus.tsx.
     */
    @Nullable
    public ChangeViewModel<String> targetScheduleChange;

    @Nullable
    public ChangeViewModel<String> goalNameChange;
    @Nullable
    public ChangeViewModel<String> goalPlanChange;
    @Nullable
    public ChangeViewModel<Integer> scoreChange;
    @Nullable
    public Map<String, ChangeViewModel<String>> annotationChange;
    @NonNull
    public UUID actionInstanceUuid;
    @Nullable
    public UUID parentActionInstanceUuid;
    @Nullable
    public ChangeViewModel<Short> hierarchyChange;
    @Nullable
    public ChangeViewModel<String> positionChange;

    /** For Jackson etc */
    @Deprecated
    BaseGoalUpdateCommandViewModel() {
        super();
    }

    public BaseGoalUpdateCommandViewModel(@NonNull UUID workUuid,
                                          int serviceRecipientId,
                                          @NonNull EvidenceGroup evidenceGroup,
                                          @NonNull EvidenceTask evidenceTask,
                                          long actionDefId, @NonNull UUID actionInstanceUuid, @Nullable UUID parentActionInstanceUuid) {
        super(UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/goals/{actionDefId}/")
                .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName(), actionDefId)
                .toString(),
            serviceRecipientId);
        this.actionDefId = actionDefId;
        this.workUuid = workUuid;
        this.actionInstanceUuid = actionInstanceUuid;
        this.parentActionInstanceUuid = parentActionInstanceUuid;
    }

    public boolean hasChanges() {
        return statusChange != null | targetDateChange != null
                || targetScheduleChange != null
                || expiryDateChange != null
                || scoreChange != null
                || goalNameChange != null || goalPlanChange != null || annotationChange != null;
    }
}