package com.ecco.webApi.evidence

import com.ecco.dao.DemandScheduleRepository
import com.ecco.dom.CalendarEventSnapshot
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.config.web.ConvertersConfig
import com.ecco.webApi.contacts.IndividualToViewModel
import com.ecco.webApi.contacts.address.AddressToViewModel
import com.ecco.webApi.rota.DemandResourceAssembler
import org.springframework.stereotype.Service
import java.io.IOException
import java.util.function.Function

@Service
class CalendarEventSnapshotToDto(
    val serviceRecipientRepository: ServiceRecipientRepository,
    val demandScheduleRepository: DemandScheduleRepository,
) : Function<CalendarEventSnapshot?, CalendarEventSnapshotDtoResource?> {
    private val contactToViewModel = IndividualToViewModel()
    var objectMapper = ConvertersConfig.getObjectMapper()
    val demandResourceAssembler = DemandResourceAssembler(demandScheduleRepository)

    override fun apply(input: CalendarEventSnapshot?): CalendarEventSnapshotDtoResource? {
        if (input == null) {
            return null
        }
        val vm = CalendarEventSnapshotDtoResource()

        // PLANNED
        // val event = calendarService.findEntry(input.eventUid)
        val sr = serviceRecipientRepository.findOneSummary(input.serviceRecipientId)
        vm.eventUid = input.eventUid
        vm.serviceRecipientId = input.serviceRecipientId
        vm.serviceAllocationId = sr.serviceAllocationId
        vm.demandContact = if (input.demandContact == null) null else contactToViewModel.apply(input.demandContact)
        vm.resourceContact = if (input.resourceContact == null) null else contactToViewModel.apply(input.resourceContact)
        vm.plannedStartInstant = input.plannedStartInstant
        vm.plannedEndInstant = input.plannedEndInstant
        vm.plannedLocation = if (sr.address == null) null else AddressToViewModel().apply(sr.address)
        // vm.title = "";
        vm.demandScheduleId = input.demandScheduleId
        // demandScheduleId can be null - see LoneWorkingCommandAPITests#loneWorkingScenario
        if (input.demandScheduleId != null) {
            vm.demandScheduleDto =
                demandResourceAssembler.apply(demandScheduleRepository.findOne(input.demandScheduleId.toLong()))
        }

        // ACTUAL
        vm.contactId = input.contactId
        vm.startInstant = input.startInstant
        vm.endInstant = input.endInstant
        vm.workUuid = input.workUuid
        input.location?.let {
            try {
                vm.location = objectMapper.readValue(it, LocationViewModel::class.java)
            } catch (e: IOException) {
                throw IllegalStateException(e)
            }
        }
        return vm
    }
}