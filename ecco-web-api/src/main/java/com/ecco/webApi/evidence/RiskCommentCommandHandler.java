package com.ecco.webApi.evidence;

import org.jspecify.annotations.NonNull;

import com.ecco.dao.*;
import com.ecco.dom.*;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.TaskDefinitionService;
import com.ecco.webApi.CommandResult;
import com.ecco.calendar.core.CalendarService;
import org.joda.time.DateTimeZone;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CommentCommand;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;

public class RiskCommentCommandHandler extends EvidenceCommandHandler<CommentCommandViewModel, CommentCommand,
        @NonNull EvidenceParams> {

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final ThreatWorkRepository workRepository;

    @NonNull
    private final ThreatCommentRepository commentRepository;

    @NonNull
    private final EvidenceSupportCommentRepository supportCommentRepository;

    public RiskCommentCommandHandler(ObjectMapper objectMapper,
                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                     ThreatWorkRepository workRepository, ThreatCommentRepository commentRepository,
                                     ServiceRecipientRepository serviceRecipientRepository, ClientRepository clientRepository,
                                     EvidenceSupportCommentRepository supportCommentRepository, ServiceRepository serviceRepository,
                                     ParentChildResolver parentChildResolver,
                                     @NonNull EntityUriMapper entityUriMapper,
                                     @NonNull CalendarService calendarService,
                                     TaskDefinitionService taskDefinitionService) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository, serviceRepository,
                parentChildResolver, calendarService, taskDefinitionService, entityUriMapper,
                CommentCommandViewModel.class);
        this.workRepository = workRepository;
        this.commentRepository = commentRepository;
        this.supportCommentRepository = supportCommentRepository;
        this.clientRepository = clientRepository;
    }

    @NonNull
    @Override
    protected CommentCommand createCommand(Serializable targetId, @NonNull EvidenceParams params, @NonNull String requestBody,
                                           @NonNull CommentCommandViewModel viewModel, long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new CommentCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                params.serviceRecipientId,
                params.evidenceGroupKey,
                params.taskName);
    }

    @Override
    public CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId,
                                        Authentication auth, @NonNull EvidenceParams params, CommentCommandViewModel viewModel) {

        EvidenceTask task = EvidenceTask.fromTaskName(params.taskName);
        EvidenceGroup grp = EvidenceGroup.THREAT;
        Assert.state(grp.getName().equalsIgnoreCase(params.evidenceGroupKey));

        // due to the independent nature of the web-api calls, it could be that an action/goal has saved the work item
        // first, and so we need to load the work item to check it exists and populate it accordingly
        // we create a work item with the required fields, and update below for any latest info
        EvidenceThreatWork existingWork = findOrCreateWork(parentServiceRecipientId, childServiceRecipientId, auth,
                params, task, grp, viewModel);

        // see GoalController which gets the previous snapshot and constructs a new one
        // but we use an existing builder here which works updates things to the latest
        RiskEvidenceBuilder builder = new RiskEvidenceBuilder(existingWork);
        if (viewModel.workDate != null) {
            builder.withWorkDate(viewModel.workDate.to);
        }
        if (viewModel.comment != null) {
            builder.withComment(viewModel.comment.to);
        }
        if (viewModel.minsSpent != null) {
            builder.withMinutesSpent(viewModel.minsSpent.to);
        }
        if (viewModel.commentTypeId != null) {
            builder.withType(viewModel.commentTypeId.to);
        }

        EvidenceThreatWork work = builder.build();
        EvidenceThreatComment newSnapshot = work.getComment();
        BaseServiceRecipient sr = getParentAsRef(parentServiceRecipientId);

        if (viewModel.attachmentIdsToAdd != null) {
            for (Long fileId : viewModel.attachmentIdsToAdd) {
                ServiceRecipientAttachment file = entityManager.getReference(ServiceRecipientAttachment.class, fileId);
                EvidenceThreatAttachment workAttachment = new EvidenceThreatAttachment(file);
                workAttachment.setCreated(viewModel.timestamp.toDateTime());
                work.addAttachment(workAttachment);
            }
        }

        if (viewModel.flagIds != null) {
            if (viewModel.flagIds.added != null && viewModel.flagIds.added.size() > 0) {
                for (int flagDefId : viewModel.flagIds.added) {
                    //ListDefinitionEntry flagDef = entityManager.getReference(ListDefinitionEntry.class, flagDefId);
                    EvidenceThreatFlag flag = new EvidenceThreatFlag(flagDefId);
                    flag.setServiceRecipientId(sr.getId());
                    flag.setCreated(viewModel.timestamp.toDateTime());
                    flag.setValue(true);
                    work.addFlag(flag);
                }
            }
            if (viewModel.flagIds.removed != null && viewModel.flagIds.removed.size() > 0) {
                for (int flagDefId : viewModel.flagIds.removed) {
                    //ListDefinitionEntry flagDef = entityManager.getReference(ListDefinitionEntry.class, flagDefId);
                    EvidenceThreatFlag flag = new EvidenceThreatFlag(flagDefId);
                    flag.setServiceRecipientId(sr.getId());
                    flag.setCreated(viewModel.timestamp.toDateTime());
                    flag.setValue(false);
                    work.addFlag(flag);
                }
            }
        }

        //legacyFlags(viewModel, work, sr);

        // save the comment only
        commentRepository.save(newSnapshot);

        // save the support work after to try to help hibernate save the threat uuid first
        if (viewModel.riskManagementHandled != null) {
            List<UUID> handledSupportWorkIds = viewModel.riskManagementHandled.to;
            for (UUID supportWorkId : handledSupportWorkIds) {
                var supportComment = supportCommentRepository.findOneByWork_Id(supportWorkId).orElse(null);
                if (supportComment != null) {
                    supportComment.setThreatWork(work);
                    supportCommentRepository.save(supportComment);
                }
            }
        }
        return null;
    }

    /** Other commands may create work first, so we may be creating a new work item or updating it.
     * @return
     */
    private EvidenceThreatWork findOrCreateWork(int parentServiceRecipientId, Integer childServiceRecipientId,
                                                Authentication auth, EvidenceParams params, EvidenceTask task, EvidenceGroup grp, CommentCommandViewModel viewModel) {

        EvidenceThreatWork work = workRepository.findById(viewModel.workUuid).orElse(null);

        if (work == null) {
            work = createNewWork(parentServiceRecipientId, childServiceRecipientId, auth, params, task, grp, viewModel);
        }
        return work;
    }


    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     */
    public EvidenceThreatWork createNewWork(int parentServiceRecipientId, Integer childServiceRecipientId,
                                            Authentication auth, EvidenceParams params, EvidenceTask task, EvidenceGroup grp,
                                            CommentCommandViewModel viewModel) {

        ClientDetail client = clientRepository.findOneByServiceRecipientId(parentServiceRecipientId);

        RiskEvidenceBuilder builder = new RiskEvidenceBuilder(parentServiceRecipientId);  // TODO: CHECK THIS ALL
        builder
            .setChild(getChildAsRef(childServiceRecipientId))
            .fromSource(task, grp)
            .setId(viewModel.workUuid)
            .withWorkDate(viewModel.timestamp)
            .withCreatedDate(viewModel.timestamp.toDateTime(DateTimeZone.UTC))
            .withAuthor(auth)
            .withClient(client);
        EvidenceThreatWork work = builder.build();

        // see ThreatEvidenceService.setWork
        // we DON'T currently set the target dates in the calendar, etc
        // entityService allows for 'persist', which threatWorkRepository doesn't
        //uuidEntityService.setEntity(work);
        entityManager.persist(work);
        return work;
    }
}
