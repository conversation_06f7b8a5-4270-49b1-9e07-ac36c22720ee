package com.ecco.webApi.evidence;

import java.math.BigDecimal;
import java.util.List;

/** Base class for (currently referral-related, not HR, etc) work evidence */
abstract public class BaseOutcomeBasedWorkViewModel extends BaseWorkViewModel {

    /**
     * The id for the location list def
     * location is typically 'community' / 'office' etc
     */
    public Integer locationId;

    /**
     * The id for the meetingStatus list def
     * meetingStatus is typically 'client cancelled' / 'did not attend' etc
     */
    public Integer meetingStatusId;

    public BigDecimal mileageTo;

    public BigDecimal mileageDuring;

    /** Minutes spent travelling to/from client */
    public Integer minsTravel;

    /** Action ids of actions that were linked with this work */
    public List<Long> associatedActions;
}