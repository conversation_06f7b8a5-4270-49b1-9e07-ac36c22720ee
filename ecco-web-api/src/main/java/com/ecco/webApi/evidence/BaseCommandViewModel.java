package com.ecco.webApi.evidence;

import java.lang.reflect.Field;
import java.util.UUID;

import org.jspecify.annotations.NonNull;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.base.Throwables;

/**
 * Data-transfer object representing a command to record evidence.
 * In Typescript, see cmd-queue/dto.ts UpdateCommandDto
 *
 * The userId of the authenticated is written to the commands table when a command is written to the server.
 * When reading commands back, a userName field is added so that we can see who did what.  This would not
 * be required here apart from the fact that we use *CommandViewModel for reading back when using our
 * 'Actors' in the web API tests.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
abstract public class BaseCommandViewModel implements BaseCommandValid {

    @NonNull
    public static final String OPERATION_ADD = "add";
    @NonNull
    public static final String OPERATION_UPDATE = "update";
    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    /**
     * A UUID uniquely identifying the command.
     * <p/>
     * Used for de-duplication.
     */
    @NonNull
    public UUID uuid;

    /**
     * The URI identifying the type of command, relative to the Web API
     * root path.
     * <p/>
     * This is the URI to which the command object was POSTed, or to which it
     * shall be POSTed.
     */
    @NonNull
    public String commandUri;

    /**
     * The time at which the command was created on the client. The time zone is always UTC.
     * Maps onto BaseCommand.remoteCreationTime (which in the database is commandCreated).
     * Note there is no server-'created' column in the <>CommandViewModels, since they are
     * one way only - for recording only. Retrieving comands is done by extracting the commands
     * directly and converting to json - see ServiceRecipientController.
     */
    @NonNull
    public Instant timestamp;

    @Nullable
    public Boolean draft;

    /**
     * The URI identifying the type of command, relative to the Web API
     * root path. (e.g. groupsupport/cmd/ will get posted to localhost:8080/ecco-war/api/groupsupport/cmd/).
     */
    protected BaseCommandViewModel(String commandUri) {
        // NB The <>CommandViewModels are mapped from json - see BaseCommandHandler.handleCommand
        // which does not use this constructor, and hence the values of the below are supplied.
        // Only this constructor is used in tests, or data import - see eg SupportEvidenceHandler createCommentCommand
        this.commandUri = commandUri;
        timestamp = Instant.now();
        uuid = UUID.randomUUID();
    }

    /** Utility function to test if this VM contains required fields - just annotate with {@link NonNull}
     *  to mark a field as required */
    public boolean valid() {
        return valid(getClass(), this);
    }

    public static boolean valid(Class c, Object v) {
        try {
            for (Field field : c.getFields()) {
                if (field.isAnnotationPresent(NonNull.class) // Can we annotate for add vs update (i.e. required for add
                        && field.get(v) == null) {
                    LoggerFactory.getLogger(c).error("Nonnull check failed on field {}", field.getName());
                    return false;
                }
                else if (field.isAnnotationPresent(RequiredForOperations.class)
                    && ArrayUtils.contains(field.getAnnotation(RequiredForOperations.class).value(), c.getField("operation").get(v))
                    && field.get(v) == null) {
                    LoggerFactory.getLogger(c).error("RequiredForOperation({}) check failed on field {}", c.getField("operation").get(v), field.getName());
                    return false;

                }
            }
            return true;
        } catch (Exception e) {
            throw Throwables.propagate(e);
        }
    }

}
