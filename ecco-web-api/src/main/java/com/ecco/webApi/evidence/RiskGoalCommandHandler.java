package com.ecco.webApi.evidence;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.ThreatWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.*;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.dom.EvidenceThreatWorkAction;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.ThreatActionRepository;
import com.ecco.evidence.repositories.ThreatWorkActionAssociationRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.jspecify.annotations.NonNull;

import com.ecco.calendar.core.CalendarService;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

public class RiskGoalCommandHandler extends BaseGoalCommandHandler<GoalUpdateCommandViewModel> {

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final ThreatActionRepository actionRepository;

    @NonNull
    private final ThreatWorkRepository workRepository;

    @NonNull
    private final ThreatWorkActionAssociationRepository riskWorkActionAssociationRepository;

    public RiskGoalCommandHandler(ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ThreatActionRepository actionRepository, ThreatWorkRepository workRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ServiceRepository serviceRepository, ClientRepository clientRepository,
            ParentChildResolver parentChildResolver,
            CalendarService calendarService,
            TaskDefinitionService taskDefinitionService,
            @NonNull EntityUriMapper entityUriMapper,
            ThreatWorkActionAssociationRepository riskWorkActionAssociationRepository) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository,
                serviceRepository, parentChildResolver, calendarService, taskDefinitionService,
                entityUriMapper, GoalUpdateCommandViewModel.class);
        this.actionRepository = actionRepository;
        this.workRepository = workRepository;
        this.clientRepository = clientRepository;
        this.riskWorkActionAssociationRepository = riskWorkActionAssociationRepository;
    }

    @Override
    public CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId,
                                        Authentication auth, GoalParams params, GoalUpdateCommandViewModel viewModel) {

        var grp= taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        Assert.state(grp == EvidenceGroup.THREAT);

        // NB mysql column don't hold ms - it does from 5.6.4 if use datetime(3),
        // therefore the inserting of a timestamp is rounded - see applyCommonUpdates,
        // so for rapid updates (eg testing) then this query <= approach doesn't find it
        // so we round up the query to ensure we find it
        // NB however, this just moves the problem as rapid updates could find 2+ rows
        // NB so perhaps we play safe, and just fix the test?
        DateTime created = viewModel.timestamp.toDateTime(DateTimeZone.UTC);//.withMillisOfSecond(0).plusSeconds(1);
        // get latest snapshot that is before or equal to the created timestamp of this entry, so that it is
        // inserted in the correct place
        List<EvidenceThreatAction> snapshots = actionRepository
                .findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo(parentServiceRecipientId, viewModel.actionInstanceUuid,
                        created, PageRequest.of(0, 1));

        boolean statusHasChanged = viewModel.hasChanges();

        EvidenceThreatAction newSnapshot, previousSnapshot;
        if (snapshots.isEmpty()) {
            newSnapshot = EvidenceThreatAction.builder(params.serviceRecipientId, viewModel.actionInstanceUuid,
                    viewModel.parentActionInstanceUuid, params.actionDefId)
                    .withStatusChange(statusHasChanged)
                    .build();
            if (viewModel.statusChange != null && viewModel.statusChange.to != null) {
                newSnapshot.setStatus(viewModel.statusChange.to);
            }
            previousSnapshot = newSnapshot; // we're starting with this.
        }
        else {
            previousSnapshot = snapshots.get(0);
            newSnapshot = EvidenceThreatAction.fromPrevious(previousSnapshot);
        }

        findOrCreateWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel, newSnapshot);

        if (viewModel.hasChanges()) {
            applyUpdatesToNewSnapshot(auth, parentServiceRecipientId, params.actionDefId, viewModel, previousSnapshot, newSnapshot);
            actionRepository.save(newSnapshot);
        } else {
            // this is for a command for marking a SupportWorkAction as relevant
            riskWorkActionAssociationRepository.save(new EvidenceThreatWorkAction(params.actionDefId, viewModel.workUuid));
        }
        return null;
    }

    /** Other commands may create work first, so we may be creating a new work item or updating it.
     */
    private void findOrCreateWork(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth,
                                  GoalParams params, BaseGoalUpdateCommandViewModel viewModel, EvidenceThreatAction newSnapshot) {

        EvidenceThreatWork work = workRepository.findById(viewModel.workUuid).orElse(null);

        if (work == null) {
            work = createNewWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel);
        }
        newSnapshot.setWork(work);
    }


    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     */
    public EvidenceThreatWork createNewWork(int parentServiceRecipientId, Integer childServiceRecipientId,
                                            Authentication auth, GoalParams params,
                                            BaseGoalUpdateCommandViewModel viewModel) {

        // NOTE: This will be null for non-Referral evidence
        ClientDetail client = clientRepository.findOneByServiceRecipientId(parentServiceRecipientId);
        EvidenceTask task = EvidenceTask.fromTaskName(params.getTaskName());
        EvidenceGroup grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        RiskEvidenceBuilder builder = createNewRiskWork(parentServiceRecipientId, childServiceRecipientId, auth, task, grp,
                viewModel.workUuid, viewModel.timestamp, client);
        EvidenceThreatWork work = builder.build();
        entityManager.persist(work);
        return work;
    }

    protected void applyUpdatesToNewSnapshot(Authentication authentication, long referralId, long actionDefId,
            GoalUpdateCommandViewModel vm, EvidenceThreatAction previousSnapshot,
            EvidenceThreatAction newSnapshot) {

        applyCommonUpdates(authentication, vm, previousSnapshot, newSnapshot);

        if (vm.likelihoodChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.likelihoodChange, previousSnapshot.getLikelihood(),
                    "likelihood");
            newSnapshot.setLikelihood(vm.likelihoodChange.to);
        }

        if (vm.severityChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.severityChange, previousSnapshot.getSeverity(),
                    "severity");
            newSnapshot.setSeverity(vm.severityChange.to);
        }

        if (vm.triggerChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.triggerChange, previousSnapshot.getHazard(), "trigger");
            newSnapshot.setHazard(vm.triggerChange.to);
        }

        if (vm.controlChange != null) {
            warnIfPrevValueDoesntMatch(vm.serviceRecipientId, vm.actionDefId, vm.controlChange, previousSnapshot.getIntervention(), "control");
            newSnapshot.setIntervention(vm.controlChange.to);
        }
    }
}
