package com.ecco.webApi.managedvoids;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.security.repositories.IndividualRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

// see also UserListRowResourceAssembler
public class ManagedVoidListRowResourceAssembler extends RepresentationModelAssemblerSupport<ManagedVoid, ManagedVoidViewModel> {

    public static final String REL_EDIT = "edit";

    private final ManagedVoidToViewModel managedVoidToViewModel;
    protected final ObjectMapper objectMapper = ConvertersConfig.getObjectMapper();
    private final ListDefinitionRepository listDefinitionRepository;
    private final IndividualRepository individualRepository;

    public ManagedVoidListRowResourceAssembler(ListDefinitionRepository listDefinitionRepository, IndividualRepository individualRepository) {
        super(ManagedVoidListController.class, ManagedVoidViewModel.class);
        this.listDefinitionRepository = listDefinitionRepository;
        this.individualRepository = individualRepository;
        this.managedVoidToViewModel = new ManagedVoidToViewModel(listDefinitionRepository);
    }

    @Override
    public ManagedVoidViewModel toModel(ManagedVoid input) {
        var result = managedVoidToViewModel.apply(input);

        result.serviceDescription = input.getServiceRecipient().getServiceAllocation().description();
        if (input.getSupportWorkerId() != null) {
            result.supportWorkerDisplayName = individualRepository.findOne(input.getSupportWorkerId()).getDisplayName();
        }

        result.displayName = input.getClientDisplayName();

        return result;

        //addEditLink(resource, vm);
    }

}
