package com.ecco.webApi.singleValue;

import com.ecco.dao.SingleValueHistoryRepository;
import com.ecco.dom.SingleValueHistory;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

import java.util.stream.Stream;

import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.io.IOException;
import java.util.List;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class SingleValueHistoryController extends BaseWebApiController {

    @NonNull
    private final SingleValueHistoryRepository singleValueHistoryRepository;
    @NonNull
    private final SingleValueHistoryToViewModel singleValueHistoryToViewModel;
    @NonNull
    private final SingleValueHistoryCommandHandler singleValueHistoryCommandHandler;


    @Autowired
    public SingleValueHistoryController(
            SingleValueHistoryRepository singleValueHistoryRepository,
            SingleValueHistoryCommandHandler singleValueHistoryCommandHandler) {
        this.singleValueHistoryRepository = singleValueHistoryRepository;
        this.singleValueHistoryCommandHandler = singleValueHistoryCommandHandler;
        this.singleValueHistoryToViewModel = new SingleValueHistoryToViewModel();
    }

    /** Used in tests only */
    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/singlevaluehistory/{key}/", method = GET, produces = APPLICATION_JSON_VALUE)
    public Stream<SingleValueHistoryViewModel> findAllByKey(@PathVariable int serviceRecipientId, @PathVariable String key) {
        List<SingleValueHistory> values = singleValueHistoryRepository.findByServiceRecipientIdAndKeyOrderByValidFromDesc(serviceRecipientId, key);
        return values.stream().map(singleValueHistoryToViewModel);
    }

    /*** Used in reports - so we can load all without filtering when loading additional entities */
    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/singlevaluehistory/", method = GET, produces = APPLICATION_JSON_VALUE)
    public Stream<SingleValueHistoryViewModel> findAll(@PathVariable int serviceRecipientId) {
        List<SingleValueHistory> values = singleValueHistoryRepository.findByServiceRecipientIdOrderByKeyAscValidFromDesc(serviceRecipientId);
        return values.stream().map(singleValueHistoryToViewModel);
    }

    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/singlevaluehistory/{key}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result updateContact(
            SingleValueHistoryParams params,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        return singleValueHistoryCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
