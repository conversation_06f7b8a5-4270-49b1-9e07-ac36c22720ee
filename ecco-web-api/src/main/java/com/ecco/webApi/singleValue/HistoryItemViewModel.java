package com.ecco.webApi.singleValue;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@SuperBuilder
@NoArgsConstructor
@Getter
public class HistoryItemViewModel {

    /**
     * The surrogate id.
     */
    public Integer id;

    /**
     * The service recipient
     */
    public Integer serviceRecipientId;

    /**
     * The date and time when the value applied from
     */
    public LocalDateTime validFrom;

    /**
     * The date and time when the value applied to
     */
    public LocalDateTime validTo;

}
