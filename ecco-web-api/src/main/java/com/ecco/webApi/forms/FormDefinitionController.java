package com.ecco.webApi.forms;

import com.ecco.config.repositories.FormDefinitionRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

import static java.util.stream.Collectors.toList;

/**
 * web api for everything related to form definitions and commands
 */
@RestController
@RequiredArgsConstructor
public class FormDefinitionController extends BaseWebApiController {

    @NonNull
    private final FormDefinitionRepository formDefinitionRepository;

    @NonNull
    private final FormDefinitionCommandHandler formDefinitionCommandHandler;

    @NonNull
    private final FormDefinitionToViewModel toViewModel = new FormDefinitionToViewModel();


    /**
     * Retrieve the list of form definitions
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/formDef/")
    public List<FormDefinitionViewModel> findForms() {
        return formDefinitionRepository.findAllByOrderByOrderbyAsc()
                .map(toViewModel)
                .collect(toList());
    }

    /**
     * Retrieve a single form definition
     */
    @GetJson("/formDef/{formDefUuid}")
    public FormDefinitionViewModel findForm(
            @NonNull @PathVariable UUID formDefUuid) {
        return formDefinitionRepository.findById(formDefUuid)
                .map(toViewModel)
                .orElseThrow(NullPointerException::new);
    }

    @PostJson("/formDef/")
    public Result handleFormDef(@NonNull Authentication authentication,
                                 @NonNull @RequestBody String requestBody) throws IOException {
        return formDefinitionCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
