package com.ecco.webApi.featureConfig;

import java.util.function.Function;

import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;

public class ListDefinitionEntryIdToViewModel implements Function<Integer, ListDefinitionEntryViewModel> {

    private final ListDefinitionRepository listDefRepository;

    public ListDefinitionEntryIdToViewModel(ListDefinitionRepository listDefRepository) {
        this.listDefRepository = listDefRepository;
    }

    @Override
    public ListDefinitionEntryViewModel apply(Integer input) {
        if (input == null) {
            return null;
        }

        ListDefinitionEntryViewModel result = new ListDefinitionEntryViewModel();
        result.id = input;
        if (-1 == input) {
            result.name = "-";
        } else {
            ListDefinitionEntry entry = listDefRepository.findById(input).orElse(null);
            ListDefinitionEntryViewModel.builder()
                    .id(input)
                    .name(entry != null ? entry.getName() : "missing selection entry: " + input.toString())
                    .businessKey(entry != null ? entry.getBusinessKey() : null)
                    .listName(entry != null ? entry.getListName() : null)
                    .order(entry != null ? entry.getOrder() : null)
                    .defaulted(entry != null ? entry.isDefault() : false)
                    .disabled(entry != null ? entry.isDisabled() : false)
                    .metadata(entry != null ? entry.getMetadata() : null)
                    .parentId(entry != null ? entry.getParentId() : null)
                    .build();
        }
        return result;
    }

}
