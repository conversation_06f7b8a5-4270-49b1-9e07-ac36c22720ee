package com.ecco.webApi.featureConfig;

import java.io.IOException;
import java.io.Serializable;

import org.jspecify.annotations.NonNull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.webApi.CommandResult;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.ecco.config.dom.ListDefCommand;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class ListDefCommandHandler extends BaseCommandHandler<ListDefCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ListDefinitionRepository listDefinitionRepository;

    public ListDefCommandHandler(ObjectMapper objectMapper,
                                 @NonNull ListDefinitionRepository listDefinitionRepository,
                                 ConfigCommandRepository configCommandRepository) {
        super(objectMapper, configCommandRepository, ListDefCommandViewModel.class);
        this.listDefinitionRepository = listDefinitionRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull ListDefCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case ListDefCommandViewModel.OPERATION_ADD:
                this.addListDef(auth, viewModel);
                break;

            case ListDefCommandViewModel.OPERATION_UPDATE:
                this.updateListDef(auth, viewModel);
                break;

            case ListDefCommandViewModel.OPERATION_REMOVE:
                throw new UnsupportedOperationException("list def entries should be disabled not removed");

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addListDef(Authentication auth, ListDefCommandViewModel cmdVM) {

        // validate listName and name exist (they are unique together in the database)
        Assert.hasText(cmdVM.nameNew, "the name cannot be empty");
        Assert.isNull(cmdVM.id, "the list def must be null");

        ListDefinitionEntry listDef = new ListDefinitionEntry();
        listDef.setName(cmdVM.nameNew);

        this.applyChanges(listDef, cmdVM);

        // if still null, have a good guess
        if (listDef.getBusinessKey() == null) {
            listDef.setBusinessKey(generateBusinessKey(listDef.getListName(), listDef.getName()));
        }

        this.listDefinitionRepository.save(listDef);
    }

    public static String generateBusinessKey(String listName, String name) {
        String listNameWithName = new StringBuilder()
                .append(listName)
                .append("-")
                .append(name)
                .toString();
        return org.apache.commons.lang3.StringUtils.left(listNameWithName, 128);
    }

    private void updateListDef(Authentication auth, ListDefCommandViewModel cmdVM) {
        Assert.notNull(cmdVM.id, "the list def id cannot be empty when updating");
        Assert.isNull(cmdVM.nameNew, "the view model's nameNew must be empty");

        ListDefinitionEntry listDef = this.listDefinitionRepository.findById(cmdVM.id).orElse(null);
        this.applyChanges(listDef, cmdVM);
        this.listDefinitionRepository.save(listDef);
    }

    private void applyChanges(ListDefinitionEntry listDef, ListDefCommandViewModel cmdVM) {
        if (cmdVM.listName != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.listName, listDef.getListName(), "listName");
            listDef.setListName(cmdVM.listName.to);
        }
        if (cmdVM.entryName != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.entryName, listDef.getName(), "entryName");
            listDef.setName(cmdVM.entryName.to);
        }
        if (cmdVM.businessKey != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.businessKey, listDef.getBusinessKey(), "businessKey");
            listDef.setBusinessKey(cmdVM.businessKey.to);
        }
        if (cmdVM.parentId != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.parentId, listDef.getParentId(), "parentId");
            listDef.setParentId(cmdVM.parentId.to);
        }
        if (cmdVM.defaultForList != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.defaultForList, listDef.isDefault(), "default");
            listDef.setDefault(cmdVM.defaultForList.to);
        }
        if (cmdVM.disabled != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.disabled, listDef.isDisabled(), "disabled");
            listDef.setDisabled(cmdVM.disabled.to);
        }
        if (cmdVM.order != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.order, listDef.getOrder(), "order");
            listDef.setOrder(cmdVM.order.to);
        }

        String metadata = !StringUtils.hasText(listDef.getMetadata())
                ? "{}"
                : listDef.getMetadata();
        ObjectNode node = null;
        try {
            node = (ObjectNode) objectMapper.readTree(metadata);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (cmdVM.metaDisplayName != null) {
            String origDisplayName = node.get("displayName") != null ?
                    node.get("displayName").asText() : null;
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.metaDisplayName, origDisplayName, "metaDisplayName");
            node.put("displayName", cmdVM.metaDisplayName.to);
            listDef.setMetadata(node.toString());
        }

        if (cmdVM.metaValue != null) {
            String origValue = node.get("value") != null ?
                    node.get("value").asText() : null;
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.metaValue, origValue, "metaValue");
            node.put("value", cmdVM.metaValue.to);
            listDef.setMetadata(node.toString());
        }

    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull ListDefCommandViewModel viewModel, long userId) {

        return new ListDefCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

    private String getJsonValue(String metadata, String field) {
        JsonNode node;
        try {
            node = objectMapper.readTree(metadata).get(field);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return node.get(field).asText();
    }

}
