package com.ecco.webApi.groupSupport;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

public class GroupActivityInvitationCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public static final String OPERATION_ADD = "add";

    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    @NonNull
    public static final String OPERATION_UPDATE = "update";

    public static GroupActivityInvitationCommandViewModel add(UUID activityUuid, long referralId) {
        return new GroupActivityInvitationCommandViewModel(OPERATION_ADD, activityUuid, referralId);
    }

    public static GroupActivityInvitationCommandViewModel remove(UUID activityUuid, long referralId) {
        return new GroupActivityInvitationCommandViewModel(OPERATION_REMOVE, activityUuid, referralId);
    }

    public String operation;

    public long activityId;

    public UUID activityUuid;

    public long referralId;

    @Nullable
    public ChangeViewModel<Boolean> invited;

    @Nullable
    public ChangeViewModel<Boolean> attending;

    @Nullable
    public ChangeViewModel<Boolean> attended;

    @Nullable
    public ChangeViewModel<String> evidenceNotes;

    @Nullable
    public ChangeViewModel<Integer> evidenceType;

    public GroupActivityInvitationCommandViewModel() {
    	super();
    }

    private GroupActivityInvitationCommandViewModel(String operation, UUID activityUuid, long referralId) {
        super("activities/commands/invitation/");
        this.operation = operation;
        this.activityUuid = activityUuid;
        this.referralId = referralId;
    }

}
