package com.ecco.webApi.groupSupport;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.groupsupport.*;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.groupsupport.repositories.GroupSupportActivityRepository;
import com.ecco.groupsupport.repositories.GroupSupportCommandRepository;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTimeZone;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController.REL_EDIT;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@Component
public class GroupActivityCommandHandler
    extends BaseCommandHandler<GroupActivityCommandViewModel, Long, GroupSupportCommand, @Nullable Void> {

    @PersistenceContext
    private EntityManager entityManager;
    private final GroupSupportActivityRepository repository;
    private final GroupSupportActivityInvolvementRepository involvementRepository;
    private final ServiceCategorisationRepository serviceCategorisationRepository;

    public GroupActivityCommandHandler(ObjectMapper objectMapper, GroupSupportCommandRepository commandRepository,
                                       GroupSupportActivityRepository repository,
                                       GroupSupportActivityInvolvementRepository involvementRepository,
                                       ServiceCategorisationRepository serviceCategorisationRepository) {
        super(objectMapper, commandRepository, GroupActivityCommandViewModel.class);
        this.serviceCategorisationRepository = serviceCategorisationRepository;
        this.involvementRepository = involvementRepository;
        this.repository = repository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull GroupActivityCommandViewModel viewModel) {
        GroupSupportActivity activity;

        switch (viewModel.operation) {
            case GroupActivityCommandViewModel.OPERATION_CREATE:
                activity = new GroupSupportActivity();
                activity.setDiscriminator_orm(viewModel.discriminator_orm);
                activity.setParentId(viewModel.parentId);
                activity.setUuid(viewModel.activityUuid);
                activity.setCourse(false); // by default
                break;

            case GroupActivityCommandViewModel.OPERATION_UPDATE:
                activity = repository.findOneByUuid(viewModel.activityUuid);
                break;

            case GroupActivityCommandViewModel.OPERATION_REMOVE:
                repository.deleteByUuid(viewModel.activityUuid);
                return null;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }

        if (viewModel.activityTypeId != null) {
            Assert.notNull(viewModel.activityTypeId.to, "Activity must have a type");
            activity.setGroupSupportActivityType(
                    entityManager.getReference(ListDefinitionEntry.class, viewModel.activityTypeId.to));
        }
        if (Boolean.TRUE.equals(viewModel.course)) {
            activity.setCourse(true);
        }
        if (viewModel.description != null) {
            activity.setDescription(viewModel.description.to);
        }
        var hasService = viewModel.serviceId != null && viewModel.serviceId.to != null;
        var hasProject = viewModel.projectId != null && viewModel.projectId.to != null;
        if (hasService || hasProject) {
            ServiceCategorisation sc;
            if (activity.getServiceAllocation() == null) {
                Long projectId = viewModel.projectId != null ? viewModel.projectId.to : null;
                sc = serviceCategorisationRepository.findOneByService_IdAndProject_Id(viewModel.serviceId.to, projectId);
            } else {
                long serviceId = viewModel.serviceId != null ? viewModel.serviceId.to : activity.getServiceAllocation().getServiceId();
                Long projectId = viewModel.projectId != null ? viewModel.projectId.to : activity.getServiceAllocation().getProjectId();
                sc = serviceCategorisationRepository.findOneByService_IdAndProject_Id(serviceId, projectId);
            }
            activity.setServiceAllocation(sc);
        }

        if (viewModel.startDateTime != null) {
            // viewModel.startDateTime has no specific zone
            // this gets assumed into a UTC zone
            activity.setFromDate(viewModel.startDateTime.to == null ? null
                    : viewModel.startDateTime.to.toDateTime(DateTimeZone.UTC));
        }
        if (viewModel.endDate != null) { // TODO: change to storing LocalDate
            activity.setToDate(viewModel.endDate.to == null ? null
                    : viewModel.endDate.to.toDateTimeAtStartOfDay(DateTimeZone.UTC));
        }
        if (viewModel.venueId != null) {
            activity.setVenue(entityManager.getReference(ListDefinitionEntry.class, viewModel.venueId.to));
        }
        if (viewModel.capacity != null) {
            activity.setCapacity(viewModel.capacity.to);
        }
        if (viewModel.duration != null) {
            activity.setMinutes(viewModel.duration.to);
        }

        activity = repository.save(activity);

        // if we are a create, and we are a session from a course, then include all the clients also
        if (viewModel.operation.equals(GroupActivityCommandViewModel.OPERATION_CREATE) && (!activity.getCourse() && activity.getParentId() != null)) {
            var aId = activity.getId();
            var parent = repository.findById(activity.getParentId()).get();
            parent.getActivityReferrals().forEach(ar -> {
                involvementRepository.create(aId, ar.getReferralId());
            });
        }
        return CommandResult.ofLink(linkToApi(methodOn(GroupSupportActivityController.class).findOneActivity(activity.getId())).withRel(REL_EDIT));
    }

    @NonNull
    @Override
    protected GroupActivityCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody, @NonNull GroupActivityCommandViewModel viewModel,
                                                long userId) {
        return new GroupActivityCommand(viewModel.uuid, viewModel.timestamp, userId, viewModel.activityUuid,  requestBody);
    }
}