package com.ecco.webApi.groupSupport;


import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import org.joda.time.DateTime;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

public class GroupActivityAttendanceCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public static final String OPERATION_CREATE = "create";

    @NonNull
    public static final String OPERATION_UPDATE = "update";


    public String operation;

    public UUID activityUuid;

    public Long activityId;

    public Long referralId;

    public DateTime attendedAt;

    @Nullable
    public ChangeViewModel<Boolean> cancelled;

    @Nullable
    public ChangeViewModel<Boolean> attendedAllDay;

    GroupActivityAttendanceCommandViewModel() {
        super("activities/commands/attendance/");
    }

    public GroupActivityAttendanceCommandViewModel cancelledChange(@Nullable Boolean from, @Nullable Boolean to) {
        this.cancelled = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityAttendanceCommandViewModel attendedAllDayChange(@Nullable Boolean from, @Nullable Boolean to) {
        this.attendedAllDay = ChangeViewModel.create(from, to);
        return this;
    }
}
