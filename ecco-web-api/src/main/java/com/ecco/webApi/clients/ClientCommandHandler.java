package com.ecco.webApi.clients;

import com.ecco.dao.commands.ClientCommandRepository;
import com.ecco.dom.clients.ClientCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

public abstract class ClientCommandHandler<VM extends BaseClientCommandViewModel, ENTITY extends ClientCommand, PARAMS>
        extends BaseCommandHandler<VM, Integer, ENTITY, PARAMS> {

    @SuppressWarnings("unchecked")
    public ClientCommandHandler(ObjectMapper objectMapper, ClientCommandRepository clientCommandRepository,
            Class<VM> vmClass) {
        super(objectMapper, (BaseCommandRepository<ENTITY, Integer>) clientCommandRepository, vmClass);
    }
}