package com.ecco.webApi.clients;

import com.ecco.webApi.evidence.BaseCommandViewModel;

public abstract class BaseClientCommandViewModel extends BaseCommandViewModel {

    public Long clientId;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected BaseClientCommandViewModel() {
        super();
    }

    public BaseClientCommandViewModel(String commandUri, Long clientId) {
        super(commandUri);
        this.clientId = clientId;
    }

}