package com.ecco.webApi.clients;

import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.Nullable;

public class ClientAttributeChangeViewModel extends BaseClientCommandViewModel {

    /** JavaBean style path, e.g. "residence" */
    public String attributePath;

    /**
     * e.g. from: "building:23", to: "building:123", or some simple string change
     */
    @Nullable
    public ChangeViewModel<String> change;


    /** For Jackson etc */
    ClientAttributeChangeViewModel() {
        super();
    }

    public ClientAttributeChangeViewModel(long clientId) {
        super(UriComponentsBuilder
            .fromUriString("clients/{clientId}/commands/")
            .buildAndExpand(clientId)
            .toString(),
            clientId);
    }

}
