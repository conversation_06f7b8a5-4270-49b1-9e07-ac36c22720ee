package com.ecco.webApi.monitoring;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.lang.management.ManagementFactory;
import java.util.Set;

import javax.management.MBeanServer;
import javax.management.MalformedObjectNameException;
import javax.management.ObjectName;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dao.security.QueuedCommandRepository;
import com.ecco.infrastructure.health.EndpointStats;
import com.ecco.security.repositories.UserRepository;
import com.ecco.webApi.controllers.BaseWebApiController;

@RestController
public class StatsController extends BaseWebApiController {

    private final Logger log = LoggerFactory.getLogger(getClass());

    private final DbStatsToViewModel dbStatsToViewModel;
    private final QueuedCommandRepository commandRepository;
    private final UserRepository userRepository;
    private final EndpointStats endpointStats;


    @Autowired
    public StatsController(DataSource dataSource, QueuedCommandRepository commandRepository,
            UserRepository userRepository, EndpointStats endpointStats) {
        this.dbStatsToViewModel = new DbStatsToViewModel(dataSource);
        this.commandRepository = commandRepository;
        this.userRepository = userRepository;
        this.endpointStats = endpointStats;
    }

    @RequestMapping(value = "/stats/", method = GET, produces = APPLICATION_JSON_VALUE)
    public StatsViewModel findAll() {
        StatsViewModel result = new StatsViewModel();
        result.dbStats = dbStatsToViewModel.apply(null);
        result.endpointStats = this.endpointStats;
        addMBeanInfo(result);
        return result;
    }

    private void addMBeanInfo(StatsViewModel result) {

        result.threadsHealthy = true;

        MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();

        try {
            ObjectName name = new ObjectName("Catalina:type=ThreadPool,name=*");
            Set<ObjectName> queryNames = mBeanServer.queryNames(name, null);
            queryNames.forEach(objectName -> {
                try {
                    @SuppressWarnings("unused")
                    Integer current = (Integer) mBeanServer.getAttribute(objectName, "currentThreadCount");
                    Integer max = (Integer) mBeanServer.getAttribute(objectName, "maxThreads");
                    if (current != null && (current * 100 / max) > 80) {
                        result.threadsHealthy = false;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (MalformedObjectNameException e) {
            e.printStackTrace();
        }
    }

    private OfflineStatsViewModel getOfflineStats() {
        OfflineStatsViewModel result = new OfflineStatsViewModel();
        result.totalExecuted = commandRepository.countByExecuted(true);
        result.totalNotExecuted = commandRepository.countByExecuted(false);
        return result;
    }

    @RequestMapping(value = "/stats/tests/start/{testClass}/{testMethod}/", method = GET, produces = APPLICATION_JSON_VALUE)
    public void recordTestState(@PathVariable String testClass,
            @PathVariable String testMethod) {
        log.info("======>> Starting test: {} - {} <<======", testClass, testMethod);
    }

    @RequestMapping(value = "/p/licence/", method = RequestMethod.GET)
    public AuditViewModel getAuditViewModel(HttpServletResponse response) {
        cacheModerately(response);
        AuditViewModel result = new AuditViewModel();
        result.offlineStats = getOfflineStats();
        result.activeUsersLast7Days = userRepository.countByLastLoggedInGreaterThanAndEnabledIsTrue(DateTime.now().minusDays(7));
        result.activeUsersLast30Days = userRepository.countByLastLoggedInGreaterThanAndEnabledIsTrue(DateTime.now().minusDays(30));
        return result;
    }
}
