package com.ecco.webApi.monitoring;

import com.ecco.infrastructure.health.EndpointStats;

public class StatsViewModel {

    public DbStatsViewModel dbStats;
    public boolean threadsHealthy;
    public EndpointStats endpointStats;

    /** get overall status - useful for text match on simple New Relic alert to match
     * "healthStatus":"green" */
    public String getHealthStatus() {
        if (dbStats.canConnect && threadsHealthy) {
            return "green";
        }

        return "red";
    }

}
