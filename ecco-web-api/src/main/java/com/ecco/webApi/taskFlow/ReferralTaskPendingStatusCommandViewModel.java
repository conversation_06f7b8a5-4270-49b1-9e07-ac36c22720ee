package com.ecco.webApi.taskFlow;

import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.Nullable;

public class ReferralTaskPendingStatusCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_NAME = "pendingStatus";

    @Nullable
    public ChangeViewModel<Integer> pendingStatus;

    /** For Jackson etc */
    @Deprecated
    ReferralTaskPendingStatusCommandViewModel() {
        super();
    }

    public ReferralTaskPendingStatusCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
    }
}
