package com.ecco.webApi.taskFlow;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dto.ChangeViewModel;
import com.ecco.dto.ClientDefinitionCommandDto;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.jspecify.annotations.Nullable;


@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
public class ReferralTaskClientDetailCommandViewModel extends TaskClientDetailAbstractCommandViewModel {

    // TODO // implements ExternalSource {

    static String TASK_CLIENT_DETAIL = "clientWithContact";

    @Nullable
    public ChangeViewModel<String> housingBenefitChange;

    /**
     * Indicates whether this originates from an external system
     */
    @Nullable
    public Boolean externalSource;

    public ReferralTaskClientDetailCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceR<PERSON><PERSON>ientId, TASK_CLIENT_DETAIL, taskHandle);
    }

    /**
     * Convert the command update to an external system command update.
     * NB This command doesn't have address change information.
     * NB This command does have, if wanted, housingBenefitChange / preferredContactMethodChange
     */
    public static ClientDefinitionCommandDto toClientDefinitionCommand(String externalClientRef,
                                                                       ReferralTaskClientDetailCommandViewModel input,
                                                                       ListDefinitionRepository listDefinitionRepository) {
        var cmd = new ClientDefinitionCommandDto();
        cmd.externalClientRef = externalClientRef;

        // TODO if wanted to update external system
        //  .code(input.getCode())

        if (input.firstNameChange != null) {
            cmd.firstName = input.firstNameChange;
        }
        if (input.lastNameChange != null) {
            cmd.lastName = input.lastNameChange;
        }
        if (input.birthDateChange != null) {
            cmd.birthDate = input.birthDateChange;
        }
        if (input.niChange != null) {
            cmd.ni = input.niChange;
        }
        if (input.genderChange != null) {
            cmd.genderKey = new ChangeViewModel<>();
            cmd.genderKey.from = input.genderChange.from == null
                    ? null : listDefinitionRepository.findById(input.genderChange.from).orElseThrow().getBusinessKey();
            cmd.genderKey.to = input.genderChange.to == null
                    ? null : listDefinitionRepository.findById(input.genderChange.to).orElseThrow().getBusinessKey();
        }
        if (input.genderAtBirthChange != null) {
            cmd.genderAtBirthKey = new ChangeViewModel<>();
            cmd.genderAtBirthKey.from = input.genderAtBirthChange.from == null
                    ? null : listDefinitionRepository.findById(input.genderAtBirthChange.from).orElseThrow().getBusinessKey();
            cmd.genderAtBirthKey.to = input.genderAtBirthChange.to == null
                    ? null : listDefinitionRepository.findById(input.genderAtBirthChange.to).orElseThrow().getBusinessKey();
        }
        if (input.firstLanguageChange != null) {
            cmd.firstLanguageKey = new ChangeViewModel<>();
            cmd.firstLanguageKey.from = input.firstLanguageChange.from == null
                    ? null : listDefinitionRepository.findById(input.firstLanguageChange.from).orElseThrow().getBusinessKey();
            cmd.firstLanguageKey.to = input.firstLanguageChange.to == null
                    ? null : listDefinitionRepository.findById(input.firstLanguageChange.to).orElseThrow().getBusinessKey();
        }
        if (input.ethnicOriginChange != null) {
            cmd.ethnicOriginKey = new ChangeViewModel<>();
            cmd.ethnicOriginKey.from = input.ethnicOriginChange.from == null
                    ? null : listDefinitionRepository.findById(input.ethnicOriginChange.from).orElseThrow().getBusinessKey();
            cmd.ethnicOriginKey.to = input.ethnicOriginChange.to == null
                    ? null : listDefinitionRepository.findById(input.ethnicOriginChange.to).orElseThrow().getBusinessKey();
        }
        if (input.religionChange != null) {
            cmd.religionKey = new ChangeViewModel<>();
            cmd.religionKey.from = input.religionChange.from == null
                    ? null : listDefinitionRepository.findById(input.religionChange.from).orElseThrow().getBusinessKey();
            cmd.religionKey.to = input.religionChange.to == null
                    ? null : listDefinitionRepository.findById(input.religionChange.to).orElseThrow().getBusinessKey();
        }
        if (input.sexualOrientationChange != null) {
            cmd.sexualOrientationKey = new ChangeViewModel<>();
            cmd.sexualOrientationKey.from = input.sexualOrientationChange.from == null
                    ? null : listDefinitionRepository.findById(input.sexualOrientationChange.from).orElseThrow().getBusinessKey();
            cmd.sexualOrientationKey.to = input.sexualOrientationChange.to == null
                    ? null : listDefinitionRepository.findById(input.sexualOrientationChange.to).orElseThrow().getBusinessKey();
        }
        if (input.disabilityChange != null) {
            cmd.disabilityKey = new ChangeViewModel<>();
            cmd.disabilityKey.from = input.disabilityChange.from == null
                    ? null : listDefinitionRepository.findById(input.disabilityChange.from).orElseThrow().getBusinessKey();
            cmd.disabilityKey.to = input.disabilityChange.to == null
                    ? null : listDefinitionRepository.findById(input.disabilityChange.to).orElseThrow().getBusinessKey();
        }
        if (input.nationalityChange != null) {
            cmd.nationalityKey = new ChangeViewModel<>();
            cmd.nationalityKey.from = input.nationalityChange.from == null
                    ? null : listDefinitionRepository.findById(input.nationalityChange.from).orElseThrow().getBusinessKey();
            cmd.nationalityKey.to = input.nationalityChange.to == null
                    ? null : listDefinitionRepository.findById(input.nationalityChange.to).orElseThrow().getBusinessKey();
        }
        if (input.maritalStatusChange != null) {
            cmd.maritalStatusKey = new ChangeViewModel<>();
            cmd.maritalStatusKey.from = input.maritalStatusChange.from == null
                    ? null : listDefinitionRepository.findById(input.maritalStatusChange.from).orElseThrow().getBusinessKey();
            cmd.maritalStatusKey.to = input.maritalStatusChange.to == null
                    ? null : listDefinitionRepository.findById(input.maritalStatusChange.to).orElseThrow().getBusinessKey();
        }

        // NB address, postCode, town, county are NOT part of this command dto

        if (input.phoneNumberChange != null) {
            cmd.phoneNumber = input.phoneNumberChange;
        }
        if (input.mobileNumberChange != null) {
            cmd.mobileNumber = input.mobileNumberChange;
        }
        if (input.emailChange != null) {
            cmd.email = input.emailChange;
        }

        return cmd;
    }

}
