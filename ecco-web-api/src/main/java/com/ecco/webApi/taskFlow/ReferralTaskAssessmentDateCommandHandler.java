package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.NonNull;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import kotlin.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.ReferralService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.util.DateTimeUtils;

import java.util.Optional;

@Component
public class ReferralTaskAssessmentDateCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskAssessmentDateCommandViewModel> {

    @NonNull
    private final ReferralRepository referralRepository;

    @NonNull
    private final ReferralService referralService;

    @NonNull
    private final IndividualRepository individualRepository;

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    public ReferralTaskAssessmentDateCommandHandler(
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull ObjectMapper objectMapper,
            @NonNull WorkflowTaskController workflowTaskController,
            @NonNull ReferralRepository referralRepository,
            @NonNull ReferralService referralService,
            @NonNull IndividualRepository individualRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskAssessmentDateCommandViewModel.class);
        this.referralRepository = referralRepository;
        this.referralService = referralService;
        this.individualRepository = individualRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params, ReferralTaskAssessmentDateCommandViewModel vm) {

        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        if (vm.interviewer1 != null) {
            r.setInterviewer1(vm.interviewer1.to == null ? null : individualRepository.getOne(vm.interviewer1.to));
        }

        if (vm.interviewer2 != null) {
            r.setInterviewer2(vm.interviewer2.to == null ? null : individualRepository.getOne(vm.interviewer2.to));
        }

        if (vm.decisionDate != null) {
            r.setInterviewDate(vm.decisionDate.to);

            // set the first response only on the first time the decision date is set
            if (r.getFirstResponseMadeOn() == null) {
                // firstResponseMadeOn is a user's local date mainly for use in 'next meeting date'
                // so we save 'now' with the users' timezone
                r.setFirstResponseMadeOn(DateTimeUtils.convertFromUsersLocalDateTime(DateTimeUtils.getLocalNow()));
            }
        }

        if (vm.firstOfferedInterviewDate != null) {
            r.setFirstOfferedInterviewDate(vm.firstOfferedInterviewDate.to);
        }

        if (vm.location != null) {
            r.setInterviewLocation(vm.location.to);
        }

        if (vm.interviewSetupComments != null) {
            r.setInterviewSetupComments(vm.interviewSetupComments.to);
        }

        if (vm.interviewDna != null) {
            r.setInterviewDna(vm.interviewDna.to);
        }

        if (vm.interviewDnaComments != null) {
            r.setInterviewDnaComments(vm.interviewDnaComments.to);
        }

        referralService.setReferralWithInterviewDate(r); // so that calendar bits get updated
        return null;
    }

    @NonNull
    @Override
    protected Pair<ServiceRecipientTaskCommand, Optional<CommandResult>> handleInternalAndSaveCommand(@NonNull Authentication authentication, @NonNull ServiceRecipientTaskParams serviceRecipientTaskParams, @NonNull String requestBody, @NonNull ReferralTaskAssessmentDateCommandViewModel viewModel, long userId) {
        var result = super.handleInternalAndSaveCommand(authentication, serviceRecipientTaskParams, requestBody, viewModel, userId);
        entityManager.flush();
        return result;
    }
}
