package com.ecco.webApi.taskFlow;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.SignatureRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class ReferralTaskEditConsentCommandHandler extends ReferralTaskEditAgreementCommandHandler {

    @Autowired
    public ReferralTaskEditConsentCommandHandler(@NonNull ObjectMapper objectMapper,
                                                 @NonNull WorkflowTaskController workflowTaskController,
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull ClientRepository clientRepository,
            @NonNull SignatureRepository signatureRepository,
            @NonNull ReferralRepository referralRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, clientRepository,
                signatureRepository, referralRepository, ReferralTaskEditConsentCommandViewModel.class);
    }

    protected void setAgreementDate(Referral r, DateTime utcDate) {
        r.setConsentSigned(utcDate);
    }

    protected void setAgreementStatus(Referral r, Boolean status) {
        r.setConsentStatus(status);
    }

    protected void setAgreementSignature(Referral r, UUID signatureId) {
        r.getServiceRecipient().setConsentSignatureId(signatureId);
    }

}
