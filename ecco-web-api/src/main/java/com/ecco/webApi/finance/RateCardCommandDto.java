package com.ecco.webApi.finance;

import com.ecco.dom.contracts.RateCard;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * View model, designed for JSON serialization.
 */
@NoArgsConstructor
@Getter // Required for MapStruct to access fields
public class RateCardCommandDto extends BaseCommandViewModel {

    @NonNull
    public String operation;

    @Nullable
    public Integer rateCardId;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<LocalDateTime> startDateTime;

    @Nullable
    public ChangeViewModel<LocalDateTime> endDateTime;

    @Nullable
    public ChangeViewModel<Integer> advisoryTotalDuration;

    @Nullable
    public ChangeViewModel<BigDecimal> advisoryTotalCharge;

    @Nullable
    public ChangeViewModel<Integer> chargeNameId;

    @Nullable
    public ChangeViewModel<List<Integer>> contractsChange;

    @Nullable
    public ChangeViewModel<RateCard.PartsOfWeek> matchingPartsOfWeek;

    @Nullable
    public ChangeViewModel<LocalTime> matchingStartTime;

    @Nullable
    public ChangeViewModel<LocalTime> matchingEndTime;

    @Nullable
    Integer serviceRecipientId;

    /**
     * @param operation add or update or remove
     */
    public RateCardCommandDto(@Nullable Integer serviceRecipientId, @NonNull String operation, @Nullable Integer rateCardId) {
        super(UriComponentsBuilder
                        .fromUriString("contracts/rateCards/")
                        .toUriString());
        this.serviceRecipientId = serviceRecipientId;
        this.operation = operation;
        this.rateCardId = rateCardId;
    }

}
