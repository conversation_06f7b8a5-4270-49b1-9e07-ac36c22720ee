package com.ecco.webApi.finance;

import com.ecco.dom.finance.FinanceReceipt;
import com.ecco.dom.finance.commands.FinanceReceiptCommand;
import com.ecco.repositories.finance.FinanceReceiptRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.jspecify.annotations.Nullable;
import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class FinanceReceiptCommandHandler extends ServiceRecipientCommandHandler<FinanceReceiptCommandViewModel, FinanceReceiptCommand, @NonNull FinanceReceiptParams> {

    private final ServiceRecipientRepository serviceRecipientRepository;
    private final FinanceReceiptRepository financeReceiptRepository;

    public FinanceReceiptCommandHandler(ObjectMapper objectMapper,
                               ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                               ServiceRecipientRepository serviceRecipientRepository,
                               FinanceReceiptRepository financeReceiptRepository) {
        super(objectMapper, serviceRecipientCommandRepository, FinanceReceiptCommandViewModel.class);
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.financeReceiptRepository = financeReceiptRepository;
    }

    @NonNull
    @Override
    protected FinanceReceiptCommand createCommand(@Nullable Serializable targetId, @NonNull FinanceReceiptParams params, @NonNull String requestBody, @NonNull FinanceReceiptCommandViewModel viewModel, long userId) {
        assert params != null;
        assert viewModel.serviceRecipientId != null;
        Assert.state(params.getServiceRecipientId() == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");
        return new FinanceReceiptCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.serviceRecipientId);
    }

    @Nullable
    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull FinanceReceiptParams params, @NonNull FinanceReceiptCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case BaseCommandViewModel.OPERATION_ADD -> {
                return this.link(addReceipt(viewModel));
            }
            case BaseCommandViewModel.OPERATION_UPDATE -> {
                return this.link(updateReceipt(viewModel));
            }
            case BaseCommandViewModel.OPERATION_REMOVE -> {
                removeReceipt(viewModel);
                return null;
            }
            default -> throw new IllegalArgumentException("cannot handle operation: " + viewModel.operation);
        }
    }

    private CommandResult link(int receiptId) {
        return CommandResult.ofLink(linkToApi(methodOn(FinanceReceiptController.class).getReceipt(receiptId)).withSelfRel())
                .withTargetId(receiptId);
    }

    private int addReceipt(FinanceReceiptCommandViewModel viewModel) {
        var receipt = new FinanceReceipt();
        assert viewModel.serviceRecipientId != null;
        BaseServiceRecipient recipient = serviceRecipientRepository.findById(viewModel.serviceRecipientId).orElseThrow(NullPointerException::new);
        receipt.setServiceRecipientId(recipient.getId());
        setCommonProperties(viewModel, receipt);
        financeReceiptRepository.save(receipt);
        return receipt.getId();
    }

    private void setCommonProperties(FinanceReceiptCommandViewModel viewModel, FinanceReceipt receipt) {
        if (viewModel.receivedDate != null) {
            receipt.setReceivedDate(viewModel.receivedDate.to);
        }
        if (viewModel.amount != null) {
            receipt.setAmount(viewModel.amount.to);
        }
        if (viewModel.description != null) {
            receipt.setDescription(viewModel.description.to);
        }
        if (viewModel.typeDefId != null) {
            receipt.setTypeDefId(viewModel.typeDefId.to);
        }
    }

    private int updateReceipt(FinanceReceiptCommandViewModel viewModel) {
        assert viewModel.receiptId != null;
        var receipt = financeReceiptRepository.getById(viewModel.receiptId);
        setCommonProperties(viewModel, receipt);
        financeReceiptRepository.save(receipt);
        return receipt.getId();
    }

    private void removeReceipt(FinanceReceiptCommandViewModel viewModel) {
        assert viewModel.receiptId != null;
        var receipt = financeReceiptRepository.getById(viewModel.receiptId);
        financeReceiptRepository.delete(receipt);
    }

}