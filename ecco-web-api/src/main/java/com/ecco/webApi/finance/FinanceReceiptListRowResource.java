package com.ecco.webApi.finance;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import java.math.BigDecimal;
import java.time.LocalDate;

import static lombok.AccessLevel.PROTECTED;

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public class FinanceReceiptListRowResource extends RepresentationModel<FinanceReceiptListRowResource> {
    // Not a visible column
    private int receiptId;

    @JsonSchemaMetadata(title = "serviceRecipientId", order = 15)
    public Integer serviceRecipientId;

    @JsonSchemaMetadata(title = "receivedDate", order = 20)
    @JsonSchemaProperty(format = JsonValueFormat.DATE)
    public LocalDate receivedDate;

    @JsonSchemaMetadata(title = "type", order = 25)
    public String typeDefName;

    @JsonSchemaMetadata(title = "amount", order = 30)
    public BigDecimal amount;

    @JsonSchemaMetadata(title = "description", order = 40)
    public String description;

}
