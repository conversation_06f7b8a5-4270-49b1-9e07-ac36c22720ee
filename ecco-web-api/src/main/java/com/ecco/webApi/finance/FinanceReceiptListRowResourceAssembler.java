package com.ecco.webApi.finance;

import com.ecco.dom.finance.FinanceReceipt;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

import org.jspecify.annotations.NonNull;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;


public class FinanceReceiptListRowResourceAssembler extends RepresentationModelAssemblerSupport<FinanceReceipt, FinanceReceiptListRowResource> {

    public static final String REL_EDIT = "edit";

    public FinanceReceiptListRowResourceAssembler() {
        super(FinanceReceiptListController.class, FinanceReceiptListRowResource.class);
    }

    @Override
    @NonNull
    public FinanceReceiptListRowResource toModel(FinanceReceipt f) {
        FinanceReceiptListRowResource resource = new FinanceReceiptListRowResource(f.getId(),
                f.getServiceRecipientId(), f.getReceivedDate(), f.getType() != null ? f.getType().getName() : null,
                f.getAmount(), f.getDescription());
        addEditLink(resource, f);
        return resource;
    }

    private void addEditLink(FinanceReceiptListRowResource resource, FinanceReceipt f) {
        resource.add(linkToReceipt(f.getId()).withRel(REL_EDIT));
    }

    public LinkBuilder linkToReceipt(int receiptId) {
        // eg. http://localhost:8080/ecco-war/api/finance/receipts/{id}
        var method = methodOn(FinanceReceiptController.class).getReceipt(receiptId);
        return linkToApi(method);
    }
}
