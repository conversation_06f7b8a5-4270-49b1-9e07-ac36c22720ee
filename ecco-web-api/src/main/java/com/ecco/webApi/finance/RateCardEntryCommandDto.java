package com.ecco.webApi.finance;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

import java.time.LocalDate;
import lombok.NoArgsConstructor;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * View model, designed for JSON serialization.
 */
@NoArgsConstructor
@NullMarked
public class RateCardEntryCommandDto extends BaseCommandViewModel {

    public String operation;

    public Integer rateCardId;

    @Nullable
    public Integer rateCardEntryId;

    @Nullable
    public Integer parentRateCardEntryId;

    @Nullable
    public ChangeViewModel<LocalDate> validFrom;

    @Nullable
    public ChangeViewModel<Boolean> disabled;

    @Nullable
    public ChangeViewModel<Boolean> defaultEntry;

    @Nullable
    public ChangeViewModel<Integer> matchingCategoryTypeId;

    @Nullable
    public ChangeViewModel<Integer> matchingChargeCategoryId;

    @Nullable
    public Map<String, Object> addedToMatchingFactors;

    @Nullable
    public List<String> removedFromMatchingFactors;

    @Nullable
    public ChangeViewModel<String> chargeTypeFixedTemporal;

    @Nullable
    public ChangeViewModel<BigDecimal> fixedCharge;

    @Nullable
    public ChangeViewModel<Integer> unitMeasurementId;

    @Nullable
    public ChangeViewModel<Integer> units;

    @Nullable
    public ChangeViewModel<BigDecimal> unitCharge;

    @Nullable
    public ChangeViewModel<Integer> unitsToRepeatFor;

    /**
     * @param operation add or update or remove
     */
    public RateCardEntryCommandDto(String operation, int rateCardId, @Nullable Integer rateCardEntryId) {
        super(UriComponentsBuilder
                        .fromUriString("contracts/rateCardEntries/")
                        .toUriString());
        this.operation = operation;
        this.rateCardId = rateCardId;
        this.rateCardEntryId = rateCardEntryId;
    }

}
