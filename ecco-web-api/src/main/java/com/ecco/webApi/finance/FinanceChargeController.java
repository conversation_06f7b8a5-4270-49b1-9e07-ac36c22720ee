package com.ecco.webApi.finance;

import com.ecco.finance.webApi.dto.ClientSalesChargeInvoiceDetailResource;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

import java.time.LocalDate;
import lombok.RequiredArgsConstructor;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import org.jspecify.annotations.NonNull;
import java.io.IOException;
import java.util.List;


@RestController
@RequestMapping("/finance")
@RequiredArgsConstructor
@NullMarked
public class FinanceChargeController extends BaseWebApiController {

    private final ServiceRecipientCreateInvoiceCommandHandler createInvoiceCommandHandler;
    private final FinanceService financeService;

    /**
     * Create a draft invoice for a service recipient.
     * As per RotaActivityInvoiceController - here to indicate this placeholder, if invoices were persisted.
     */
    @PostJson("/invoice/serviceRecipient/{serviceRecipientId}/")
    public Result createClientSalesInvoice(ServiceRecipientCreateInvoiceParams params,
                                           @NonNull Authentication authentication,
                                           @NonNull @RequestBody String requestBody) throws IOException {
        return createInvoiceCommandHandler.handleCommand(authentication, params, requestBody);
    }

    // {from}-{to}
    // @PathVariable @DateTimeFormat(iso= DateTimeFormat.ISO.DATE) LocalDate from

    // used via referral-based report, and tests via FinanceChargeActor
    @GetJson("/service-recipients/{serviceRecipientId}/charges/")
    public List<ClientSalesChargeInvoiceDetailResource.Line> getChargesByServiceRecipient(
            @PathVariable Integer serviceRecipientId,
            @RequestParam(required = false) @Nullable LocalDate fromDate,
            @RequestParam(required = false) @Nullable LocalDate toDate) {
        return financeService.getChargesByServiceRecipient(serviceRecipientId, fromDate, toDate, false);
    }

    // used via referral-based report
    @GetJson("/service-recipients/{serviceRecipientId}/receipts/")
    public List<FinanceReceiptViewModel> getReceiptsByServiceRecipient(
            @PathVariable Integer serviceRecipientId) {
        return financeService.getReceiptsByServiceRecipient(serviceRecipientId);
    }

}
