package com.ecco.webApi.finance

import com.ecco.dom.finance.HousingBenefitIncomeStatement
import com.ecco.webApi.serialization.LocalDateSerializer
import kotlinx.serialization.Serializable
import java.sql.Date
import java.time.LocalDate

@Serializable
data class HousingBenefitIncomeStatementViewModel(
    val reference: String?,
    @Serializable(with = LocalDateSerializer::class)
    val issueDate: LocalDate?,
    @Serializable(with = LocalDateSerializer::class)
    val periodStart: LocalDate?,
    @Serializable(with = LocalDateSerializer::class)
    val periodEnd: LocalDate?,
    val lineItems: List<HousingBenefitLineItemViewModel>,
    val id: Int? = null,
) {
    companion object {
        fun fromHeaderAndLineItems(
            header: HousingBenefitIncomeStatementHeaderViewModel,
            lineItems: Sequence<HousingBenefitLineItemViewModel>,
        ) = HousingBenefitIncomeStatementViewModel(
            header.reference,
            header.issueDate,
            header.periodStart,
            header.periodEnd,
            lineItems.toList(),
        )
    }

    fun toDomain(): HousingBenefitIncomeStatement = HousingBenefitIncomeStatement(
        reference,
        issueDate?.let { Date.valueOf(it) },
        periodStart?.let { Date.valueOf(it) },
        periodEnd?.let { Date.valueOf(it) },
        lineItems.map { it.toDomain() },
        id,
    )

    fun toHeaderAndLineItems() = Pair(HousingBenefitIncomeStatementHeaderViewModel(reference, issueDate, periodStart, periodEnd), lineItems)
}

fun HousingBenefitIncomeStatement.toViewModel(): HousingBenefitIncomeStatementViewModel = HousingBenefitIncomeStatementViewModel(
    reference,
    issueDate?.toLocalDate(),
    periodStart?.toLocalDate(),
    periodEnd?.toLocalDate(),
    lineItems.map { it.toViewModel() },
    id,
)