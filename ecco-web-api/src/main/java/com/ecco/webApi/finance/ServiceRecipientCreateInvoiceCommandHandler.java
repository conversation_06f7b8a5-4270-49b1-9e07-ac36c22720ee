package com.ecco.webApi.finance;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController.REL_EDIT;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import com.ecco.dao.ClientSalesInvoiceRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.agreements.ClientSalesInvoice;
import com.ecco.dom.commands.ServiceRecipientCreateInvoiceCommand;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.rota.ClientSalesInvoiceResourceAssembler;
import com.ecco.webApi.rota.RotaActivityInvoiceController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.Serializable;

@Component
public class ServiceRecipientCreateInvoiceCommandHandler extends ServiceRecipientCommandHandler<CreateClientSalesInvoiceCommandViewModel,
        ServiceRecipientCreateInvoiceCommand, @NonNull ServiceRecipientCreateInvoiceParams> {

    @NonNull
    private final ClientSalesInvoiceResourceAssembler clientSalesInvoiceResourceAssembler;

    @NonNull
    private final ClientSalesInvoiceRepository clientSalesInvoiceRepository;

    @Autowired
    public ServiceRecipientCreateInvoiceCommandHandler(@NonNull ObjectMapper objectMapper,
                                                       @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                       @NonNull ClientSalesInvoiceResourceAssembler clientSalesInvoiceResourceAssembler,
                                                       @NonNull ClientSalesInvoiceRepository clientSalesInvoiceRepository) {
        super(objectMapper, serviceRecipientCommandRepository, CreateClientSalesInvoiceCommandViewModel.class);

        this.clientSalesInvoiceRepository = clientSalesInvoiceRepository;
        this.clientSalesInvoiceResourceAssembler = clientSalesInvoiceResourceAssembler;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull ServiceRecipientCreateInvoiceParams invoiceParams,
                                           @NonNull CreateClientSalesInvoiceCommandViewModel vm) {
        ClientSalesInvoice invoice = clientSalesInvoiceResourceAssembler.fromResource(vm.serviceRecipientId, vm);
        Assert.isNull(clientSalesInvoiceRepository.findByServiceRecipientIdAndInvoiceDate(invoice.getServiceRecipientId(), invoice.getInvoiceDate()),
                "Invoice already exists for srId,date");
        invoice = clientSalesInvoiceRepository.save(invoice);
        return CommandResult.ofLink(linkToApi(methodOn(RotaActivityInvoiceController.class).fetchSingleInvoice(invoice.getId())).withRel(REL_EDIT));
    }

    @NonNull
    @Override
    protected ServiceRecipientCreateInvoiceCommand createCommand(Serializable targetId, @NonNull ServiceRecipientCreateInvoiceParams params,
                                                                 @NonNull String requestBody,
                                                                 @NonNull CreateClientSalesInvoiceCommandViewModel viewModel,
                                                                 long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");
        return new ServiceRecipientCreateInvoiceCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.serviceRecipientId);
    }

}
