package com.ecco.webApi.finance

import com.ecco.config.repositories.ListDefinitionRepository
import com.ecco.dom.contracts.RateCardEntry
import com.ecco.dom.contracts.commands.RateCardEntryCommand
import com.ecco.infrastructure.rest.hateoas.ApiLinkTo
import com.ecco.repositories.contracts.RateCardEntryCommandRepository
import com.ecco.repositories.contracts.RateCardEntryRepository
import com.ecco.repositories.contracts.RateCardRepository
import com.ecco.repositories.contracts.UnitOfMeasurementRepository
import com.ecco.webApi.CommandResult
import com.ecco.webApi.CommandResult.Companion.ofLink
import com.ecco.webApi.evidence.BaseCommandHandler
import com.ecco.webApi.evidence.BaseCommandViewModel
import com.ecco.webApi.rota.ContractController
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import java.io.Serializable
import java.time.LocalDate

@Component
class RateCardEntryCommandHandler
@Autowired
constructor(
    rateCardEntryCommandRepository: RateCardEntryCommandRepository,
    objectMapper: ObjectMapper,
    private val rateCardEntryRepository: RateCardEntryRepository,
    private val rateCardRepository: RateCardRepository,
    private val unitOfMeasurementRepository: UnitOfMeasurementRepository,
    private val listDefinitionRepository: ListDefinitionRepository,
) : BaseCommandHandler<RateCardEntryCommandDto, Int, RateCardEntryCommand, Void?>(
    objectMapper,
    rateCardEntryCommandRepository,
    RateCardEntryCommandDto::class.java,
) {
    override fun handleInternal(auth: Authentication, params: Void?, dto: RateCardEntryCommandDto): CommandResult {
        when (dto.operation) {
            BaseCommandViewModel.OPERATION_ADD -> addRateCardEntry(dto)
            BaseCommandViewModel.OPERATION_UPDATE -> updateRateCardEntry(dto)
            BaseCommandViewModel.OPERATION_REMOVE -> deleteRateCardEntry(dto)
            else -> throw IllegalArgumentException("cannot handle operation: " + dto.operation)
        }

        return ofLink(
            ApiLinkTo
                .linkToApi(
                    WebMvcLinkBuilder
                        .methodOn(
                            ContractController::class.java,
                        ).getRateCard(dto.rateCardId),
                ).withSelfRel(),
        )
    }

    private fun addRateCardEntry(dto: RateCardEntryCommandDto): Int {
        val r = RateCardEntry()
        r.setRateCard(rateCardRepository.getReferenceById(dto.rateCardId))
        if (dto.validFrom?.to == null) {
            // Default to always valid if none specified
            r.setValidFrom(LocalDate.EPOCH)
        }
        applyChanges(r, dto)
        val rateCardEntry = rateCardEntryRepository.save(r)
        applyChild(dto, rateCardEntry)
        return rateCardEntry.id
    }

    private fun updateRateCardEntry(dto: RateCardEntryCommandDto) {
        val entry = rateCardEntryRepository.findOne(dto.rateCardEntryId)
        applyChanges(entry!!, dto)
        rateCardEntryRepository.save(entry)
        applyChild(dto, entry)
    }

    private fun deleteRateCardEntry(dto: RateCardEntryCommandDto) {
        rateCardEntryRepository.deleteById(dto.rateCardEntryId!!) // always nonnull for one that's been persisted
    }

    private fun applyChild(dto: RateCardEntryCommandDto, rateCardEntry: RateCardEntry) {
        if (dto.parentRateCardEntryId != null) {
            val parent = rateCardEntryRepository.findOne(dto.parentRateCardEntryId)
            if (parent!!.getChildRateCardEntry() == null) {
                parent.setChildRateCardEntry(rateCardEntry)
                rateCardEntryRepository.save(parent)
            }
        }
    }

    private fun applyChanges(r: RateCardEntry, dto: RateCardEntryCommandDto) {
        dto.disabled?.let {
            r.isDisabled = it.to == true
        }
        dto.defaultEntry?.let {
            r.isDefaultEntry = it.to == true
        }

        dto.validFrom?.let {
            r.setValidFrom(it.to)
        }

        dto.matchingCategoryTypeId?.let {
            r.setMatchingCategoryTypeId(it.to)
        }

        dto.matchingChargeCategoryId?.let {
            r.setMatchingChargeCategory(
                listDefinitionRepository.getReferenceById(it.to!!),
            )
        }

        dto.addedToMatchingFactors?.let {
            if (r.matchingFactors == null) {
                r.matchingFactors = HashMap<String, Any>()
            }
            r.matchingFactors!!.putAll(it)
        }
        if (dto.removedFromMatchingFactors != null && r.matchingFactors != null) {
            (dto.removedFromMatchingFactors as Iterable<String>).forEach { name ->
                r.matchingFactors!!.remove(name)
            }
        }

        dto.chargeTypeFixedTemporal?.let {
            r.setChargeTypeFixedTemporal(it.to)
        }

        dto.fixedCharge?.let {
            r.setFixedCharge(it.to)
        }

        dto.unitMeasurementId?.let {
            r.setUnitMeasurement(unitOfMeasurementRepository.getReferenceById(it.to!!))
        }

        dto.units?.let {
            r.setUnits(it.to)
        }

        dto.unitCharge?.let {
            r.setUnitCharge(it.to)
        }

        dto.unitsToRepeatFor?.let {
            r.setUnitsToRepeatFor(it.to)
        }
    }

    override fun createCommand(
        targetId: Serializable?,
        params: Void?,
        requestBody: String,
        dto: RateCardEntryCommandDto,
        userId: Long,
    ): RateCardEntryCommand = RateCardEntryCommand(dto.uuid, dto.timestamp, userId, requestBody)
}