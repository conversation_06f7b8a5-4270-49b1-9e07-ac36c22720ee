package com.ecco.webApi;

import org.jspecify.annotations.Nullable;

import org.springframework.hateoas.RepresentationModel;

import java.util.function.Function;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

public abstract class GuavaResourceAssemblerSupport<T, R extends RepresentationModel<R>>
        extends RepresentationModelAssemblerSupport<T, R> implements Function<T, R> {

    public GuavaResourceAssemblerSupport(Class<?> controllerClass, Class<R> resourceType) {
        super(controllerClass, resourceType);
    }

    @Nullable
    @Override
    public R apply(@Nullable T input) {
        return toModel(input);
    }

}