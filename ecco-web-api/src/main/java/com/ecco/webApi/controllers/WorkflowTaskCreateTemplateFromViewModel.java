package com.ecco.webApi.controllers;

import java.util.function.Function;

import com.ecco.webApi.viewModels.EntityReferenceViewModel;
import com.ecco.webApi.viewModels.WorkflowTaskCreateTemplateViewModel;
import com.ecco.workflow.WorkflowLinkedResource;
import com.ecco.workflow.WorkflowTaskCreateTemplate;
import com.google.common.collect.Lists;

/**
 * Converts a workflow task view model into a template to create a new task.
 *
 * @since 03/01/2014
 */
class WorkflowTaskCreateTemplateFromViewModel implements Function<WorkflowTaskCreateTemplateViewModel, WorkflowTaskCreateTemplate> {
    private final Function<EntityReferenceViewModel, WorkflowLinkedResource> linkedResourceFromViewModel;

    WorkflowTaskCreateTemplateFromViewModel(Function<EntityReferenceViewModel, WorkflowLinkedResource> linkedResourceFromViewModel) {
        this.linkedResourceFromViewModel = linkedResourceFromViewModel;
    }

    @Override
    public WorkflowTaskCreateTemplate apply(WorkflowTaskCreateTemplateViewModel input) {
        return WorkflowTaskCreateTemplate.BuilderFactory.create()
                .name(input.taskName)
                .dueDate(input.dueDate)
                .linkedResources(Lists.transform(input.entities, linkedResourceFromViewModel::apply)).build();
    }
}
