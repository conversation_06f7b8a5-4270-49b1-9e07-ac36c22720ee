package com.ecco.webApi.controllers;

import com.ecco.config.service.SettingsService;

import com.ecco.infrastructure.web.WebSlice;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

/**
 * @since 14/10/2014
 */
@PreAuthorize("hasRole('ROLE_ADMIN')")
@RestController
@WebSlice("api")
@RequestMapping("/settings")
public class SettingsController {
    private final SettingsService settingsService;


    @Autowired
    public SettingsController(SettingsService settingsService) {
        this.settingsService = settingsService;
    }

    @RequestMapping(value = "/logo/linkHidden/{fileId}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> linkLogoToFile(@PathVariable String fileId, HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        try {
            settingsService.setSetting("com.ecco", "LOGO_FILE_ID", fileId);
            data.put("success", true);
            String urlServletBase = request.getContextPath();
            data.put("logoImageUrl", urlServletBase + "/api/images/f/" + fileId);
        } catch (Exception e) {
            data.put("success", false);
            data.put("error", e.getMessage());
        }
        return data;
    }

    @PostJsonReturningJson(value = "/{namespace}/{keyName}")
    @PreAuthorize("hasRole('ROLE_SYSADMIN')")
    public void updateSetting(@PathVariable String namespace, @PathVariable String keyName, @RequestBody SettingDto dto) {
        settingsService.setSetting(namespace, keyName, dto.keyValue);
    }

    @Data
    private static class SettingDto implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        String keyValue;
    }
}
