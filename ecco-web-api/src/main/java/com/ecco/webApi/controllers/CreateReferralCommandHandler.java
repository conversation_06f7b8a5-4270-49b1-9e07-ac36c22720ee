package com.ecco.webApi.controllers;

import org.jspecify.annotations.NonNull;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.*;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.dom.commands.CreateServiceRecipientCommand;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.*;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class CreateReferralCommandHandler extends ServiceRecipientCommandHandler<CreateReferralCommandViewModel,
        CreateServiceRecipientCommand, @NonNull CreateServiceRecipientParams> {

    @NonNull
    private final ReferralRepository referralRepository;
    private final ReferralFromViewModel referralFromViewModel;
    private final WorkflowController workflowController;

    public CreateReferralCommandHandler(ObjectMapper objectMapper,
                                        @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        FundingSourceRepository fundingSourceRepository,
                                        LocalAuthorityRepository localAuthorityRepository,
                                        @NonNull ReferralRepository referralRepository, ServiceRepository serviceRepository, ProjectRepository projectRepository,
                                        ServiceCategorisationRepository serviceCategorisationRepository,
                                        IndividualRepository individualRepository,
                                        ListDefinitionRepository listDefinitionRepository,
                                        WorkflowController workflowController,
                                        SignatureRepository signatureRepository,
                                        ClientRepository clientRepository) {
        super(objectMapper, serviceRecipientCommandRepository, CreateReferralCommandViewModel.class);
        this.referralRepository = referralRepository;
        this.workflowController = workflowController;
        this.referralFromViewModel = new ReferralFromViewModel(fundingSourceRepository, localAuthorityRepository,
                serviceRepository, serviceCategorisationRepository, projectRepository, individualRepository,
                signatureRepository, clientRepository, listDefinitionRepository);
    }

    @NonNull
    @Override
    protected CreateServiceRecipientCommand createCommand(Serializable targetId, @NonNull CreateServiceRecipientParams params, @NonNull String requestBody,
                                                          @NonNull CreateReferralCommandViewModel viewModel, long userId) {
        Assert.state(params.prefix.equals(viewModel.getPrefix()), "prefix in body must match URI");

        return new CreateServiceRecipientCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                viewModel.getReferralViewModel().serviceRecipientId != null
                        ? viewModel.getReferralViewModel().serviceRecipientId
                        : (Integer) targetId);
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull CreateServiceRecipientParams params, @NonNull CreateReferralCommandViewModel viewModel) {
        long rId = CreateReferralCommandHandler.createReferral(viewModel.getReferralViewModel(), this.referralFromViewModel,
                this.referralRepository, this.workflowController);
        Referral r = this.referralRepository.findById(rId).get();

        return CommandResult.ofLink(linkToApi(methodOn(ReferralController.class).findOneReferral(rId)).withSelfRel())
                .withTargetId(r.getServiceRecipientId());
    }

    public static long createReferral(ReferralViewModel referralViewModel, ReferralFromViewModel referralFromViewModel,
                                      ReferralRepository referralRepository, WorkflowController workflowController) {
        final Referral referral = referralFromViewModel.apply(referralViewModel);
        referralRepository.save(referral);
        // TODO: Review.  This was quickest way to get tasks set up after save/import of a referral (which is probably
        // NOTE: This relies on the side effect of auto completing wizard tasks
        //noinspection ConstantConditions
        workflowController.findOneWorkflowByServiceRecipientId(referral.getServiceRecipientId());
        return referral.getId();
    }

}
