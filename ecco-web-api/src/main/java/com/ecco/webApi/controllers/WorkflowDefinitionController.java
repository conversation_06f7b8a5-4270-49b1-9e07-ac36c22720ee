package com.ecco.webApi.controllers;

import com.ecco.workflow.WorkflowService;
import com.ecco.workflow.WorkflowTaskDefinition;
import com.ecco.workflow.WorkflowTaskDefinition.TaskDefinitionHandle;
import com.ecco.workflow.activiti.ActivitiWorkflowServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class WorkflowDefinitionController extends BaseWebApiController {

    private final WorkflowService workflowService;


    @Autowired
    public WorkflowDefinitionController(ActivitiWorkflowServiceImpl workflowService) {
        this.workflowService = workflowService;
    }

    // Currently unused
    @RequestMapping(value = "/task-definition/{taskDefinitionHandle}/", produces = MediaType.APPLICATION_JSON_VALUE)
    public WorkflowTaskDefinition findWorkflowTaskDefinitionByHandle(@PathVariable String taskDefinitionHandle) {

        return workflowService.getWorkflowTaskDefinition(TaskDefinitionHandle.fromExternal(taskDefinitionHandle));
    }
}
