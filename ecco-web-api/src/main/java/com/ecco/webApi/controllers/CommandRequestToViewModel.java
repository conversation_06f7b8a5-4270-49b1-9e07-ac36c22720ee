package com.ecco.webApi.controllers;

import com.ecco.security.dom.CommandRequest;
import com.ecco.webApi.viewModels.CommandRequestViewModel;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

public class CommandRequestToViewModel implements Function<CommandRequest, CommandRequestViewModel> {

    @Nullable
    @Override
    public CommandRequestViewModel apply(@Nullable CommandRequest input) {

        final CommandRequestViewModel vm = new CommandRequestViewModel();
        vm.method = input.getMethod();
        vm.url = input.getUrl() == null ? null : input.getUrl().toString();
        vm.contentType = input.getContentType();
        vm.acceptType = input.getAcceptType();
        vm.body = input.getBody();

        return vm;
    }

}
