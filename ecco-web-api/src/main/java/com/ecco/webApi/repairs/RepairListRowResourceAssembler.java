package com.ecco.webApi.repairs;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.repairs.Repair;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.security.repositories.IndividualRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

// see also UserListRowResourceAssembler
public class RepairListRowResourceAssembler extends RepresentationModelAssemblerSupport<Repair, RepairViewModel> {

    public static final String REL_EDIT = "edit";

    private final RepairToViewModel repairToViewModel;
    protected final ObjectMapper objectMapper = ConvertersConfig.getObjectMapper();
    private final ListDefinitionRepository listDefinitionRepository;
    private final IndividualRepository individualRepository;

    public RepairListRowResourceAssembler(ListDefinitionRepository listDefinitionRepository, IndividualRepository individualRepository, RepairRateRepository repairRateRepository) {
        super(RepairListController.class, RepairViewModel.class);
        this.listDefinitionRepository = listDefinitionRepository;
        this.individualRepository = individualRepository;
        this.repairToViewModel = new RepairToViewModel(listDefinitionRepository, repairRateRepository);
    }

    @Override
    public RepairViewModel toModel(Repair input) {
        var result = repairToViewModel.apply(input);

        result.serviceDescription = input.getServiceRecipient().getServiceAllocation().description();
        if (input.getSupportWorkerId() != null) {
            result.supportWorkerDisplayName = individualRepository.findOne(input.getSupportWorkerId()).getDisplayName();
        }

        result.displayName = input.getClientDisplayName();

        if (input.getCategoryId() != null) {
            var category = listDefinitionRepository.findById(input.getCategoryId()).get();
            if (category.getMetadata() != null) {
                JsonNode tree;
                try {
                    tree = objectMapper.readTree(category.getMetadata());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        return result;

        //addEditLink(resource, vm);
    }

}
