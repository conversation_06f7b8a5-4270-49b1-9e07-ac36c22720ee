package com.ecco.webApi.repairs;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.viewModels.ServiceRecipientSourceViewModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.hateoas.RepresentationModel;

import java.time.LocalDate;

@Getter
@Setter
public class RepairViewModel extends RepresentationModel<RepairViewModel> implements ServiceRecipientSourceViewModel {

    Long serviceTypeId; // needed from an import to save with
    Integer serviceRecipientId;
    Integer serviceAllocationId;
    Integer buildingId;
    @JsonSchemaMetadata(title = "building", order = 7)
    String displayName;

    @JsonSchemaMetadata(title = "repair-id", order = 5)
    Integer repairId;

    @JsonSchemaMetadata(title = "status", order = 8)
    String statusMessage;

    @JsonSchemaMetadata(title = "statusKey", order = 9)
    String statusMessageKey;

    @JsonSchemaMetadata(title = "received", order = 10)
    LocalDate receivedDate;

    // when using RepairListRowResourceAssembler
        @JsonSchemaMetadata(title = "service", order = 15)
        String serviceDescription; // easier for schema
        @JsonSchemaMetadata(title = "category", order = 20)
        String categoryName; // easier for schema
        @JsonSchemaMetadata(title = "priority", order = 25)
        String priorityName; // easier for schema
    // when using RepairListRowResourceAssembler

    // for InboundRepairController
    Integer categoryId;
    Integer rateId;
    String rateName; // for reporting
    Integer priorityId;

    AcceptState acceptOnServiceState;
    org.joda.time.LocalDate decisionMadeOn;
    String signpostedExitComment;
    Integer signpostedReasonId;
    Long signpostedAgencyId;
    boolean signpostedBack;

    // source
    public boolean selfReferral;
    public Long referrerAgencyId;
    public Long referrerIndividualId;
    public String source; // self referral / individual / agency name
    public AgencyViewModel sourceAgency; // as per referrerAgencyId


    // start
    Long supportWorkerId;
    org.joda.time.LocalDate receivingServiceDate;
    @JsonSchemaMetadata(title = "assigned worker", order = 35)
    String supportWorkerDisplayName;

    @JsonSchemaMetadata(title = "review", order = 90)
    LocalDate reviewDate;

    @JsonSchemaMetadata(title = "exit", order = 95)
    LocalDate exitedDate;
    Integer exitReasonId;
}
