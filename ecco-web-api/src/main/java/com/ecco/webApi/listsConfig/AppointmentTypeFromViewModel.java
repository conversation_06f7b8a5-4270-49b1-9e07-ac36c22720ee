package com.ecco.webApi.listsConfig;

import java.util.function.Function;

import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.dom.Service;
import com.ecco.dom.agreements.AppointmentType;

import org.jspecify.annotations.Nullable;

public class AppointmentTypeFromViewModel implements Function<AppointmentTypeViewModel, AppointmentType> {

    private final ServiceRepository serviceRepository;

    public AppointmentTypeFromViewModel(ServiceRepository serviceRepository) {
        this.serviceRepository = serviceRepository;
    }


    @Override
    @Nullable
    public AppointmentType apply(@Nullable AppointmentTypeViewModel input) {
        if (input == null) {
            throw new NullPointerException("input AppointmentTypeViewModel must not be null");
        }

        AppointmentType appointmentType = new AppointmentType();
        if (input.service != null) {
            appointmentType.setService(findService(input));
        }
        appointmentType.setRecommendedDurationInMinutes(input.recommendedDurationInMinutes);
        appointmentType.setName(input.name);
        return appointmentType;
    }


    protected Service findService(AppointmentTypeViewModel input) {
        Service found = serviceRepository.findOneByName(input.service);
        if (found == null) {
            throw new IllegalArgumentException("input AppointmentTypeViewModel.service must be valid");
        }
        return found;
    }
}
