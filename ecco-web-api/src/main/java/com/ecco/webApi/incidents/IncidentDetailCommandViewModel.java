package com.ecco.webApi.incidents;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskCommandViewModel;
import lombok.*;

import java.time.LocalDate;

@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@Getter
@Setter
public class IncidentDetailCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_INCIDENTDETAILS = "incidentDetails";

    public IncidentDetailCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_INCIDENTDETAILS, taskHandle);
    }

    //ChangeViewModel<String> name;
    ChangeViewModel<LocalDate> receivedDate;
    ChangeViewModel<String> reportedBy;
    ChangeViewModel<Integer> categoryId;
    ChangeViewModel<Boolean> emergencyServicesInvolved;
    ChangeViewModel<Boolean> hospitalisationInvolved;

}
