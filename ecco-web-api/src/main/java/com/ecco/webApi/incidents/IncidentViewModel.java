package com.ecco.webApi.incidents;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.servicerecipient.AcceptState;
import lombok.Getter;
import lombok.Setter;
import org.springframework.hateoas.RepresentationModel;

import java.time.LocalDate;

@Getter
@Setter
public class IncidentViewModel extends RepresentationModel<IncidentViewModel> {

    Long serviceTypeId; // needed from an import to save with
    Integer serviceRecipientId;
    Integer serviceAllocationId;
    //String name; // seemingly unused

    @JsonSchemaMetadata(title = "i-id", order = 5)
    Integer incidentId;

    @JsonSchemaMetadata(title = "status", order = 8)
    String statusMessage;

    @JsonSchemaMetadata(title = "statusKey", order = 9)
    String statusMessageKey;

    @JsonSchemaMetadata(title = "received", order = 10)
    LocalDate receivedDate;

    // when using IncidentListRowResourceAssembler
        @JsonSchemaMetadata(title = "service", order = 15)
        String serviceDescription; // easier for schema
        @JsonSchemaMetadata(title = "category", order = 20)
        String categoryName; // easier for schema
        @JsonSchemaMetadata(title = "significant", order = 25)
        Boolean significant; // easier for schema
    // when using IncidentListRowResourceAssembler

    Long reportedById;
    @JsonSchemaMetadata(title = "reportedBy", order = 30)
    String reportedBy;
    String reportedByContact;

    @JsonSchemaMetadata(title = "incident manager", order = 35)
    String supportWorkerDisplayName;

    // for InboundIncidentController
    Integer categoryId;

    @JsonSchemaMetadata(title = "emergency", order = 40)
    Boolean emergencyServicesInvolved;

    @JsonSchemaMetadata(title = "hospital", order = 45)
    Boolean hospitalisationInvolved;

    AcceptState acceptOnServiceState; // list column

    // http://localhost:8899/api/incidents/$schema/
    // gets to IncidentListController.describe, which creates the schema for IncidentViewModel
    // breakpoint on JodaDateSerializerBase acceptJsonFormatVisitor shows this produces JsonValueFormat.DATE_TIME
    // so we probably should just convert to java's LocalDate
    // NB this is different to the inbound incidents, which uses http://localhost:8899/api/inbound/incidents/$schema/
    LocalDate decisionMadeOn;

    String signpostedExitComment;
    Integer signpostedReasonId;
    Long signpostedAgencyId;
    boolean signpostedBack;

    // start
    Long supportWorkerId;
    org.joda.time.LocalDate receivingServiceDate;

    @JsonSchemaMetadata(title = "review", order = 90)
    LocalDate reviewDate;

    @JsonSchemaMetadata(title = "exit", order = 95)
    LocalDate exitedDate;
    Integer exitReasonId;
}
