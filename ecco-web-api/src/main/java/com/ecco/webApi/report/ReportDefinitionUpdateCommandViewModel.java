package com.ecco.webApi.report;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

/**
 * View model to take update .ts command changes and apply to things the server known about.
 * This isn't used to retrieve the objects since either the history of json commands can
 * be sent directly, or the view model of the object in question (ReportDefinition) is used
 */
public class ReportDefinitionUpdateCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public static final String OPERATION_ADD = "add";

    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    /**
     * The operation to perform; either {@link #OPERATION_ADD}, or
     * {@link #OPERATION_REMOVE}.
     */
    @NonNull
    public String operation;

    @NonNull
    public UUID reportDefUuid;

    // this maps from ts: nameChange?: cmdDtos.StringChange;
    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<String> friendlyName;

    @Nullable
    public ChangeViewModel<Boolean> showOnDashboardManager;

    @Nullable
    public ChangeViewModel<Boolean> showOnDashboardFile;

    @Nullable
    public ChangeViewModel<Boolean> deleted;

    @Nullable
    public ChangeViewModel<Number> orderby;

    // userId ISN'T a property of a .ts command since the user is known server side

    /** Change to the definition of this report */
    @Nullable
    public ChangeViewModel<String> definition;


}
