package com.ecco.webApi.workers;

import com.ecco.hr.command.HrCommand;
import com.ecco.hr.command.HrCommandRepository;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;

public abstract class BaseHrCommandHandler<VM extends BaseCommandViewModel, ENTITY extends HrCommand, PARAMS>
        extends Base<PERSON>ommandHandler<VM, Integer, ENTITY, PARAMS> {

    @SuppressWarnings("unchecked")
    public BaseHrCommandHandler(ObjectMapper objectMapper, HrCommandRepository hrCommandRepository,
                                Class<VM> vmClass) {
        super(objectMapper, (BaseCommandRepository<ENTITY, Integer>) hrCommandRepository, vmClass);
    }
}