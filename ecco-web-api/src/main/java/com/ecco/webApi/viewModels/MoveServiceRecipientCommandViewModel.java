package com.ecco.webApi.viewModels;

import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

public class MoveServiceRecipientCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public String reason;

    /**
     * Move to another file
     */
    public Long parentId;

    public boolean deleteParentIfPossible = false;

    /**
     * Move service
     */
    public Integer serviceAllocationId;

    /** For Jackson etc */
    MoveServiceRecipientCommandViewModel() {
        super();
    }

    public MoveServiceRecipientCommandViewModel(int serviceRecipientId, @NonNull Long parentId) {
        super(UriComponentsBuilder
                        .fromUriString("service-recipient/{serviceRecipientId}/move/")
                        .buildAndExpand(serviceRecipientId)
                        .toString(),
                serviceRecipientId);
        this.parentId = parentId;
    }

}
