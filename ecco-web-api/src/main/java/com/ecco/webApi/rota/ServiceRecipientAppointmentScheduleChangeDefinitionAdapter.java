package com.ecco.webApi.rota;

import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.calendar.core.RecurringEntryChangeDefinition;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import org.jspecify.annotations.NonNull;
import java.util.List;

/**
 * Command to update an appointment, which could also be used to create an appointment (ad-hoc or not).
 */
@Slf4j
public class ServiceRecipientAppointmentScheduleChangeDefinitionAdapter implements RecurringEntryChangeDefinition {

    private final ServiceRecipientAppointmentScheduleCommandDto dto;
    private String resourceCalendarId;

    public ServiceRecipientAppointmentScheduleChangeDefinitionAdapter(@NonNull ServiceRecipientAppointmentScheduleCommandDto dto,
                                                                      ServiceRecipientRepository serviceRecipientRepository) {
        this.dto = dto;
        if (dto.resourceSrId != null) {
            this.resourceCalendarId = serviceRecipientRepository.findOne(dto.resourceSrId).getCalendarId();
        }
    }

    @Override
    public LocalDate getApplicableFromDate() {
        return JodaToJDKAdapters.localDateToJoda(dto.applicableFromDate);
    }

    @Override
    public Boolean isAdHoc() {
        return dto.adHoc;
    }

    @Override
    public Integer getAgreementId() {
        return dto.agreementId;
    }

    @Override
    public Integer getAppointmentTypeId() {
        return dto.appointmentTypeId != null ? dto.appointmentTypeId.to : null;
    }

    @Override
    public Integer getDurationMins() {
        return dto.durationMins != null ? dto.durationMins.to : null;
    }

    @Override
    public String getTasks() {
        return dto.tasks != null ? dto.tasks.to : null;
    }

    @Override
    public Integer getAdditionalStaff() {
        return dto.additionalStaff;
    }

    @Override
    public Wrapper<Integer> getRateCardId() {
        return dto.rateCardId != null ? new Wrapper<>(dto.rateCardId.to) : null;
    }

    @Override
    public LocalDate getStart() {
        return dto.startDate != null
                ? JodaToJDKAdapters.localDateToJoda(dto.startDate.to)
                : null;
    }

    @Override
    public LocalTime getStartTime() {
        return (dto.time != null && dto.time.to != null)
                ? JodaToJDKAdapters.localTimeToJoda(dto.time.to)
                : null;
    }

    @Override
    public LocalDate getEnd() {
        return dto.endDate != null
                ? JodaToJDKAdapters.localDateToJoda(dto.endDate.to)
                : null;
    }
    @Override
    public boolean startChanged() {
        return dto.startDate != null;
    }
    @Override
    public boolean endChanged() {
        return dto.endDate != null;
    }

    @Override
    public List<Integer> getDaysAdded() {
        return dto.days != null ? dto.days.added : null;
    }

    @Override
    public List<Integer> getDaysRemoved() {
        return dto.days != null ? dto.days.removed : null;
    }

    @Override
    public String getIntervalType() { return dto.intervalType != null ? dto.intervalType.to : null;}

    @Override
    public Integer getIntervalFrequency() {
        return dto.intervalFrequency != null ? dto.intervalFrequency.to : null;
    }

    @Override
    public String getResourceCalendarId() { return this.resourceCalendarId; }
}
