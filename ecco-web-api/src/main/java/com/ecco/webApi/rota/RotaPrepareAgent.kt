@file:Suppress("SameParameterValue")

package com.ecco.webApi.rota

import com.ecco.buildings.repositories.FixedContainerRepository
import com.ecco.config.dom.SoftwareModule
import com.ecco.config.service.SettingsService
import com.ecco.config.service.SoftwareModuleService
import com.ecco.infrastructure.time.JodaToJDKAdapters
import com.ecco.rota.service.CalendarEventSnapshotService
import com.ecco.rota.webApi.dto.AgreementResource
import com.ecco.rota.webApi.dto.Rota
import lombok.RequiredArgsConstructor
import org.joda.time.LocalDate
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.function.Consumer

@Controller
@RequiredArgsConstructor
class RotaPrepareAgent(
    private val rotaController: RotaController,
    private val agreementController: AgreementController,
    private val buildingRepository: FixedContainerRepository,
    private val calendarEventSnapshotService: CalendarEventSnapshotService,
    private val settingsService: SettingsService,
    private val softwareModuleService: SoftwareModuleService,
) {

    /** Cache 5mins after startup then every 999 days ;-) (i.e. only after startup) */
    @Scheduled(initialDelayString = "PT1M", fixedDelayString = "P999D")
    fun startup() {
        val rotaEnabled = softwareModuleService.getEnabledModules().get(SoftwareModule.MODULE_ROTA) != null
        if (rotaEnabled) {
            preCacheRota()
        }
        // prePopulateExtendSnapshotRota()
    }

    /** Spring's cron includes seconds (first), so this is 2.30am every day. */
    @Scheduled(cron = "#{environment.rotaPrepCron?:'0 30 2 * * *'}") // NOTE: this is UTC (override with -DrotaPrepCron="...")
    fun scheduled() {
        // TODO combine preCacheRota and prePopulate?
        //  if so, loop in day/week chunks for the duration of com.ecco.rota:care.scheduler.days
        //  which will mean we cache the snapshot, but also the correct srId in the process

        val rotaEnabled = softwareModuleService.getEnabledModules().get(SoftwareModule.MODULE_ROTA) != null
        if (rotaEnabled) {
            preCacheRota()
            prePopulateExtendSnapshotRota()
        }
    }

    /**
     * It gets every building (that isn't a care run) and caches the care run and rota page for the day.
     * It also does the same for Mon-Sun for this current week view, and tomorrow - for good measure?
     */
    @GetMapping("/rota/preCache")
    @ResponseStatus(HttpStatus.OK)
    fun preCacheRota() {
        val today = LocalDate.now()
        val monday = today.minusDays(today.dayOfWeek - 1) // 1 is Monday, 7 is Sunday
        val sunday = monday.plusDays(6)

        // NB we're not caching whole org rota
        // preCacheDemand("workers:all", "referrals:all:200003", today)

        // TODO cache service-based rotas by looping services_projects that have a buildingId
        //  because caching is based on demandFilter only - see RotaController.viewRota
        //  and exclude the same buildings below, as they are unlikely to overlap

        // we need to do per building as that's currently how the rota API is structured
        buildingRepository.findAll().stream() // Get List *then* Stream, so we don't require a transaction around whole process
            .filter { it.parent == null }
            .forEach {
                // 'runs' rota
                preCacheRota("careruns:all", "buildings:" + it.id, today) // today
                preCacheRota("careruns:all", "buildings:" + it.id, monday, sunday) // all week
                preCacheRota("careruns:all", "buildings:" + it.id, today.plusDays(1)) // tomorrow
                // TODO: establish if we can unify API for per srId for these
                // 'staff' rota
                preCacheRota("workers:all", "buildings:" + it.id, today)
                preCacheRota("workers:all", "buildings:" + it.id, monday, sunday)
                preCacheRota("workers:all", "buildings:" + it.id, today.plusDays(1))
            }
    }

    // TODO loop by services with agreements and call srId directly
    @GetMapping("/rota/prePopulate")
    @ResponseStatus(HttpStatus.OK)
    fun prePopulateAllSnapshotRota(
        @RequestParam srId: Int?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
    ) {
        val dte = JodaToJDKAdapters.localDateToJDk(from)?.atStartOfDay(ZoneId.of("UTC"))?.toInstant() ?: Instant.now()

        // if we know the srId, we don't need to find out from the agreements, so do the snapshot from 'dte' (end date is always today+care.scheduler.days)
        if (srId != null) {
            calendarEventSnapshotService.recreateRotaAppointmentsForServiceRecipient(
                srId,
                dte,
                null,
                false,
            )
        } else {
            prePopulateAllSnapshotRota(dte)
        }
    }

    // TODO loop by services with agreements and call srId directly
    // NB http://localhost:8899/api/rota/prePopulateExtend
    @GetMapping("/rota/prePopulateExtend")
    @ResponseStatus(HttpStatus.OK)
    fun prePopulateExtendSnapshotRota() {
        // NB we're not caching whole org rota
        // assume that the past is valid and the existing cache period
        // and only extend one day from the end of the care.scheduler.days
        val utc = ZoneId.of("UTC")
        val today = java.time.LocalDate.now(utc).atStartOfDay(utc).toInstant()
        val days = settingsService.settingFor("com.ecco.rota", "care.scheduler.days").asNumber
        val from = today.plus(days.toLong(), ChronoUnit.DAYS)

        prePopulateAllSnapshotRota(from)

        // TODO cache service-based rotas by looping services_projects that have a buildingId
        //  because caching is based on demandFilter only - see RotaController.viewRota
        //  and exclude the same buildings below, as they are unlikely to overlap
        /*
        // we need to do per building as that's currently how the rota API is structured
        buildingRepository.findAll().stream() // Get List *then* Stream, so we don't require a transaction around whole process
            .filter { it.parent == null }
            .forEach {
                // 'runs' rota
                prePopulateSnapshotRota("careruns:all", "buildings:" + it.id, today, to)
                // 'staff' rota
                prePopulateSnapshotRota("workers:all", "buildings:" + it.id, today, to)
            }*/
    }

    /**
     * Calendar code is quick for finding appointments, but finding all relevant ones for the page involves looping every demand.
     * We can avoid looping the demand by simply loading each resource, but this does not include unallocated appointments.
     * A better approach would be to use a calendar per resource (eg a building) and have all appointments there, so that
     * a whole page is just one request - although it might be that this shows delays as there will be a lot in a week?
     *
     * For now, we loop each service recipient demand and cache it. We only need the serviceRecipientId, but we use the same
     * logic as the rota via the agreements to ensure we are getting only relevant demand in the period (eg not closed clients etc).
     * To do this, the demandFilter is further appended with ':<srId>', which handlers pick up in getDemandServiceRecipientId.
     */
    private fun preCacheRota(resourceFilter: String, demandFilter: String, start: LocalDate, end: LocalDate = start) {
        val agreements = agreementController.findAllAgreementsByDemandAndScheduleDate(
            resourceFilter,
            demandFilter,
            start.toString(),
            end.toString(),
        )
        agreements
            // .take(20) // Maybe limit number of residents to pre-cache to balance memory usage for pre-caching (but actual footprint is tiny)
            .forEach(
                Consumer { a: AgreementResource ->
                    val srId = a.serviceRecipientId
                    // NB we could do something with this complete returned rota (for the srId) if we needed to process something else
                    val rota = prepRotaForServiceRecipient(resourceFilter, demandFilter, start, end, srId)
                    // prepCalendarEventSnapshotForServiceRecipient(srId);
                },
            )
    }

    /**
     * Extracted from CalendarEventSnapshot:
     *      Originally, this snapshot was for lone working to determine uncompleted visits, then it was pre-populated (DEV-2443)
     *      to allow us to determine missed/late visits easily for alerting. Now, its being used as a way to cache data which
     *      could be used for ScheduleView (and EvidenceView) replacing the findAllUninvoiced, but could even be used for the rota itself.
     * Summarised from DEV-2455:
     *      A service calendar could be quick, also with the caching, but the scheduler implies many visits which could overwhelm the calendar?
     *      We could also re-use the cache, but we'd still need a place for the uncompleted visit data (and anything other in-flight data).
     *      Re-using the snapshot table seems a sensible place for being quick to find the time-consuming data as unallocated visits or
     *      visits without any work. It could then be used by rotaView and EvidenceView - finding unallocated is the only reason to cache by srId.
     */
    private fun prepRotaForServiceRecipient(
        resourceFilter: String,
        demandFilter: String,
        start: LocalDate,
        end: LocalDate = start,
        srId: Int,
    ): Rota = rotaController.viewRota(
        null,
        null,
        resourceFilter,
        "$demandFilter:$srId",
        false,
        true,
        start,
        end,
    )

    // NB start is actually ignored, and becomes today, with end being days +1 - see prePopulatingUntilDate
    private fun prePopulateAllSnapshotRota(from: Instant) {
        // end date is always today+care.scheduler.days
        val utc = ZoneId.of("UTC")
        val today = java.time.LocalDate.now(utc).atStartOfDay(utc).toInstant()
        val days = settingsService.settingFor("com.ecco.rota", "care.scheduler.days").asNumber
        // add extra 1 to cover 1 day, as per recreateRotaAppointmentsForServiceRecipient, we extend one day if the same
        val to = today.plus(days.toLong() + 1, ChronoUnit.DAYS)

        val resourceFilter = "workers:all"
        val demandFilter = "referrals:all"
        val agreements = agreementController.findAllAgreementsByDemandAndScheduleDate(
            resourceFilter,
            demandFilter,
            from.atZone(utc).format(DateTimeFormatter.ISO_LOCAL_DATE),
            to.atZone(utc).format(DateTimeFormatter.ISO_LOCAL_DATE),
        )
        agreements
            // .take(20) // Maybe limit number of residents to pre-cache to balance memory usage for pre-caching (but actual footprint is tiny)
            .forEach(
                Consumer { a: AgreementResource ->
                    val srId = a.serviceRecipientId
                    // NB we did have the below, to allow of the closed upper value, but we can just extend the care.scheduler.days
                    // val end = start.plus(1, ChronoUnit.DAYS)
                    // val range = Range.closed(start, end)
                    calendarEventSnapshotService.recreateRotaAppointmentsForServiceRecipient(srId, from, null, true)
                },
            )
    }
}