package com.ecco.webApi.rota;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * View model, designed for JSON serialization.
 */
@NoArgsConstructor(access = AccessLevel.PACKAGE)
public class ContractTaskContractDetailCommandViewModel extends TaskContractDetailAbstractCommandViewModel {

    static String TASK_CONTRACT_DETAIL = "contractDetail";

    public ContractTaskContractDetailCommandViewModel(String taskHandle) {
        super(TASK_CONTRACT_DETAIL, taskHandle);
    }

}
