package com.ecco.webApi.contacts;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.hr.Worker;
import java.util.function.Function;

import org.jspecify.annotations.Nullable;
import java.util.stream.Collectors;

public final class WorkerToViewModel implements Function<Worker, WorkerViewModel> {
    private final ClientDetailAbstractToViewModel detailToViewModel = new ClientDetailAbstractToViewModel();
    private final PersonToViewModel personToViewModel;
    private final WorkerJobToViewModel workerjobToViewModel = new WorkerJobToViewModel();
    private final boolean includeJobs;

    public WorkerToViewModel(ListDefinitionRepository listDefinitionRepository, boolean includeJobs) {
        this.personToViewModel = new PersonToViewModel(listDefinitionRepository);
        this.includeJobs = includeJobs;
    }

    @Nullable
    @Override
    public WorkerViewModel apply(@Nullable Worker input) {
        if (input == null) {
            throw new NullPointerException("input Worker must not be null");
        }

        WorkerViewModel result = new WorkerViewModel();

        personToViewModel.apply(input, result);
        detailToViewModel.apply(input, result);

        result.workerId = input.getId();
        result.displayName = input.getDisplayName();
        result.contactId = input.getContact().getId();
        result.calendarId = input.getContact().getCalendarId();
        if (input.getPrimaryLocation() != null) {
            result.primaryLocationId = input.getPrimaryLocation().getId();
            result.primaryLocationName = input.getPrimaryLocation().getName();
        }
        result.crbNumber = input.getCrbNumber();

        if (includeJobs) {
            result.jobs = input.getWorkerJobs().stream()
                    .map(workerjobToViewModel)
                    .collect(Collectors.toList());
        }

        return result;
    }
}
