package com.ecco.webApi.contacts.address;

import com.ecco.dom.AddressedLocation;

import org.jspecify.annotations.Nullable;

import java.util.Arrays;
import java.util.function.Function;

/**
 * Convert an AddressLocation (not Address) into AddressViewModel, for contact-dto's Address.
 */
public final class AddressedLocationToViewModel implements Function<AddressedLocation, AddressViewModel> {

    @Nullable
    @Override
    public AddressViewModel apply(@Nullable AddressedLocation input) {
        if (input == null) {
            throw new NullPointerException("input Address must not be null");
        }

        AddressViewModel addressViewModel = new AddressViewModel();
        addressViewModel.addressId = input.getId();
        addressViewModel.disabled = input.isDisabled();
        addressViewModel.address = Arrays.asList(input.getLine1(), input.getLine2(), input.getLine3())
                .toArray(new String[3]);
        addressViewModel.town = input.getTown();
        addressViewModel.postcode = input.getPostCode();
        addressViewModel.county = input.getCounty();

        return addressViewModel;
    }
}
