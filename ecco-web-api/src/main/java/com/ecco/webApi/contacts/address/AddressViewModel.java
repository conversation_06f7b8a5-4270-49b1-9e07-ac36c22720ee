package com.ecco.webApi.contacts.address;

import com.ecco.dom.contacts.Address;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import org.jspecify.annotations.Nullable;

/**
 * A postal address.
 */
@Getter
@Setter
@ToString
public class AddressViewModel {

    /**
     * Used with the newer AddressedLocation - eg EditAddressedLocationForm
     */
    @Nullable
    public Integer addressId;

    public Boolean disabled;

    /**
     * Components of the address within a town/city/district, usually up to
     * three entries.
     */
    public String[] address = new String[3];

    /**
     * The town/city/district of this address.
     */
    public String town;

    /**
     * The county of this address, or null if not known.
     */
    public String county;

    /**
     * The postcode, or null if not known.
     */
    public String postcode;

    public void setLine1(String line1) {
        this.address[0] = line1;
    }

    // Property accessors are unfortunately required for com.ecco.data.client.csv.CSVBeanReader.
    // See ECCO-703

    // NB Copied from Address just for convenience

    public String toCommaSepString() {
        final String separator = ", ";
        StringBuilder builder = new StringBuilder()
                .append(appendIfExists(address[0], separator))
                .append(appendIfExists(address[1], separator))
                .append(appendIfExists(address[2], separator))
                .append(appendIfExists(getTown(), separator))
                .append(appendIfExists(getCounty(), separator))
                .append(appendIfExists(getPostcode(), separator));

        // lose the last separator and space
        String commandStr = builder.toString();
        if (commandStr.length() > 2) {
            commandStr = StringUtils.left(commandStr, commandStr.length()-2);
        }
        return commandStr;
    }

    public static String appendIfExists(String value, String separator) {
        if (StringUtils.isNotBlank(value))
            return value + separator;
        else
            return "";
    }

    public static AddressViewModel fromAddress(Address src) {
        if (src == null) {
            return null;
        }
        AddressViewModel addr = new AddressViewModel();
        addr.address[0] = src.getLine1();
        addr.address[1] = src.getLine2();
        addr.address[2] = src.getLine3();
        addr.town = src.getTown();
        addr.postcode = src.getPostCode();
        addr.county = src.getCounty();
        //addr.country = src.getCountry();
        return addr;
    }
}
