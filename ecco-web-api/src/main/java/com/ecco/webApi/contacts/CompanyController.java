package com.ecco.webApi.contacts;

import com.ecco.dao.ReferralRepository;
import com.ecco.dom.Company;
import com.ecco.security.repositories.CompanyRepository;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.webApi.controllers.BaseWebApiController;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class CompanyController extends BaseWebApiController {

    private final CompanyRepository companyRepository;

    private final IndividualRepository individualRepository;

    private final ReferralRepository referralRepository;

    private final CompanyToViewModel companyToViewModel = new CompanyToViewModel();


    @Autowired
    public CompanyController(CompanyRepository companyRepository, IndividualRepository individualRepository,
            ReferralRepository referralRepository) {
        this.companyRepository = companyRepository;
        this.individualRepository = individualRepository;
        this.referralRepository = referralRepository;
    }

    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    @RequestMapping(value = "/company/{id}/", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompanyViewModel findOne(@PathVariable long id) {
        return companyToViewModel.apply(companyRepository.findById(id).orElse(null));
    }

    @RequestMapping("/company/merge/{oldCompanyId}/into/{masterCompanyId}")
    public void merge(@PathVariable long oldCompanyId,
            @PathVariable long masterCompanyId) {
        Company master = companyRepository.findById(masterCompanyId).orElseThrow();
        Company old = companyRepository.findById(oldCompanyId).orElseThrow();
        master.inheritUnsetFieldsFrom(old);
        companyRepository.save(master);

        // NOTABLY more to do on this with address etc
        individualRepository.bulkSwitchCompany(oldCompanyId, masterCompanyId);
        referralRepository.bulkSwitchAgency(oldCompanyId, masterCompanyId);
        referralRepository.bulkSwitchSignpostedAgency(oldCompanyId, masterCompanyId);

        old.setCompanyName(old.getCompanyName() + " (to delete)");
    };
}
