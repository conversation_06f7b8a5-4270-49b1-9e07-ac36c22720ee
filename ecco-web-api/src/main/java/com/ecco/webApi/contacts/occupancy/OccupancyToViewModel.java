package com.ecco.webApi.contacts.occupancy;

import com.ecco.dom.contacts.AddressHistory;
import com.ecco.webApi.contacts.address.BaseHistoryToViewModel;

public final class OccupancyToViewModel extends BaseHistoryToViewModel<OccupancyViewModel> {

    public OccupancyToViewModel() {
        super(OccupancyViewModel::new);
    }

    @Override
    protected void customise(AddressHistory input, OccupancyViewModel result) {
    }

}
