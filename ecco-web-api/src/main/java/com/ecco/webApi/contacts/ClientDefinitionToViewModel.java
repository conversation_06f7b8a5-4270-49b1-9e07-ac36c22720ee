package com.ecco.webApi.contacts;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dto.ClientDefinition;
import java.util.function.Function;

import org.jspecify.annotations.Nullable;

/**
 * Takes a ClientDefinition (involved in communicating with external systems) and converts to a ClientViewModel for displaying in ECCO
 */
public class ClientDefinitionToViewModel extends PersonDefinitionToViewModel implements Function<ClientDefinition, ClientViewModel> {

    public ClientDefinitionToViewModel(ListDefinitionRepository listDefinitionRepository) {
        super(listDefinitionRepository);
    }

    @Nullable
    @Override
    public ClientViewModel apply(@Nullable ClientDefinition input) {
        if (input == null) {
            return null;
        }
        final ClientViewModel viewModel = new ClientViewModel();
        viewModel.setClientId(input.getLocalClientId());
        viewModel.setContactId(input.getLocalContactId());
        viewModel.setCode(input.getCode());
        viewModel.setNi(input.getNi());
        applyPersonFields(input, viewModel);
        viewModel.setResidenceId(input.getAssignedLocationId());
        viewModel.setResidenceName(input.getAssignedLocationName());

        return viewModel;
    }

}
