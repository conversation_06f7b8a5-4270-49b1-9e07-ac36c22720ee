package com.ecco.webApi.contacts;

import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;

/**
 * Alters the link between a contact and the srId.
 * We group together the associatedTypes in the same command. This makes it clearer in the audits of the same command
 * but does suggest a restriction of data model in that any notes/comments will be on the link, not the link+associatedType,
 * but we could always create a new command for that which requires srId+contactId+associatedTypeId.
 */
public class ServiceRecipientAssociatedContactCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public int contactId;

    @NonNull
    public String operation;

    @Nullable
    public ChangeViewModel<String> addedAssociatedTypeIds; // csv like addedThreatFlags
    @Nullable
    public ChangeViewModel<String> removedAssociatedTypeIds;

    @Nullable
    public ChangeViewModel<String> archivedChange;

    @Nullable
    public Integer associatedServiceRecipientId;
    @Nullable
    public ChangeViewModel<Integer> associatedRelationship;

    /** For Jackson etc */
    @Deprecated
    ServiceRecipientAssociatedContactCommandViewModel() {
        super();
    }

    public ServiceRecipientAssociatedContactCommandViewModel(@NonNull String operation, int serviceRecipientId, int contactId) {
        super(UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/contact/{contactId}/command/")
                .buildAndExpand(serviceRecipientId, contactId)
                .toString(),
            serviceRecipientId);
        this.operation = operation;
        this.contactId = contactId;
    }

    public boolean hasChanges() {
        return archivedChange != null || addedAssociatedTypeIds != null || removedAssociatedTypeIds != null || associatedRelationship != null;
    }
}
