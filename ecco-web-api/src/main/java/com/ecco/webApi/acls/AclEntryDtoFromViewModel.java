package com.ecco.webApi.acls;

import java.util.function.Function;

import org.jspecify.annotations.Nullable;
import org.springframework.security.acls.model.Permission;

import com.ecco.security.dto.AclEntryDto;
import com.ecco.util.ClassUtils;

public class AclEntryDtoFromViewModel implements Function<AclEntryViewModel, AclEntryDto> {

    @Override
    @Nullable
    public AclEntryDto apply(@Nullable AclEntryViewModel input) {
        if (input == null) {
            throw new NullPointerException("input AclEntryDto must not be null");
        }

        Class<?> clazz = ClassUtils.getEntityClass(input.getClazz(), true);

        // NB nothing is done in terms of processing this permission
        // DummyPermission uses the same equals/hashcode method - which operates purely on mask
        Permission p = new DummyPermission(input.getPermissionMask());

        return new AclEntryDto(input.getUsername(), input.getSecureObjectId(), clazz, p);
    }

}

