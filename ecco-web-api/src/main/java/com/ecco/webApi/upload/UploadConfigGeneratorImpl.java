package com.ecco.webApi.upload;

import com.ecco.dao.ServiceRecipientAttachmentRepository;
import com.ecco.dom.TaskDefinitionNameIdMappings;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.service.TaskDefinitionService;
import com.ecco.service.upload.UploadConfigServiceRecipient;
import com.ecco.upload.dao.SimpleUploadedFileRepository;
import com.ecco.web.upload.BasicUploadConfig;
import com.ecco.web.upload.UploadConfig;
import com.ecco.web.upload.UploadConfigGenerator;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.multipart.MultipartFile;

import org.jspecify.annotations.Nullable;
import javax.servlet.http.HttpServletRequest;

@Component
public class UploadConfigGeneratorImpl implements UploadConfigGenerator {

    private final ServiceRecipientAttachmentRepository uploadRepository;
    private final SimpleUploadedFileRepository basicRepository;
    private final TaskDefinitionService taskDefinitionService;

    @Autowired
    public UploadConfigGeneratorImpl(
            ServiceRecipientAttachmentRepository uploadRepository,
            SimpleUploadedFileRepository basicRepository,
            TaskDefinitionService taskDefinitionService) {
        this.uploadRepository = uploadRepository;
        this.basicRepository = basicRepository;
        this.taskDefinitionService = taskDefinitionService;
    }

    @SneakyThrows
    @Override
    public UploadConfig<?> constructUploadConfig(HttpServletRequest request, MultipartFile file) {
        Integer serviceRecipientId;
        try {
            serviceRecipientId = request != null ? ServletRequestUtils.getIntParameter(request, "serviceRecipientId"): null;
        } catch (ServletRequestBindingException e) {
            serviceRecipientId = null; // which is just what getLongParamter does when we supply a default
        }

        String source = request != null ? ServletRequestUtils.getStringParameter(request, "source"): null;

        return constructUploadConfig(UploadedFile.Source.fromString(source), request, file, serviceRecipientId);

        //throw new IllegalArgumentException("no workerId or referralId present for the upload to attach to");
    }

    @Override
    public UploadConfig<?> constructUploadConfig(
            UploadedFile.Source source,
            HttpServletRequest request,
            MultipartFile file,
            @Nullable Integer serviceRecipientId
    ) {
        String taskName = getTaskName(request);
        String evidenceGroupName = getEvidenceGroupName(request);
        return switch (source) {
            case SERVICE_RECIPIENT -> new UploadConfigServiceRecipient(serviceRecipientId, file, taskName,
                    evidenceGroupName, uploadRepository);
            case NONE -> new BasicUploadConfig(file, basicRepository);
            default -> throw new IllegalArgumentException("Source '" + source + "' is not supported.");
        };
    }

    @SneakyThrows
    private String getTaskName(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String taskName = ServletRequestUtils.getStringParameter(request, "taskName");
        return StringUtils.isBlank(taskName) ? null
                : String.valueOf(TaskDefinitionNameIdMappings.fromTaskNameToTaskDefId(taskName));
    }

    @SneakyThrows
    private String getEvidenceGroupName(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String evidenceGroupName = ServletRequestUtils.getStringParameter(request, "evidenceGroupName");
        if (StringUtils.isBlank(evidenceGroupName)) {
            return null;
        } else {
            return String.valueOf(taskDefinitionService.findGroupFromGroupName(evidenceGroupName).getId());
        }
    }

}
