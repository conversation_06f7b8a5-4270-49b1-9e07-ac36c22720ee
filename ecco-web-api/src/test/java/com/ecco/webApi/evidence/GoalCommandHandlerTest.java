package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceSupportAction;
import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.Individual;
import com.ecco.dto.ChangeViewModel;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class GoalCommandHandlerTest {

    @InjectMocks
    private GoalCommandHandler handler;

    private TestingAuthenticationToken authentication;

    @Before
    public void setUp() {
        Individual individual = Individual.builder("staff", "member").withNewUser("staff1").build();

        authentication = new TestingAuthenticationToken(individual.getUser(), null);
        authentication.setAuthenticated(true);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    @Test
    public void givenCronExpressionThenTargetDateReturned() {
        GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel();
        vm.serviceRecipientId = 99;
        vm.targetScheduleChange = ChangeViewModel.changeNullTo(Schedule.from(java.time.LocalDate.now().plusDays(1)).getScheduleString());
        vm.timestamp = Instant.now();

        EvidenceSupportAction newSnapshot = applyToHandler(vm);

        assertEquals(LocalDate.now().toDateTimeAtStartOfDay().plusDays(1), newSnapshot.getTarget());
    }

    @Test
    public void givenStartAndScheduleAndEndThenGoodTargetDateReturned() {
        GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel();
        vm.serviceRecipientId = 99;
        vm.targetDateChange = ChangeViewModel.changeNullTo(LocalDate.now().plusDays(2));
        LocalDate nextDueSchedule = LocalDate.now().plusDays(5);
        vm.targetScheduleChange = ChangeViewModel.changeNullTo("start:"+nextDueSchedule.toString(ISODateTimeFormat.date())); // yyyy-MM-dd
        vm.timestamp = Instant.now();

        EvidenceSupportAction newSnapshot = applyToHandler(vm);

        assertEquals(nextDueSchedule.toDateTimeAtStartOfDay(), newSnapshot.getTarget());
    }

    private EvidenceSupportAction applyToHandler(GoalUpdateCommandViewModel vm) {
        EvidenceSupportAction newSnapshot = EvidenceSupportAction.builder(99)
                .withActionId(99)
                .build();
        EvidenceSupportWork work = new EvidenceSupportWork();
        work.setWorkDate(DateTime.now());
        newSnapshot.setWork(work);
        EvidenceSupportAction oldSnapshot = newSnapshot; // as per GoalCommandHandler.82
        handler.applyCommonUpdates(authentication, vm, oldSnapshot, newSnapshot);
        return newSnapshot;
    }
}
