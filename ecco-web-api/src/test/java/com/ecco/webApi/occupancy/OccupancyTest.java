package com.ecco.webApi.occupancy;

import com.ecco.dom.ReportCriteriaDto;
import com.ecco.webApi.contacts.occupancy.OccupancyViewModel;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import static org.assertj.core.api.Assertions.assertThat;

public class OccupancyTest {

    private ReportCriteriaDto dto;

    @Before
    public void setUp() {
        dto = new ReportCriteriaDto();
        // Set default date range for testing
        dto.setFrom("2023-01-01");
        dto.setTo("2023-12-31");
    }

    @Test
    public void testFillOccupancyGaps_EmptyList() {
        List<OccupancyViewModel> input = new ArrayList<>();

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).isEmpty();
    }

    @Test
    public void testFillOccupancyGaps_SingleEntry() {
        var from = LocalDateTime.of(2023, 1, 1, 0, 0);
        var to = LocalDateTime.of(2023, 6, 30, 0, 0);
        ReportCriteriaDto dtoMatch = new ReportCriteriaDto();
        dtoMatch.setFrom(from.format(DateTimeFormatter.ISO_DATE));
        dtoMatch.setTo(to.format(DateTimeFormatter.ISO_DATE));

        List<OccupancyViewModel> input = new ArrayList<>();
        OccupancyViewModel entry = createOccupancyEntry(1, 123,
                from, to,
                "tenant");
        input.add(entry);

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dtoMatch);

        assertThat(result.size()).isEqualTo(1);
    }

    @Test
    public void testFillOccupancyGaps_NoGapBetweenEntries() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Consecutive entries with no gap
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 6, 30, 0, 0),
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 6, 30, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result.size()).isEqualTo(2);
    }

    @Test
    public void testFillOccupancyGaps_WithGapBetweenEntries() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entries with a gap
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 6, 30, 0, 0),
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 9, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(3);

        // Check the order and content (sorted by validFrom descending)
        assertThat(result.get(0).validFrom).isEqualTo(LocalDateTime.of(2023, 9, 1, 0, 0));
        assertThat(result.get(0).validTo).isEqualTo(LocalDateTime.of(2023, 12, 31, 0, 0));

        // Gap entry should be inserted
        assertThat(result.get(1).validFrom).isEqualTo(LocalDateTime.of(2023, 6, 30, 0, 0));
        assertThat(result.get(1).validTo).isEqualTo(LocalDateTime.of(2023, 9, 1, 0, 0));
        assertThat(result.get(1).buildingId).isEqualTo(123);
        assertThat(result.get(1).serviceRecipientId).isNull();
        assertThat(result.get(1).id).isNull();

        assertThat(result.get(2).validFrom).isEqualTo(LocalDateTime.of(2023, 1, 1, 0, 0));
        assertThat(result.get(2).validTo).isEqualTo(LocalDateTime.of(2023, 6, 30, 0, 0));
    }

    @Test
    public void testFillOccupancyGaps_MultipleBuildingsWithGaps() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Building 123 with gap
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 6, 30, 0, 0),
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 9, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));

        // Building 456 with gap
        input.add(createOccupancyEntry(3, 456,
                LocalDateTime.of(2023, 2, 1, 0, 0),
                LocalDateTime.of(2023, 5, 31, 0, 0),
                "tenant3"));
        input.add(createOccupancyEntry(4, 456,
                LocalDateTime.of(2023, 8, 1, 0, 0),
                LocalDateTime.of(2023, 11, 30, 0, 0),
                "tenant4"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(8); // 4 original + 4 gap entries (2 internal + 2 DTO gaps for building 456)

        // Count unoccupied entries - we expect 4 gap entries total
        // (1 internal gap for building 123, 1 internal + 2 DTO gaps for building 456)
        long unoccupiedCount = result.stream()
                .filter(OccupancyViewModel::isUnoccupied)
                .count();
        assertThat(unoccupiedCount).isEqualTo(4);

        // Verify entries are sorted by validFrom descending (most recent first)
        List<LocalDateTime> validFromOrder = result.stream()
                .map(vm -> vm.validFrom)
                .toList();

        // Should be sorted by validFrom descending
        for (int i = 0; i < validFromOrder.size() - 1; i++) {
            assertThat(validFromOrder.get(i)).isAfterOrEqualTo(validFromOrder.get(i + 1));
        }
    }

    // NB Not sure if occupancy should be allowed with no buildingId - perhaps filter out in the loading
    @Test
    public void testFillOccupancyGaps_NullBuildingId() {
        var from = LocalDateTime.of(2023, 1, 1, 0, 0);
        var to = LocalDateTime.of(2023, 6, 30, 0, 0);
        ReportCriteriaDto dtoMatch = new ReportCriteriaDto();
        dtoMatch.setFrom(from.format(DateTimeFormatter.ISO_DATE));
        dtoMatch.setTo(to.format(DateTimeFormatter.ISO_DATE));

        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry with null buildingId
        input.add(createOccupancyEntry(1, null,
                from, to,
                "tenant1"));

        // Entry with valid buildingId
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 12, 30, 0, 0),
                "tenant2"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dtoMatch);

        assertThat(result).hasSize(2);

        // Null buildingId entry should be preserved
        assertThat(result.stream().anyMatch(vm -> vm.buildingId == null)).isTrue();
        assertThat(result.stream().anyMatch(vm -> vm.buildingId != null && vm.buildingId.equals(123))).isTrue();
    }

    @Test
    public void testFillOccupancyGaps_NullValidTo() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry with null validTo (ongoing occupancy)
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                null, // ongoing
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 9, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        // Should not create gap entry when validTo is null
        assertThat(result).hasSize(2);
        assertThat(result.stream().filter(OccupancyViewModel::isUnoccupied).count()).isEqualTo(0);
    }

    @Test
    public void testFillOccupancyGaps_MultipleGapsInSameBuilding() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Building with multiple gaps
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 3, 31, 0, 0),
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 6, 1, 0, 0),
                LocalDateTime.of(2023, 8, 31, 0, 0),
                "tenant2"));
        input.add(createOccupancyEntry(3, 123,
                LocalDateTime.of(2023, 11, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant3"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(5); // 3 original + 2 gap entries

        // Verify the sequence (sorted by validFrom descending)
        assertThat(OccupancyViewModel.isUnoccupied(result.get(0))).isFalse(); // tenant3
        assertThat(OccupancyViewModel.isUnoccupied(result.get(1))).isTrue(); // Gap 2
        assertThat(OccupancyViewModel.isUnoccupied(result.get(2))).isFalse(); // tenant2
        assertThat(OccupancyViewModel.isUnoccupied(result.get(3))).isTrue(); // Gap 1
        assertThat(OccupancyViewModel.isUnoccupied(result.get(4))).isFalse(); // tenant1

        // Verify gap periods
        assertThat(result.get(1).validFrom).isEqualTo(LocalDateTime.of(2023, 8, 31, 0, 0));
        assertThat(result.get(1).validTo).isEqualTo(LocalDateTime.of(2023, 11, 1, 0, 0));

        assertThat(result.get(3).validFrom).isEqualTo(LocalDateTime.of(2023, 3, 31, 0, 0));
        assertThat(result.get(3).validTo).isEqualTo(LocalDateTime.of(2023, 6, 1, 0, 0));
    }

    @Test
    public void testFillOccupancyGaps_UnsortedInput() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Add entries in wrong chronological order
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 9, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 6, 30, 0, 0),
                "tenant1"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(3);

        // Should be sorted correctly with gap filled (sorted by validFrom descending)
        assertThat(result.get(0).validFrom).isEqualTo(LocalDateTime.of(2023, 9, 1, 0, 0)); // id 2

        assertThat(result.get(1).validFrom).isEqualTo(LocalDateTime.of(2023, 6, 30, 0, 0)); // gap
        assertThat(result.get(1).validTo).isEqualTo(LocalDateTime.of(2023, 9, 1, 0, 0)); // gap

        assertThat(result.get(2).validFrom).isEqualTo(LocalDateTime.of(2023, 1, 1, 0, 0)); // id 1
    }

    @Test
    public void testFillOccupancyGaps_OverlappingEntries() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Overlapping entries (edge case - shouldn't create gaps)
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 7, 31, 0, 0),
                "tenant1"));
        input.add(createOccupancyEntry(2, 123,
                LocalDateTime.of(2023, 6, 1, 0, 0), // Overlaps with previous
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant2"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(2); // No gap should be created for overlapping entries
        assertThat(result.stream().filter(OccupancyViewModel::isUnoccupied).count()).isEqualTo(0);
    }

    @Test
    public void testCreateUnoccupiedEntry() {
        Integer buildingId = 123;
        LocalDateTime validFrom = LocalDateTime.of(2023, 6, 30, 0, 0);
        LocalDateTime validTo = LocalDateTime.of(2023, 9, 1, 0, 0);

        OccupancyViewModel result = OccupancyViewModel.createUnoccupiedEntry(buildingId, validFrom, validTo);

        assertThat(result.id).isNull();
        assertThat(result.serviceRecipientId).isNull();
        assertThat(result.validFrom).isEqualTo(validFrom);
        assertThat(result.validTo).isEqualTo(validTo);
        assertThat(result.contactId).isNull();
        assertThat(result.buildingId).isEqualTo(buildingId);
        assertThat(result.addressId).isNull();
    }

    @Test
    public void testFillOccupancyGaps_WithDtoStartGap() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry starts after DTO start date
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 6, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant1"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(2); // Original entry + start gap

        // First entry should be the original (sorted by validFrom descending)
        assertThat(OccupancyViewModel.isUnoccupied(result.get(0))).isFalse();
        assertThat(result.get(0).validFrom).isEqualTo(LocalDateTime.of(2023, 6, 1, 0, 0));

        // Second entry should be the gap from DTO start to first entry
        assertThat(OccupancyViewModel.isUnoccupied(result.get(1))).isTrue();
        assertThat(result.get(1).validFrom).isEqualTo(LocalDateTime.of(2023, 1, 1, 0, 0));
        assertThat(result.get(1).validTo).isEqualTo(LocalDateTime.of(2023, 6, 1, 0, 0));
        assertThat(result.get(1).buildingId).isEqualTo(123);
    }

    @Test
    public void testFillOccupancyGaps_WithDtoEndGap() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry ends before DTO end date
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 6, 30, 0, 0),
                "tenant1"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(2); // Original entry + end gap

        // First entry should be the gap from last entry to DTO end (sorted by validFrom descending)
        assertThat(OccupancyViewModel.isUnoccupied(result.get(0))).isTrue();
        assertThat(result.get(0).validFrom).isEqualTo(LocalDateTime.of(2023, 6, 30, 0, 0));
        assertThat(result.get(0).validTo).isEqualTo(LocalDateTime.of(2023, 12, 31, 0, 0));
        assertThat(result.get(0).buildingId).isEqualTo(123);

        // Second entry should be the original
        assertThat(OccupancyViewModel.isUnoccupied(result.get(1))).isFalse();
        assertThat(result.get(1).validTo).isEqualTo(LocalDateTime.of(2023, 6, 30, 0, 0));
    }

    @Test
    public void testFillOccupancyGaps_WithBothDtoGaps() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry that doesn't cover the full DTO date range
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 6, 1, 0, 0),
                LocalDateTime.of(2023, 8, 31, 0, 0),
                "tenant1"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(3); // Start gap + original entry + end gap

        // First entry: gap from last entry to DTO end (sorted by validFrom descending)
        assertThat(OccupancyViewModel.isUnoccupied(result.get(0))).isTrue();
        assertThat(result.get(0).validFrom).isEqualTo(LocalDateTime.of(2023, 8, 31, 0, 0));
        assertThat(result.get(0).validTo).isEqualTo(LocalDateTime.of(2023, 12, 31, 0, 0));

        // Second entry: original
        assertThat(OccupancyViewModel.isUnoccupied(result.get(1))).isFalse();

        // Third entry: gap from DTO start to first entry
        assertThat(OccupancyViewModel.isUnoccupied(result.get(2))).isTrue();
        assertThat(result.get(2).validFrom).isEqualTo(LocalDateTime.of(2023, 1, 1, 0, 0));
        assertThat(result.get(2).validTo).isEqualTo(LocalDateTime.of(2023, 6, 1, 0, 0));
    }

    @Test
    public void testFillOccupancyGaps_NoDtoGapsNeeded() {
        List<OccupancyViewModel> input = new ArrayList<>();

        // Entry that exactly covers the DTO date range
        input.add(createOccupancyEntry(1, 123,
                LocalDateTime.of(2023, 1, 1, 0, 0),
                LocalDateTime.of(2023, 12, 31, 0, 0),
                "tenant1"));

        List<OccupancyViewModel> result = OccupancyViewModel.fillOccupancyGaps(input, dto);

        assertThat(result).hasSize(1); // Only the original entry
        assertThat(OccupancyViewModel.isUnoccupied(result.get(0))).isFalse();
    }

    @Test
    public void testGetDays_WithValidFromAndValidTo() {
        LocalDateTime validFrom = LocalDateTime.of(2023, 1, 1, 0, 0);
        LocalDateTime validTo = LocalDateTime.of(2023, 1, 11, 0, 0); // 10 days later

        OccupancyViewModel entry = createOccupancyEntry(1, 123, validFrom, validTo, "tenant");

        assertThat(OccupancyViewModel.getDays(entry)).isEqualTo(10);
    }

    @Test
    public void testGetDays_WithNullValidTo() {
        LocalDateTime validFrom = LocalDateTime.of(2023, 1, 1, 0, 0);

        OccupancyViewModel entry = createOccupancyEntry(1, 123, validFrom, null, "tenant");

        // Should calculate days from validFrom to now
        long expectedDays = ChronoUnit.DAYS.between(validFrom, LocalDateTime.now());
        assertThat(OccupancyViewModel.getDays(entry)).isEqualTo(expectedDays);
    }

    @Test
    public void testGetDays_WithNullValidFrom() {
        LocalDateTime validTo = LocalDateTime.of(2023, 1, 11, 0, 0);

        OccupancyViewModel entry = createOccupancyEntry(1, 123, null, validTo, "tenant");

        assertThat(OccupancyViewModel.getDays(entry)).isEqualTo(0);
    }

    @Test
    public void testGetDays_SameDay() {
        LocalDateTime validFrom = LocalDateTime.of(2023, 1, 1, 9, 0);
        LocalDateTime validTo = LocalDateTime.of(2023, 1, 1, 17, 0); // Same day, different times

        OccupancyViewModel entry = createOccupancyEntry(1, 123, validFrom, validTo, "tenant");

        assertThat(OccupancyViewModel.getDays(entry)).isEqualTo(0);
    }

    @Test
    public void testCreateUnoccupiedEntry_DaysCalculation() {
        Integer buildingId = 123;
        LocalDateTime validFrom = LocalDateTime.of(2023, 6, 1, 0, 0);
        LocalDateTime validTo = LocalDateTime.of(2023, 6, 16, 0, 0); // 15 days later

        OccupancyViewModel result = OccupancyViewModel.createUnoccupiedEntry(buildingId, validFrom, validTo);

        assertThat(result.days).isEqualTo(15);
        assertThat(OccupancyViewModel.getDays(result)).isEqualTo(15);
    }

    @Test
    public void testCreateUnoccupiedEntry_WithNullValidTo() {
        Integer buildingId = 123;
        LocalDateTime validFrom = LocalDateTime.of(2023, 6, 1, 0, 0);

        OccupancyViewModel result = OccupancyViewModel.createUnoccupiedEntry(buildingId, validFrom, null);

        long expectedDays = ChronoUnit.DAYS.between(validFrom, LocalDateTime.now());
        assertThat(result.days).isEqualTo(expectedDays);
        assertThat(OccupancyViewModel.getDays(result)).isEqualTo(expectedDays);
    }

    private OccupancyViewModel createOccupancyEntry(Integer id, Integer buildingId,
                                                    LocalDateTime validFrom, LocalDateTime validTo, String occupancyType) {
        OccupancyViewModel entry = new OccupancyViewModel();
        entry.id = id;
        entry.buildingId = buildingId;
        entry.validFrom = validFrom;
        entry.validTo = validTo;
        entry.days = validFrom != null ? ChronoUnit.DAYS.between(validFrom, validTo != null ? validTo : LocalDateTime.now()) : 0L;
        //entry.occupancyType = occupancyType;
        entry.serviceRecipientId = id; // Use id as serviceRecipientId for simplicity
        return entry;
    }
}
