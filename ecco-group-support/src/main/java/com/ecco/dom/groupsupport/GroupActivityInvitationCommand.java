package com.ecco.dom.groupsupport;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;


@Entity
@DiscriminatorValue("clientInvitation")
public class GroupActivityInvitationCommand extends GroupSupportCommand {
    public GroupActivityInvitationCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull UUID activityUuid, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, activityUuid, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected GroupActivityInvitationCommand() {
    }
}
