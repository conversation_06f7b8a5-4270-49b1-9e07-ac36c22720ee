package com.ecco.dom.contracts;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import com.google.common.collect.Range;
import kotlin.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Implementation for choosing RateCard entries to charge by, and the calculation based on the visit time.
 * This assumes there is nothing smaller than a minute to charge by.
 * This is based on the rate card entries CHOOSING days/time and so they do not need to be chosen by the schedule.
 * It also doesn't use the logic of rate entries having children. It could but it may be easier to change here than the UI
 * and perhaps not have the concept of child rate card entries - which could simplify things. Instead it just sorts
 * multiple entries by durationMins and applies accordingly.
 */
@RequiredArgsConstructor
@Slf4j
@NullMarked
public abstract class RateCardCalculationBase implements RateCardCalculation {

    public static boolean isWeekend(OffsetDateTime dateTime) {
        var day = dateTime.getDayOfWeek().getValue();
        return day == 5 || day == 6;
    }

    public Optional<RateCard> getRateCardInDate(Instant instant, List<RateCard> rateCards, boolean matchDayTimeOfDay) {
        var singleDate = Range.atLeast(instant);
        var inDate = getRateCardsInDateRange(singleDate, rateCards);

        if (matchDayTimeOfDay) {
            // MATCH ON day/time - ie the day of the week and time of day, not just in date
            var matchesOnDayTime = inDate.stream().filter(r -> this.matchOnDayTime(r, instant));
            return matchesOnDayTime.findFirst();
        } else {
            return inDate.stream().findFirst();
        }

    }

    public List<RateCard> getRateCardsInDateRange(Range<Instant> instant, List<RateCard> rateCards) {
        return rateCards.stream().filter(rc -> {
            boolean isBefore = !instant.hasUpperBound() || rc.getStartInstant().isBefore(instant.upperEndpoint());
            boolean isAfterOrNull = rc.getEndInstant() == null || rc.getEndInstant().isAfter(instant.lowerEndpoint());
            return isBefore && isAfterOrNull;
        }).toList();
    }

    public List<RateCardEntry> determineRateCardEntries(RateCard rateCard, @Nullable Integer matchCategoryTypeId,
                                                        @Nullable Integer matchChargeCategoryId, @Nullable Collection<String> matchFactors,
                                                        boolean useOnlyOneRateCardEntry) {

        // collect children/parents (parents can be null or a parent)
        var childrenIds = rateCard.getRateCardEntries().stream().filter(rce -> rce.childRateCardEntry != null).map(rce -> rce.childRateCardEntry.getId()).toList();
        var parentIds = rateCard.getRateCardEntries().stream().filter(rce -> !childrenIds.contains(rce.getId())).map(AbstractIntKeyedEntity::getId).toList();

        // get the matching criteria - matchingCategoryTypeId, matchingOutcome, matchingFactors
        Set<RateCardEntry> rateCardEntries = rateCard.getRateCardEntries().stream()
                .filter(rce -> {
                    // MATCH on parents only
                    boolean matchesOnNotChild = parentIds.contains(rce.getId()); // children are used for follow on calculations, not for matching

                    // MATCH not disabled
                    boolean matchesNotDisabled = !rce.isDisabled();

                    boolean matchesOnCategory = matchCategoryTypeId == null
                            || (rce.getMatchingCategoryTypeId() != null && rce.getMatchingCategoryTypeId().equals(matchCategoryTypeId));

                    boolean matchesOnOutcome = matchChargeCategoryId == null
                            || (rce.getMatchingChargeCategory() != null && rce.getMatchingChargeCategory().getId().equals(
                            matchChargeCategoryId));

                    boolean matchesOnFactors = matchFactors == null
                            ? rce.getMatchingFactors() == null || rce.getMatchingFactors().isEmpty()
                            : Stream.of(rce.getMatchingFactors().keySet()).allMatch(matchFactors::contains);

                    return matchesOnNotChild && matchesNotDisabled && matchesOnCategory && matchesOnOutcome && matchesOnFactors;
                }).collect(Collectors.toSet());

        // check there is only one rate card - else its silly otherwise
        List<Integer> rateCardIds = rateCardEntries.stream()
                .map(rce -> rce.getRateCard().getId())
                .distinct()
                .toList();
        if (rateCardIds.size() > 1) {
            throw new IllegalStateException("rate card entries have TOO MANY matches");
        }

        if (useOnlyOneRateCardEntry) {
            return matchToOneEntry(matchChargeCategoryId, rateCardEntries);
        } else {
            // sort the entries by minutes - FIXME: units is nullable, we're doing other stuff
            List<RateCardEntry> sortedEntries = rateCardEntries.stream()
                    .sorted(Comparator.comparing(RateCardEntry::getUnits))
                    .collect(Collectors.toList());
            return sortedEntries;
        }
    }

    protected Pair<BigDecimal, Integer> calculateOneCharge(Pair<BigDecimal, Integer> chargeAndRemainingTime, boolean hasAnotherEntry, RateCardEntry entry) {
        switch (entry.getChargeTypeFixedTemporal()) {
            case FIXED:
                chargeAndRemainingTime = new Pair<>(entry.getFixedCharge(), 0);
                break;
            case TEMPORAL:
                chargeAndRemainingTime = this.getTemporalCharge(entry, hasAnotherEntry, chargeAndRemainingTime.component2());
                break;
            case FIXED_TEMPORAL:
                BigDecimal chargeFixed = entry.getFixedCharge() == null ? BigDecimal.ZERO : entry.getFixedCharge();
                Pair<BigDecimal, Integer> chargeTemporal = this.getTemporalCharge(entry, hasAnotherEntry, chargeAndRemainingTime.component2());
                chargeAndRemainingTime = new Pair<>(chargeFixed.add(chargeTemporal.component1()), chargeTemporal.component2());
                break;
        }
        return chargeAndRemainingTime;
    }

    protected Pair<BigDecimal, Integer> getTemporalCharge(RateCardEntry entry, boolean hasAnotherEntry, int temporalActualMinutes) {
        // IGNORE unitsToRepeatFor for now - this is the notion that a 1hr appointment can be repeated for 2x30mins - which we simply assume here
        //int unitsToRepeatFor = entry.getUnitsToRepeatFor() != null ? entry.getUnitsToRepeatFor() : unitsActual;

        int unitsActual = entry.getUnitMeasurement().getTemporalUnitsFromMinutes(temporalActualMinutes);
        int unitsLimit = entry.getUnits();

        BigDecimal charge = BigDecimal.ZERO;
        int unitsCurrent = unitsActual;

        // keep charging this entry until it falls below the threshold
        while (unitsCurrent >= unitsLimit) {
            charge = charge.add(entry.getUnitCharge());
            unitsCurrent -= unitsLimit;
        }

        // if not has another entry, then we charge the unit again, else there is work time remaining uncharged for
        // so we only charge once for entry, for the units provided
        if (unitsCurrent > 0 && !hasAnotherEntry) {
            charge = charge.add(entry.getUnitCharge());
        }

        int unitsRemaining = unitsCurrent * entry.getUnitMeasurement().getUnits();
        int temporalRemainingMinutes = entry.getUnitMeasurement().getTemporalUnitsAsMinutes(unitsRemaining);
        return new Pair<>(charge, temporalRemainingMinutes);
    }

    private boolean matchOnDayTime(RateCard rateCard, Instant instant) {
        var matchPartsOfWeek = rateCard.getMatchingPartsOfWeek();
        var matchStart = rateCard.getMatchingStartTime();
        var matchEnd = rateCard.getMatchingEndTime();

        var visitDateTime = instant.atZone(ZoneOffset.UTC).toOffsetDateTime();
        var visitWeekend = RateCardCalculationBase.isWeekend(visitDateTime);
        var visitStart = visitDateTime.toLocalTime();

        boolean matchedOnPartsOfWeek = (matchPartsOfWeek == RateCard.PartsOfWeek.WEEKDAY_WEEKEND)
                || (matchPartsOfWeek == RateCard.PartsOfWeek.WEEKDAY && !visitWeekend)
                || (matchPartsOfWeek == RateCard.PartsOfWeek.WEEKEND && visitWeekend);

        boolean matchedOnTime = matchStart != null && matchEnd != null && (!visitStart.isBefore(matchStart)) && (visitStart.isBefore(matchEnd));

        return matchedOnPartsOfWeek && matchedOnTime;
    }

    private List<RateCardEntry> matchToOneEntry(Integer matchOutcomeId, Set<RateCardEntry> rateCardEntries) {
        if (matchOutcomeId != null && rateCardEntries.size() != 1) {
            throw new IllegalStateException("rate card entries can't be determined to only one match - instead found " + rateCardEntries.size() + ": " + Arrays.toString(rateCardEntries.toArray()));
        }

        // return the match, or the first default within the match if all the criteria for the cards matched
        if (rateCardEntries.size() > 1) {
            return Collections.singletonList(rateCardEntries.iterator().next());
        } else {
            // at least log the options in a warning
            var firstDefault = rateCardEntries.stream().filter(RateCardEntry::isDefaultEntry).findFirst();
            if (firstDefault.isEmpty()) {
                log.warn("rate card entries NOT FOUND for instant: (a shift without a contract?) " + Arrays.toString(rateCardEntries.toArray()));
                return Collections.emptyList();
            } else {
                RateCardEntry defaultEntry = firstDefault.get();
                log.info("rate card entries can't be determined to only one matching entry, so finding the first default amongst " + rateCardEntries.size() + " which is " + defaultEntry + " out of: " + Arrays.toString(rateCardEntries.toArray()));
                return Collections.singletonList(defaultEntry);
            }

        }

    }

}
