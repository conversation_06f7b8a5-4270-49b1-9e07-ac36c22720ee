package com.ecco.dom.contracts;

import com.google.common.collect.Range;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * Multiple - where all the rate cards get a stab at 'calculateOneCharge',
 *   even if only the initial charge is applied - see getTemporalCharge using hasAnotherEntry.
 */
@Slf4j
public class RateCardCalculationBasedOnMultipleRateCardEntriesWithoutChildren extends RateCardCalculationBase implements RateCardCalculation {

    final boolean matchDateTimeOfDay;

    public RateCardCalculationBasedOnMultipleRateCardEntriesWithoutChildren(boolean matchDateTimeOfDay) {
        this.matchDateTimeOfDay = matchDateTimeOfDay;
    }

    @Override
    public Optional<RateCard> getRateCardInDate(@NonNull Instant instant, @NonNull List<RateCard> rateCards) {
        return getRateCardInDate(instant, rateCards, true);
    }

    @Override
    public List<RateCard> getRateCardsInDateRange(@NonNull Range<Instant> range, @NonNull List<RateCard> rateCards) {
        return super.getRateCardsInDateRange(range, rateCards);
    }

    @Override
    public List<RateCardEntry> determineRateCardEntries(@NonNull RateCard rateCard, @Nullable Integer matchCategoryTypeId,
                                                        @Nullable Integer matchChargeCategoryId, @Nullable Collection<String> matchFactors) {

        return determineRateCardEntries(rateCard, matchCategoryTypeId, matchChargeCategoryId, matchFactors, false);
    }

    /**
     * Calculate the charge based on the rate card entry.
     * @param entries The rate card entries to use for calculation
     * @param dateTimeRange The date-time range to calculate charges for, allowing for calendar month calculations
     */
    @Override
    public BigDecimal calculateCharge(@NonNull List<RateCardEntry> entries, @NonNull Range<ZonedDateTime> dateTimeRange) {
        // Calculate the duration in minutes between the lower and upper bounds of the range
        ZonedDateTime start = dateTimeRange.lowerEndpoint();
        ZonedDateTime end = dateTimeRange.upperEndpoint();
        int minutes = (int) Duration.between(start, end).toMinutes();

        // Use the existing calculation method with the calculated minutes
        return this.calculateCharge(entries, new Pair<>(BigDecimal.ZERO, minutes)).component1();
    }

    /**
     * Calculate the charge using a date-time range.
     *
     * @param entries The rate card entries to use for calculation
     * @param chargeAndRemainingTime The current charge and remaining time
     * @return A pair containing the charge amount and remaining minutes
     */
    private Pair<BigDecimal, Integer> calculateCharge(@NonNull List<RateCardEntry> entries, Pair<BigDecimal, Integer> chargeAndRemainingTime) {
        if (entries.isEmpty()) {
            return new Pair<>(BigDecimal.ZERO, chargeAndRemainingTime.component2());
        }
        boolean hasAnotherParentEntry = entries.size() > 1;
        RateCardEntry entry = entries.get(0);

        Pair<BigDecimal, Integer> cumulativeChargeAndRemainingTime = calculateChargeSingleEntryAndChildren(chargeAndRemainingTime, hasAnotherParentEntry, entry);

        return hasAnotherParentEntry
                ? this.calculateCharge(entries.subList(1, entries.size()), cumulativeChargeAndRemainingTime)
                : cumulativeChargeAndRemainingTime;
    }

    @NonNull
    private Pair<BigDecimal, Integer> calculateChargeSingleEntryAndChildren(Pair<BigDecimal, Integer> chargeAndRemainingTime, boolean hasAnotherParentEntry, RateCardEntry entry) {
        var hasAnotherChildEntry = entry.childRateCardEntry != null;

        // hasAnotherEntry currently means we only charge once for the entry, for the units provided
        var entryChargeAndRemainingTime = this.calculateOneCharge(chargeAndRemainingTime, hasAnotherParentEntry || hasAnotherChildEntry, entry);
        var cumulativeChargeAndRemainingTime = new Pair<>(chargeAndRemainingTime.component1().add(entryChargeAndRemainingTime.component1()), entryChargeAndRemainingTime.component2());

        if (hasAnotherChildEntry) {
            entryChargeAndRemainingTime = this.calculateChargeSingleEntryAndChildren(cumulativeChargeAndRemainingTime, entry.childRateCardEntry.childRateCardEntry != null, entry.childRateCardEntry);
            cumulativeChargeAndRemainingTime = new Pair<>(chargeAndRemainingTime.component1().add(entryChargeAndRemainingTime.component1()), entryChargeAndRemainingTime.component2());
        }

        return cumulativeChargeAndRemainingTime;
    }

}
