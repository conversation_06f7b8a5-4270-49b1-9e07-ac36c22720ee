package com.ecco.dom.contracts.commands;

import lombok.NoArgsConstructor;
import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

import org.jspecify.annotations.NonNull;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("rateCard")
@NoArgsConstructor
public class RateCardCommand extends FinanceCommand {

    @Column
    Integer serviceRecipientId;

    public RateCardCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                           long userId, @NonNull String body, @Nullable Integer serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body);
        this.serviceRecipientId = serviceRecipientId;
    }

}
