package com.ecco.dom.contracts;

import com.google.common.collect.Range;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@NullMarked
public interface RateCardCalculation {

    /**
     * Determine the rate card.
     * RateCard's provided could be one RateCard (when an exact one is used) or a range when the rateCardName is used
     * But we'd expect the single RateCard to obey the rules of being in date anyway
     * NB we now enforce (on the return) a single rate card just so we can easily record it on the invoice line
     */
    Optional<RateCard> getRateCardInDate(Instant instant, List<RateCard> rateCards);

    /**
     * Find rate cards over a period of time, where matching a single instant may be too restrictive.
     * For instance, service charges could last a year, but the rate may change within this timeframe.
     * We wouldn't expect to split a 'visit' into different charge periods - however, we may if there was
     * a midnight change to a bank-holiday, for instance.
     */
    List<RateCard> getRateCardsInDateRange(Range<Instant> range, List<RateCard> rateCards);

    /**
     * Get the charge for an instant in time, based on provided data.
     *
     * @param rateCard associated with the entry's schedule contract - see com.ecco.dom.agreements.DemandSchedule#allowableRateCards
     * @param matchCategoryTypeId match the type of appointment or resource (eg one-to-one)
     * @param matchChargeCategoryId match relevant charge category (e.g. waking nights shift, band B social housing week rent), if available
     * @param matchFactors the set of criteria which must all match to find the one appropriate rate card entry (eg 'mileage')
     * @return appropriate rate card entries to charge-down, or empty if no matches
     */
    List<RateCardEntry> determineRateCardEntries(RateCard rateCard, @Nullable Integer matchCategoryTypeId,
                                                 @Nullable Integer matchChargeCategoryId, @Nullable Collection<String> matchFactors);

    /**
     * Calculate the charge based on the rate card entry.
     * @param entries The rate card entries to use for calculation
     * @param dateTimeRange The date-time range to calculate charges for, allowing for calendar month calculations
    */
    BigDecimal calculateCharge(List<RateCardEntry> entries, Range<ZonedDateTime> dateTimeRange);

}
