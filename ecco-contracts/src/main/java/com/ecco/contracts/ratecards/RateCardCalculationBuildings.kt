package com.ecco.contracts.ratecards

import com.ecco.dom.contracts.RateCard
import com.ecco.dom.contracts.RateCardCalculationBase
import com.ecco.dom.contracts.RateCardEntry
import com.ecco.dom.contracts.UnitOfMeasurement.Unit
import com.ecco.infrastructure.time.Formatters
import com.google.common.collect.Range
import io.github.oshai.kotlinlogging.KotlinLogging.logger
import org.springframework.util.Assert
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.Instant
import java.time.ZonedDateTime
import java.util.Optional
import kotlin.collections.get

private val Range<ZonedDateTime>.isOrderedBounds
    get() = lowerEndpoint() <= upperEndpoint()

private val log = logger {}

class RateCardCalculationBuildings : RateCardCalculationBase() {
    private val handlers =
        mapOf<Unit, RateCardEntryHandler>(
            Unit.MONTH to MonthRateCardEntryHandler(),
            Unit.DAY to DayRateCardEntryHandler(),
            Unit.HOUR to HourRateCardEntryHandler(),
        )

    override fun getRateCardInDate(instant: Instant, rateCards: List<RateCard>): Optional<RateCard> =
        getRateCardInDate(instant, rateCards, false)

    override fun determineRateCardEntries(
        rateCard: RateCard,
        matchCategoryTypeId: Int?,
        matchChargeCategoryId: Int?,
        matchFactors: Collection<String>?,
    ): List<RateCardEntry> = determineRateCardEntries(rateCard, matchCategoryTypeId, matchChargeCategoryId, matchFactors, false)

    override fun calculateCharge(entries: List<RateCardEntry>, dateTimeRange: Range<ZonedDateTime>): BigDecimal {
        // Pre-requisite: entries.size() >= 1
        // rate card entries are in order of priority
        // the supplied rate card entries are the applicable ones via determineRateCardEntries()

        return calculateForEntry(entries[0], entries.subList(1, entries.size), ZERO, dateTimeRange).first
    }

    private fun calculateForEntry(
        currentRateCardEntry: RateCardEntry,
        entries: List<RateCardEntry>,
        cumulativeCharge: BigDecimal,
        dateTimeRange: Range<ZonedDateTime>,
    ): Pair<BigDecimal, Range<ZonedDateTime>> {
        // let's have some rate card entry handlers
        val unitMeasurement = currentRateCardEntry.unitMeasurement?.unitMeasurement
        val handler =
            handlers[unitMeasurement]
                ?: throw IllegalArgumentException("No handler for unit of measurement: $unitMeasurement")
        val result = handler.handle(currentRateCardEntry, dateTimeRange)
        Assert.state(result.second.isEmpty || result.second.isOrderedBounds, { "invalid range: ${result.second}" })
        val newCumulativeCharge = cumulativeCharge.add(result.first)

        log.debug {
            """${currentRateCardEntry.unitMeasurement?.name} @ ${currentRateCardEntry.unitCharge}/${currentRateCardEntry.units}:
                |  ${handler.javaClass.simpleName} -> £${result.first} for ${dateTimeRange.lowerEndpoint().format(
                Formatters.D_MMM_YYYY_HH_MM_Z,
            )} to ${dateTimeRange.upperEndpoint().format(Formatters.D_MMM_YYYY_HH_MM_Z)}
                |  cumulative charge: £$newCumulativeCharge
            """.trimMargin()
        }

        if (result.second.isEmpty) {
            // Don't need to use any other rate card entries
            return Pair(newCumulativeCharge, result.second)
        }

        if (currentRateCardEntry.childRateCardEntry != null) {
            return calculateForEntry(
                currentRateCardEntry.childRateCardEntry,
                entries,
                newCumulativeCharge,
                result.second,
            )
        } else if (entries.isNotEmpty()) {
            return calculateForEntry(
                entries[0],
                entries.subList(1, entries.size),
                newCumulativeCharge,
                result.second,
            )
        }

        log.debug { "No more rate card entries to use for calculation with remaining period: ${result.second}" }
        // run out of entries so we return whatever we have left
        return Pair(newCumulativeCharge, result.second)
    }
}