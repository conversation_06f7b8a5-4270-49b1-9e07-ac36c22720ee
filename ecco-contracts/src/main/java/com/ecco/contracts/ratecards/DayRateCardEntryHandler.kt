package com.ecco.contracts.ratecards

import com.ecco.dom.contracts.RateCardEntry
import com.google.common.collect.Range
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

class DayRateCardEntryHandler : RateCardEntryHandler {
    /**
     * Handles the calculation of charges for rate card entries with a unit of measurement of days.
     * If the period spans a DST change, e.g. from 1 Mar 15:00 GMT to 1 Apr 17:00 BST this we should charge for 31 days and return a period
     * of 1 Apr 15:00 BST to 1 Apr 17:00 BST.
     */
    override fun handle(rateCardEntry: RateCardEntry, chargePeriod: Range<ZonedDateTime>): Pair<BigDecimal, Range<ZonedDateTime>> {
        val hourDiff = chargePeriod.upperEndpoint().hour - chargePeriod.lowerEndpoint().hour
        val days = chargePeriod.lowerEndpoint().toLocalDate().until(chargePeriod.upperEndpoint().toLocalDate(), ChronoUnit.DAYS) -
            if (hourDiff < 0) 1 else 0
        val charge = rateCardEntry.unitCharge!!.multiply(BigDecimal(days)).div(BigDecimal(rateCardEntry.units))
        return Pair(
            charge,
            Range.closedOpen(chargePeriod.lowerEndpoint().plusDays(days), chargePeriod.upperEndpoint()),
        )
    }
}