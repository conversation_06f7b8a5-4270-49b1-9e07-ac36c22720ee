# TO ACTIVATE: use SPRING_PROFILES_ACTIVE=dev in your environment (not -Dspring.profiles.active=dev)
#
debug: true

spring:
  jpa:
    show-sql: true
    properties.hibernate:
      format_sql: true

logging:
  level:
    #org.springframework.security: debug
    #org.springframework.transaction: trace
    # root: trace
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    # find the request matching, although we did a breakpoint on PatternsRequestCondition.getMatchingPattern
    #org.springframework.web.servlet.DispatcherServlet=DEBUG:

    # also see application.properties