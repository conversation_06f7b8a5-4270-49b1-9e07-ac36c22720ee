
Terminology
-----------
- ICA client - is standalone (this is easier to use I find - it has a separate icon and easier to copy/paste)
- xenapp - is th 'in browser' version


Install Citrix
---------------
Follow instructions at https://help.ubuntu.com/community/CitrixICAClientHowTo for installing 13.1 64-bit .deb and do the "add more certificates step".

This is the ICA client, so its safer to disable the plugin from Firefox which attempts the in-browser version. Open Tools -> Add-Ons. Check the 'plugins' "Citrix receiver for Linux" is disabled.

In Firefox, this should mean that when you click a connection icon, you get a "Run or Save" dialog, with the option to open with Citrix Receiver. 

[ah] I have had working the in-browser version and its possible through Chrome, but the above I found easiest and reliable after messing around with other setting and installations.


Connecting with Citrix
----------------------
Get a connection, and go.
