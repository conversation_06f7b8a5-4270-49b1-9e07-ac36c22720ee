package com.ecco.workflow;

import com.ecco.dto.AbstractHandle;
import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.ProxyDtoBuilderProxy;

import java.io.Serializable;

/**
 * An opaque reference to an instance of a workflow.
 */
public interface WorkflowInstance extends Serializable, BuildableDto<WorkflowInstance> {
    /** Opaque handle to allow task references to be passed as strings e.g. as URL parameters. */
    final class Handle extends AbstractHandle {
        Handle(String handle) {
            super(handle);
        }

        public static Handle fromString(String s) {
            return s != null ? new Handle(s) : null;
        }
    }

    /** Handle of this instance to allow it to be retrieved again. */
    Handle getHandle();

    /** Business key associated with this instance (unique within the type of workflow). */
    String getBusinessKey();

    interface Builder extends DtoBuilder<WorkflowInstance> {
        Builder handle(Handle handle);
        Builder businessKey(String businessKey);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, WorkflowInstance.class);
        }
    }

}
