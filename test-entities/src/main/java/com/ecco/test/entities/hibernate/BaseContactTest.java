package com.ecco.test.entities.hibernate;

import org.junit.Before;
import org.mockito.MockitoAnnotations;
import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;

@DatabaseSetup(value="classpath:ContactTest.xml", type=DatabaseOperation.CLEAN_INSERT)
public class BaseContactTest extends BaseTest {

    @Before
    public void initMockitoMocks() {
        MockitoAnnotations.initMocks(this);
    }

/*        config.addAnnotatedClass(com.ecco.test.entities.dom.TestContact.class);
        config.addAnnotatedClass(com.ecco.test.entities.dom.TestChild.class);
        config.addAnnotatedClass(com.ecco.test.entities.dom.TestPartner.class);
*/

}
