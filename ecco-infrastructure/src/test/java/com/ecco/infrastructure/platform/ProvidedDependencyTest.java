package com.ecco.infrastructure.platform;

import org.junit.Test;

import static org.junit.Assert.assertNotNull;

/**
 * Check that key libraries are available that we don't depend on directly, but are depended on by our direct dependencies.
 *
 * @since 30/12/15
 */
public class ProvidedDependencyTest {
    @Test
    public void testAvailabilityOfJavaExpressionLanguage() throws ClassNotFoundException {
        // Without this, Hibernate will not apply validations.
        assertNotNull("Expect JSR-341 Expression Language support", Class.forName("javax.el.ExpressionFactory"));
    }
}
