[{"uuid": "22beb1ac-416c-4560-7143-98ecdff11eac", "commandUri": "service-config/-100/task/needsAssessment/", "timestamp": "2016-05-16T14:07:55.141Z", "operation": "add", "serviceTypeId": -100, "taskName": "needsAssessment", "orderbyChange": {"from": 0, "to": 1}, "allowNextChange": null}, {"uuid": "19a83237-9aa9-4094-7c3a-b72796f876d8", "commandUri": "service-config/-100/task/needsReduction/", "timestamp": "2016-05-16T14:07:55.145Z", "operation": "add", "serviceTypeId": -100, "taskName": "needsReduction", "orderbyChange": {"from": 0, "to": 2}, "allowNextChange": null}, {"uuid": "bb4748c9-a3f9-410d-7378-f363db5a469c", "commandUri": "service-config/-100/task/referralView/", "timestamp": "2018-11-29T10:17:14.225Z", "operation": "add", "serviceTypeId": -100, "taskName": "referral<PERSON>iew", "orderbyChange": {"from": null, "to": 3}, "allowNextChange": {"from": null, "to": false}}, {"uuid": "b6519966-755c-4e7e-7d89-dc8c1a4b42bc", "commandUri": "service-config/-100/task/needsChecklist/", "timestamp": "2016-05-16T14:07:55.148Z", "operation": "add", "serviceTypeId": -100, "taskName": "needsChecklist", "orderbyChange": {"from": 0, "to": 4}, "allowNextChange": null}, {"uuid": "88810c46-71cf-444f-7da7-e69f9e7557f9", "commandUri": "service-config/-100/task/autoStart/", "timestamp": "2018-11-29T10:17:14.226Z", "operation": "add", "serviceTypeId": -100, "taskName": "autoStart", "orderbyChange": {"from": null, "to": 5}, "allowNextChange": {"from": null, "to": false}}, {"uuid": "5a1c9cee-45b5-4506-7239-5b0ace6a6185", "commandUri": "service-config/-100/task/needsReduction/setting/showMenus/", "timestamp": "2016-05-16T14:25:15.159Z", "serviceTypeId": -100, "taskName": "needsReduction", "settingName": "showMenus", "valueChange": {"from": null, "to": "addGoal"}}]