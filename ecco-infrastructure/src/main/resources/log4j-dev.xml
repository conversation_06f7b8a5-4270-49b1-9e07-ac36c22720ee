<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
    <appenders>
        <RandomAccessFile name="syncFile" fileName="logs/ecco.log" append="false">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </RandomAccessFile>
        <Console name="syncStdout" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </Console>

        <Async name="file" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncFile"/>
        </Async>
        <Async name="stdout" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncStdout"/>
        </Async>

    </appenders>

    <Loggers> <!-- NOTE: We could make all loggers async - see https://logging.apache.org/log4j/2.x/manual/async.html
    or for an individual logger we could use asyncLogger or asyncRoot -->

        <!-- Show SQL and params, but only log these to file, change via api/loggers/ to DEBUG if you want to see it -->
        <logger name="org.hibernate.SQL" level="INFO" additivity="false">
            <AppenderRef ref="file" />
        </logger>

        <!-- show the values in the SQL - change via api/loggers/ to TRACE if you want to see it -->
        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO" additivity="false">
            <AppenderRef ref="file" />
        </logger>

        <!-- Show stats enabled via -Dhibernate.generate_statistics=true -->
        <logger name="org.hibernate.stat" level="INFO" additivity="false">
            <!--<appender-ref ref= "file" />-->
            <appender-ref ref="stdout" />
        </logger>

        <logger name="org.hibernate.loader" level="INFO" additivity="false">
            <!-- This really gets hammered on ServiceType -->
            <appender-ref ref="file" />
        </logger>

        <!-- We never want .ast it's very noisy -->
        <logger name="org.hibernate.ast" level="WARN" additivity="false">
            <appender-ref ref="file" />
        </logger>

        <!--<logger name="org.springframework.cache" additivity="false" level="TRACE">-->
        <!--<appender-ref ref="stdout" />-->
        <!--</logger>-->

        <logger name="liquibase.executor.jvm.JdbcExecutor" level="WARN"/>

        <!-- show entities state - transient etc
        <logger name="org.hibernate.event.def" additivity="false" level="TRACE">
            <appender-ref ref="stdout" />
        </logger>
        -->
        <!-- show what causes an entity to be 'dirty'
        <logger name="org.hibernate.event.def.DefaultFlushEntityEventListener" additivity="false" level="TRACE">
            <appender-ref ref="file" />
        </logger>
        -->
        <!-- see what liquibase is doing in stdout
        <logger name="liquibase" additivity="false" level="DEBUG">
            <appender-ref ref="stdout" />
        </logger>
        -->

        <!-- This is too noisy at INFO telling us about MBean registrations -->
        <logger name="org.ehcache.jsr107.Eh107CacheManager" level="WARN"/>

        <logger name="org.ehcache.core.EhcacheManager" level="WARN"/>

        <logger name="org.hibernate.cfg" level="WARN"/>
        <!--    <logger name="org.hibernate.envers" additivity="false" level="WARN">
                <appender-ref ref="stdout" />
            </logger>-->
        <logger name="org.hibernate.tool.hbm2ddl.TableMetadata" level="WARN"/>
        <logger name="org.hibernate.id.enhanced.TableGenerator" level="WARN"/>

        <logger name="org.springframework.transaction" level="INFO" additivity="false">
            <!-- info not very useful, use TRACE -->
            <AppenderRef ref="file" />
        </logger>

        <!-- detail the methods involved the tx and what the exceptions are (aspectj only) -->
        <!-- useful with JpaTransactionManager on DEBUG and search for "Participating transaction failed" -->
        <logger name="org.springframework.transaction.interceptor.TransactionInterceptor" level="TRACE" additivity="false">
            <AppenderRef ref="file" />
        </logger>

        <logger name="org.springframework.orm.jpa.JpaTransactionManager" level="WARN"/>

        <logger name="org.springframework.data.repository" level="WARN"/>


    <logger name="org.springframework.data.repository" level="INFO" additivity="false">
        <appender-ref ref= "file" />
        <appender-ref ref="stdout" />
    </logger>

    <logger name="org.springframework.security" level="INFO" additivity="false">
        <appender-ref ref= "file" />
        <appender-ref ref="stdout" />
    </logger>

    <!-- help diagnose urls -->
    <!--<logger name="org.springframework.web.servlet.DispatcherServlet" level="TRACE" additivity="false">
        <appender-ref ref="stdout" />
    </logger>-->

        <!-- Clickstream logging
        <logger name="com.opensymphony.clickstream" additivity="false" level="DEBUG">
            <appender-ref ref="stdout" />
        </logger>
        <logger name="org.springframework.security.web.authentication" additivity="false" level="DEBUG">
            <appender-ref ref="stdout" />
        </logger>
        -->

        <!-- WEB SOCKET -->
        <!-- see the generated correlationId -->
        <logger name="com.ecco.service.WebSocketHttpRequestFactory" level="WARN"/>
        <!-- see the handled correlationId (new) -->
        <logger name="com.ecco.service.WebSocketResponseHandler" level="WARN"/>
        <!-- see the processing of events (use trace for triggers) -->
        <logger name="com.ecco.infrastructure.bus.MBassadorMessageBus" level="DEBUG" additivity="false">
            <appender-ref ref= "file" />
            <appender-ref ref="stdout" />
        </logger>
        <!-- see the starting, and results of syncing -->
        <logger name="com.ecco.service.event.ExternalSystemSyncAgent" level="WARN"/>
        <!-- see the starting of rest requests -->
        <logger name="com.ecco.service.DefaultClientSyncStrategy" level="WARN"/>

        <Root level="INFO">
            <AppenderRef ref="file" />
            <AppenderRef ref="stdout" />
        </Root>
    </Loggers>
</configuration>
