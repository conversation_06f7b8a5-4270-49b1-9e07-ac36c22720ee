<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2021/evidence-domain">

    <!--
    select workUuid, actionInstanceUuid from supportplanactions group by workUuid, actionInstanceUuid having count(actioninstanceuuid) > 1;
    select * from supportplanactions where workUuid like '%e9dc9e19-8c21-4eb0-8698%' order by created desc
    see 20210406_test8_spa_duplicates.txt for resolving issues
    -->
    <changeSet id="DEV-1912-prevent-duplicate-actionInstanceUuids" author="adamjhamer">
        <addUniqueConstraint tableName="supportplanactions" columnNames="workUuid,actionInstanceUuid"
                             constraintName="uniq_work_instanceId"/>
    </changeSet>
    <changeSet id="DEV-1912-prevent-duplicate-risk-actionInstanceUuids" author="adamjhamer">
        <addUniqueConstraint tableName="supportthreatactions" columnNames="workUuid,actionInstanceUuid"
                             constraintName="uniq_risk_work_instanceId"/>
    </changeSet>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->

</databaseChangeLog>