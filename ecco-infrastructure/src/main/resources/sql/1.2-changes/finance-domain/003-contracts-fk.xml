<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="DEV-1328-contracts-contrType" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/finance-domain/003-contracts.xml">
        <addForeignKeyConstraint constraintName="fk_contrType_lists" baseTableName="fin_contracts"
                                 baseColumnNames="contractTypeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-1328-contracts-agreements" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/finance-domain/003-contracts.xml">
        <addColumn tableName="appointmentagreements">
            <column name="contractId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_appsagree_contr" baseTableName="appointmentagreements"
                                 baseColumnNames="contractId" referencedTableName="fin_contracts" referencedColumnNames="id" />
    </changeSet>

    <!-- If we have a fin_commands_archive then miss out the archive -> servicerecipients FK as this doesn't work when deleting data -->
    <!-- But we'll probably merge back into the same svcrec_commands to avoid such duplication of comand infrastructure -->
    <changeSet id="DEV-1447-fin_cmd_srId_FK" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/finance-domain/003-contracts.xml">
        <addForeignKeyConstraint baseTableName="fin_commands" baseColumnNames="servicerecipientId"
                                 constraintName="fk_fincmd_srId" referencedTableName="servicerecipients" referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>
