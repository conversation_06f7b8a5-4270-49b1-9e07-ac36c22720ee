<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">


    <changeSet id="ECCO-1455-unitofmeasurements" author="adamjhamer">
        <createTable tableName="fin_unitofmeasurements">
            <column name="id" type="INT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="unitMeasurement" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="units" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1455-unitofmeasurements-base" author="adamjhamer">
        <insert tableName="fin_unitofmeasurements">
            <column name="id" valueNumeric="1"/>
            <column name="name" value="minute"/>
            <column name="unitMeasurement" value="MINUTE"/>
            <column name="units" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="fin_unitofmeasurements">
            <column name="id" valueNumeric="2"/>
            <column name="name" value="hour"/>
            <column name="unitMeasurement" value="HOUR"/>
            <column name="units" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="fin_unitofmeasurements">
            <column name="id" valueNumeric="3"/>
            <column name="name" value="day"/>
            <column name="unitMeasurement" value="DAY"/>
            <column name="units" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1454-ratecards" author="adamjhamer">
        <createTable tableName="fin_ratecards">
            <column name="id" type="INT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="startInstant" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="endInstant" type="DATETIME">
                <constraints nullable="true"/>
            </column>
            <column name="advisoryTotalDuration" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="advisoryTotalCharge" type="DECIMAL(9,2)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1454-ratecardentries2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1454-ratecardentries" author="adamjhamer" changeLogFile="classpath:sql/1.2-changes/finance-domain/002-rateCards.xml"/>
            </not>
        </preConditions>
        <createTable tableName="fin_ratecardentries">
            <column name="id" type="INT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version" type="INT"/>
            <column name="rateCardId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="matchingCategoryTypeId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="matchingOutcomeId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="matchingFactors" type="CLOB">
                <constraints nullable="true"/>
            </column>
            <column name="defaultEntry" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="chargeType" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="fixedCharge" type="DECIMAL(9,2)">
                <constraints nullable="true"/>
            </column>
            <column name="unitMeasurementId" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="unitCharge" type="DECIMAL(9,2)">
                <constraints nullable="true"/>
            </column>
            <column name="childRateCardEntryId" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="unitsToRepeatFor" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="DEV-1400-ratecardentries" author="adamjhamer">
        <dropNotNullConstraint tableName="fin_ratecardentries" columnName="matchingCategoryTypeId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-1447-ratecard-matching" author="adamjhamer">
        <addColumn tableName="fin_ratecards">
            <column name="matchingPartsOfWeek" type="VARCHAR(50)"/>
        </addColumn>
        <addColumn tableName="fin_ratecards">
            <column name="matchingStartTime" type="TIME"/>
        </addColumn>
        <addColumn tableName="fin_ratecards">
            <column name="matchingEndTime" type="TIME"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1447-ratecardentries-units" author="adamjhamer">
        <addColumn tableName="fin_ratecardentries">
            <column name="units" type="INT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
