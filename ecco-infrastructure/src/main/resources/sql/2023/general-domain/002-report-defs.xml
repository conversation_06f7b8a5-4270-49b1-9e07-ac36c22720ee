<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2023/general-domain">

    <!-- lets just check for status changes only across whole srId's, and hope for the best with performance -->
    <!--<changeSet id="DEV-2448-archive-report-performance" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="hidden" valueDate="2023-03-28T07:00:00"/>
            <where>uuid in ('03100000-0000-babe-babe-dadafee1600d', '00400000-0000-babe-babe-dadafee1600d', '00600000-0000-babe-babe-dadafee1600d')</where>
        </update>
        <sql>
            UPDATE reportdefinitons SET name=concat(name, ' - archived, use: outcome progress (06000000)')
            WHERE uuid in ('03100000-0000-babe-babe-dadafee1600d', '00400000-0000-babe-babe-dadafee1600d', '00600000-0000-babe-babe-dadafee1600d');
        </sql>
    </changeSet>-->

    <changeSet id="DEV-2457-repDef-summary-referral-sources" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06600000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2023-04-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="dashboard referral sources"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <!-- could go into a referralsBySource analyser and CHART but can't click through without a little more work -->
    <!-- and at the moment we're trying to re-create what they have -->
    <changeSet id="DEV-2457-repDef-summary-referral-sources-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard referral sources"/>
            <column name="body">
                {
                "description": "referrals in the period",
                "selectionCriteria": {
                "groupBy": "receivedDate",
                "referralStatus": "received",
                "selectionRootEntity": "ReferralsBySource",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ByMonthAnalysis",
                "columns": [
                {
                "title": "source",
                "representation": "name"
                },
                "count"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06600000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2457-repDef-summary-referral-ethnicities" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2023-04-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="dashboard referral ethnicities"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <!-- could go into a referralsBySource analyser and CHART but can't click through without a little more work -->
    <!-- and at the moment we're trying to re-create what they have -->
    <changeSet id="DEV-2457-repDef-summary-referral-ethnicities-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard referral ethnicities"/>
            <column name="body">
                {
                "description": "referrals in the period",
                "selectionCriteria": {
                "groupBy": "receivedDate",
                "referralStatus": "received",
                "selectionRootEntity": "ReferralsByEthnicity",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ByMonthAnalysis",
                "columns": [
                {
                "title": "ethnicity",
                "representation": "name"
                },
                "count"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2457-repDef-summary-referral-sexualOrientation" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2023-04-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="dashboard referral sexual orientations"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2457-repDef-summary-referral-sexualOrientation-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard referral sexual orientations"/>
            <column name="body">
                {
                "description": "referrals in the period",
                "selectionCriteria": {
                "groupBy": "receivedDate",
                "referralStatus": "received",
                "selectionRootEntity": "ReferralsBySexualOrientation",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ByMonthAnalysis",
                "columns": [
                {
                "title": "sexual orientation",
                "representation": "name"
                },
                "count"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2457-repDef-summary-referral-disability" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2023-04-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="dashboard referral disabilities"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2457-repDef-summary-referral-disability-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard referral disabilities"/>
            <column name="body">
                {
                "description": "referrals in the period",
                "selectionCriteria": {
                "groupBy": "receivedDate",
                "referralStatus": "received",
                "selectionRootEntity": "ReferralsByDisability",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ByMonthAnalysis",
                "columns": [
                {
                "title": "disability",
                "representation": "name"
                },
                "count"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06900000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2461-reportdefs-dashboard-col" author="adamjhamer">
        <addColumn tableName="reportdefinitions">
            <column name="showOnDashboardManager" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2463-reportdefs-close-to-exit" author="adamjhamer">
        <sql>
            UPDATE reportdefinitions SET name = REPLACE(name, 'closed', 'exited');
            UPDATE reportdefinitions SET friendlyName = REPLACE(friendlyName, 'closed', 'exited');
            UPDATE reportdefinitions SET name = REPLACE(name, 'then by closed reason', 'then by exit reason');
            UPDATE reportdefinitions SET name = REPLACE(name, 'not rejected/closed', 'not rejected/exited');
            UPDATE reportdefinitions SET body = REPLACE(body, 'closed', 'exited');
        </sql>
    </changeSet>

    <changeSet id="DEV-2463-reportdefs-rejected-to-signposted" author="adamjhamer">
        <sql>
            UPDATE reportdefinitions SET name = REPLACE(name, 'rejected', 'signposted');
            UPDATE reportdefinitions SET friendlyName = REPLACE(friendlyName, 'rejected', 'signposted');
            UPDATE reportdefinitions SET body = REPLACE(body, 'rejected', 'signposted');
        </sql>
    </changeSet>

    <changeSet id="DEV-2529-repDef-service-charges" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="07000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2023-10-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="service charges"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>


    <changeSet id="DEV-2529-repDef-service-charges-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "finance charges",
                "selectionCriteria": {
                "selectionRootEntity": "FinanceCharge",
                "selectionPropertyPath": "validFrom",
                "selectorType": "byEndOfQuarter",
                "absoluteFromDate": "1970-01-01",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["receipts"]
                },
                "stages": [
                {
                "description": "breakdown of service charges",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "FinanceChargeWithReferralAndBuilding",
                "columns": ["sr-id", "r: name", "b: name", "b: address", "b: postcode", "description", "netAmount", "receiptTotal", "dueAmount"]
                }
                }
                ]
                }
            </column>
            <where>uuid='07000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>
    <changeSet id="DEV-2529-repDef-service-charges-body-title2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="finance charges"/>
            <column name="body">
                {
                "description": "finance charges",
                "selectionCriteria": {
                "selectionRootEntity": "FinanceCharge",
                "selectionPropertyPath": "validFrom",
                "selectorType": "byEndOfQuarter",
                "absoluteFromDate": "1970-01-01",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["receipts"]
                },
                "stages": [
                {
                "description": "breakdown of charges",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "FinanceChargeWithReferralAndBuilding",
                "columns": ["sr-id", "r: name", "b: name", "b: address", "b: postcode", "description", "netAmount", "receiptTotal", "dueAmount"]
                }
                }
                ]
                }
            </column>
            <where>uuid='07000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2713-reportdefs-dashboardref-col" author="adamjhamer">
        <addColumn tableName="reportdefinitions">
            <column name="showOnDashboardFile" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
