<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
    logicalFilePath="2023/buildings-domain"
>

    <!-- HANDLES: (based on search for <createTable) -->

    <property name="now" value="CURRENT_TIMESTAMP()" dbms="h2"/>
    <property name="now" value="CURRENT_TIMESTAMP()" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="sysdate" dbms="oracle"/>

    <!-- we need hibernate_sequences on first domain file -->
    <changeSet id="hibernate_sequences" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="hibernate_sequences"/>
            </not>
        </preConditions>
        <createTable tableName="hibernate_sequences">
            <column name="sequence_name" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="next_val" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="DEV-2686-managedVoids-sequence" author="adamjhamer">
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="managedvoids"/>
            <column name="next_val" valueNumeric="100"/>
        </insert>
    </changeSet>

    <!-- bldg_managedvoids -->
    <changeSet id="DEV-2686-managedVoids" author="adamjhamer">
        <createTable tableName="bldg_managedvoids">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"><constraints nullable="false"/></column>
            <column name="name" type="VARCHAR(63)"/>
            <column name="receivedDate" type="DATE"/>
        </createTable>
    </changeSet>
    <!-- NB ChangeSetValidatorTest issues 'add column non-null' -->
    <changeSet id="DEV-2686-managedvoids-srId" author="adamjhamer">
        <addColumn tableName="bldg_managedvoids">
            <column name="servicerecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_mvoids_srId"
                baseTableName="bldg_managedvoids" baseColumnNames="servicerecipientId"
                referencedTableName="servicerecipients" referencedColumnNames="id"/>
    </changeSet>

    <!-- bldg_mvoid_commands -->
    <changeSet id="DEV-2686-mvoid-commands" author="adamjhamer">
        <createTable tableName="bldg_mvoid_commands">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="commandname" type="VARCHAR(63)">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="commandCreated" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="userid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="managedVoidId" type="INT">
                <constraints nullable="false"/> <!-- when creating, we get the id and inject it to the command -->
            </column>
            <column name="serviceRecipientId" type="INT"/>
        </createTable>
        <addForeignKeyConstraint
                constraintName="fk_mvoidcmds_author"
                baseTableName="bldg_mvoid_commands" baseColumnNames="userid"
                referencedTableName="users" referencedColumnNames="id"/>
        <addForeignKeyConstraint
                constraintName="fk_mvoidcmds_rId"
                baseTableName="bldg_mvoid_commands" baseColumnNames="managedVoidId"
                referencedTableName="bldg_managedvoids" referencedColumnNames="id"/>
        <addForeignKeyConstraint
                constraintName="fk_mvoidcmds_srId"
                baseTableName="bldg_mvoid_commands" baseColumnNames="serviceRecipientId"
                referencedTableName="servicerecipients" referencedColumnNames="id"/>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_uuid">
            <column name="uuid"/>
        </createIndex>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_ctd">
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_mvid_crtd">
            <column name="managedVoidId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_name_id_ctd">
            <column name="managedVoidId"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_sr_ctd">
            <column name="serviceRecipientId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmds_sr_name_ctd">
            <column name="serviceRecipientId"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>
    <!-- command drafts -->
    <changeSet id="DEV-2686-commands-mvoid-draft" author="adamjhamer">
        <addColumn tableName="bldg_mvoid_commands">
            <column name="draft" type="boolean" valueBoolean="false"/>
        </addColumn>
        <createIndex tableName="bldg_mvoid_commands" indexName="idx_mvoidcmd_draft">
            <column name="draft"/>
            <column name="serviceRecipientId"/>
            <column name="userId"/>
        </createIndex>
    </changeSet>

    <!-- bldg_managedvoids address history -->
    <!-- NB ChangeSetValidatorTest issues 'add column non-null' -->
    <changeSet id="DEV-2686-managedvoids-adrhistId" author="adamjhamer">
        <addColumn tableName="bldg_managedvoids">
            <column name="addresshistoryId" type="INT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="bldg_managedvoids" baseColumnNames="addresshistoryId" constraintName="fk_mvoid_adrhistId"
                                 referencedTableName="addresshistory" referencedColumnNames="id"/>
    </changeSet>

    <!-- bldg_managedvoids exit+signposting -->
    <!-- NB ChangeSetValidatorTest issues 'defaultValue without not null constraint' -->
    <changeSet id="DEV-2686-managedvoids-signposting" author="adamjhamer">
        <validCheckSum>8:cc382877221d190042a4d51b0e1fc825</validCheckSum>
        <addColumn tableName="bldg_managedvoids">
            <column name="decisionmadeon" type="DATETIME"/>
            <column name="signpostedexitcomment" type="LONGTEXT"/>
            <column name="exited" type="DATE"/>
            <column name="exitreasonId" type="INT"/>
            <column name="signpostedreasonId" type="INT"/>
            <column name="signpostedagencyid" type="BIGINT"/>
            <column name="signpostedback" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="acceptedonservice" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="bldg_managedvoids" baseColumnNames="signpostedreasonid" constraintName="fk_mvoid_signrId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="bldg_managedvoids" baseColumnNames="signpostedagencyid" constraintName="fk_mvoid_signaId"
                                 referencedTableName="contacts" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="bldg_managedvoids" baseColumnNames="exitreasonid" constraintName="fk_mvoid_exitrId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <!-- bldg_managedvoids start -->
    <changeSet id="DEV-2686-managedvoids-start" author="adamjhamer">
        <addColumn tableName="bldg_managedvoids">
            <column name="supportworkerid" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="receivingservicedate" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="supportworkerid" baseTableName="bldg_managedvoids" constraintName="fk_mvoid_suppwk" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>

    <!-- bldg_managedvoids source -->
    <changeSet id="DEV-2686-managedvoids-source" author="adamjhamer">
        <addColumn tableName="bldg_managedvoids">
            <column name="selfReferral" type="BOOLEAN" valueBoolean="false"/>
            <column name="referrerId" type="BIGINT"/>
            <column name="referrerAgencyId" type="BIGINT"/>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="referrerId" baseTableName="bldg_managedvoids" constraintName="FK_mvoid_referrer" referencedColumnNames="id" referencedTableName="contacts"/>
        <addForeignKeyConstraint baseColumnNames="referrerAgencyId" baseTableName="bldg_managedvoids" constraintName="FK_mvoid_referrerA" referencedColumnNames="id" referencedTableName="contacts"/>
    </changeSet>

    <!-- service type -->
    <changeSet id="DEV-2686-managedvoids-servicetype" author="adamjhamer" context="!test-data">
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="-700"/>
            <column name="version" valueNumeric="0"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="managedvoid-default"/>
            <column name="type" value="mvoid"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="-700"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="managed voids"/>
            <column name="servicetypeid" valueNumeric="-700"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-700"/>
            <column name="serviceId" valueNumeric="-700"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="18"/> <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-700"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="9"/>  <!-- decideFinal -->
            <column name="servicetypeId" valueNumeric="-700"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="50"/>  <!-- close -->
            <column name="servicetypeId" valueNumeric="-700"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-700"/>
        </insert>
    </changeSet>

</databaseChangeLog>
