<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2023/finance-domain">

    <!-- HANDLES: (based on search for <createTable)
     - see main financeDomainChangeLog.xml for what tables are involved in the domain
    -->

    <changeSet id="DEV-2445-fin-lines-rateCardId" author="adamjhamer">
        <addColumn tableName="fin_invoicelines">
            <column name="rateCardId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_lines_ratecard" baseTableName="fin_invoicelines"
                                 baseColumnNames="rateCardId" referencedTableName="fin_ratecards" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-2522-fin-receipts" author="adamjhamer">
        <createTable tableName="fin_receipts">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="receivedDate" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(9,2)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="DEV-2522-fin-receipts-fk" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_finreceipt_srId"
                                 baseTableName="fin_receipts" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2725-fin-receipts" author="adamjhamer">
        <addColumn tableName="fin_receipts">
            <column name="typeDefId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_finreceipts_types"
             baseTableName="fin_receipts" baseColumnNames="typeDefId"
             referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-2525-fin-lines-discriminator" author="adamjhamer">
        <validCheckSum>8:117d281c493dfa38f5c9b49c0da74811</validCheckSum>
        <validCheckSum>8:097c25ae341e5f58228194db32f1a24e</validCheckSum>
        <addColumn tableName="fin_invoicelines">
            <column name="discriminator_orm" type="VARCHAR(31)" value="rota"/>
        </addColumn>
        <addNotNullConstraint tableName="fin_invoicelines" columnName="discriminator_orm" columnDataType="VARCHAR(31)"/>
    </changeSet>
    <changeSet id="DEV-2525-fin-lines-lineUuid-to-uid" author="adamjhamer">
        <renameColumn tableName="fin_invoicelines" oldColumnName="lineUuid" newColumnName="uuid" columnDataType="CHAR(36)"/>
    </changeSet>
    <changeSet id="DEV-2525-fin-lines-version" author="adamjhamer">
        <validCheckSum>8:f9748d4570521c9a8c00378245fbaefb</validCheckSum>
        <validCheckSum>8:7556a59788e01832c36ba579372a4d0c</validCheckSum>
        <addColumn tableName="fin_invoicelines">
            <column name="version" type="INT" valueNumeric="0"/>
        </addColumn>
        <addNotNullConstraint tableName="fin_invoicelines" columnName="version" columnDataType="INT"/>
    </changeSet>

    <!--
    <changeSet id="DEV-2525-unitofmeasurements-base" author="adamjhamer">
        <insert tableName="fin_unitofmeasurements">
            <column name="id" valueNumeric="4"/>
            <column name="name" value="week"/>
            <column name="unitMeasurement" value="HOUR"/>
            <column name="units" valueNumeric="24"/>
            <column name="version" valueNumeric="0"/>
        </insert>
    </changeSet>
    -->

    <changeSet id="DEV-2529-ratecardentries-relax" author="adamjhamer">
        <dropNotNullConstraint tableName="fin_ratecardentries" columnName="matchingCategoryTypeId" columnDataType="INT"/>
        <dropNotNullConstraint tableName="fin_ratecardentries" columnName="matchingOutcomeId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-2529-ratecardentries-fx" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="fin_ratecardentries" baseColumnNames="rateCardId" constraintName="fk_rateentry_rate"
                                 referencedTableName="fin_ratecards" referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>