<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-baseline/securityDomainDataBaseline.xml">

    <changeSet context="1.1-base-data" author="baseline" id="1366042568880-61"
            logicalFilePath="classpath:sql/1.1-baseline/portableDataBaseline.xml">
        <validCheckSum>3:9a39da104ecd8fbfed0f92cc48d311ad</validCheckSum>
        <insert tableName="users">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="13"/>
            <column name="username" value="sysadmin"/>
            <column name="activedirectoryuser" valueBoolean="false"/>
            <column name="calendarid" value="e37aede8-9f45-4bef-b6a5-d7ad27ca7841"/>
            <column name="country" value="GB"/>
            <column name="domain"/>
            <column name="enabled" valueBoolean="true"/>
            <column name="failedloginattempts" valueNumeric="0"/>
            <column name="lastloggedin" valueDate="2013-04-12T14:40:48.0"/>
            <column name="locale" value="en_GB"/>
            <column name="password" value="11ecea26b444d61fbfe34a5550473ade4db514b5"/>
            <column name="registered" valueDate="2010-06-01T13:23:00.0"/>
            <column name="timezone" value="Europe/London"/>
        </insert>
    </changeSet>


    <changeSet context="1.1-base-data,test-data" author="baseline" id="1366042568880-23"
            logicalFilePath="classpath:sql/1.1-baseline/portableDataBaseline.xml">
        <insert tableName="groups">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="management"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="staff"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="client"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="4"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="commissioner"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="5"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="hr"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="6"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="demo"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="7"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="useradmin"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="8"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="sysadmin"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="10"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="hr-volunteers"/>
        </insert>
    </changeSet>

    <changeSet context="1.1-base-data" author="baseline" id="1366042568880-53"
            logicalFilePath="classpath:sql/1.1-baseline/portableDataBaseline.xml">
        <insert tableName="group_members">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="1"/>
            <column name="username" value="sysadmin"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="5"/>
            <column name="username" value="sysadmin"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="7"/>
            <column name="username" value="sysadmin"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="4"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="8"/>
            <column name="username" value="sysadmin"/>
        </insert>
    </changeSet>
    <changeSet author="nealeu" id="add-offline-group-to-sysadmin" context="acceptanceTests"
            logicalFilePath="classpath:sql/selectSoftwareModules.xml">
        <insert tableName="group_members">
            <column name="id" valueNumeric="8" />
            <column name="version" valueNumeric="0" />
            <column name="group_id" valueNumeric="16"/>
            <column name="username" value="sysadmin"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-234-poc-groups" author="bodeng" context="1.1-base-data"
            logicalFilePath="classpath:sql/1.1-changes/006-addWorkflowDrivenServices.xml">
        <insert tableName="groups">
            <column name="id" valueNumeric="12"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="admin"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="13"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="case-manager"/>
        </insert>
        <insert tableName="groups">
            <column name="id" valueNumeric="14"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="duty"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="5"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="12"/>
            <column name="username" value="sysadmin"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="6"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="13"/>
            <column name="username" value="sysadmin"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="7"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_id" valueNumeric="14"/>
            <column name="username" value="sysadmin"/>
        </insert>
    </changeSet>

    <changeSet context="1.1-base-data" author="baseline" id="DEV-129-add-external-user">
        <insert tableName="users">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="1"/>
            <column name="username" value="external"/>
            <column name="activedirectoryuser" valueBoolean="false"/>
            <!--<column name="calendarid" value="e37aede8-9f45-4bef-b6a5-d7ad27ca7841"/>-->
            <column name="country" value="GB"/>
            <column name="enabled" valueBoolean="false"/>
            <column name="failedloginattempts" valueNumeric="0"/>
            <!--<column name="lastloggedin" valueDate="2013-04-12T14:40:48.0"/>-->
            <column name="locale" value="en_GB"/>
            <column name="password" value="11ecea26b444d61fbfe34a5550473ade4db514b5"/>
            <column name="registered" valueDate="2017-06-29T14:41:00.0"/>
            <column name="timezone" value="Europe/London"/>
        </insert>
    </changeSet>

</databaseChangeLog>