<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!--
     - For changesets that must be referenced in the general changelogs itself, and not the domain,
     - we need some way to run them after - for completeness, in case we want to run just the domain
     - itself. We could just not have this file, but perhaps its non-existence could cause confusion.
     -
     - e.g. renaming groups -> sec_groups would cause a massive rewrite of general changelogs so we
     - reference the change in the general changelog back to the security domain table rename, but
     - then the security domain doesn't run it - unless there is some post-processing.
      -->

    <include file="classpath:sql/1.2-changes/security-domain/002-groups-rename.xml"/>

</databaseChangeLog>
