<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/006-ECCO-85-setting-table-changes.xml">

    <changeSet author="baseline" id="ECCO-85-extra-setting-columns">
        <addColumn tableName="setting">
            <column name="description" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(15)"/>
            <column name="enumType" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet author="baseline" id="ECCO-85-setting-inserts">
        <insert tableName="setting">
            <column name="id" valueNumeric="20"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="COMPLEXITY"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="SIMPLE"/>
            <column name="description" value="Password complexity checking algorithm (see also password.MIN_LENGTH)"/>
            <column name="type" value="ENUM"/>
            <column name="enumType" value="com.ecco.dom.PasswordComplexity"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="15"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="HISTORY"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="5"/>
            <column name="description" value="How many passwords to keep to ensure changes are not same as historical ones"/>
            <column name="type" value="NUMBER"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="16"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="MIN_LENGTH"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="8"/>
            <column name="description" value="Minimum password length"/>
            <column name="type" value="NUMBER"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="17"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="LOGIN_FAILURES_BEFORE_LOCKOUT"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="-1"/>
            <column name="description" value="Number of failed login attempts after which the account is locked. -1 = never locks out"/>
            <column name="type" value="NUMBER"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="18"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="LOCKOUT_DURATION"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="120"/>
            <column name="description" value="Duration of account lockout in seconds once a certain number of failed login attempts are made. -1 = permanent"/>
            <column name="type" value="NUMBER"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="19"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="PASSWORD_VALIDITY_PERIOD"/>
            <column name="namespace" value="com.ecco.authn.password"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="-1"/>
            <column name="description" value="Length of time (in days) that a password may be used for before it must be changed. -1 = permanent"/>
            <column name="type" value="NUMBER"/>
        </insert>
    </changeSet>
</databaseChangeLog>
