<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
        logicalFilePath="classpath:sql/1.1-changes/0001-config-domain/901-config-domain-2016-06-onwards-changes.xml"
>

    <changeSet id="ECCO-2040-insert-menus" author="nealeu">

        <delete tableName="cfg_menu_cfg_menuitem"/>
        <delete tableName="cfg_menuitem"/>
        <sql>
            <![CDATA[
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (10, '/icons/crystal/documentadd/png/48.png', 'referralView.newReferral', 'ROLE_STAFF,ROLE_ADMINREFERRAL', '/dynamic/secure/referralAspectFlow.html?newReferral=true', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (20, '/icons/crystal/search/png/48.png', 'menu.linktext.find_referral', 'ROLE_STAFF', '/dynamic/secure/referralAspectClientFlow.html?searchUntilFound=true', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (30, '/icons/crystal/import/png/48.png', 'menu.linktext.list_referrals', 'ROLE_STAFF', '/online/referrals/list/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (40, '/icons/crystal/card/png/48.png', 'menu.linktext.support_plans', 'ROLE_STAFF,ROLE_CLIENT,ROLE_COMMISSIONER', '/dynamic/secure/supportPlanFlow.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (60, '/icons/crystal/documentadd/png/48.png', 'referralView.newReferral', 'ROLE_STAFF', '/dynamic/secure/referralAspectFlow.html?newReferral=true', 'client-centric');

        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (70, '/icons/crystal/import/png/48.png', 'menu.linktext.my_referrals', 'ROLE_EVANGELIST', '/online/referrals/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (80, '/icons/crystal/documentadd/png/48.png', 'referralView.newReferral.beta', 'ROLE_DEMO,ROLE_SYSADMIN', '/online/r/referrals/new', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (90, '/icons/crystal/users/png/48.png', 'menu.linktext.group_support', 'ROLE_STAFF', '/online/groupsupport/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (100, '/icons/crystal/calendar/png/48.png', 'menu.linktext.calendar', 'ROLE_STAFF,ROLE_CLIENT', '/dynamic/secure/viewCalendar.html', 'core');

        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (110, '/icons/crystal/addressbook/png/adressbook-48.png', 'menu.linktext.contacts', 'ROLE_DUMMY', '/dynamic/secure/entities/contacts/get.html', 'contact-centric');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (120, '/home.png', 'menu.linktext.projects', 'ROLE_STAFF_X', '/dynamic/secure/menuProject.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (130, '/rota.png', 'menu.linktext.rota', 'ROLE_ADMINROTA', '/dynamic/secure/rota/', 'rota');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (140, '/home_yellow.png', 'menu.linktext.hr', 'ROLE_HR,ROLE_HR-VOLUNTEER', '/dynamic/secure/entities/workers/get.html', 'hr');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (150, 'home.png', 'menu.linktext.buildings', 'ROLE_STAFF', '/online/buildings/', 'buildings');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (160, '/icons/crystal/telephone/png/48.png', 'menu.linktext.offline_install_use', 'ROLE_SYSADMIN,ROLE_OFFLINE', '/dynamic/online/offline/manage', 'offline');

        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (170, '/icons/crystal/users/png/48.png', 'menu.linktext.community_support', 'ROLE_STAFF', '/dynamic/secure/menuActivities.html', 'community');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (180, '/icons/crystal/user/png/user-48.png', 'menu.linktext.clients', 'ROLE_STAFF', '/dynamic/secure/entities/clients/get.html', 'client-centric');

        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (200, '/icons/basket-with-tick-48.png', 'menu.linktext.allocation_basket', 'ROLE_STAFF', '/dynamic/secure/tasks/group/allocations.html', 'workflow');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (210, '/icons/basket-with-stop2-48.png', 'menu.linktext.duty_basket', 'ROLE_STAFF', '/dynamic/secure/tasks/group/duty.html', 'workflow');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (220, '/icons/crystal/toolbox/png/48.png', 'menu.linktext.resources.beds', 'ROLE_STAFF', '/dynamic/secure/rota/resources/beds', 'rota');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (230, '/icons/crystal/traffic/png/48.png', 'menu.linktext.workload', 'ROLE_SYSADMIN', '/dynamic/secure/tasks/all.html', 'workflow');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (240, '/icons/crystal/toolbox/png/48.png', 'menu.linktext.resources.dayService', 'ROLE_STAFF', '/dynamic/secure/rota/resources/dayService', 'rota');


        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1000, '/icons/gauge.png', 'menu.linktext.savedCharts', 'ROLE_REPORTS', '/online/charts/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1010, '/icons/crystal/user/png/user-48.png', 'menu.linktext.user_audit', 'ROLE_LOGIN', '/dynamic/secure/reports/userAudit.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1020, '/icons/crystal/user/png/user-48.png', 'menu.linktext.referrals', 'ROLE_REPORTS', '/dynamic/secure/reports/referrals.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1030, '/icons/crystal/card/png/48.png', 'menu.linktext.support', 'ROLE_REPORTS', '/dynamic/secure/reports/supportPlans.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1040, '/icons/plug.png', 'menu.linktext.submissions', 'ROLE_ADMIN', '/dynamic/secure/menuReportSubmissions.html', 'dev-only');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1050, '/icons/gauge.png', 'menu.linktext.reportCharts', 'ROLE_DEMO,ROLE_SYSADMIN', '/dynamic/secure/reports/charts.html', 'dev-only');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1060, '/rota.png', 'menu.linktext.rota', 'ROLE_REPORTS', '/dynamic/secure/reports/rotas.html', 'rota');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1070, '/icons/hand_share2.png', 'menu.linktext.services', 'ROLE_REPORTS', '/dynamic/secure/menuReportServices.html', 'dev-only');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1080, '/home_yellow.png', 'menu.linktext.hr', 'ROLE_HR', '/dynamic/secure/reports/hr.html', 'hr');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1090, '/icons/money.jpg', 'menu.linktext.finances', 'ROLE_REPORTS', '/dynamic/secure/menuReportFinances.html', 'dev-only');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (1100, '/icons/gauge.png', 'menu.linktext.dashboard', 'ROLE_DEMO,ROLE_SYSADMIN', '/dynamic/secure/reports/dashboard.html', 'dev-only');


        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2000, '/icons/crystal/documentremove/png/48.png', 'menu.linktext.delete_referral', 'ROLE_SOFTDELETE', '/dynamic/secure/referrals/delete.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2010, '/icons/crystal/user/png/user-48.png', 'menu.linktext.logins', 'ROLE_LOGIN', '/dynamic/secure/entities/users/get.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2020, '/icons/crystal/db/png/48.png', 'menu.linktext.LDAP_group_mapping', 'ROLE_LOGINADMIN', '/dynamic/secure/settings/ldapGroupMapping/list.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2030, 'gear.png', 'menu.linktext.siteAdmin', 'ROLE_ADMIN,ROLE_SYSADMIN', '/dynamic/secure/admin/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2040, '/icons/crystal/text/png/48.png', 'menu.linktext.lists', 'ROLE_ADMIN,ROLE_ADMINGROUPSUPPORT', '/dynamic/secure/listIdNames.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2050, '/target-48.png', 'menu.linktext.outcomes', 'ROLE_SYSADMIN', '/dynamic/secure/outcomesFlow.html', 'dev-only');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2060, '/icons/crystal/user/png/user-48.png', 'menu.linktext.referral_process', 'ROLE_SYSADMIN', '/dynamic/secure/referralProcess.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2070, '/icons/crystal/emblem-photos/png/48.png', 'menu.linktext.logo', 'ROLE_ADMIN', '/dynamic/secure/settings/logo/upload.html', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2080, '/icons/crystal/calendar/png/48.png', 'menu.linktext.calendar', 'ROLE_SYSADMIN', '/dynamic/secure/reports/appointments.html', 'rota');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2090, '/icons/crystal/protection/png/48.png', 'menu.linktext.offline_admin', 'ROLE_SYSADMIN', '/dynamic/secure/offlineAdmin.html', 'offline');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2100, '/icons/crystal/addressbook/png/adressbook-48.png', 'menu.linktext.contactAdmin', 'ROLE_SYSADMIN', '/online/contacts/', 'core');
        INSERT INTO cfg_menuitem (id, imageUrl, linkText, roles, url, module_name) VALUES (2110, '/icons/crystal/addressbook/png/adressbook-48.png', 'menu.linktext.agencyAdmin', 'ROLE_SYSADMIN', '/online/agencies/', 'core');

        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 10);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 20);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 30);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 40);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 60);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 70);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 80);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 90);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 100);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 110);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 120);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 130);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 140);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 150);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 160);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 170);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 180);

        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 200);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 210);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 220);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 230);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('welcome', 240);

        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1000);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1010);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1020);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1030);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1040);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1050);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1060);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1070);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1080);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1090);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('reports', 1100);

        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2000);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2010);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2020);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2030);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2040);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2050);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2060);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2070);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2080);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2090);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2100);
        INSERT INTO cfg_menu_cfg_menuitem (cfg_menu_name, menuItems_id) VALUES ('settings', 2110);
]]>
        </sql>
    </changeSet>
    <changeSet id="ECCO-2040-referrals-list-first" author="nealeu">
        <delete tableName="cfg_menu_cfg_menuitem">
            <where>menuItems_id = 10 or menuItems_id = 30</where>
        </delete>
        <update tableName="cfg_menuitem">
            <column name="id" valueNumeric="31"/>
            <where>id = 10</where>
        </update>
        <update tableName="cfg_menuitem">
            <column name="id" valueNumeric="10"/>
            <where>id = 30</where>
        </update>
        <update tableName="cfg_menuitem">
            <column name="id" valueNumeric="30"/>
            <where>id = 31</where>
        </update>
        <insert tableName="cfg_menu_cfg_menuitem">
            <column name="cfg_menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="10"/>
        </insert>
        <insert tableName="cfg_menu_cfg_menuitem">
            <column name="cfg_menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="30"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-2040-hide-cpu-hungry-reports" author="adamjhamer">
        <update tableName="cfg_menuitem">
            <column name="module_name" value="dev-only"/>
            <where>linkText in ('menu.linktext.referrals', 'menu.linktext.support')</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2040-hide-rota icons" author="adamjhamer">
        <update tableName="cfg_menuitem">
            <column name="module_name" value="dev-only"/>
            <where>linkText in ('menu.linktext.resources.beds', 'menu.linktext.resources.dayService')</where>
        </update>
    </changeSet>


    <changeSet author="nealeu" id="ECCO-2124-delete-supportStaff.evidence.inline">
        <delete tableName="cfg_feature">
            <where>name='supportStaff.evidence.inline'</where>
        </delete>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-1445-referralOverview.supportHistory-enabled">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
            <where>name='referralOverview.supportHistory'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-1176-delete-commandBasedEvidence">
        <delete tableName="cfg_feature">
            <where>name='smartStepEvidence.commandBased'</where>
        </delete>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2102-delete-s-n-r.forcePopups">
        <delete tableName="cfg_feature">
            <where>name='support.forcePopups' or name='needsAssessment.forcePopups' or name='risk.forcePopups'</where>
        </delete>
    </changeSet>

    <changeSet author="adamjhamer" id="z1294-setting-client-paginationSizes">
        <insert tableName="setting">
            <column name="id" valueNumeric="48"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="pageSize.clients"/>
            <column name="namespace" value="com.ecco.report"/>
            <column name="readOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="200"/>
            <column name="description" value="The size of paging for clients"/>
            <column name="type" value="NUMBER"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-2081-offline-manage-dyn-to-nav" author="nealeu">
        <update tableName="cfg_menuitem">
            <column name="url" value="/nav/online/offline/manage"/>
            <where>id=160</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2128-feature-evidence.history.totals">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.history.totals"/>
            <column name="description" value="Show totals at top of evidence history lists"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2130-delete-support.evidence.inline">
        <delete tableName="cfg_feature">
            <where>name='support.evidence.inline'</where>
        </delete>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2130-delete-risk.evidence.inline">
        <delete tableName="cfg_feature">
            <where>name='risk.evidence.inline'</where>
        </delete>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-1488-feature-evidence.recordLocation">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.recordLocation"/>
            <column name="description" value="Record the lat,lon location where work is done (assuming the user permits it in the browser)"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- use a shorter name for the id, for liquibase 2.0.5 compatibility to 63 length -->
    <changeSet author="adamjhamer" id="ECCO-2094-create-feature-commandAudit-short">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/0001-config-domain/901-config-domain-2016-06-onwards-changes.xml" author="adamjhamer" id="ECCO-2094-create-feature-referralOverview.tasks.evidence.commandAudit"/>
            </not>
        </preConditions>
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.tasks.evidence.commandAudit"/>
            <column name="description" value="Have the audit of the evidence screens come from commands (eg new sites, esp. central processing)"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <!-- rename if it has been set on 3.0.8 liquibase versions with 255 length, back to 63 length on 2.0.5 -->
    <!-- liqubase does not reload the table on each changeset, so we can't rely on the renamed id to be found in this changeset -->
    <changeSet author="adamjhamer" id="ECCO-2094-create-feature-commandAudit-rename">
        <validCheckSum>7:27aba54bacb5b7a7e6f20e48d4885a05</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/0001-config-domain/901-config-domain-2016-06-onwards-changes.xml" author="adamjhamer" id="ECCO-2094-create-feature-referralOverview.tasks.evidence.commandAudit"/>
        </preConditions>
        <update tableName="DATABASECHANGELOG">
            <column name="id" value="ECCO-2094-create-feature-commandAudit-long"/>
            <where>id = 'ECCO-2094-create-feature-referralOverview.tasks.evidence.commandAudit'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2173-feature-welcome.search">
        <insert tableName="cfg_feature">
            <column name="name" value="welcome.search"/>
            <column name="description" value="Show search bar on welcome page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2173-feature-welcome.search.clientLink">
        <insert tableName="cfg_feature">
            <column name="name" value="welcome.search.clientLink"/>
            <column name="description" value="Show link to client page in search result popup"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2188-setting-history-pageSize">
        <insert tableName="setting">
            <column name="id" valueNumeric="49"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="pageSize.history"/>
            <column name="namespace" value="com.ecco.evidence"/>
            <column name="readOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="20"/>
            <column name="description" value="The size of paging for evidence history"/>
            <column name="type" value="NUMBER"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2190-feature-referralOverview.auditHistory">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.auditHistory"/>
            <column name="description" value="Show audit history to login admin users"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="ECCO-1383-setting-activities-paginationSizes">
        <insert tableName="setting">
            <column name="id" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="pageSize.activities"/>
            <column name="namespace" value="com.ecco.report"/>
            <column name="readOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="100"/>
            <column name="description" value="The size of paging for activities"/>
            <column name="type" value="NUMBER"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="ECCO-2279-feature-evidence.history.editComment">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.history.editComment"/>
            <column name="description" value="Allow edit of history comment area"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-18-feature-referralOverview.invoices">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.invoices"/>
            <column name="description" value="Show client's invoices on the referral overview"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="DEV-55-feature-referral.list.slaTasks">
        <insert tableName="cfg_feature">
            <column name="name" value="referral.list.slaTasks"/>
            <column name="description" value="Show 'action required' column on on referrals list"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-121-AD-lookup-setting">
        <insert tableName="setting">
            <column name="id" valueNumeric="51"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="LDAP_READ_USER_SEARCH_FILTER"/>
            <column name="namespace" value="com.ecco.authn.ad"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value=""/>
            <column name="description" value="see ActiveDirectorySettings"/>
            <column name="type" value="STRING"/>
        </insert>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-121-AD-lookup-setting-default">
        <update tableName="setting">
            <column name="keyvalue" value="(userPrincipalName={0}@example.com)"/>
            <where>id = 51</where>
        </update>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-121-AD-lookup-rootdomain-setting">
        <insert tableName="setting">
            <column name="id" valueNumeric="52"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="LDAP_ROOTDOMAIN"/>
            <column name="namespace" value="com.ecco.authn.ad"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value=""/>
            <column name="description" value="see ActiveDirectorySettings"/>
            <column name="type" value="STRING"/>
        </insert>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-121-AD-lookup-correct-name">
        <update tableName="setting">
            <column name="keyname" value="ROOTDOMAIN"/>
            <where>id = 52</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-287-feature-referralOverview.tasks.workflowTasks">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.tasks.workflowTaskStatus"/>
            <column name="description" value="Show workflow tasks on the 'tasks' tab"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-307-make-NRv2-default" author="nealeu">
        <update tableName="cfg_menuitem">
            <column name="url" value="/online/r/referrals/new"/>
            <where>url='/dynamic/secure/referralAspectFlow.html?newReferral=true'</where>
        </update>
        <update tableName="cfg_menuitem">
            <column name="url" value="/dynamic/secure/referralAspectFlow.html?newReferral=true"/>
            <column name="linkText" value="referralView.newReferral.old"/>
            <where>linkText='referralView.newReferral.beta'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DEV-172-feature-referral.list.service-group">
        <insert tableName="cfg_feature">
            <column name="name" value="referral.list.service-group"/>
            <column name="description" value="Use service group dropdown instead of service on referrals list"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="DEV-323-del-NRv1-menuitems">
        <delete tableName="cfg_menu_cfg_menuitem">
            <where>
                menuItems_id in (select id from cfg_menuitem
                where url='/dynamic/secure/referralAspectFlow.html?newReferral=true')</where>
        </delete>
        <delete tableName="cfg_menuitem">
            <where>url='/dynamic/secure/referralAspectFlow.html?newReferral=true'</where>
        </delete>
    </changeSet>

    <changeSet id="DEV-336-add-ext-sys-clientSink" author="nealeu">
        <addColumn tableName="cfg_externalsystem">
            <column name="clientSink" defaultValueBoolean="false" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="nealeu" id="DEV-341-feature-tasks.emergencyDetails.cmd">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.emergencyDetails.cmd"/>
            <column name="description" value="Use command based form for emergency details"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet author="nealeu" id="DEV-341-delete-feature-beta.snackbar-notifications">
        <delete tableName="cfg_feature">
            <where>name='beta.snackbar-notifications'</where>
        </delete>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-352-feature-evidence.stayOnTimeout">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.stayOnTimeout"/>
            <column name="description" value="After a session timeout, stay on the page for re-auth on save"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="DEV-401-delete-review.forcePopups">
        <delete tableName="cfg_feature">
            <where>name='needsReview.forcePopups'</where>
        </delete>
    </changeSet>

    <changeSet id="DEV-361-clob-for-template" author="nealeu">
        <addColumn tableName="templates">
            <column name="body" type="CLOB"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-361-workerassigned-template" author="nealeu">
        <insert tableName="templates">
            <column name="name" value="WorkerAssigned"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="WORKER_ASSIGNED_EVENT"/>
            <column name="body" type="CLOB"><![CDATA[You have been allocated a new client

[Click here to open their file](http://localhost:8080/ecco-war/nav/sr/{serviceRecipientId})]]></column>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-???-feature-signaturePrintBlurb">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.signaturePrintBlurb"/>
            <column name="description" value="'printable' evidence shows markdown text"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-550-feature-referral.cards.filter">
        <insert tableName="cfg_feature">
            <column name="name" value="referral.cards.filter"/>
            <column name="description" value="Show 'service' filter option on appointments/tasks"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="feature-support.forwardPlan">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.support.forwardPlan"/>
            <column name="description" value="Allow the support plan to be presented by target date order"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-557-remove-feature-tasks.emergencyDetails.cmd">
        <delete tableName="cfg_feature">
            <where>name='tasks.emergencyDetails.cmd'</where>
        </delete>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-512-tasks.edit.new.referralDetails-feature">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.edit.new.referralDetails"/>
            <column name="description" value="Use new UI for editing the task: details of referral"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-634-tasks.edit.new.close-feature">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.edit.new.close"/>
            <column name="description" value="Use new UI for editing the task: close off"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-634-tasks.edit.new.triggerEmail-feature">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.edit.new.triggerEmail"/>
            <column name="description" value="Trigger email on varios tasks"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-629-setting-clientsearch">
        <insert tableName="setting">
            <column name="id" valueNumeric="54"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="ENABLED"/>
            <column name="namespace" value="com.ecco.authn.clientsearch"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="false"/>
            <column name="description" value="Restrict client searches to the users security permission"/>
            <column name="type" value="BOOLEAN"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-654-remove-older-reports">
        <!--
        1020, '/dynamic/secure/reports/referrals.html', 'core'
        1030, '/dynamic/secure/reports/supportPlans.html', 'core'
        1040, '/dynamic/secure/menuReportSubmissions.html', 'dev-only'
        1050, '/dynamic/secure/reports/charts.html', 'dev-only'
        1060, '/dynamic/secure/reports/rotas.html', 'rota'
        1070, '/dynamic/secure/menuReportServices.html', 'dev-only'
        1080, '/dynamic/secure/reports/hr.html', 'hr'
        1090, '/dynamic/secure/menuReportFinances.html', 'dev-only'
        1100, '/dynamic/secure/reports/dashboard.html', 'dev-only'
        -->
        <delete tableName="cfg_menu_cfg_menuitem">
            <where>menuItems_id in (1020,1030,1040,1050,1060,1070,1080,1090,1100)</where>
        </delete>
        <delete tableName="cfg_menuitem">
            <where>id in (1020,1030,1040,1050,1060,1070,1080,1090,1100)</where>
        </delete>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-692-aclEnabledConfig">
        <insert tableName="setting">
            <column name="id" valueNumeric="55"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="ENABLED"/>
            <column name="namespace" value="com.ecco.authn.acl.config"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="false"/>
            <column name="description" value="Specifies whether ACLs should be used in user management"/>
            <column name="type" value="BOOLEAN"/>
        </insert>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-692-aclEnabledConfig-default">
        <update tableName="setting">
            <column name="keyvalue" valueComputed="(select a1.keyvalue from (select * from setting) a1 where a1.namespace='com.ecco.authn.acl' and a1.keyname='ENABLED')"/>
            <where>id=55</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-692-renameLoginAdmin-on-menu">
        <update tableName="cfg_menuitem">
            <column name="roles" value="ROLE_ADMINLOGINLDAP"></column>
            <where>id=2020 and roles='ROLE_LOGINADMIN'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-692-renameLogin-on-menu">
        <!-- secure/reports/userAudit.html -->
        <update tableName="cfg_menuitem">
            <column name="roles" value="ROLE_ADMINLOGIN"></column>
            <where>id=1010 and roles='ROLE_LOGIN'</where>
        </update>
        <!-- secure/entities/users/get.html -->
        <update tableName="cfg_menuitem">
            <column name="roles" value="ROLE_ADMINLOGIN"></column>
            <where>id=2010 and roles='ROLE_LOGIN'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-692-settings-read-rename">
        <renameColumn tableName="setting" oldColumnName="readOnStartup" newColumnName="consumedOnStartup" columnDataType="BOOLEAN"/>
    </changeSet>

    <changeSet author="nealeu" id="DEV-836-browser-advice-IE10">
        <update tableName="setting">
            <column name="keyvalue"
                    value="You are using a very old browser that does not support modern web features and is no longer secure. Ecco supports modern web browsers which includes: Internet Explorer 11, Microsoft Edge, Chrome, and Safari for iOS"/>
            <where>keyname='OldBrowserAdviceText' and namespace='com.ecco.browser'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-344-notifyappointments-template" author="bodeng">
        <insert tableName="templates">
            <column name="name" value="NotifyAppointments"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="APPOINTMENT_NOTIFICATION_EVENT"/>
            <column name="body" type="CLOB"><![CDATA[You have some apppointments coming up.

|Title|With|Start|End|
|-----|----|-----|---|
{tableRows}
]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1278-external-systems-bldg-staff" author="nealeu">
        <addColumn tableName="cfg_externalsystem">
            <column name="buildingSource" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="buildingSink" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="staffSource" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="staffSink" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
