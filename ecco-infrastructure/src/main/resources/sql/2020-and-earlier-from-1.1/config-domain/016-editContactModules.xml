<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
                   logicalFilePath="classpath:sql/1.1-changes/016-editContactModules.xml">

    <changeSet author="default" id="ECCO-273">
        <!-- contacts is not for everyone (security implications?) -->
        <insert tableName="softwaremodule">
            <column name="name" value="contact-centric"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <!-- contacts is at /contacts/ -->
        <update tableName="menuitem">
            <column name="url" value="/dynamic/secure/entities/contacts/get.html"/>
            <column name="module_name" value="contact-centric"/>
            <where>id=4</where>
        </update>
    </changeSet>

</databaseChangeLog>
