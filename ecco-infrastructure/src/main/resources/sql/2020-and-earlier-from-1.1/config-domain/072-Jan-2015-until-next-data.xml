<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/0001-config-domain/072-Jan-2015-until-next-data.xml"
>

    <changeSet id="ECCO-1669-risk-matrix-list-definition" author="guillaune"
               logicalFilePath="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="100"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="unlikely"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="101"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="possible"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="102"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="very likely"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="103"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="almost certain"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="104"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="minor"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="105"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="moderate"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="106"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="significant"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="107"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="serious"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="108"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="major"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1669-risk-matrix-list-definition-matchDocs" author="adamhamer"
               logicalFilePath="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="very unlikely"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;value&quot;: &quot;1&quot;}</column>
        </insert>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="slight"/>
            <column name="metadata">{&quot;value&quot;: &quot;2&quot;}</column>
            <where>id=100</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="feasible"/>
            <column name="metadata">{&quot;value&quot;: &quot;3&quot;}</column>
            <where>id=101</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="likely"/>
            <column name="metadata">{&quot;value&quot;: &quot;4&quot;}</column>
            <where>id=102</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixLikelihood"/>
            <column name="name" value="very likely"/>
            <column name="metadata">{&quot;value&quot;: &quot;5&quot;}</column>
            <where>id=103</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="insignificant"/>
            <column name="metadata">{&quot;value&quot;: &quot;1&quot;}</column>
            <where>id=104</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="minor"/>
            <column name="metadata">{&quot;value&quot;: &quot;2&quot;}</column>
            <where>id=105</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="significant"/>
            <column name="metadata">{&quot;value&quot;: &quot;3&quot;}</column>
            <where>id=106</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="critical"/>
            <column name="metadata">{&quot;value&quot;: &quot;5&quot;}</column>
            <where>id=108</where> <!-- swapped order below, else get unique exception -->
        </update>
        <update tableName="cfg_list_definitions">
            <column name="listName" value="riskMatrixSeverity"/>
            <column name="name" value="major"/>
            <column name="metadata">{&quot;value&quot;: &quot;4&quot;}</column>
            <where>id=107</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1702-default-checks-list-definition" author="nealeu"
               logicalFilePath="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="109"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="default-checks"/>
            <column name="name" value="pass"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-check-circle&quot;}</column>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="110"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="default-checks"/>
            <column name="name" value="warning"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-warning&quot;}</column>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="111"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="default-checks"/>
            <column name="name" value="fail"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-times&quot;}</column>
        </insert>
    </changeSet>

    <changeSet id="DEV-188-smiley-checks-list-definition" author="nealeu"
               logicalFilePath="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="112"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="smiley-checks"/>
            <column name="name" value="happy"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-smile-o&quot;}</column>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="113"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="smiley-checks"/>
            <column name="name" value="so-so"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-meh-o&quot;}</column>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="114"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="smiley-checks"/>
            <column name="name" value="sad"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-frown-o&quot;}</column>
        </insert>
    </changeSet>
    <changeSet id="DEV-188-smiley-checks-red-amber-green" author="nealeu"
               logicalFilePath="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml">
        <update tableName="cfg_list_definitions">
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-smile-o green&quot;}</column>
            <where>id=112</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-meh-o amber&quot;}</column>
            <where>id=113</where>
        </update>
        <update tableName="cfg_list_definitions">
            <column name="metadata">{&quot;iconClasses&quot;: &quot;fa fa-frown-o red&quot;}</column>
            <where>id=114</where>
        </update>
    </changeSet>

</databaseChangeLog>
