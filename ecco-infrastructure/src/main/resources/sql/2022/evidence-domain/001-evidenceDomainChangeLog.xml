<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2022/evidence-domain">

    <!--
    NB files here relate to evidence-domain, but sometimes rely on other domains (eg config) in some of the sql (eg DEV-2352-listDef-commenttypes-populate-settings)
    that we could filter out if required - using the logicalFilePath makes the databasechangelog simpler.
    -->

    <!-- foreign keys can't be in evidenceDomainChangeLog when evidence-domain is before generalChangeLog where the tables are created -->
    <!--<include file="classpath:sql/2022/general-domain/003-commentTypes-to-settings.xml"/>
    <include file="classpath:sql/2022/evidence-domain/003-commentTypes-to-listdef.xml"/>-->

    <include file="classpath:sql/2022/evidence-domain/004-evidenceattachments.xml"/>

    <!-- TODO drop supportthreatoutcomes textMap and customObjectData if all the same -->

</databaseChangeLog>