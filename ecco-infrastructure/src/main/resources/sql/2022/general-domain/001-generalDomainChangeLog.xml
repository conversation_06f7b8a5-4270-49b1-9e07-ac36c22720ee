<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2022/general-domain">

    <changeSet id="DEV-2208-referralaspects-metadata" author="adamjhamer">
        <addColumn tableName="referralaspects">
            <column name="metadata" type="CLOB">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <update tableName="referralaspects">
            <column name="metadata" value="{}"/>
            <where>metadata is null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1725-carerun_disable" author="adamjhamer">
        <addColumn tableName="bldg_fixed">
            <column name="disabled" type="boolean" valueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2271-demandscheduledirecttasks" author="adamjhamer">
        <createTable tableName="appointmentscheduledirecttasks">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="scheduleId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="taskInstanceId" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseTableName="appointmentscheduledirecttasks" baseColumnNames="scheduleId" constraintName="fk_appschedtask_appsched"
                                 referencedTableName="appointmentschedules" referencedColumnNames="id"/>
        <!-- FK on taskInstanceId is difficult and cuts domains, so leave at db level -->
    </changeSet>

    <changeSet id="DEV-2271-referralaspects-add-evidence" author="adamjhamer">
        <!-- for sites with a care plan, we should sort it out (could just change existing id?) -->
        <preConditions>
            <sqlCheck expectedResult="0">select count(1) from referralaspects where name='carePlan'</sqlCheck>
        </preConditions>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="148"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_SUPPORT"/>
            <column name="friendlyName" value=""/>
            <!-- More useful, but note we can't use spaces etc since the controller-page is restrictive. -->
            <column name="name" value="carePlan"/>
        </insert>
    </changeSet>

    <!-- signpostedreasons table has:
        <column defaultValueNumeric="0" name="id" type="BIGINT">
            <constraints nullable="false" primaryKey="true"/>
        </column>
     - where mssql creates explicit constraints:
        - CONSTRAINT [PK_<TABLE>] PRIMARY KEY CLUSTERED ([id] ASC)
        - '[id] [bigint] NOT NULL CONSTRAINT [DF_ethnicorigins_id]  DEFAULT ((0))'
     - We need to drop these constraints before modifying the data type.
     - We can use dropDefaultValue to drop the default constraint.
     - We need to use dropPrimaryKey to drop the primary key constraint.
    -->
    <!-- remove known duplicate in base data -->
    <changeSet id="DEV-2293-listDef-signpostreasons-prep-dupl" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="2">select count(1) from signpostreasons where name='recalled'</sqlCheck>
        </preConditions>
        <update tableName="signpostreasons">
            <column name="name" value="recalled-"/>
            <where>id=20 and name='recalled'</where>
        </update>
    </changeSet>
    <changeSet id="DEV-2293-listDef-signpostreasons-prep-fk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F8768B7AB3DF"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-signpostreasons-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="signpostreasons" columnName="id"/>
        <dropPrimaryKey tableName="signpostreasons"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-signpostreasons-prep" author="adamjhamer">
        <modifyDataType tableName="signpostreasons" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="referrals" columnName="signpostedreasonid" newDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-signpostreasons-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="signpostreasons" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="signpostreasons" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-signpostreasons-migrationId" author="adamjhamer">
        <addColumn tableName="signpostreasons">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-2293-listDef-signpostreasons-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="signpostreasons" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <!-- select * from signpostreasons group by name having count(*) > 1 -->
    <changeSet id="DEV-2293-listDef-signpostreasons-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'signpostReason', name, concat('signpostReason-', migrationId) FROM signpostreasons;
        </sql>
    </changeSet>
    <changeSet id="DEV-2293-listDef-signpostreasons-update-refs" author="adamjhamer">
        <update tableName="referrals">
            <column name="signpostedreasonid" valueComputed="(SELECT eo.migrationId FROM signpostreasons eo WHERE eo.id = referrals.signpostedreasonid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="referrals" baseColumnNames="signpostedreasonid" constraintName="FK_ref_signId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <!-- exitreasons table has:
    <column defaultValueNumeric="0" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true"/>
    </column>
     - where mssql creates explicit constraints:
        - CONSTRAINT [PK_<TABLE>] PRIMARY KEY CLUSTERED ([id] ASC)
        - '[id] [bigint] NOT NULL CONSTRAINT [DF_ethnicorigins_id]  DEFAULT ((0))'
     - We need to drop these constraints before modifying the data type.
     - We can use dropDefaultValue to drop the default constraint.
     - We need to use dropPrimaryKey to drop the primary key constraint.
    -->
    <!-- remove known duplicate in base data -->
    <changeSet id="DEV-2293-listDef-exitreasons-prep-dupl" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="2">select count(1) from exitreasons where name='evicted through court proceedings'</sqlCheck>
        </preConditions>
        <update tableName="exitreasons">
            <column name="name" value="evicted through court proceedings-"/>
            <where>id=16 and name='evicted through court proceedings'</where>
        </update>
    </changeSet>
    <changeSet id="DEV-2293-listDef-exitreasons-prep-fk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F8767C5B9FC2"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-exitreasons-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="exitreasons" columnName="id"/>
        <dropPrimaryKey tableName="exitreasons"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-exitreasons-prep" author="adamjhamer">
        <modifyDataType tableName="exitreasons" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="referrals" columnName="exitreasonid" newDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-exitreasons-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="exitreasons" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="exitreasons" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2293-listDef-exitreasons-migrationId" author="adamjhamer">
        <addColumn tableName="exitreasons">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-2293-listDef-exitreasons-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="exitreasons" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <!-- select * from signpostreasons group by name having count(*) > 1 -->
    <changeSet id="DEV-2293-listDef-exitreasons-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'exitReason', name, concat('exitReason-', migrationId) FROM exitreasons;
        </sql>
    </changeSet>
    <changeSet id="DEV-2293-listDef-exitreasons-update-refs" author="adamjhamer">
        <update tableName="referrals">
            <column name="exitreasonid" valueComputed="(SELECT eo.migrationId FROM exitreasons eo WHERE eo.id = referrals.exitreasonid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="referrals" baseColumnNames="exitreasonid" constraintName="FK_ref_exitId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2309-eventstatus-listdef-migrate" author="adamjhamer">
        <update tableName="contacts_events">
            <column name="eventstatus" valueComputed="null"/>
            <where>eventstatus=-1</where>
        </update>
        <update tableName="contacts_events">
            <column name="eventstatus" valueNumeric="194"/>
            <where>eventstatus=0</where>
        </update>
        <update tableName="contacts_events">
            <column name="eventstatus" valueNumeric="195"/>
            <where>eventstatus=1</where>
        </update>
        <update tableName="contacts_events">
            <column name="eventstatus" valueNumeric="196"/>
            <where>eventstatus=2</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2310-contacts_events-eventStatusId" author="adamjhamer">
        <renameColumn tableName="contacts_events" oldColumnName="eventstatus" newColumnName="eventContactStatusId" columnDataType="INTEGER"/>
    </changeSet>

    <changeSet id="DEV-2309-events-eventStatusId" author="adamjhamer">
        <addColumn tableName="events">
            <column name="eventStatusId" type="INTEGER">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="events" baseColumnNames="eventStatusId" constraintName="events_status_lists"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="DEV-2309-eventsrecurring-eventStatusId" author="adamjhamer">
        <addColumn tableName="eventsrecurring">
            <column name="eventStatusId" type="INTEGER">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="eventsrecurring" baseColumnNames="eventStatusId" constraintName="evrec_status_lists"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2324-demandscheduledirecttasks-uniq" author="adamjhamer">
        <addUniqueConstraint tableName="appointmentscheduledirecttasks" columnNames="scheduleId,taskInstanceId"/>
    </changeSet>

    <!-- 1.1 baseline -->
    <changeSet id="DEV-2722-old-lists-not-null-baseline" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-2722-old-lists-not-null-constraints" author="nealeu" changeLogFile="2022/config-domain"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="accommodationCategories" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="accommodations" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="agencycategories" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="commenttypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="economicstatuses" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="ethnicorigins" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="exitreasons" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="flags" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="languages" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="leavereasons" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="localauthorities" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="pendingstatuses" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="referralactivitytypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="regions" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="religions" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="servicetypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="signpostreasons" columnName="name" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <!-- 1.1-changes -->
    <changeSet id="DEV-2722-old-lists-not-null-1.1changes" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-2722-old-lists-not-null-constraints" author="nealeu" changeLogFile="2022/config-domain"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="addresstypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="appointmenttypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="familytypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="grp_activitytypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="grp_activityvenues" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="likelihoods" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="nextmeetinglocations" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="nextmeetingcontacttypes" columnName="name" columnDataType="VARCHAR(255)"/>
        <addNotNullConstraint tableName="outcomebenefits" columnName="name" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="DEV-2330-ecco_adam-user" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="!oracle">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(1) from users where username='ecco_adam'</sqlCheck>
        </preConditions>
        <update tableName="contacts">
            <column name="email" value="<EMAIL>"/>
            <where>id=4 and email='<EMAIL>'</where>
        </update>
    </changeSet>

    <!-- STEP 1: change venueId to be INT, add migrationId -->
    <changeSet id="DEV-2349-listDef-venues-prep-fk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="grp_activities" constraintName="FK7A1B3BED88F2AFBB"/>
    </changeSet>
    <changeSet id="DEV-2349-listDef-venues-prep-mssql" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="grp_activityvenues" columnName="id"/>
        <dropPrimaryKey tableName="grp_activityvenues"/>
    </changeSet>
    <changeSet id="DEV-2349-listDef-venues-prep" author="adamjhamer">
        <modifyDataType tableName="grp_activityvenues" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="grp_activities" columnName="venueId" newDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-2349-listDef-venues-prep2-mssql" author="adamjhamer" dbms="!mysql,!oracle">
        <addNotNullConstraint tableName="grp_activityvenues" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="grp_activityvenues" columnNames="id"/>
    </changeSet>
    <!-- oracle already has a notnullconstraint - presumably as part of the set which isn't removed in this section -->
    <!--
    select max(orderexecuted) from DATABASECHANGELOG;
    select * from DATABASECHANGELOG order by orderexecuted desc;
    insert into DATABASECHANGELOG (id, author, filename, dateexecuted, orderexecuted, exectype, md5sum, description, comments, tag, liquibase)
    values ('DEV-2349-listDef-venues-prep2-mssql', 'adamjhamer', '2022/general-domain', sysdate, 2278, 'EXECUTED', null, 'manual ignored', '', null, '3.5.3');
    insert into DATABASECHANGELOG (id, author, filename, dateexecuted, orderexecuted, exectype, md5sum, description, comments, tag, liquibase)
    values ('DEV-2349-listDef-venues-prep2-oracle', 'adamjhamer', '2022/general-domain', sysdate, 2279, 'EXECUTED', null, 'manual ignored', '', null, '3.5.3');
    -->
    <changeSet id="DEV-2349-listDef-venues-prep2-oracle" author="adamjhamer" dbms="oracle">
        <addPrimaryKey tableName="grp_activityvenues" columnNames="id"/>
    </changeSet>
    <changeSet id="DEV-2349-listDef-venues-prep-migrationId" author="adamjhamer">
        <addColumn tableName="grp_activityvenues">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-2349-listDef-venues-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="grp_activityvenues" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <!-- select * from grp_activityvenues group by name having count(*) > 1 -->
    <changeSet id="DEV-2349-listDef-venues-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'venue', name, concat('venue-', migrationId) FROM grp_activityvenues;
        </sql>
    </changeSet>
    <changeSet id="DEV-2349-listDef-venues-update-refs" author="adamjhamer">
        <update tableName="grp_activities">
            <column name="venueId" valueComputed="(SELECT eo.migrationId FROM grp_activityvenues eo WHERE eo.id = grp_activities.venueId)"/>
        </update>
        <addForeignKeyConstraint baseTableName="grp_activities" baseColumnNames="venueId" constraintName="FK_grp_venueId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <!-- remove ROLE_SWITCHUSER from 'site sysadmin' -->
    <!-- we put this changeset in the same area that useradmin was created, therefore a non-domain (general) area -->
    <!-- so that isolated tests (which run domain-areas only) have data that remains consistent with non-domain areas -->
    <changeSet author="adamjhamer" id="DEV-2222-remove-switchuser-from-sitesysadmin">
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <delete tableName="group_authorities">
            <where>group_id=19 and authority='ROLE_SWITCHUSER'</where>
        </delete>
    </changeSet>

    <!-- remove ROLE_SWITCHUSER from 'site sysadmin' -->
    <!-- we put this changeset in the same area that useradmin was created, therefore a non-domain (general) area -->
    <!-- so that isolated tests (which run domain-areas only) have data that remains consistent with non-domain areas -->
    <changeSet author="adamjhamer" id="DEV-2222-remove-switchuser-from-sitesysadmin">
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <delete tableName="group_authorities">
            <where>group_id=19 and authority='ROLE_SWITCHUSER'</where>
        </delete>
    </changeSet>

    <!-- foreign keys can't be in evidenceDomainChangeLog when evidence-domain is before generalChangeLog where the tables are created -->
    <include file="classpath:sql/2022/general-domain/003-commentTypes-to-settings.xml"/>
    <include file="classpath:sql/2022/evidence-domain/003-commentTypes-to-listdef.xml"/>

    <changeSet id="DEV-2354-contacts-archive" author="adamjhamer">
        <addColumn tableName="contacts">
            <column name="archived" type="DATE">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="OPS-101-rename-individualTypes-to-individualTypes" author="nealeu" dbms="!h2">
        <renameTable oldTableName="individual_individualTypes" newTableName="individual_individualtypes"/>
    </changeSet>

    <changeSet id="DEV-2330-ecco_neale-user" author="nealeu" context="!test-data AND !1.1-apply-encryption" dbms="!oracle">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(1) from users where username='ecco_neale'</sqlCheck>
        </preConditions>
        <update tableName="contacts">
            <column name="email" value="<EMAIL>"/>
            <where>id=5 and email='<EMAIL>'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1703-addressHistory-fk-srId" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="addresshistory" baseColumnNames="serviceRecipientId" constraintName="adrhst_sr"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"/>
    </changeSet>

    <!-- The object 'DF_servicetypes_childService' is dependent on column 'childService' -->
    <!-- alter table servicetypes drop constraint DF_servicetypes_childService; -->
    <changeSet id="DEV-2408-remove-childService" author="adamjhamer" dbms="!mssql">
        <dropColumn tableName="servicetypes" columnName="childService"/>
    </changeSet>
    <changeSet id="DEV-2408-remove-childService-mssql" author="adamjhamer" dbms="mssql">
        <sql dbms="mssql">
            alter table servicetypes drop constraint DF_servicetypes_childService;
            alter table servicetypes drop column childService;
            commit;
        </sql>
    </changeSet>

    <changeSet id="DEV-2418-referralaspects-add-customFormsMgr" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="149"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customFormMgr"/>
            <column name="name" value="customFormMgr"/>
        </insert>
    </changeSet>

</databaseChangeLog>