<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- We add and use the userkey field even if encryption not yet enabled -->
    <changeSet id="ECCO-81-migrate-add-key-column" author="default">
        <addColumn tableName="contacts">
            <column name="userkey" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-81-migrate-1" author="default">
        <customChange class="com.ecco.infrastructure.liquibase.EncodeSortableChange"
            tableName="contacts" primaryKeyColumns="id" keyColumnName="userkey" sourceColumnName="lastname"/>
    </changeSet>

    <changeSet id="ECCO-81-migrate-contacts" author="default" context="1.1-apply-encryption">
        <customChange class="com.ecco.infrastructure.liquibase.EncryptColumnsChange"
            tableName="contacts" primaryKeyColumns="id"
            columnNames="firstname,lastname,addressline1,addressline2,addressline3"
            searchableColumnNames="email,phonenumber,mobilenumber"/>
    </changeSet>

    <changeSet id="ECCO-81-migrate-venues-address" author="default" context="1.1-apply-encryption">
        <customChange class="com.ecco.infrastructure.liquibase.EncryptColumnsChange"
            tableName="venues" primaryKeyColumns="id" columnNames="addressline1,addressline2,addressline3"/>
    </changeSet>

    <changeSet id="ECCO-81-migrate-projects-address" author="default" context="1.1-apply-encryption">
        <customChange class="com.ecco.infrastructure.liquibase.EncryptColumnsChange"
            tableName="projects" primaryKeyColumns="id" columnNames="addressline1,addressline2,addressline3"/>
    </changeSet>

    <changeSet id="ECCO-81-lengthen-nhs-field" author="default" context="1.1-apply-encryption">
        <modifyDataType tableName="clientdetails" columnName="nhs" newDataType="VARCHAR(31)" />
    </changeSet>

    <changeSet id="ECCO-81-migrate-clientdetails" author="default" context="1.1-apply-encryption">
        <customChange class="com.ecco.infrastructure.liquibase.EncryptColumnsChange"
            tableName="clientdetails" primaryKeyColumns="id"
            searchableColumnNames="mothersfirstname,militarynumber,ni,nhs,paris"/>
    </changeSet>

</databaseChangeLog>
