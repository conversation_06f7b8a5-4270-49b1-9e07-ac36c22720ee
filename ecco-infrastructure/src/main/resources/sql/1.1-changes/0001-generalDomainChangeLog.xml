<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Remaining schema that hasn't been split by domain but builds on config and security -->
    <include file="classpath:sql/1.1-baseline/portableSchemaBaseline.xml" context="1.1-baseline"/>

    <!-- Data -->
    <include file="classpath:sql/1.1-baseline/portableDataBaseline.xml" context="1.1-baseline"/>
    <include file="classpath:sql/selectSoftwareModules.xml"/>

    <!-- Changes since baseline -->
    <include file="classpath:sql/1.1-changes/002-addSubmissionsTable.xml"/>
    <include file="classpath:sql/1.1-changes/004-editActionsTable_initialStatus.xml"/>
    <include file="classpath:sql/1.1-baseline/001-data-for-continuingCare.xml" context="1.1-nhs-base-data"/>
    <include file="classpath:sql/1.1-changes/006-addWorkflowDrivenServices.xml"/>
    <include file="classpath:sql/1.1-baseline/002-ECCO-257-demo-all-workflow.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-changes/006-editSubmissionsTable.xml"/>
    <include file="classpath:sql/1.1-changes/008-editClientDetailsTable.xml"/>
    <include file="classpath:sql/1.1-changes/009-editEvidenceThreatTable.xml"/>
    <include file="classpath:sql/1.1-changes/010-editReferralAspectSettingsTable.xml"/>
    <include file="classpath:sql/1.1-changes/011-referralAspectEmergenctSheet.xml"/>
    <include file="classpath:sql/1.1-changes/014-addSeniorManager.xml"/>
    <include file="classpath:sql/1.1-changes/018-self-funding.xml"/>
    <include file="classpath:sql/1.1-changes/019-roleReport.xml"/>
    <include file="classpath:sql/1.1-changes/023-ECCO-30x-scnhs-additional-workflows.xml"/>
    <include file="classpath:sql/1.1-changes/024-safe-keyword.xml"/>
    <include file="classpath:sql/1.1-changes/025-jobTitle.xml"/>
    <include file="classpath:sql/1.1-baseline/003-ECCO-332-migrate-o_st_ra-data-to-st_ra_settings.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-changes/025-ECCO-332-migrate-o_st_ra-to-st_ra_settings.xml"/>
    <include file="classpath:sql/1.1-changes/026-questionnaire-feedback.xml"/>
    <include file="classpath:sql/1.1-changes/027-questionnaire-iaptGoals.xml"/>
    <include file="classpath:sql/1.1-changes/028-questionnaire-iaptImpact.xml"/>
    <include file="classpath:sql/1.1-changes/029-questionnaire-iaptFeedback.xml"/>
    <include file="classpath:sql/1.1-changes/030-questionnaire-ecorys.xml"/>
    <include file="classpath:sql/1.1-baseline/004-ECCO-391-reorder-nhs-smart-steps.xml" context="1.1-nhs-base-data"/>
    <include file="classpath:sql/1.1-changes/031-reviewClear.xml"/>
    <include file="classpath:sql/1.1-changes/032-rejectedReferrals.xml"/>
    <include file="classpath:sql/1.1-changes/033-addOrderToQuestion.xml"/>
    <include file="classpath:sql/1.1-baseline/005-ECCO-416-iapt-cyp-workflow.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-baseline/006-ECCO-354-youth-support-workflow.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-changes/034-ECCO-465-evidenceGuidance.xml"/>
    <include file="classpath:sql/1.1-changes/035-referralaspect_nextMeeting.xml"/>
    <include file="classpath:sql/1.1-changes/035-ECCO-476-addIndexes.xml"/>
    <include file="classpath:sql/1.1-changes/036-ECCO-346-rename-MinimalReferral-to-Referral.xml"/>
    <include file="classpath:sql/1.1-changes/037-ECCO-473-fix-hibernate-sequences.xml"/>
    <include file="classpath:sql/1.1-changes/038-ECCO-422-fix-questionnairereport.xml"/>
    <include file="classpath:sql/1.1-changes/039-ECCO-481-eligibleFundingDate.xml"/>
    <include file="classpath:sql/1.1-changes/040-ECCO-495-support-staffNotes.xml"/>
    <include file="classpath:sql/1.1-changes/034-agreementOfAppointments.xml"/>
    <include file="classpath:sql/1.1-changes/041-ECCO-512-schedulemeetings.xml"/>
    <include file="classpath:sql/1.1-changes/042-ECCO-148-dataProtectionSignatures.xml"/>
    <include file="classpath:sql/1.1-changes/043-referralaspect-spellingCorrection.xml"/>
    <include file="classpath:sql/1.1-changes/044-ECCO-526-questionAnswerAlignment.xml"/>
    <include file="classpath:sql/1.1-changes/045-ECCO-528-questionAnswerRequired.xml"/>
    <include file="classpath:sql/1.1-changes/046-ECCO-531-questionAnswerDefault.xml"/>
    <include file="classpath:sql/1.1-changes/047-ECCO-200-clicklogging.xml"/>
    <include file="classpath:sql/1.1-changes/050-ECCO-578-comment-orphans.xml"/>
    <include file="classpath:sql/1.1-changes/050-ECCO-565-remove-some-passwordhistory.xml"/>
    <include file="classpath:sql/1.1-changes/052-ECCO-565-remove-all-passwordhistory.xml"/>
    <include file="classpath:sql/1.1-changes/035-contract.xml"/>
    <include file="classpath:sql/1.1-changes/053-ECCO-606-outcomebenefits.xml"/>
    <include file="classpath:sql/1.1-changes/053-ECCO-560-nextmeeting.xml"/>
    <include file="classpath:sql/1.1-changes/041-ECCO-363-groupsupport-evidence.xml"/>
    <include file="classpath:sql/1.1-changes/054-ECCO-640-editClientDetailsTable.xml"/>
    <include file="classpath:sql/1.1-changes/055-ECCO-365-engagementComments.xml"/>
    <include file="classpath:sql/1.1-changes/056-ECCO-643-riskRatings.xml"/>
    <include file="classpath:sql/1.1-changes/057-ECCO-650-riskTriggerControl.xml"/>
    <include file="classpath:sql/1.1-changes/043-ECCO-522-extraFields.xml"/>
    <include file="classpath:sql/1.1-changes/059-ECCO-657-economicstatus.xml"/>
    <include file="classpath:sql/1.1-changes/038-ECCO-81-migrate-data.xml"/>
    <include file="classpath:sql/1.1-changes/058-ECCO-519-add-contacts-code-column.xml"/>
    <include file="classpath:sql/1.1-changes/060-ECCO-743-mileage.xml"/>
    <include file="classpath:sql/1.1-changes/062-ECCO-752-referralaspect-rotaVisit.xml"/>
    <include file="classpath:sql/1.1-changes/063-ECCO-800-resourceSchedules.xml"/>
    <include file="classpath:sql/1.1-changes/064-ECCO-816-ClientDetailAndWorkerExtraFields.xml"/>
    <include file="classpath:sql/1.1-changes/063-ECCO-821-activities.xml"/>
    <include file="classpath:sql/1.1-changes/064-ECCO-822-activities.xml"/>
    <include file="classpath:sql/1.1-changes/066-ECCO-829-resourceSchedules.xml"/>
    <include file="classpath:sql/1.1-changes/066-ECCO-826-appointment-diary.xml"/>
    <include file="classpath:sql/1.1-changes/067-ECCO-832-financeBand.xml"/>
    <include file="classpath:sql/1.1-changes/068-ECCO-869-add-isDefault-columns.xml"/>
    <include file="classpath:sql/1.1-changes/042-ECCO-513-addresstypes.xml"/>
    <include file="classpath:sql/1.1-changes/068-ECCO-820-rename-Activity-to-GroupSupportActivity.xml"/>
    <include file="classpath:sql/1.1-changes/069-ECCO-820-groupsupport-with-project.xml"/>
    <include file="classpath:sql/1.1-changes/070-ECCO-870-groupsupport-with-actions.xml"/>

    <include file="classpath:sql/1.1-changes/071-Feb-2014-until-next.xml"/>
    <include file="classpath:sql/1.1-baseline/007-baseline-2014-May-onwards-til-next.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-changes/072-Jan-2015-until-next.xml"/>
    <include file="classpath:sql/1.1-changes/073-report-defs.xml"/>
    <include file="classpath:sql/1.1-changes/074-ECCO-1573-central-proc-svc-type.xml"/>
    <include file="classpath:sql/1.1-baseline/008-baseline-2015-Nov-onwards-til-next.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
    <include file="classpath:sql/1.1-changes/076-report-defs-2.xml"/>
    <!-- STOP: DO NOT ADD TO THIS FILE -->
</databaseChangeLog>
