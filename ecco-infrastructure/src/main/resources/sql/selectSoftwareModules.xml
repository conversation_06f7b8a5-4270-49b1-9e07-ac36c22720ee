<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- This is pulled in by -Ddb.extraContexts=acceptanceTests -->
    <changeSet author="nealeu" id="optionally-enable-offline" context="acceptanceTests">
        <validCheckSum>3:b1dfb3861c22467b1750b88f004544e8</validCheckSum>
        <update tableName="cfg_module">
            <column name="enabled" valueBoolean="true" />
            <where>name='offline'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="enable-buildings-module" context="acceptanceTests">
        <update tableName="cfg_module">
            <column name="enabled" valueBoolean="true" />
            <where>name='buildings'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="enable-hact-module-inTests" context="acceptanceTests">
        <insert tableName="cfg_module">
            <column name="name" value="hact" />
            <column name="enabled" valueBoolean="true" />
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="DEV-634-enable-new.triggerEmail-feature" context="acceptanceTests">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
            <where>name='tasks.edit.new.triggerEmail'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DEV-1701-enable-rota.shifts-feature" context="acceptanceTests">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
            <where>name='rota.shifts'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2035-enable-communication-feature" context="acceptanceTests">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
            <where>name='referralOverview.communication'</where>
        </update>
    </changeSet>

</databaseChangeLog>
