package com.ecco.infrastructure.entity;

import com.ecco.dom.IntegerKeyedEntity;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;
import javax.persistence.*;

/**
 * Abstract Entity that uses {@link javax.persistence.AccessType#FIELD} access.
 */
@MappedSuperclass
public abstract class AbstractIntKeyedEntity extends AbstractUnidentifiedVersionedEntity<Integer> implements IntegerKeyedEntity {

    private static final long serialVersionUID = 1L;

    @EqualsAndHashCode.Include
    @Id
    @GeneratedValue(generator="another_id_nameTableGenerator")
    @GenericGenerator(name="another_id_nameTableGenerator", strategy="com.ecco.infrastructure.entity.UseExistingOrGenerateTableGenerator", parameters={
            @Parameter(name="table_name", value="hibernate_sequences"),
            @Parameter(name="increment_size", value="1"),
            @Parameter(name="optimizer", value="none"),
            @Parameter(name="initial_value", value="1000"),
            @Parameter(name="segment_value", value="id_name")})
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Integer id = null;

    public AbstractIntKeyedEntity() {
    }

    protected AbstractIntKeyedEntity(Integer id) {
        this.id = id;
    }


    // annotations are on the getters - because then we can access the getId without instantiating another sql (for eg parents)
    // see - https://forum.hibernate.org/viewtopic.php?f=1&t=996491&view=previous
    // also it does seem to be recommended - http://stackoverflow.com/questions/305880/hibernate-annotation-placement-question
    // should be private (assuming its a surrogate key to be controlled by hibernate)
    // unsaved-value is legacy
    // we did attempt to return 'Serializable' but caused a problem with unit test table generation
    // instead, we can override getId in subclasses (eg composite key ids) because its on the getter
    //
    // generatedvalue:
    // in oracle, the default is for a single sequence to be created for the whole db
    // in 'identity' databases, an autogenerated id is given per table
    // both are available through 'auto' - but a new approach is in hibernate - org.hibernate.id.enhanced...
    //
    // it is recommended for a sequence to go to sequence per table
    // but we need to specify that hibernate won't cache some numbers if clusters are used - unless a standard safe algorithm is used - eg hilo
    // and we feel safer if the sequences are in a table we can control
    // so we follow http://elegando.jcg3.org/2009/08/hibernate-generatedvalue/
    // @Id @GeneratedValue(strategy=GenerationType.TABLE, generator="generatorName")
    // @TableGenerator(name="generatorName", allocationSize=1)
    //
    // however, hibernate's new org.hibernate.id.enhanced.TableGenerator does the same thing
    // see http://www.javaplex.com/blog/how-to-achieve-database-auto-id-generation-portability-in-hibernate/
    // and http://in.relation.to/2082.lace
    @Override
    public Integer getId() {
        return id;
    }

    // ideally id should be private and we use business keys for identification
    // however its public because we need to set it in places in multiId code
    @Override
    public void setId(Integer id) {
        this.id = id;
    }
}
