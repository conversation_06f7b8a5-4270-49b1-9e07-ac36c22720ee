package com.ecco.infrastructure.entity;

import javax.persistence.MappedSuperclass;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

/**
 * Provide most of Spring's @{@link Configurable} support but without the need
 * for AspectJ
 * @deprecated Use ConfigurableIntKeyedEntity instead
 */
@Deprecated
@Configurable
@MappedSuperclass
public abstract class ConfigurableLongKeyedEntity extends AbstractLongKeyedEntity {

    {
        try {
            injectServices();
        } catch (BeanCreationException e) {
            LoggerFactory.getLogger(getClass())
                    .warn("Failed to inject services (expected during some tests, but not at runtime) ", e);
        }
    }

    public ConfigurableLongKeyedEntity() {
        super();
    }

    public ConfigurableLongKeyedEntity(Long id) {
        super(id);
    }

    public Object readResolve() {
        injectServices();
        return this;
    }

    private final void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }
}