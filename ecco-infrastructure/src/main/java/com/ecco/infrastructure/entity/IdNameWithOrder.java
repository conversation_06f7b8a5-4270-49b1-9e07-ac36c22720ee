package com.ecco.infrastructure.entity;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public class IdNameWithOrder extends IdName {

    private static final long serialVersionUID = 1L;

    @Column(nullable=false)
    private int orderby;


    public IdNameWithOrder() {
    }

    public IdNameWithOrder(Long id, String name) {
        super(id, name);
    }


    /**
     * Order to allow re-ordering of items within a collection (e.g. Questions within a QuestionGroup)
     */
    public int getOrderby() {
        return orderby;
    }

    public void setOrderby(int order) {
        this.orderby = order;
    }
}
