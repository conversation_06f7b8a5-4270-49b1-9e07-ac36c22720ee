package com.ecco.infrastructure.dom;

import java.util.UUID;

import com.ecco.infrastructure.Created;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

import org.jspecify.annotations.NonNull;

import javax.persistence.*;

@MappedSuperclass
@Access(AccessType.FIELD)
public abstract class BaseIntKeyedCommand extends BaseCommand<Integer> implements Created {

    @Id
    @GeneratedValue(generator="id_nameTableGenerator")
    @TableGenerator(
            name = "id_nameTableGenerator", initialValue = 1000, pkColumnValue = "id_name",
            allocationSize = 1, table = "hibernate_sequences")
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Integer id = null;

    /**
     * @param uuid               A UUID uniquely identifying the command, used for de-duplication.
     * @param remoteCreationTime The time at which this command was created on the client.
     * @param userId             The ID of the user who submitted the command.
     * @param body               The body of the command as a JSON string.
     */
    protected BaseIntKeyedCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

    /**
     * Required by JPA/Hibernate & Jackson
     */
    protected BaseIntKeyedCommand() {
        super();
    }

    @Override
    @Nullable
    public Integer getId() {
        return id;
    }

}
