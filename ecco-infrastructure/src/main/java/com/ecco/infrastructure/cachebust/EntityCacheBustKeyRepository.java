package com.ecco.infrastructure.cachebust;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import static com.ecco.infrastructure.config.root.CacheConfig.CACHE_ENTITY_CACHEBUST_KEY;
import static java.lang.System.currentTimeMillis;

@SuppressWarnings("unused")
@Component
public class EntityCacheBustKeyRepository {

    @Cacheable(CACHE_ENTITY_CACHEBUST_KEY)
    public String getTimestamp(Class<?> entity, Number entityId) {
        return entity.getSimpleName() + "-" + currentTimeMillis();
    }

    @CacheEvict(CACHE_ENTITY_CACHEBUST_KEY)
    public void invalidateCacheFor(Class<?> entity, Number entityId) { }
}
