package com.ecco.infrastructure.config.root;

import com.ecco.infrastructure.cachebust.ResourceVersionSource;
import com.ecco.infrastructure.cachebust.StartupTimestampResourceVersionSource;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.ApplicationPropertiesImpl;
import com.ecco.infrastructure.health.EndpointAdvice;
import com.ecco.infrastructure.health.EndpointStats;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.hibernate.EntityUriMapperImpl;
import com.ecco.infrastructure.markdown.FlexmarkMarkdownProcessor;
import com.ecco.infrastructure.markdown.MarkdownProcessor;
import com.ecco.infrastructure.rest.hateoas.ApiLinkTo;
import com.ecco.infrastructure.util.AppContextConfig;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.annotation.*;
import org.springframework.context.annotation.aspectj.EnableSpringConfigured;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@EnableSpringConfigured
@EnableAspectJAutoProxy(proxyTargetClass=true) // for @Aspect. Must match proxying used on @EnableTransactionManagement
// light mode because we don't have beans depend on other beans via method calls, and we use constructor args instead of @Autowired beans
// see HibernateJpaConfiguration and parent class
@Configuration(proxyBeanMethods = false)
@Import({
    UTCConfig.class,
    AppContextConfig.class,
    SecureConfig.class,
    CommonPropertiesConfig.class,
    DevelopmentPropertiesConfig.class,
    ProductionMySqlPropertiesConfig.class,
    ProductionOraclePropertiesConfig.class,
    ProductionSqlServerPropertiesConfig.class,
    CacheConfig.class,
    PersistenceConfig.class,
    SchemaExportConfig.class,
    DBUnitExportConfig.class,
    DBUnitImportConfig.class,
    MessageBusConfig.class
})
@ComponentScan(basePackageClasses = {
        ApiLinkTo.class
})
public class InfrastructureConfig {

    @Bean
    public ResourceVersionSource resourceVersionSource() {
        return new StartupTimestampResourceVersionSource();
    }

    @Bean
    public ApplicationProperties applicationProperties(ResourceVersionSource resourceVersionSource) {
        return new ApplicationPropertiesImpl(resourceVersionSource);
    }

    @Bean
    public MarkdownProcessor markdownProcessor() {
        return new FlexmarkMarkdownProcessor();
    }

    @Bean
    public EntityUriMapper entityUriMapper() {
        return new EntityUriMapperImpl();
    }

    @Bean
    public EndpointStats endpointStats() {
        return new EndpointStats();
    }

    @Bean
    public EndpointAdvice endpointAdvice() {
        return new EndpointAdvice();
    }

    @Bean
    public AnnotationBeanConfigurerAspect annotationBeanConfigurerAspect() {
        return new AnnotationBeanConfigurerAspect();
    }

    @Bean
    public ExecutorService singleThreadExecutor() {
        return Executors.newSingleThreadExecutor();
    }
}
