package com.ecco.infrastructure.config.root;

import java.util.Queue;
import java.util.concurrent.atomic.AtomicInteger;

import org.hibernate.EmptyInterceptor;

import com.google.common.collect.EvictingQueue;

/** record stats mainly for use in test for checking we aren't doing too much work, but could also become
 * a transaction-scoped bean for stats and keeping ref to statement SQL in an {@link EvictingQueue} so we can log them
 * after an error.
 */
public class StatementStatsInterceptor extends EmptyInterceptor {

    private static final long serialVersionUID = 1L;

    private AtomicInteger statementCount = new AtomicInteger();

    private EvictingQueue<String> capturedSQL;

    @Override
    public String onPrepareStatement(String sql) {
        statementCount.incrementAndGet();
        if (capturedSQL != null) {
            capturedSQL.add(sql);
        }
        return super.onPrepareStatement(sql);
    }

    public int getStatementCount() {
        return statementCount.get();
    }

    public void resetStats() {
        statementCount.set(0);
    }

    public void enableSQLCapture() {
        capturedSQL = EvictingQueue.create(20);
    }

    public Queue<String> getCapturedSQL() {
        return capturedSQL;
    }
}
