package com.ecco.infrastructure.config.root;

import org.springframework.core.env.Environment;

public interface EccoEnvironment extends Environment {

    DBUnitMode getDBUnitMode();

    LiquibaseMode getLiquibaseMode();

    /** The snapshot version that this is based on.  1.0-changes were used to migrate pre-liquibase environments to
     *  align with a portable baseline based on an export of that base data.
     *  1.1 was then applied going forwards.
     *  If we make a new snapshot (by initialising and then exporting) then it will be a new version and will save
     *  all of the table schema migrations.  Tests and new clients could then use that snapshot.
     */
    SchemaVersion getSchemaVersion();

    /**
     * Audit can be disabled by -DdisableAudit - generally useful for testing only.
     *
     * @return true if audit is disabled
     */
    boolean isDisableAudit();

    boolean isTestRun();

    boolean shouldRebuild();

    /** Should Liquibase initialise base schema and populate initial data prior to applying updates */
    boolean shouldCreateSchema();

    boolean shouldRunLiquibaseChangelogs();

    boolean shouldEncryptDatabaseFields();

    String extraContexts();

    /**
     * @return the hostname to use when building jdbc URL, e.g. localhost.
     */
    String getDbHostName();

    /** The database name */
    String getDbName();

    /** The schema within that database */
    String getDbSchema();

    /**
     * @return port (e.g. 3306) or null if not set (if null, port should be left to {@link MySqlDatabaseConfig},
     * {@link EmbeddedSqlDatabaseConfig} and friends.
     */
    Integer getDbPort();

    /** True if env=dev (rather than prod) */
    boolean isDevMode();

    /** e.g. db.embedded-persist-path="file:~/h2/ecco" */
    String getEmbeddedPersistPath();
}
