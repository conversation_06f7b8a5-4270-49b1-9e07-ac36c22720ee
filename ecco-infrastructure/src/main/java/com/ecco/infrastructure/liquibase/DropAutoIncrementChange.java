package com.ecco.infrastructure.liquibase;

import liquibase.change.ChangeMetaData;
import liquibase.change.DatabaseChange;
import liquibase.change.custom.CustomSqlChange;
import liquibase.database.Database;
import liquibase.database.core.PostgresDatabase;
import liquibase.ext.DropAutoIncrementStatement;
import liquibase.statement.SqlStatement;
import liquibase.statement.core.DropDefaultValueStatement;
import liquibase.statement.core.DropSequenceStatement;
import liquibase.util.StringUtils;

@DatabaseChange(
        name="dropAutoIncrement",
        description = "Changes an existing column to remove auto-increment on a column",
        priority = ChangeMetaData.PRIORITY_DEFAULT,
        appliesTo = "column")
public class DropAutoIncrementChange extends AbstractCustomSqlChange implements CustomSqlChange {
    private String schemaName;
    private String tableName;
    private String columnName;
    private String columnDataType;

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = StringUtils.trimToNull(schemaName);
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnDataType() {
        return columnDataType;
    }

    public void setColumnDataType(String columnDataType) {
        this.columnDataType = columnDataType;
    }

    @Override
    public SqlStatement[] generateStatements(Database database) {
        if (database instanceof PostgresDatabase) {
            String sequenceName = (getTableName() + "_" + getColumnName() + "_seq").toLowerCase();
            return new SqlStatement[]{
                    new DropDefaultValueStatement(null, schemaName, getTableName(), getColumnName(), getColumnDataType()),
                    new DropSequenceStatement(null, schemaName, sequenceName)
            };
        }

        return new SqlStatement[]{
                new DropAutoIncrementStatement(getSchemaName(), getTableName(), getColumnName(), getColumnDataType())};
    }

    @Override
    public String getConfirmationMessage() {
        return "Auto-increment dropped from " + getTableName() + "." + getColumnName();
    }

}
