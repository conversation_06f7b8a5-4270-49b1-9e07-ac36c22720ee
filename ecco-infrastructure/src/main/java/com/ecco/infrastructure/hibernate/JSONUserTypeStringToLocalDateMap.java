package com.ecco.infrastructure.hibernate;

import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.fasterxml.jackson.databind.util.ISO8601DateFormat;
import org.joda.time.LocalDate;

/**
 * A simple, but manual approach to having a Map be the required type after de-serialising.
 * A generics-backed Map suffers from type erasure so jack<PERSON> can't understand the typing
 * back into LocalDate.
 * Without this class, the literal JSON string was being presented in the jsp date fields.
 * See also http://stackoverflow.com/questions/18002132/deserializing-into-a-hashmap-of-custom-objects-with-jackson
 * and "Data Binding with Generics" from http://wiki.fasterxml.com/JacksonInFiveMinutes
 */
public class JSONUserTypeStringToLocalDateMap extends JSONUserTypeStringToObjectMap {

    private static JSONClobMap converter = new JSONClobMap<>(
            ConvertersConfig.getObjectMapper()
                .setDateFormat(new ISO8601DateFormat()),
            LocalDate.class);

    @Override
    public JSONClobMap getConverter() {
        return converter;
    }

}
