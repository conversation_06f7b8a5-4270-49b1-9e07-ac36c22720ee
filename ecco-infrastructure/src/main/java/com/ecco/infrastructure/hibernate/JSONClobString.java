package com.ecco.infrastructure.hibernate;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JSONClobString extends BaseJSONClob<String> {

    private final JavaType type;

    public JSONClobString(ObjectMapper mapper) {
        super(mapper);
        this.type = mapper.getTypeFactory()
                .constructType(String.class);
    }

    @Override
    public JavaType getTargetType() {
        return type;
    }

    @Override
    public Class getReturnedClass() {
        return String.class;
    }

}