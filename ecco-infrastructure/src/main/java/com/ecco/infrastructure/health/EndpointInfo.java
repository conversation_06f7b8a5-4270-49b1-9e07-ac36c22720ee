package com.ecco.infrastructure.health;

public class EndpointInfo {

    /** alpha value for low pass filter */
    private final double ALPHA = 0.1;

    private final String endpointKey;

    private int callCount = 0;

    private double averageResponseMillis = 0;

    private double lowPassResponseMillis = 0;


    public EndpointInfo(String endpointKey) {
        this.endpointKey = endpointKey;
    }

    public void addResponse(double responseMillis) {

        callCount++;

        // see http://math.stackexchange.com/a/106720
        averageResponseMillis = averageResponseMillis + (responseMillis - averageResponseMillis) / callCount;

        lowPassResponseMillis = lowPassResponseMillis + (responseMillis - lowPassResponseMillis) * ALPHA;
    }

    public String getEndpointKey() {
        return endpointKey;
    }

    public int getCallCount() {
        return callCount;
    }

    public double getAverageResponseMillis() {
        return averageResponseMillis;
    }


    /** Return response by applying a low pass filter */
    public double getLowPassResponseMillis() {
        return lowPassResponseMillis;
    }

}
