package com.ecco.infrastructure.util;

import org.jspecify.annotations.NonNull;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.WeakHashMap;

public final class CaseInsensitiveString implements Comparable<CaseInsensitiveString> {
    static Map<String, CaseInsensitiveString> cache = Collections.synchronizedMap(new WeakHashMap<>());

    final String originalCase;
    final String lowerCase;

    public CaseInsensitiveString(@NonNull String str) {
        Assert.notNull(str);
        this.originalCase = str;
        this.lowerCase = originalCase.toLowerCase();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CaseInsensitiveString that = (CaseInsensitiveString) o;

        return lowerCase.equals(that.lowerCase);
    }

    @Override
    public int compareTo(CaseInsensitiveString that) {
        return this.lowerCase.compareTo(that.lowerCase);
    }

    @Override
    public int hashCode() {
        return lowerCase.hashCode();
    }

    @Override
    public String toString() {
        return originalCase;
    }

    public static CaseInsensitiveString valueOf(String str) {
        CaseInsensitiveString result = cache.get(str);
        if (result == null) {
            cache.put(str, result = new CaseInsensitiveString(str));
        }
        return result;
    }
}
