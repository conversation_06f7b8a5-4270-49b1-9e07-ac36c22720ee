package com.ecco.infrastructure.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import org.jspecify.annotations.NonNull;

@Component
public class AppContextAware implements ApplicationContextAware {

    static final long serialVersionUID = 1L;

    static ApplicationContext applicationContext = null;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) {
        AppContextAware.applicationContext = applicationContext;
    }

    public static Object getBean(String name) {
        if (applicationContext != null && applicationContext.containsBean(name))
            return applicationContext.getBean(name);
        return null;
    }

}
