import * as React from "react";
import {FC, useState, ReactElement} from "react";
import {
    possiblyModalForm
} from "ecco-components-core";
import {Grid, Button, ButtonGroup} from '@eccosolutions/ecco-mui';

const Wizard = React.lazy(() => import("./RepairComponent").then(i => ({default: i.Wizard})));

const ModalWizard: FC<{open: boolean, close: () => void}> = props => {
    return possiblyModalForm(
        "new repair",
        true,
        props.open,
        () => props.close(),
        () => props.close(),
        true, // TODO could emitChangesTo and see if there are any commands
        true,
        <Wizard schemaUri="repairs/$schema/" buildingId={null} />,
        undefined,
        undefined,
        undefined,
        "lg",
        undefined,
        true
    );
}

const ButtonRenderer = (onClick: () => void) => <Grid item>
    <ButtonGroup size="small" aria-label="new repair">
        <Button onClick={() => onClick()}>
            new repair
        </Button>
    </ButtonGroup>
    </Grid>;

export const NewRepair: FC<{linkContactId?: number | undefined, renderer?: ((onClick: () => void) => ReactElement) | undefined}> = props => {
    const [modal, setModal] = useState(false);

    return (<>
        {modal && <ModalWizard open={modal} close={() => setModal(false)}/>}
        {props.renderer
                ? props.renderer(() => setModal(true))
                : ButtonRenderer(() => setModal(true))}
    </>)
};
