import $ = require("jquery");
import ActionButton = require("../../controls/ActionButton");
import SummarisedElement = require("../../controls/SummarisedElement");
import * as cfgDomain from "ecco-dto";
import Outcome = cfgDomain.Outcome;
import EditNameForm = require("../../service-config/EditNameForm");

class OutcomeSummaryControl implements SummarisedElement<Outcome> {

    private $title: $.JQuery;
    private $icon: $.JQuery;

    constructor(public outcome: Outcome) {
        this.render();
    }

    private render() {
        var button = new ActionButton("edit")
                .addClass("btn btn-primary")
                .clickSynchronous( () => EditNameForm.editOutcomeInModal(this.outcome) );

        this.$icon = $("<i>").addClass("fa fa-angle-double-right");
        this.$title = $("<div>").addClass("container-fluid")
            .append($("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-11")
                    .text(this.outcome.getName())
                )
                .append($("<div>").addClass("col-xs-1")
                    .append(this.$icon)
                    .append("&nbsp;")
                )
            )
            .append($("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-12").append(button.element()))
            );
    }

    private serviceDisplayName(item: Outcome): string {
        var displayName: string = item.getName();
        return displayName;
    }

    public searchId() {
        return String(this.outcome.getId());
    }

    public title(): $.JQuery {
        return this.$title;
    }

    public body(): $.JQuery {
        return null;
    }

    initiallyHidden():boolean {
        return false;
    }

    public target() {
        return this.outcome;
    }
}

export = OutcomeSummaryControl
