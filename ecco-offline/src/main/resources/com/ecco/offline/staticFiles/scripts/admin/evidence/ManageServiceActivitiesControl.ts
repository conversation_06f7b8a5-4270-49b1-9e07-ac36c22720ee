import $ = require("jquery");

import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import TableControl = require("../../controls/TableControl");
import TableRow = require("../../controls/TableRow");
import {apiClient} from "ecco-components";
import {Action, ActivityType, ServiceType, ServiceTypeAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import {ActionDefActivityAssociationChangeCommand} from "../../service-config/commands";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";

var activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(apiClient);
var serviceTypeRepository = new ServiceTypeAjaxRepository(apiClient.withCachePeriod(0));

class BackingData {
    constructor(public activityTypes: ActivityType[], public serviceType: ServiceType) {
    }
}

class ManageServiceActivitiesControl extends BaseAsyncCommandForm<BackingData> {

    public static showInModal(serviceId: number, serviceTypeId: number) {
        var form = new ManageServiceActivitiesControl(serviceId, serviceTypeId);
        showFormInModalDom(form);
        form.load();
    }

    constructor(private serviceId: number, private serviceTypeId: number) {
        super("Manage service activities for service id: " + serviceId.toString());
    }

    fetchViewData(): Promise<BackingData> {
        return activityTypeRepository.findActivityTypesByServiceId(this.serviceId)
            .then( (activityTypes) => {
                return serviceTypeRepository.findOneServiceType(this.serviceTypeId)
                    .then( (serviceType) => {
                        return new BackingData(activityTypes, serviceType);
                    });
            });
    }

    private renderCell(action: Action, activity: ActivityType) {
        var selected = (action.activityTypes.some( (selected) => selected.id == activity.id ) );
        return $("<td>").text(selected ? "Y" : "-");
    }

    private activityChange(action: Action, activity: ActivityType, checked: boolean) {
        var cmd = new ActionDefActivityAssociationChangeCommand(checked ? "add" : "remove",
            this.serviceId, action.getId(), activity.id);
        this.commandQueue.addCommand(cmd);
    }

    private onCellClick(action: Action, activity: ActivityType, $cell: $.JQuery) {
        this.enableSubmit();
        var wasFalse = $cell.text() == "-";
        this.activityChange(action, activity, wasFalse);
        $cell.text(wasFalse ? "Y" : "-");
    }

    render(data: BackingData) {
        this.element().empty();

        data.serviceType.getOutcomes().forEach( (outcome) => {
            var table = new TableControl<ActivityType,Action>(data.activityTypes,
                (activity) => activity.name, (action) => action.getName());
            this.append(table);
            var row = new TableRow(outcome.getName(), undefined, null, () => $("<td>"),
                                data.activityTypes, false);
            row.element().addClass("active");
            table.appendRow(row);
            outcome.getActionGroups().forEach( (group) => {
                group.getActions().forEach( (action) => {
                    var row = new TableRow<ActivityType, Action>(action.getName(), undefined, action,
                            (r,c) => this.renderCell(r,c),
                            data.activityTypes, false,
                            (r,c,$cell) => this.onCellClick(r,c,$cell));
                    table.appendRow(row);
                });
            });
        });
    }
}
export = ManageServiceActivitiesControl;
