import jQuery = require("jquery");

class CommentTypeFilter {

    private types: {[s :string] :string} = {};

    private $buttons = jQuery("#jsCommentTypeFilterButtons");
    private $items = jQuery(".workItem");


    /** Find the different items we support and attach the appropriate component to each of them */
    public attach() {
        this.$items.each( (index, element) => {
            this.extractType(element);
        });

        // add 'none' first for consistency
        this.addButton('(none)');
        for( var type in this.types) {
            if (type != '(none)')
                this.addButton(type);
        }
        // add 'all' last for consistency
        this.addButton("all");
    }

    private extractType(element:Element) {
        var commentType = element.getAttribute("data-comment-type");
        this.types[commentType] = commentType;
    }

    private addButton(type: string) {
        var wrapper = jQuery('<span style="margin-right: 5px;">');
        wrapper.append(jQuery('<input type="radio" name="typefilter">')
            .click( () => {
                this.filter(type);
            })
            .attr("value", type)
        );
        wrapper.append(jQuery('<span>').text(type));
        this.$buttons.append(wrapper);
    }

    private filter(type: string) {

        if (type == "all") {
            this.$items.show();
            return;
        }

        this.$items.each( (index, element) => {
            var commentType = element.getAttribute("data-comment-type");
            if (type == commentType) {
                jQuery(element).show();
            }
            else {
                jQuery(element).hide();
            }
        });

    }
}

jQuery( () => {
    var instance = new CommentTypeFilter();
    instance.attach();
});


