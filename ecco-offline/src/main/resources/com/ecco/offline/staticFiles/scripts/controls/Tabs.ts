import $ = require("jquery-bundle");
import {delay} from "@eccosolutions/ecco-common"
import {TabSelectedEvent} from "../common/tabEvents";

/**
 * Wrap tabs functionality.
 *
 * data API:
 * - data-key attr = attr used to store which tab is selected (defaults to 'ui-tabs')
 * - data-selected attr = number of tab to select if defined
 * - data-disabled attr = space separated indices of tabs to disable
 * - data-no-remember attr = if specified and is 'true' disable persistence
 *
 * Note: the cookie code is intelligent enough to see if something else is selected [see jquery ui docs]
 * but not if this is done programatically after, when we run into
 * this: http://stackoverflow.com/questions/4316296/jquery-tabs-cookie-plugins-2-tabs-highlighted
 */
class Tabs {

    public static attach(selector: string) {
        $(selector).each( (index: number, element: Element) => {
            const $tab = $(element);
            Tabs.attachTab($tab);
        });
    }

    private static attachTab($tabs: $.JQuery) {
        const noRemember = $tabs.attr("data-no-remember");
        const disablePersistence = noRemember && noRemember == "true";
        const persistSelectedTab = !disablePersistence && typeof window.sessionStorage != 'undefined';

        const key = $tabs.attr("data-key") || 'ui-tabs';
        const currTabIndex = persistSelectedTab ? sessionStorage[key] : 0;
        (<any>$tabs).tabs({
            spinner: '',
            active: currTabIndex,
            activate: function(event,ui) {
                if (persistSelectedTab) {
                    sessionStorage[key] = ui.newPanel.index();
                }
            }
        });
        const noWidget = $tabs.attr("data-no-widget");
        if (noWidget && noWidget == "true") {
            $tabs.removeClass("ui-widget ui-widget-content ui-corner-all");
        }

        // Disable any marked for being disabled
        const disabled = $tabs.attr("data-disabled");
        if (disabled) {
            (<any>$tabs).tabs("option", "disabled", $.parseJSON(disabled));
        }

        const selected = $tabs.attr("data-selected");
        if ($.isNumeric(selected)) {
            (<any>$tabs).tabs("option", "active", selected);
        }

        // Delay this call as we've got stuff we're loading first - TODO: Delay is a bit iffy so may still trigger early
        delay(100)
            .then( () => Tabs.fireSelectedEvent($tabs, currTabIndex) );
    }

    /** Fire event for the currently selected tab. tabIndex starts at zero for the first tab */
    private static fireSelectedEvent($tabs: $.JQuery, tabIndex: number) {
        const tabId = $tabs.find("ul>li:eq(" + tabIndex + ")").attr("aria-labelledby");
        TabSelectedEvent.bus(tabId).fire(new TabSelectedEvent());
    }
}

export = Tabs;
