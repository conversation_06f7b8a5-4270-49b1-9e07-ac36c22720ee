import _ = require("lodash");
import $ = require("jquery");
import Element = require("./Element");

/**
 Control which presents a tabbed UI onto a series of labelled, switchable panels.
 Uses Bootstrap tabs - http://getbootstrap.com/javascript/#tabs

 For how we deal with disabling tabs, see http://stackoverflow.com/questions/20668880/bootstrap-tabs-pills-disabling-and-with-jquery
*/
class TabbedContainer implements Element {
    private $tabsContainer = $("<ul>").addClass("nav nav-tabs");
    private $panelsContainer = $("<div>").addClass("tab-content");
    private $container = $("<div>")
            .append(this.$tabsContainer)
            .append(this.$panelsContainer);
    private $panelContainerTemplate = $("<div>").addClass("tab-pane");

    private tabs: { [tabId: string]: $.JQuery; } = {};
    private panels: { [tabId: string]: $.JQuery; } = {};

    /** Adds a tab to the tabbed container with a custom ID, label and content element, and returns the index of that tab, 0-based. */
    public append(tabId: string, label: string, element: $.JQuery): number;
    public append(tabId: string, label: string, element: Element | HTMLElement): number;
    append(tabId: string, label: string, element: any): number {
        var containerId = _.uniqueId('tab_' + tabId + '_');

        this.tabs[tabId] = $("<li>").append($("<a>").attr("href", "#" + containerId).attr("data-toggle", "tab").text(label));
        this.panels[tabId] = this.$panelContainerTemplate.clone().attr('id', containerId).append(this.toJQuery(element));
        var index = this.$panelsContainer.children().length;
        if (index == 0) {
            // This is the first tab, so make it active
            this.tabs[tabId].addClass("active");
            this.panels[tabId].addClass("active");
        }
        this.$tabsContainer.append(this.tabs[tabId]);
        this.$panelsContainer.append(this.panels[tabId]);
        return index;
    }

    private toJQuery(element: any): $.JQuery {
        var $content: $.JQuery;
        if ((<Element>element).element) {
            $content = element.element();
        }
        else {
            $content = $(element);
        }
        return $content;
    }

    /** Returns true if the tab exists in this container. */
    public hasTab(tabId: string): boolean {
        return this.tabs[tabId] != null;
    }

    /** Shows the specified tab. */
    public show(tabId: string) {
        this.enable(tabId);
        (<any> this.tabs[tabId].find("a")).tab('show');
    }

    public disable(tabId: string) {
        var tab = this.tabs[tabId];
        if (!tab.hasClass("disabled")) {
            tab.addClass("disabled").find("a").removeAttr("data-toggle");
        }
    }

    public enable(tabId: string) {
        var tab = this.tabs[tabId];
        if (tab.hasClass("disabled")) {
            tab.removeClass("disabled").find("a").attr("data-toggle", "tab");
        }
    }

    public annotateAsRelevant(tabId: string) {
        var tab = this.tabs[tabId];
        if (!tab.hasClass("relevant")) {
            tab.find("a").append("&nbsp;").append("<i class='fa fa-check-square-o'></i>");
            tab.addClass("relevant");
        }
    }

    /** Hides the specified tab. */
    public hide(tabId: string) {
        (<any> this.tabs[tabId].find("a")).tab('hide');
    }

    public element(): $.JQuery {
        return this.$container;
    }
}

export = TabbedContainer