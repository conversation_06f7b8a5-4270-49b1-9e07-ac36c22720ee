import * as React from "react"
import {ClassAttributes} from "react"
import {isEmpty} from "@eccosolutions/ecco-common";
import {CommandQueue, CommandSource} from "ecco-commands";
import {showInCommandForm} from "../components/CommandForm";

import {
    AsyncServiceRecipientWithEntities,
    AsyncSessionData,
    CommandForm,
    CommandSubform,
    ServiceRecipientWithEntitiesContext,
    withCommandForm
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {SessionData} from "ecco-dto";
import {StaffDto} from "ecco-dto/hr-dto";

import {StaffDetailCommand} from "ecco-commands";
import {individualDetailsCommonFields, ClientAddressLocation} from "ecco-components";
import {Grid} from '@eccosolutions/ecco-mui';

/**
 * LOAD FORM - non-jsx, jsp
 */
export function staffDetail(srId: number, onCompleted: () => void) {
    showInCommandForm(
        <AsyncServiceRecipientWithEntities srId={srId}>
            <StaffDetailEditor serviceRecipientId={srId} formRef={() => {}}/>
        </AsyncServiceRecipientWithEntities>,
        onCompleted
    );
}
/**
 * Command-based editing of a staff
 */
const StaffDetailEditor = (props: {serviceRecipientId: number, formRef: (c: StaffDetail) => void}) =>
        withCommandForm(commandForm =>
            possiblyModalForm(
                "staff details",
                true, true,
                () => commandForm.cancelForm(),
                () => commandForm.submitForm(),
                commandForm.getErrors().length > 0, // TODO: Doesn't work dynamically as it needs to be a proper component
                false,
                getStaffDetailSubform(props.serviceRecipientId, props.formRef, commandForm)
            )
        );

/* @Exemplar */
/*
export const StaffForm: FC = ({workerId, show, setShow, notifyNewLastName}) => {

    return <ModalCommandForm
        title={workerId ? `edit staff` : "new staff"}
        action={workerId ? "update" : "save"}
        show={show}
        setShow={setShow}
        // helpTitle="managing staff"
        // helpContent={<StaffHelp/>}
    >
        {username ? <StaffDetail workerId={workerId}/>
            : <UserSubform notifyNewLastName={notifyNewLastName}/>}
    </ModalCommandForm>
};
*/

/**
 *
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
function getStaffDetailSubform(serviceRecipientId: number,
                                          formRef: (c: StaffDetail) => void,
                                          commandForm?: CommandForm | undefined) {
    return <AsyncServiceRecipientWithEntities.Resolved>{(srContext: ServiceRecipientWithEntitiesContext) =>
            <AsyncSessionData.Resolved>{sessionData =>
                <React.Fragment>
                <StaffDetail
                    ref={formRef}
                    serviceRecipientId={serviceRecipientId}
                    staff={srContext.worker}
                    readOnly={false}
                    commandForm={commandForm}
                    sessionData={sessionData}
                />
                <ClientAddressLocation
                    ref={null}
                    sessionData={sessionData}
                    serviceRecipientId={serviceRecipientId}
                    contactId={srContext.worker.contactId}
                    addressLocationId={srContext.worker.addressedLocationId}
                    legacyAddress={srContext.worker.address}
                    commandForm={commandForm}
                />
                </React.Fragment>
            }</AsyncSessionData.Resolved>
        }</AsyncServiceRecipientWithEntities.Resolved>;
}

interface Props extends ClassAttributes<StaffDetail> {
    readOnly: boolean;
    serviceRecipientId: number;
    staff: StaffDto;
    sessionData: SessionData;
}

type StaffField = keyof StaffDto;

interface State {
    staff: StaffDto;
}

export class StaffDetail extends CommandSubform<Props, State> implements CommandSource {
    //private clientAddressLocationEditor: ClientAddressLocationEditor = null;

    private requiredFields: StaffField[] = [];//this.props.sessionData.getSettingAsArray('com.ecco.forms:WORKER_DETAIL_REQUIRED_FIELDS') as StaffField[];
    private optionalFields: StaffField[] = this.props.sessionData.getSettingAsArray('com.ecco.forms:WORKER_OPTIONAL_FIELDS') as StaffField[];

    constructor(props) {
        super(props);

        this.state = {
            staff: this.props.staff,
        };
    }

    getErrors(): string[] {
        // firstName and lastName are always required
        return ['firstName', 'lastName'].concat(this.requiredFields).reduce( (errors, staffField) => {
            if (isEmpty(this.state.staff[staffField])) {
                errors.push(`${staffField} is required`);
            }
            return errors;
        }, []);
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.queueStaffDetailCommand(commandQueue);
    }

    protected queueStaffDetailCommand(commandQueue: CommandQueue) {
        const cmd = new StaffDetailCommand(this.props.serviceRecipientId);
        cmd.change(this.props.staff, this.state.staff);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }

        //this.clientAddressLocationEditor.
    }

    override render() {
        const staffStateSetter = (staff: StaffDto) => this.setState({staff});

        return (
            <div className="v-gap-15">
                <Grid container>
                    {individualDetailsCommonFields(staffStateSetter, this.state.staff, this.isRequired,
                        this.isOptional, this.props.readOnly, this.props.sessionData)}
                </Grid>
            </div>
        );
    }

    private isRequired = (fieldName: keyof StaffDto) => {
        return this.requiredFields.indexOf(fieldName) >= 0;
    };

    private isOptional = (fieldName: keyof StaffDto) => {
        return this.optionalFields.indexOf(fieldName) >= 0;
    };
}
