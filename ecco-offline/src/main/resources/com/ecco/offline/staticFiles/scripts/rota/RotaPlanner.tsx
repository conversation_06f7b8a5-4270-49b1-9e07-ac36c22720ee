import {showReactInModal} from "ecco-components-core";
import * as React from "react";
import {ClassAttributes, useState} from "react";
import { EccoDate, intervalFromDto } from "@eccosolutions/ecco-common";
import {Allocation as RotaAllocation, Rota, RotaProposal} from "ecco-rota";
import {SessionDataService} from "../feature-config/SessionDataService";
import {rotaRepository} from "./rota-services";
import {
    AsyncSessionData,
    withSessionData
} from "ecco-components";
import {SessionData} from "ecco-dto";
import {
    Checkbox,
    Chip,
    ExpansionPanel,
    ExpansionPanelDetails,
    ExpansionPanelSummary,
    FormControlLabel, List, ListItem, ListItemText,
    PropTypes,
} from "@eccosolutions/ecco-mui";
import {ExpandMore} from "@eccosolutions/ecco-mui-controls";
import {CommandQueue, AppointmentActionCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";

const TIME_LIMIT_SECS = 10;

const ListDefEntry = (props: {id: number, sessionData: SessionData, color?: Exclude<PropTypes.Color, 'inherit'> | undefined}) => {
    const entry = props.sessionData.getListDefinitionEntryById(props.id);
    return <Chip label={entry.getDisplayName()} color={props.color}/>
};

// See https://material-ui.com/components/expansion-panels/#additional-actions
const Allocation = (props: {allocation: RotaAllocation, sessionData: SessionData}) => {
    const [checked, setChecked] = useState(props.allocation.selected);
    const {allocation, sessionData} = props;
    const demand = allocation.getDto().demand;
    const apptInterval = intervalFromDto(demand.time);
    return <ExpansionPanel>
        <ExpansionPanelSummary
            expandIcon={<ExpandMore />}
            aria-label="Expand"
            aria-controls="additional-actions1-content"
            id="additional-actions1-header"
        >
            <FormControlLabel
                aria-label="Assign appointment"
                onClick={event => event.stopPropagation()}
                onFocus={event => event.stopPropagation()}
                control={<Checkbox
                    checked={checked}
                    onChange={(_, checked) => {
                        props.allocation.selected = checked;
                        return setChecked(checked);
                    }}
                />}
                label={`Assign ${allocation.resourceName()} to ${apptInterval.formatHoursMinsWithDay()} with ${allocation.demandName()} `}
            />
        </ExpansionPanelSummary>
        <ExpansionPanelDetails>
            <ListItem>
                <ListItemText>
                    requires: {!demand.requirements || demand.requirements.length == 0 ? "-" : demand.requirements.map(r => {
                        const supply = allocation.getDto().allocatedSupply;
                        const matches = supply.provides.map(p => p.id).indexOf(r.id) >= 0;
                        return <ListDefEntry key={r.id} id={r.id} sessionData={sessionData} color={matches ? "primary" : "default"}/>;
                    })}
                </ListItemText>
            </ListItem>
            <ListItem>
                <ListItemText>
                    staff shift: {props.allocation.getMatchingResourceAvailability()
                    ? props.allocation.getMatchingResourceAvailability().formatHoursMinsWithDay()
                    : "none"}
                </ListItemText>
            </ListItem>
        </ExpansionPanelDetails>
    </ExpansionPanel>;
};

interface FormProps extends ClassAttributes<ScheduleForm> {
    startDate: EccoDate
    /** endDate is inclusive, so = startDate for 1 day */
    endDate: EccoDate
    rota: Rota
}

interface FormState {
    loading: boolean
    timeRemaining: number
    proposal?: RotaProposal | undefined
}

class ScheduleForm extends React.Component<FormProps, FormState> {

    constructor(props: FormProps) {
        super(props);
        this.state = {
            loading: true,
            timeRemaining: TIME_LIMIT_SECS
        };
        setTimeout(this.onTick, 1000);
        rotaRepository.suggestAllocationsByDate(props.startDate, props.endDate,
            props.rota.getResourceFilter(), props.rota.getDemandFilter())
            .then(proposal => this.setState({proposal}))
    }

    private onTick = () => {
        this.setState( state => ({timeRemaining: state.timeRemaining - 1}),
            () => {
            if (this.state.timeRemaining > 0) setTimeout(this.onTick, 1000);
        })
    };

    override render() {
        const {proposal} = this.state;
        const dto = proposal && proposal.getDto();

        return !proposal ? <div>Calculating. Approx {this.state.timeRemaining} secs to go...</div>
            : withSessionData(sessionData => <div>
                <List dense={false}>
                    <ListItem>
                        <ListItemText
                            title={JSON.stringify(dto.score)}
                            primary={dto.score.hardScore == 0 ? "All hard requirements met" : `${-dto.score.hardScore} hard requirement clashes` }
                            secondary="These are scheduling, skills and qualifications"
                        />
                    </ListItem>
                    <ListItem>
                        <ListItemText
                            primary={dto.score.softScore == 0 ? "All soft requirements met" : `${-dto.score.softScore} soft requirement clashes` }
                            secondary="These are language and gender"
                        />
                    </ListItem>
                </List>
                {proposal.getAllocations().map(allocation =>
                        <Allocation key={allocation.getDto().demand.target.ref} allocation={allocation} sessionData={sessionData}/>) }
            </div>
            );
    }

    submit() {
        const queue = new CommandQueue();
        const allocations = this.state.proposal.getAllocations()
            .filter(a => a.selected);

        allocations.forEach( a => {
                const appt = a.getDto().demand.target;
                const actionCommand = new AppointmentActionCommand("allocate", Uuid.randomV4(), appt.ref, appt.serviceRecipientId,  this.props.rota.getResourceFilter(),
                    this.props.rota.getDemandFilter());
                actionCommand.allocateResourceId = a.getDto().allocatedSupply.id;
                // TODO: We can now add rescheduledTo param if the rota was packing
                //actionCommand.allocateRescheduledTo = rescheduleTo.formatIso8601();

                queue.addCommand(actionCommand);
                console.log(a.resourceName());
            });
        return queue.flushCommands();
    }


}

/**
 * endDate - same as start date for single day
 */
export function planSchedule(onComplete: () => void, startDate: EccoDate, endDate: EccoDate, rota: Rota) {
    const formRef: {ref: ScheduleForm | null} = {ref: null};
    const form = <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
        <ScheduleForm startDate={startDate} endDate={endDate} rota={rota} ref={r => {formRef.ref = r}} />
    </AsyncSessionData>;
    showReactInModal("auto plan", form, {
        onAction: () => formRef.ref.submit().then(() => onComplete()),
        action: "update"
    });
}
