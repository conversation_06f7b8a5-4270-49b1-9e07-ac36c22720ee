import $ = require("jquery");
import {Activity} from "ecco-rota";

class ActivityLabel {
    private $container: $.JQuery = $("<span>");

    constructor(private activity: Activity, private onClick: (activity: Activity) => void) {
    }

    public attach($replacedElement?: $.JQuery): $.JQuery {

        if ($replacedElement) {
            this.$container.attr("id", $replacedElement.attr("id"))
                .attr("class", $replacedElement.attr("class"));
            $replacedElement.replaceWith(this.$container);
        } else {
            this.$container.attr("id", "")
                .attr("class", "");
        }

        this.$container
            .attr("title", this.getActivityTitle())
            .text(this.activity.getServiceRecipientName())
            .click({action:"click"}, (event) => this.clickHandler(event));

        return this.$container;
    }

    private getActivityTitle() {
        const event = this.activity.getEvent();
        const eventTypeName = this.activity.getEventTypeName();
        return this.activity.getServiceRecipientName() + " : " + (event ? event : eventTypeName ? eventTypeName : "-");
    }

    /**
     * when a label is clicked, we want to show options for the associated component,
     * and while active, we want to show updates
     */
    private clickHandler(event: $.JQueryMouseEventObject) {
         event.preventDefault();
         this.onClick(this.activity);
    }
}

export = ActivityLabel;
