/* Initialise moment - this relies on module mapping in require-boot.js as per
 * http://requirejs.org/docs/jquery.html#noconflictmap
 */
define(['moment'], function (moment) {
    moment.locale('en', {
        longDateFormat : {
            LT: "h:mm A",
            L: "DD-MMM-YYYY", // normally DD/MM/YYYY
            LL: "Do MMMM YYYY",
            LLL: "Do MMMM YYYY LT",
            LLLL: "dddd, Do MMMM YYYY LT"
        },
        calendar : {
            lastDay : '[Yesterday at] LT',
            sameDay : '[Today at] LT',
            nextDay : '[Tomorrow at] LT',
            lastWeek : '[last] dddd [at] LT',
            nextWeek : 'dddd [at] LT',
            sameElse : 'L [at] LT'
        }
    });
    return moment;
});