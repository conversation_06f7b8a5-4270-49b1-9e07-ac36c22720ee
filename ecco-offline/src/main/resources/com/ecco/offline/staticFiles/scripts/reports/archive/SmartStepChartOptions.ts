import $ = require("jquery");

import NameValue = require("../../common/NameValue");
import SelectList = require("../../controls/SelectList");

/**
 * Holds the report criteria for the referal graph
 */
class SmartStepChartOptions {

    private byCriteria: SelectList;

    constructor(private onChange: (source: SmartStepChartOptions) => void) {
        this.createByCriteria();
    }

    public getByCriteria(): string {
        return this.byCriteria.selected(false);
    }

    public attachTo($inputElement: $.JQuery): void {
        $inputElement.append(this.byCriteria.element());
    }

    /* Creates the drop down lists for 'by x' crtieria */
    private createByCriteria(): void {
        this.byCriteria = new SelectList("displayCriteriaSmartStep");
        this.byCriteria.change((id) => this.onChange(this));
        var lst: NameValue<string>[] = [];
        lst.push(new NameValue<string>("byOutcome", "by outcome"));
        lst.push(new NameValue<string>("byService", "by service"));
        lst.push(new NameValue<string>("byProject", "by project"));
        lst.push(new NameValue<string>("byWorker", "by worker"));

        this.byCriteria.populateFromList(lst, (item) => {
                return {key: item.name(), value: item.value()};
            });
    }

}

export = SmartStepChartOptions;
