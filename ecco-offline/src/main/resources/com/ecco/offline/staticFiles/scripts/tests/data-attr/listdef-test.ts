import qunit = require("qunit");

import $ = require("jquery");
import ListDefSelectList = require("../../data-attr/ListDefSelectList");
import featuresMock = require("./featuresMocks");

qunit.asyncTest("can load data", () => {

    var selectorTest = "#test1";
    var myList = new ListDefSelectList($(selectorTest), featuresMock.featureRepository);

    qunit.expect(1);

    myList.load().then( () => {
        var itemsFound = $(selectorTest).find("option").length;
        qunit.strictEqual(itemsFound, 3, "loaded data matched");
        qunit.start();
    });

});

qunit.asyncTest("initial value (form backing object value)", () => {

    var selectorTest = "#test2";
    var myList = new ListDefSelectList($(selectorTest), featuresMock.featureRepository);

    qunit.expect(1);

    myList.load().then( () => {
        var initialItem = myList.getSelectedId();
        qunit.strictEqual(initialItem, 12, "initial data matched");
        qunit.start();
    });

});

qunit.load();
qunit.start();
