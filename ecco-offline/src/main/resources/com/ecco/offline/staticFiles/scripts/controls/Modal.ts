import $ = require("jquery");
import _ = require("lodash");

import BaseControl = require("./BaseControl");
import ModalMode = require("./ModalMode");
import View = require("./View");
import * as bowser from "bowser"
import {Footer} from "./DialogContent";
import {ResizeEvent} from "@eccosolutions/ecco-common";

/**
 * Implementation of our standard dialog approach.
 *
 * @deprecated Use MUIComponentUtils.showInModalDom()
 */
class Modal extends BaseControl {

    private $modal: $.JQuery;
    private $title: $.JQuery;
    private $body: $.JQuery;
    private $header: $.JQuery;
    private $headerContent: $.JQuery;
    private footer = new Footer();
    private $footer: $.JQuery;
    private $footerContent = this.footer.element();
    private $closeButton: $.JQuery;
    private $closeTimes: $.JQuery;
    private $content: $.JQuery;

    private modalSizeClass: string;
    private mode: ModalMode;
    private clickAwayToHide: boolean;

    private onCancel: () => void;

    /**
     * modalSizeClass = [modal-lg, modal-sm, modal-full | ""]
     */
    constructor(modalSizeClass = "modal-full",
            mode = ModalMode.limitToScreenHeight,
            clickAwayToHide = true) {
        super($("<div>"));
        this.$modal = this.element();
        this.modalSizeClass = modalSizeClass;
        this.mode = mode;
        this.clickAwayToHide = clickAwayToHide;

        this.generateElements();

        if (this.mode == ModalMode.fillScreenHeight || this.mode == ModalMode.limitToScreenHeight) {
            ResizeEvent.bus.addHandler( () => this.$modal.trigger("resize") );
        }
    }

    private generateElements() {
        this.$headerContent = $("<span>");

        this.$closeButton = $("<button>")
                .addClass("btn btn-link")
                .text("cancel");

        this.$closeTimes = $("<button>")
                .attr("type","button")
                .addClass("close")
                .attr("aria-hidden","true")
                .html("&times;");

        var titleId = _.uniqueId("modal-");
        this.$title = $("<div>")
            .addClass("modal-title pull-left")
            .attr("id", titleId);

        this.$body = $("<div>").addClass("modal-body");

        this.$header = $("<div>").addClass("modal-header clearfix")
            .append(this.$title)
            .append(this.$closeTimes)
            .append(this.$headerContent);

        this.$footer = $("<div>").addClass("modal-footer").css('margin-top', 0)
            .append($("<span>")
                .append(this.$footerContent)
                .append( this.$closeButton )
            );

        this.$content = $("<div>").addClass("modal-content")
            .append(this.$header)
            .append(this.$body)
            .append(this.$footer);

        this.$modal.addClass("modal controls-Modal")
            .addClass(this.modalSizeClass)
            .attr("role", "dialog")
            .attr("aria-labelledby", titleId)
            .attr("aria-hidden", "true")
            .append(this.$content);
    }

    /** Make cancel link a primary button with specified text */
    public closeButton(text: string) {
        this.$closeButton.removeClass("btn-link").addClass("btn-primary").text(text);
        return this;
    }

    /** Just change the text of the close/cancel link */
    public closeButtonText(text: string) {
        this.$closeButton.text(text);
        return this;
    }

    public setHeader( $header: $.JQuery ) {
        if (!$header) {
            return;
        }
        this.$headerContent.replaceWith( $header );
        this.$headerContent = $header;
    }

    public setFooter( $footer: $.JQuery | null ) {
        if ($footer) {
            this.$footerContent.replaceWith($footer);
            this.$footerContent = $footer;
        }
        else {
            this.$footer.remove();
        }
    }

    public title(title: string | $.JQuery) {
        if (typeof title == "string") {
            this.$title.text(<string>title);
        }
        else {
            this.$title.empty().append(title);
        }
        return this;
    }

    private popWithContent(html: string) {
        this.$body.html(html);
        this.dialogPop();
    }

    /**
     * This should be migrated to
     *
     * <pre>
     *   const cancelRef = {} as MutableRefObject<() => void>; // so can dismiss from elsewhere with: cancelRef.current()
     *   showInModalDom(title ,$container[0], undefined, undefined, undefined, cancelRef);
     * </pre>
     * @deprecated use showInModalDom
     */

    public popWithJQueryContent(element: $.JQuery | Element | Element[]) {
        this.$body.empty();
        this.$body.append(element);
        this.dialogPop();
    }


    public popView(view: View) {
        this.title(view.title());
        this.setFooter(view.getFooter());
        this.popWithJQueryContent(view.element());
        view.afterShow && view.afterShow();
    }

    public dialogPop() {

        this.$closeButton.click(() => this.cancel());
        this.$closeTimes.click(() => this.cancel());
        if (this.mode == ModalMode.fillScreenHeight) {
            (<any>this.$modal).modal({ show: true, height: () => this.computeContentHeightLimit() });
        } else if (this.mode == ModalMode.limitToScreenHeight) {
            (<any>this.$modal).modal(
                { show: true,
                  maxHeight: () => this.computeContentHeightLimit(),
                  backdrop: this.clickAwayToHide ? true : "static"
                });
        }
        else {
            (<any>this.$modal).modal('show');
        }
//        console.log("data(modal)", this.$modal.data('modal'));
    }

    public cancel() {
        if (this.onCancel) {
            this.onCancel();
        } else {
            this.dialogHide();
        }
    }

    public dialogHide() {
//        console.log("hide... data(modal)", this.$modal.data('modal'));
        (<any>this.$modal).modal("hide");
        ResizeEvent.bus.fire(new ResizeEvent());
    }

    private computeContentHeightLimit(): number {
        var windowHeight = $(window).height();
        var headerHeight = this.$header.outerHeight(true);
        var footerHeight = this.$footer.outerHeight(true);
        var contentMarginBorderAndPadding = this.$content.outerHeight(true) - this.$content.innerHeight();

        var totalTrimSize = headerHeight + footerHeight + contentMarginBorderAndPadding;
        if (!document.addEventListener) { // not in IE8: se http://stackoverflow.com/a/14835682/1998186
            totalTrimSize += 30; // arbitrary HACK on IE8
        }
        else if (bowser.ios) {
            totalTrimSize += 20; // iOS issues: see http://stackoverflow.com/questions/19012135/ios-7-ipad-safari-landscape-innerheight-outerheight-layout-issue
        }
        return windowHeight - totalTrimSize - 2; // subtract 2 as we want to see borders
    }
}

export = Modal;
