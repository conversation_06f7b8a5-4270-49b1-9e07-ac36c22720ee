import $ = require("jquery");

class ElementRow {

    private $tdElementsByCol: $.JQuery[] = [];

    public push(element: $.JQuery) {
        this.$tdElementsByCol.push(element);
    }

    public elementAt(col: number) {
        return this.$tdElementsByCol[col];
    }

    public withEachElement(callback: (element: $.JQuery, col: number) => void) {
        for (let col = 0; col < this.$tdElementsByCol.length; col++) {
            callback(this.$tdElementsByCol[col], col);
        }
    }

    public length(): number {
        return this.$tdElementsByCol.length;
    }
}

export = ElementRow
