import Modal = require("../../controls/Modal");
import ReportCriteriaDtoImpl = require("../ReportCriteriaDtoImpl");
import ReportCriteriaComponent = require("./ReportCriteriaComponent");
import {EccoDate} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import * as spDto from "ecco-dto/service-config-dto";
import {EntityRestrictionsAjaxRepository} from "../../entity-restrictions/EntityRestrictionsAjaxRepository";

var entityRestrictionsRepository = new EntityRestrictionsAjaxRepository(apiClient);

/**
 * The class which controls the form of the criteria control
 */
class ReportCriteriaControl {

    private reportCriteria: ReportCriteriaDtoImpl = new ReportCriteriaDtoImpl();
    private servicesProjects: spDto.ServiceDto[];
    private onFilterChange: () => void;
    private reportCriteriaModal: Modal;

    constructor() {
        this.preload();
        this.setDefaultCriteria();
    }

    public setupOnFilterChange(onFilterChange: () => void) {
        this.onFilterChange = onFilterChange;
    }

    private preload(): void {
        var onSuccess = (services: spDto.ServiceDto[]) => {
            this.servicesProjects = services;
        };
        var servicesProjectsQ = entityRestrictionsRepository.findRestrictedServicesProjects();
        servicesProjectsQ.then(onSuccess);
    }

    private setDefaultCriteria() {
        this.reportCriteria.from = EccoDate.todayLocalTime().subtractMonths(3).formatIso8601();
        this.reportCriteria.to = EccoDate.todayLocalTime().formatIso8601();
    }

    public editLoadCriteria() {
        this.reportCriteriaModal = new Modal("modal-full");
        this.reportCriteriaModal.title("referrals load");
        var criteriaForm = this.generateForm();
        this.reportCriteriaModal.setFooter(criteriaForm.getFooter());
        this.reportCriteriaModal.popWithJQueryContent(criteriaForm.element());
    }

    private generateForm(): ReportCriteriaComponent {
        return new ReportCriteriaComponent(this.reportCriteria, this.servicesProjects,
            (criteria: ReportCriteriaDtoImpl) => {
                this.reportCriteria = criteria;
                this.onFilterChange();
                this.reportCriteriaModal.dialogHide();
            });
    }

    public dto(): ReportCriteriaDtoImpl {
        return this.reportCriteria;
    }

}

export = ReportCriteriaControl;
