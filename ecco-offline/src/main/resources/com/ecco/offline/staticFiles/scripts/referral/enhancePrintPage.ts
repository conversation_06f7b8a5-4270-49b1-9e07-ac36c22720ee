import $ = require("jquery");


class Enhancer {

    /** <PERSON><PERSON> which kicks off attach() once only */
    private static instance = new Enhancer();

    constructor() {
        $( () => {
            this.attach();
        });
    }

    private attach() {
        //EvidenceDelegatingForm.enhanceHistoryButtons();

        let $print = $(".printable-link");
        $print.each((index, element) => $(element).click(() => {
            this.toggleTabsForPrinting()
            window.print();
        }));

        // allow every print page to see 'print' because then people will click it, which we need for evidence pages
        $print.each((index, element) => $(element).show());

        // we still need 'print' unless we work on finding when the page has finished loading
        // const url = `${window.location.pathname}`;
        // if (url.indexOf("printable") > -1) {
        //     $print.first().click()
        // }
    }

    // see https://stackoverflow.com/questions/33261642/print-all-tabs-on-a-webpage
    // The css fix conflicts with '.tab-content > .tab-pane' from bootstrap (visibility hidden / display none)
    // and the css requires limiting to the pages we want to show all the tabs for. So in fact the javascript
    // approach is a simpler approach and allows us to show heading.
    private toggleTabsForPrinting() {
        let tabsVisible = $('.nav-tabs:visible').length > 0;
        if (tabsVisible) {
            $('.nav-tabs').hide();
            let $headings = $('.nav-tabs li a');
            $('.tab-content .tab-pane').each((index, element) => {
                let $printHeader = $("<h3>").addClass("on-print").text($($headings.get(index)).text());
                if ($(element).hasClass('active')) {
                    $printHeader.addClass("active");
                }
                $(element).prepend($printHeader);
                $(element).addClass('active');
            });
            //$(".printable-link").each((index, element) => $(element).text("reset print"));
            //window.print();
        }
    }

}
