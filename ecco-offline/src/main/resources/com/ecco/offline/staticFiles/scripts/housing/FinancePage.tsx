import * as React from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {FinanceReceiptList} from "ecco-finance";
import {SelectionCriteriaDto} from "ecco-dto";
import {useChart, useCurrentServiceRecipientWithEntities, useServicesContext} from "ecco-components";
import {DomElementContainer} from "ecco-components-core";
import {FC, useEffect, useMemo, useState} from "react";
import {
    ChartDefinition,
    ReportDataSourceFactory,
    RowContextImpl,
    SequenceAnalysis
} from "ecco-reports";
import {ReportStagesControlWrapper} from "../reports/charts/ReportStagesControl";
import {EccoDate} from "@eccosolutions/ecco-common";

// mimics ChartControl fetchViewData
export function useChartData(
        chartDefinitionUuid: string,
        override?: Partial<SelectionCriteriaDto> | undefined
) {
    const {sessionData} = useServicesContext();
    const {chartDefinitionDto} = useChart(chartDefinitionUuid);
    const [chartDefinition, setChartDefinition] = useState<ChartDefinition>(null);
    const [chartData, setChartData] = useState<SequenceAnalysis<any>>(null);

    // be good to use promiseToSuspendedResult so we can use 'usePromise' without depending on chartDefinitionDto
    useEffect(() => {
        if (chartDefinitionDto) {
            chartDefinitionDto.definition.selectionCriteria = {
                ...chartDefinitionDto.definition.selectionCriteria,
                ...override
            };
            const chartDef = new ChartDefinition(chartDefinitionDto, sessionData);
            setChartDefinition(chartDef);
            ReportDataSourceFactory.getDataSource(chartDef)
                    .getData()
                    .then(data => {
                        setChartData(data);
                    });
        }
    }, [chartDefinitionDto]);
    return {chartData: chartData, chartDefinition: chartDefinition};
}

// const ServiceChargeSrIdChart = (props: {srId: number}) =>
//         <Chart override={{serviceRecipientFilter: `serviceRecipient:${props.srId}`}} autoRun={true}  chartUuid={"87093513-817b-469c-7126-0dfa66c6939b"}/>;

// ChartControl (whole page, criteria and breakdown) creates new ReportStagesControl renders each stage
    // let data: Table<any> = this.getTableDataFor(reportStage, sequence);
        // getTableDataFor calls:
            // const table = new Table(notNullData, representation, initialColumn);
    // const alwaysMui = this.sessionData.isEnabled("reports.breakdown.mui");
    // let tableControl = this.getTableMuiForStage(this.reportDef.getName(), reportStage);
        // simple creates TableMuiReportStageControl which manages 'select all', title etc and creates TableMuiControl
        // TableMuiControl wraps downloadFilename and jQuery mount, creating DataTableWrapper
    // tableControl.setData(data, this.getKeyBreadcrumb(index));
// We need getTableDataFor for Table<any> to then go to ReportMuiTableWrapper (was DataTableWrapper)
const ChartSrIdWrapper: FC<{chartUuid: string, srId: number | undefined, bldgId: number | undefined}> = ({chartUuid, srId, bldgId}) => { // Base must start and end in / (i.e. /r/welcome/ or / )
    const {sessionData} = useServicesContext();
    const criteriaOverride: Partial<SelectionCriteriaDto> = {
        selectorType: "",
        absoluteFromDate: "1970-01-01",
        absoluteToDate: EccoDate.todayLocalTime().formatIso8601(),
    };
    if (bldgId) {
        criteriaOverride.buildingIds = [bldgId];
    } else {
        criteriaOverride.serviceRecipientFilter = `serviceRecipient:${srId}`
    }

    const {chartData, chartDefinition} = useChartData(chartUuid, criteriaOverride);
    const [afterPaint, setAfterPaint] = useState(false);
    const [dataSecond, setDataSecond] = useState<SequenceAnalysis<any>>(null);

    // could use lazyControlWrapper/useControl if we implemented load
    const report = useMemo(() => new ReportStagesControlWrapper(), []);
    // ReactDom.render(
    // OR DomElementContainer
    useEffect(() => {
        if (dataSecond != null) {
            report.loadWith(new RowContextImpl(sessionData), chartDefinition, chartData, false);
        }
        if (chartData && sessionData && afterPaint && dataSecond == null) {
            const d: SequenceAnalysis<any> = null;
            report.loadWith(new RowContextImpl(sessionData), chartDefinition, d);
            setTimeout(() => {
                setDataSecond(chartData);
            }, 100);
            //
            //report.redraw();
        }
    }, [chartData, sessionData, afterPaint, dataSecond])

    //if (!chartData || !sessionData) return null

    // NB we could load directly into ReportMuiTableWrapper
    // NB or could debug directly into an analyser
    //const data = chartData as ReferralAggregateAnalysis
    //let managementData: Sequence<HactManagementData> = hactManagementAnalyser(chartDefinition, Lazy(data.getData())).getData();
    //console.log(managementData.toArray());

    return <DomElementContainer key={`key-chart}`} content={report.domElement()} afterNextPaint={() => setAfterPaint(true)}/>
}
/*
export const ReportWrapper: FC<{srId: number}> = props => {
    const SrChartDefUuid = "05800000-0000-babe-babe-dadafee1600d";

    const SrChart = (props: {override?: Partial<SelectionCriteriaDto>}) =>
            <Chart override={props.override} autoRun={true} chartUuid={SrChartDefUuid}/>;
    const filter = `serviceRecipient:${props.srId}`;
    return handleLazy(<SrChart override={{serviceRecipientFilter: filter}} />);
};
*/

export const FinancePage = (props: {srId: number}) => {

    //const {serviceRecipient, loading} = useServiceRecipient(props.srId);
    //if (loading || !serviceRecipient) return null;
    const {resolved: context} = useCurrentServiceRecipientWithEntities();

    // one of bldgId or srId (prioritise bldgId since we always have an srId)
    const bldgId = context.serviceRecipient.prefix === "b" ? context.serviceRecipient.parentId : undefined;
    const srId = !bldgId ? props.srId : undefined;

    return <Grid container direction="row" justify="center" alignItems="center">
        <Grid item xs={12} lg={10} xl={8}>
            <ChartSrIdWrapper chartUuid={"07000000-0000-babe-babe-dadafee1600d"} srId={srId} bldgId={bldgId}/>
        </Grid>
        {/*<Grid item xs={12} md={8}>
            <FinanceChargesList serviceRecipientId={props.srId}/>
        </Grid>*/}
        <Grid item xs={12} lg={10} xl={8}>
            <FinanceReceiptList serviceRecipientId={props.srId}/>
        </Grid>
    </Grid>;
}
