import $ = require("jquery");
import Lazy = require("lazy");

import analysisTypes = require("ecco-reports");
import BaseControl = require("../../controls/BaseControl");
import charts = require("../../controls/charts");
import domain = require("ecco-reports");
import StatusPanel = require("../../controls/StatusPanel");
import CommandHistoryListControl = require("../../service-recipients/CommandHistoryListControl");
import Sequence = LazyJS.Sequence;
import Analysis = analysisTypes.Analysis;
import Group = analysisTypes.Group;
import GroupedAnalysis = analysisTypes.GroupedAnalysis;
import SequenceAnalysis = analysisTypes.SequenceAnalysis;
import SingleRecordAnalysis = analysisTypes.SingleRecordAnalysis;
import AllKeysSelector = domain.AllKeySelector;
import ArrayKeySelector = domain.ArrayKeySelector;
import AnalyserReportStage = domain.AnalyserReportStage;
import BadgeReportStage = domain.BadgeReportStage;
import ChartDefinition = domain.ChartDefinition;
import KeySelector = domain.KeySelector;
import ReportStage = domain.ReportStage;
import ChartReportStage = domain.ChartReportStage;
import TableReportStage = domain.TableReportStage;
import {onParentTabActivated} from "../../common/tabEvents";
import * as types from "@eccosolutions/ecco-common";
import {EventHandler} from "@eccosolutions/ecco-common";
import {
    AuditReportStage,
    BadgeClickEvent,
    CalendarEventReportStage,
    MatrixReportStage,
    TableCellKeySelector,
    Table,
    TableRepresentationBase,
    AllKeySelector
} from "ecco-reports";
import {BaseServiceRecipientCommandDto} from "ecco-dto/evidence-dto";
import {SessionData} from "ecco-dto";
import {SeriesType, StageType} from "ecco-reports";
import {MatrixAnalysis, MatrixRow} from "ecco-reports";
import {
    ClickEvent,
    getFieldRepresentation, numberColumn,
    RowContext,
    textColumn
} from "ecco-reports";
import {renderEvents} from "../../calendarlist/CalendarListFrame";
import {EventResourceDto} from "ecco-dto/calendar-dto";
import {badgeMuiAsElement} from "./BadgeMui";
import {TableControl, TableMuiControl} from "../../controls/tables";


class AuditReportStageControl extends BaseControl implements AuditReportStageControlI {
    private list: CommandHistoryListControl;

    constructor(private stage: AuditReportStage) {
        super($("<div>"));
        // assume detailPermission since we can see reports
        this.list = CommandHistoryListControl.createWithMultipleEntities(true);
        this.element().append(this.list.element());
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(sessionData: SessionData, data: Sequence<BaseServiceRecipientCommandDto>) { //}, selectedKey: string) {
        this.list.setMultipleEntitiesData(sessionData, data && data.toArray());
    }
}

class CalendarEventReportStageControl extends BaseControl implements CalendarEventReportStageControlI {

    constructor(private stage: CalendarEventReportStage) {
        super($("<div>"));
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(sessionData: SessionData, data: Sequence<EventResourceDto>) { //}, selectedKey: string) {
        renderEvents(this.domElement(), data && data.toArray());
    }
}

class BadgeReportStageControl extends BaseControl implements BadgeReportStageControlI {
    private badge: StatusPanel;
    private clickHandler: EventHandler<ClickEvent<any>>;

    constructor(private ctx: RowContext, private stage: BadgeReportStage) {
        super($("<div>"));
        this.badge = new StatusPanel("panel-ecco")
            .statusText(stage.getDescription())
            .faIcon(stage.getIconCssClass());
        this.element()
            .append(this.badge.element());
    }

    public addClickEventHandler(handler: EventHandler<ClickEvent<any>>): void {
        this.clickHandler = handler;
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
      * 'accommodation > project 5 > sysadmin' */
    setData<T>(analysis: SingleRecordAnalysis<T>, selectedKey: string) {
        this.badge.statusTextComment(selectedKey);
        const data = analysis && analysis.getData();
        if (!data) {
            this.badge.statusValue("-");
        }
        else {
            const representation = analysis.getRecordRepresentationSingle(this.stage.getRecordRepresentationName());
            const indicatorRepresentation = getFieldRepresentation(representation, this.stage.getMainIndicatorValue());
            if (indicatorRepresentation) {
                const valObj = indicatorRepresentation.represent(this.ctx, data);
                if (typeof valObj != "object") {
                    this.badge.statusValue(valObj);
                    if (this.clickHandler != null) {
                        this.badge.element().click((event: $.JQueryEventObject) => {
                            this.clickHandler();
                        });
                    }
                } else {
                    throw new Error("badge statusValue undefined for object 'represent'ations")
                }
            }
            else {
                this.badge.statusText("Error: Missing indicator value for this stage");
            }
        }
    }
}

// TODO move/merge this with BadgeMui.KPIChart (as per StatusPanel)
class BadgeArea extends BaseControl {
    static count : number = 0;
    private title: string;
    private value: string;
    private progressUp: string;
    private progressDown: string;

    constructor() {
        super();
        BadgeArea.count++;
    }

    public setTitle(title: string) {
        this.title = title;
        this.update();
    }

    public setValue(val: string) {
        this.value = val;
        this.update();
    }

    public setUp(val: string) {
        this.progressUp = val;
        this.update();
    }

    public setDown(val: string) {
        this.progressDown = val;
        this.update();
    }

    private update() {
        this.element().empty().append(
            badgeMuiAsElement(`badgeMuiId-${BadgeArea.count}`, this.title, this.value, this.progressUp, this.progressDown)
        );
    }

}

class BadgeMuiReportStageControl extends BaseControl implements BadgeMuiReportStageControlI {
    private badge = new BadgeArea();

    constructor(private ctx: RowContext, private stage: BadgeReportStage) {
        super($("<div>"));
        this.element()
                .append(this.badge.element());
    }

    setData<T>(analysis: SingleRecordAnalysis<T>, selectedKey: string) {

        // selectedKey can reflect what choices have been made in getting to this stage
        //this.badge.statusTextComment(selectedKey);

        const data = analysis && analysis.getData();

        if (!data) {
            console.warn("no data for badge");
            return;
        }

        this.badge.setTitle(selectedKey);
        const representation = analysis.getRecordRepresentationSingle(this.stage.getRecordRepresentationName());

        const mainIndicatorRepresentation = getFieldRepresentation(representation, this.stage.getMainIndicatorValue());
        if (mainIndicatorRepresentation) {
            const valObj = mainIndicatorRepresentation.represent(this.ctx, data);
            if (typeof valObj != "object") {
                this.badge.setValue(valObj);
            } else {
                throw new Error("badge statusValue undefined for object 'represent'ations")
            }
        } else {
            console.warn("Error: Missing indicator value for this stage");
        }

        const upIndicatorRepresentation = getFieldRepresentation(representation, this.stage.getUpIndicatorValue());
        if (upIndicatorRepresentation) {
            const valObj = upIndicatorRepresentation.represent(this.ctx, data);
            if (typeof valObj != "object") {
                this.badge.setUp(valObj);
            } else {
                throw new Error("badge statusValue undefined for object 'represent'ations")
            }
        }

        const downIndicatorRepresentation = getFieldRepresentation(representation, this.stage.getDownIndicatorValue());
        if (downIndicatorRepresentation) {
            const valObj = downIndicatorRepresentation.represent(this.ctx, data);
            if (typeof valObj != "object") {
                this.badge.setDown(valObj);
            } else {
                throw new Error("badge statusValue undefined for object 'represent'ations")
            }
        }

    }

}

class ChartReportStageControl extends BaseControl implements ChartReportStageControlI {
    private chart: charts.ChartControl;
    private $selected = $("<div>").addClass("center");
    private $selectMode = $("<span>");
    private selectMany = false;
    private canClickThrough;
    private chartData: charts.ChartData;

    constructor(private ctx: RowContext, private reportDefName: string, private reportStage: ReportStage) {
        super($("<div>"));
        this.chart = new charts.ChartControl();
        const $content = $("<div>").addClass("ecco-rounded")
            .append(this.$selected);

        const $links = $("<span>");

        // download link
        // there are better ways - one good idea was to post the jqplotToImageStr({}) to the server to get the image and filename back
        const $downloadBtn: $.JQuery = $("<span>").addClass("btn btn-link btn-sm pull-right").text("download");
        $downloadBtn.click( () => {
            const downloadFilename = reportDefName.concat(" [stage ").concat(reportStage.getStageIndex().toString()).concat("]").concat('.csv');
            this.chart.saveImage(downloadFilename);
        });
        $links.append($downloadBtn);

        this.canClickThrough = reportStage.canClickThrough();

        // select all link
        if (this.canClickThrough && reportStage.showSelectAll()) {
            const $selectAll = $("<span>")
                .addClass("btn btn-link btn-sm pull-right")
                .text("select all")
                .click(() => this.selectAll());
            $links.append($selectAll);
        }

        this.$selectMode
            .addClass("btn btn-link btn-sm pull-right")
            .text("select: single")
            .click( () => this.selectManyToggle() );
        if (this.canClickThrough) {
            $links.append(this.$selectMode);
        }

        $content.append($("<div>")
            .addClass("clearfix")
            .append($links));

        $content.append(this.chart.element());
        this.element().append($content);
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
      * 'accommodation > project 5 > sysadmin' */
    setData(data: charts.ChartData, selectedKey: string) {
        this.chartData = data;
        this.chart.setData(data, {canClickThrough: this.reportStage.canClickThrough()});
        this.updateChartSelectModeState(); // 'data' is a new object, so tell it what we know
        this.$selected.text(selectedKey + " - " + this.reportStage.getDescription());
    }

    selectAll() {
        // HACK we assume '0' series
        this.chartData.getDataSeries()[0].clickAll();
    }

    selectManyToggle() {
        this.selectMany = !this.selectMany;
        this.updateChartSelectModeState();
    }

    private updateChartSelectModeState() {
        if (this.canClickThrough) {
            this.$selectMode.text("select: ".concat(this.selectMany ? "many" : "single"));
            // HACK we assume '0' series
            this.chartData.getDataSeries()[0].updateClickManyState(this.selectMany);
        }
    }
}

class TableReportStageControl extends BaseControl implements TableMuiReportStageControlI {
    private table: TableControl;
    private $selected = $("<div>").addClass("center");

    constructor(private ctx: RowContext, private reportDefName: string, private reportStage: ReportStage) {
        super($("<div>"));
        const downloadFilename = reportDefName.concat(" - stage ").concat(reportStage.getStageIndex().toString());
        this.table = new TableControl(ctx, downloadFilename);

        const $content = $("<div>").addClass("ecco-rounded")
            .append(this.$selected);

        const $links = $("<span>");

        // select all link
        if (reportStage.showSelectAll()) {
            const $selectAll = $("<span>")
                .addClass("btn btn-link btn-sm pull-right")
                .text("select all")
                .click(() => this.selectAll());
            $links.append($selectAll);
        }

        $content.append($("<div>")
            .addClass("clearfix")
            .append($links));

        $content.append(this.table.element().addClass("table-control"));
        this.element().append($content);
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
      * 'accommodation > project 5 > sysadmin' */
    setData(data: Table<any>, selectedKey: string) {
        this.table.setData(data);
        this.$selected.empty()
            .append($("<span>").text(selectedKey))
            .append($("<br>"))
            .append($("<span>").text(this.reportStage.getDescription()));
    }

    selectAll() {
        this.table.emitClickAll();
    }
}


class TableMuiReportStageControl extends BaseControl implements TableMuiReportStageControlI {
    private table: TableMuiControl;
    private $selected = $("<div>").addClass("center");

    constructor(private ctx: RowContext, private reportDefName: string, private reportStage: ReportStage) {
        super();
        const downloadFilename = reportDefName.concat(" - stage ").concat(reportStage.getStageIndex().toString()).concat('.csv');

        this.table = new TableMuiControl(ctx, downloadFilename);

        const $content = $("<div>").addClass("ecco-rounded")
            .append(this.$selected);

        const $links = $("<span>");

        // select all link
        if (reportStage.showSelectAll()) {
            const $selectAll = $("<span>")
                .addClass("btn btn-link btn-sm pull-right")
                .text("select all")
                .click(() => this.selectAll());
            $links.append($selectAll);
        }

        $content.append($("<div>")
            .addClass("clearfix")
            .append($links));

        $content.append(this.table.element().addClass("table-control"));
        this.element().append($content);
    }

    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(data: Table<any>, selectedKey: string) {
        this.table.setData(data);
        this.$selected.empty()
            .append($("<span>").text(selectedKey))
            .append($("<br>"))
            .append($("<span>").text(this.reportStage.getDescription()));
    }

    selectAll() {
        this.table.emitClickAll();
    }
}

class MatrixReportStageControl extends TableReportStageControl {
}

interface BaseReportStageControlI {
    domElement(): HTMLElement;
}
interface BadgeReportStageControlI extends BaseReportStageControlI {
    addClickEventHandler(handler: EventHandler<ClickEvent<any>>): void;
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData<T>(analysis: SingleRecordAnalysis<T>, selectedKey: string);
}

interface AuditReportStageControlI extends BaseReportStageControlI {
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(sessionData: SessionData, data: Sequence<BaseServiceRecipientCommandDto>);
}
interface CalendarEventReportStageControlI extends BaseReportStageControlI {
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(sessionData: SessionData, data: Sequence<EventResourceDto>);
}

interface BadgeMuiReportStageControlI extends BaseReportStageControlI {
    setData<T>(analysis: SingleRecordAnalysis<T>, selectedKey: string);
}

interface ChartReportStageControlI extends BaseReportStageControlI {
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(data: charts.ChartData, selectedKey: string);

    selectAll();

    selectManyToggle();

    //updateChartSelectModeState();
}
interface TableReportStageControlI extends BaseReportStageControlI {
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(data: Table<any>, selectedKey: string);
    selectAll();
}
interface TableMuiReportStageControlI extends BaseReportStageControlI {
    /** selectedKey can reflect what choices have been made in getting to this stage (it could be something like
     * 'accommodation > project 5 > sysadmin' */
    setData(data: Table<any>, selectedKey: string);
    selectAll();
}
interface MatrixReportStageControlI extends TableReportStageControlI {
}


export class ReportStagesControlWrapper<INITIAL> extends BaseControl {

    private ctl: ReportStagesControlJQuery<INITIAL>;

    constructor(private pieChartClasses?, private tableClasses?,
                private barChartClasses?, private badgeClasses?) {
        super($("<div>").addClass("row ecco-rounded-row"));
        this.ctl = new ReportStagesControlJQuery(this.element(), pieChartClasses, tableClasses, barChartClasses, badgeClasses);
    }

    public loadWith(ctx: RowContext, reportDef: ChartDefinition, initialAnalysis: Analysis<INITIAL | Sequence<INITIAL>>, empty = false) {
        this.ctl.loadWith(ctx, reportDef, initialAnalysis);

        // seemingly, ReportStagesControlWrapper was a way to separate out jQuery
        // and potentially solve the mui redraw problem (missing table)
        // but this was solved in the branch using 'empty' (now in this commit, from "DEV-2713 Fix report mui 'charges' table")
        if (empty) {
            this.element().empty();
        } else {
            //this.redraw();
            // redraw if tab changes (could also do for resize)
            onParentTabActivated(this.element(), (event) => {
                this.ctl.redraw();
            })
        }
    }

}

/**
 * Responsible for choosing correct representation, showing it and adding subsequent ones.
 *
 * It should be possible to provide a reportDef that is just the selection criteria, and then
 * show what possible analysis and representation is available from there.
 */
abstract class ReportStagesControlBase<INITIAL> {

    private reportDef: ChartDefinition;
    private sessionData: SessionData;
    private ctx: RowContext;

    private badgesByStage: types.SparseArray<BadgeReportStageControlI>;
    private badgesMuiByStage: types.SparseArray<BadgeMuiReportStageControlI>;
    private chartsByStage: types.SparseArray<ChartReportStageControlI>;
    private auditsByStage: types.SparseArray<AuditReportStageControlI>;
    private calendarEventsByStage: types.SparseArray<CalendarEventReportStageControlI>;
    private tablesByStage: types.SparseArray<TableReportStageControlI>;
    private tablesMuiByStage: types.SparseArray<TableMuiReportStageControlI> = {};
    private matrixByStage: types.SparseArray<MatrixReportStageControlI>;
    private analysisByStage: types.SparseArray<Analysis<any>>;

    /** Storage for what is clicked on in each visual stage */
    private keySelections = new Array<KeySelector>();
    /** Analysis so we can do things like "skip" a stage - actually would be "all" from groupByAnalyser */
    private initialAnalysis: Analysis<INITIAL|Sequence<INITIAL>>;

    /** reset this control with the supplied information */
    public loadWith(ctx: RowContext, reportDef: ChartDefinition, initialAnalysis: Analysis<INITIAL|Sequence<INITIAL>>) {
        this.badgesByStage = {};
        this.badgesMuiByStage = {};
        this.chartsByStage = {};
        this.tablesByStage = {};
        //this.tablesMuiByStage = {}; // commented out on 25.04 branch - see "DEV-2713 Fix report mui 'charges' table"
        this.matrixByStage = {};
        this.auditsByStage = {};
        this.calendarEventsByStage = {};
        this.analysisByStage = {};
        this.reportDef = reportDef;
        this.sessionData = reportDef.getSessionData();
        this.ctx = ctx;
        this.initialAnalysis = initialAnalysis;
    }

    public redraw() {
        this.analysisByStage[0] = this.initialAnalysis!;
        this.showDataForStage(this.getStage(0), new AllKeySelector(), this.initialAnalysis!);
    }

    protected abstract createAuditForStage(reportStage: AuditReportStage): AuditReportStageControlI;
    protected abstract createCalendarEventForStage(reportStage: CalendarEventReportStage): CalendarEventReportStageControlI;
    protected abstract createBadgeForStage(rowCtx: RowContext, reportStage: BadgeReportStage): BadgeReportStageControlI;
    protected abstract createBadgeMuiForStage(rowCtx: RowContext, reportStage: BadgeReportStage): BadgeMuiReportStageControlI;
    protected abstract createChartForStage(rowCtx: RowContext, reportDefName: string, reportStage: ChartReportStage): ChartReportStageControlI;
    protected abstract createTableForStage(rowCtx: RowContext, reportDefName: string, reportStage: TableReportStage): TableReportStageControlI;
    protected abstract createTableMuiForStage(rowCtx: RowContext, reportDefName: string, reportStage: TableReportStage): TableMuiReportStageControlI;
    protected abstract createMatrixForStage(rowCtx: RowContext, reportDefName: string, reportStage: MatrixReportStage): MatrixReportStageControlI;
    protected abstract append(element: any);

    private getKeyBreadcrumb(toIndex: number) {
        let result = "";
        for (let i = 0; i <= toIndex; i++) {
            if (this.keySelections[i]) {
                result += this.keySelections[i] + " > ";
            }
        }
        if (result.length > 0) { // trim last " > " off
            result = result.substring(0, result.length - 3);
        }
        return result;
    }

    private getStage(index: number) {
        return this.reportDef.getReportStage(index);
    }

    /** keys = array of keys that were selected to produce stageData - if empty array, then it means 'all */
    public showDataForStage<T>(reportStage: ReportStage, keys: KeySelector, stageData: Analysis<T|Sequence<T>>) {
        if (!reportStage) { return; }
        // TODO: if reportStage is null, then show choices between analysers and representations

        const index = reportStage.getStageIndex();
        this.analysisByStage[index] = stageData;

        if (stageData) {
            console.debug("Stage %o: incoming analyser %o", index, (<any>stageData.constructor).name || stageData.constructor); // .name is ES6 (works in Chrome)
            this.keySelections[index] = keys;
        }

        if (reportStage instanceof TableReportStage) {
            console.debug("Stage %o: visual (table)", index);

            let sequence: SequenceAnalysis<any> = <SequenceAnalysis<any>>stageData;
            let data = this.getTableDataFor(reportStage, sequence);
            const alwaysMui = this.sessionData.isEnabled("reports.breakdown.mui");
            if (alwaysMui || reportStage.getTableRenderType() == 'mui') {
                let tableControl = this.getTableMuiForStage(this.reportDef.getName(), reportStage);
                tableControl.setData(data, this.getKeyBreadcrumb(index));
            } else {
                let tableControl = this.getTableForStage(this.reportDef.getName(), reportStage);
                tableControl.setData(data, this.getKeyBreadcrumb(index));
            }
            // TODO: allow rendering to chain from table to table, as per chart
        }
        if (reportStage instanceof MatrixReportStage) {
            console.debug("Stage %o: visual (matrix)", index);
            let matrixControl = this.getMatrixForStage(this.reportDef.getName(), reportStage);
            let sequence: SequenceAnalysis<any> = <SequenceAnalysis<any>>stageData;
            let data = this.getMatrixDataFor(reportStage, sequence);
            matrixControl.setData(data, this.getKeyBreadcrumb(index));
            // TODO: allow rendering to chain from table to table, as per chart
        }
        else if (reportStage instanceof ChartReportStage) {
            console.debug("Stage %o: visual (chart)", index);
            let control = this.getChartForStage(this.reportDef.getName(), reportStage);
            let sequence: SequenceAnalysis<any> = <SequenceAnalysis<any>>stageData;
            let data = sequence && this.getChartDataFor(reportStage, sequence);
            control.setData(data, this.getKeyBreadcrumb(index));
            // see if we have a selection on this stage to render the next
            // eg through what we already clicked on, or 'selectionKey' in the definition
            let nextKeys = this.keySelections[index + 1] || reportStage.getSelectionKey();
            // if we have a selection, render the next stage
            if (nextKeys instanceof ArrayKeySelector) {
                this.showDataForStage(this.getStage(index + 1), nextKeys, this.getAnalysisFor(reportStage, sequence, nextKeys));
            }
        }
        else if (reportStage instanceof AnalyserReportStage) {
            console.debug("Stage %o: non-visual (analyser), %s", index, reportStage.getAnalyserType());
            let sequence: SequenceAnalysis<any> = stageData && stageData.analyseWithForSequenceAnalysis(reportStage);
            let nextKeys = reportStage.getSelectionKey();

            if (nextKeys instanceof TableCellKeySelector) {

            }
            else if (nextKeys instanceof ArrayKeySelector) {
                this.showDataForStage(this.getStage(index + 1), nextKeys, this.getAnalysisFor(reportStage, sequence, nextKeys));
            }
            else {
                this.showDataForStage(this.getStage(index + 1), nextKeys, sequence);
            }
        }
        else if (reportStage instanceof BadgeReportStage) {
            console.debug("Stage %o: visual (badge)", index);

            //let data = this.getBadgeDataFor(reportStage, analysis);
            let data: SingleRecordAnalysis<any> = <SingleRecordAnalysis<any>>stageData;

            // get the next stage index after the badges, if any - so we can create a click event
            let i = index + 1;
            let stageNumAfterBadges = null;
            while (this.getStage(i)) {
                if (this.getStage(i).getStageType() != StageType.BADGE) {
                    stageNumAfterBadges = i;
                    break;
                }
                i++;
            }
            i--;

            if (reportStage.getBadgeRenderType() == 'mui') {
                // NB should add click handler to badgeMui, and clickHandler as below
                let badge = this.getBadgeMuiForStage(reportStage);
                badge.setData(data, this.getKeyBreadcrumb(index));
            } else {
                let badge = this.getBadgeForStage(reportStage);
                // this should really be in getBadgeDataFor above
                if (stageNumAfterBadges) {
                    const dataEvent = new BadgeClickEvent<any>().selectSingleItem(index, data.getData());
                    badge.addClickEventHandler(event => this.onBadgeDataClick(reportStage, this.getStage(stageNumAfterBadges), data, dataEvent));
                }
                badge.setData(data, this.getKeyBreadcrumb(index));
            }
            const nextStage = this.getStage(index + 1);
            if (nextStage && nextStage.getStageType() == StageType.BADGE) {
                this.showDataForStage(nextStage, null, data);
            }
        }
        else if (reportStage instanceof AuditReportStage) {
            console.debug("Stage %o: visual (audit)", index);
            let auditControl = this.getAuditForStage(reportStage);
            let data = this.getAuditDataFor(reportStage, <SequenceAnalysis<any>>stageData);
            auditControl.setData(this.sessionData, data);
            // TODO showDataForStage... next stage
        }
        else if (reportStage instanceof CalendarEventReportStage) {
            console.debug("Stage %o: visual (calendar events)", index);
            let calendarEventControl = this.getCalendarEventForStage(reportStage);
            let data = this.getCalendarEventDataFor(reportStage, <SequenceAnalysis<any>>stageData);
            calendarEventControl.setData(this.sessionData, data);
            // TODO showDataForStage... next stage
        }
    }

    // we only extend Group<any> here because we rely on there being a key to identify the item
    private getChartDataFor<T extends Object>(thisStage: ChartReportStage, analysis: SequenceAnalysis<T>): charts.ChartData {
        const data: Sequence<T> = analysis && analysis.getData();
        const dataSeries = thisStage.getSeriesDefinitions()
            .map((series) => {
                function representItem(item: T): charts.KeyValuePair {
                    let key = analysis.getKey(item);
                    // NB this is where the value for the charts is picked up from
                    let value = item[series.valuePath];
                    if (value == null) {
                        console.error("error: no valuePath for " + series.valuePath + ". Valid values are: "
                            + Object.keys(item));
                    }
                    return {
                        key: value != null ? key : "report mis-configured: couldn't find " + series.valuePath,
                        value: value != null ? value : 1 // use value if given, or 1 on error
                    };
                }

                let representation: charts.DataRepresentation<T>;

                if (!series.renderMode || series.renderMode == SeriesType[SeriesType.BAR]) {
                    representation = new charts.BarChartDataRepresentation<T>(representItem, series.renderOptions);
                } else if (series.renderMode == SeriesType[SeriesType.DONUT]) {
                    representation = new charts.DonutChartDataRepresentation(representItem, series.renderOptions);
                } else if (series.renderMode == SeriesType[SeriesType.PIE]) {
                    const renderOptions = {
                        ...series.renderOptions, highlightMouseOver: thisStage.canClickThrough()
                    }
                    representation = thisStage.getSeriesDefinitions().length == 1
                        ? new charts.PieChartDataRepresentation(representItem, renderOptions)
                        : new charts.DonutChartDataRepresentation(representItem, renderOptions);
                }
                // TODO: line series

                const dataSeries = new charts.DataSeries<T>(series.label, thisStage.getSelectionKey(), data.toArray(), representation);

                const nextStage = this.getStage(thisStage.getStageIndex() + 1);
                if (nextStage) {
                    dataSeries.addClickEventHandler(
                        (event) => this.onChartDataClick(thisStage, nextStage, analysis, event));
                }

                return dataSeries;
            });

        return new charts.ChartData(dataSeries);
    }

    private getAuditForStage(reportStage: AuditReportStage): AuditReportStageControlI {
        let control = this.auditsByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createAuditForStage(reportStage);
            this.auditsByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getCalendarEventForStage(reportStage: CalendarEventReportStage): CalendarEventReportStageControlI {
        let control = this.calendarEventsByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createCalendarEventForStage(reportStage);
            this.calendarEventsByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getBadgeForStage(reportStage: BadgeReportStage): BadgeReportStageControlI {
        let control = this.badgesByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createBadgeForStage(this.ctx, reportStage);
            this.badgesByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getBadgeMuiForStage(reportStage: BadgeReportStage): BadgeMuiReportStageControlI {
        let control = this.badgesMuiByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createBadgeMuiForStage(this.ctx, reportStage);
            this.badgesMuiByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getChartForStage(reportDefName: string, reportStage: ChartReportStage): ChartReportStageControlI {
        let control = this.chartsByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createChartForStage(this.ctx, reportDefName, reportStage);
            this.chartsByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getTableForStage(reportDefName: string, reportStage: TableReportStage): TableReportStageControlI {
        let control = this.tablesByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createTableForStage(this.ctx, reportDefName, reportStage);
            this.tablesByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getTableMuiForStage(reportDefName: string, reportStage: TableReportStage): TableMuiReportStageControlI {
        let control = this.tablesMuiByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createTableMuiForStage(this.ctx, reportDefName, reportStage);
            this.tablesMuiByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }
    private getMatrixForStage(reportDefName: string, reportStage: MatrixReportStage): MatrixReportStageControlI {
        let control = this.matrixByStage[reportStage.getStageIndex()];
        if (!control) {
            control = this.createMatrixForStage(this.ctx, reportDefName, reportStage);
            this.matrixByStage[reportStage.getStageIndex()] = control;
            this.append(control);
        }
        return control;
    }

    /** analysis is the analysis for the current stage, representing all the data - the event contains the clicked
     * sub-selection. analysis is used for getting the key. */
    private onChartDataClick<T>(thisStage: ChartReportStage, nextStage: ReportStage, analysis: SequenceAnalysis<T>,
            event: charts.ClickEvent<T>) {
        console.debug("Stage %o: clicked, %s", nextStage.getStageIndex()-1, event.getDatums().length > 1 ? 'multi-keys' : 'single-keys');

        // as per onTableDataClick
        // NB previously, 'event.getDatums().length == 1' did catch 'select all' with only one legend
        if (!event.isSelectedAll()) {
            let selector = new ArrayKeySelector( event.getDatums().map( (datum) => analysis.getKey(datum) ) );
            this.showDataForStage(nextStage, selector, this.getAnalysisFor(thisStage, analysis, selector));
        } else {
            // In 'select all', the previous analysis is passed directly to the next stage, unless selectionAnalyserAll is defined.
            // This works because the previous analysis is often a non-visual analysis which receives ungrouped data
            // and the next stage is often a non-visual analysis which expects ungrouped data (eg via a click)
            // and so this skips the current chart selection, and the grouping function of the previous analyser.
            // However, we may need to apply a specified analyser (similar to onTableDataClick) when chaining some charts directly
            // or when the previous stage is grouped data which needs to go to a single result.
            // To choose an analyser, we can lookup 'selectionAnalyser' in the definition, however, this can already be used for
            // clicks on a chart segment (whether that's single clicks or many clicks - many just repeat the single clicks, see types#onClickWithManyByReduce)
            // and we need a different analyser, so we create a new property "selectionAnalyserAll": "..." alongside "selectionAnalyser": "single".

            if (thisStage.getSelectionAnalyserAll() != null) {
                const selector = new AllKeysSelector()
                this.showDataForStage(nextStage, selector, this.getAnalysisFor(thisStage, analysis, selector));
            } else {
                // else pass the previous stage
                const index = Math.max(thisStage.getStageIndex() - 1, 0);
                let allAnalysis = this.analysisByStage[index];
                this.showDataForStage(nextStage, new AllKeysSelector(), allAnalysis);
            }
        }
    }

    /**
     * Filter the current analysis data using the selected key and run against the selectionAnalyser in the definition
     * producing another Analysis.
     */
    private getAnalysisFor<T>(stage: ReportStage, analysis: SequenceAnalysis<T>, selector: KeySelector): Analysis<any> {
        if (!analysis) {
            return null;
        }

        // TableCellKeySelector
        // Selecting based on a table cell selector is good, except it forces our analysers to implement 'getKey'
        // using a rowNumber so that we can find the right data based on rowNumber - or we could just rely on LazyJS
        // ability to find the nth row?
        // So without this work, currently charts gives us keys from legends, and tables give us the actual row data.

        if (selector instanceof ArrayKeySelector) {
            let dataMatching = analysis.getData().filter( (item) => selector.matches(analysis.getKey(item)) );

            // either the array is one element (single click) or many elements (many clicks)
            // clickWithMany loops the single clicks and passes the result to an Analysis (see types#onClickWithManyByReduce)
            // however, we want to do some processing on the 'many' elements first (recalculating data eg 'latestWorkDate')
            // so we first look for a defined analyser
            if (dataMatching.size() == 1) {
                return analysis.onClickWith(stage.getSelectionAnalyser(), dataMatching.first());
            } else {
                if (stage.getSelectionAnalyserMany()) {
                    return analysis.onClickWithMany(stage.getSelectionAnalyserMany(), dataMatching);
                }
                return analysis.onClickWithManyByReduce(stage.getSelectionAnalyser(), dataMatching);
            }
        }

        if (selector instanceof AllKeysSelector) {
            let data = analysis.getData();
            return analysis.onClickWithAll(stage.getSelectionAnalyserAll(), data);
        }

        throw new Error("Not implemented");
    }

    private getBadgeDataFor<T extends Object>(stage: BadgeReportStage, analysis: SingleRecordAnalysis<T>): T {
        if (!analysis) {
            return null;
        }

        // we should return the contents of the badge, like a Panel, or JQuery object
        // so that we can specify a click event on it here (see below snippet)
        return analysis.getData();

        // the below extract is from getTableDataFor, expanding the methods
        /*
        let notNullData: any[] = analysis.getData() ? (<any>analysis.getData()).toArray() : [];
        let representation: TableRepresentation<T> = analysis.getRecordRepresentation(stage.getTableRepresentationName(), stage.getTableColumns());

        const table = new Table(notNullData, representation, initialColumn);
        const nextStage = this.getStage(stage.getStageIndex() + 1);

        if (nextStage) {
            table.addClickEventHandler((event) => this.onTableDataClick(stage, nextStage, analysis, event));
        }
        else {
            hasNextStage && console.debug("Not adding click handler for 'next' analysis: %o", analysis);
        }
        return table;
        */
    }

    /** Given some report items, get the table including data and matching representation */
    private getTableDataFor<T>(stage: TableReportStage, analysis: SequenceAnalysis<T>): Table<any> {
        if (!analysis) {
            return null;
        }

        let notNullData: any[] = analysis.getData() ? (<any>analysis.getData()).toArray() : [];
        let representation: TableRepresentationBase<T> = analysis.getRecordRepresentation(stage);
        return this.getTableDataCommon<T>(analysis, stage, representation, notNullData, null);
    }

    private getMatrixDataFor<T extends MatrixRow<any>>(stage: MatrixReportStage, analysis: SequenceAnalysis<T>): Table<any> {
        if (!analysis) {
            return null;
        }
        let dummyColumn = textColumn<T>("", (row) => "");
        let representation = new TableRepresentationBase<T>([dummyColumn]);

        // calculate the columns from the first row of the data
        let data: MatrixRow<any>[] = analysis.getData() ? analysis.getData().toArray() : [];
        let columnNames: string[] = [];
        if (data.length > 0) {
            // get the first row data for the columns
            let firstRowColumns: MatrixRow<any> = data[0];
            // get the column headings
            columnNames = firstRowColumns.map(group => group.key);
            // create the column definitions
            let columnDefs = columnNames.map( (columnName) => this.getNumberColumn<T>(columnName, (row) => row.filter(grp => grp.key == columnName)[0].elements.size()));
            representation = new TableRepresentationBase<T>(columnDefs);
        }
        return this.getTableDataCommon<T>(analysis, stage, representation, data, columnNames);
    }

    /** Override the text-right of numbers */
    private getNumberColumn<T>(columnName: string, valueFn: (row: T) => number) {
        let column = numberColumn<T>(columnName, valueFn);
        let extend = {getCssClasses: (row) => ""};
        return $.extend(column, extend);
    }

    private getTableDataCommon<T>(analysis: SequenceAnalysis<T>, stage: TableReportStage | MatrixReportStage,
                representation: TableRepresentationBase<T>,
                notNullData: any[],
                initialColumn: string[]) {
        const table = new Table(notNullData, representation, initialColumn);
        const nextStage = this.getStage(stage.getStageIndex() + 1);
        if (nextStage) {
            table.addClickEventHandler((event) => this.onTableDataClick(stage, nextStage, analysis, event));
        }
        else {
            nextStage && console.debug("Not adding click handler for 'next' analysis: %o", analysis);
        }
        return table;
    }

    private onBadgeDataClick<T>(thisStage: BadgeReportStage, nextStage: ReportStage, analysis: SingleRecordAnalysis<T>,
                                event: BadgeClickEvent<T>) {

        console.debug("Stage %o: clicked, %s", nextStage.getStageIndex() - 1, event.getDatums().length > 1 ? 'multi-keys' : 'single-keys');

        // as per onTableDataClick below - see '// TableReportStage'
        let selectionAnalyser = thisStage.getSelectionAnalyser();
        let dataClickedRow = event.getDatums()[0];
        let nextStageAnalysis = analysis.onClickWith(selectionAnalyser, dataClickedRow);
        let rowIndex = 0;
        let key = new ArrayKeySelector([rowIndex.toString()]);
        this.showDataForStage(nextStage, key, nextStageAnalysis);
    }

    private onTableDataClick<T>(thisStage: TableReportStage | MatrixReportStage, nextStage: ReportStage, analysis: SequenceAnalysis<T>,
            event: ClickEvent<T>) {

        console.debug("Stage %o: clicked, %s", nextStage.getStageIndex()-1, event.getDatums().length > 1 ? 'multi-keys' : 'single-keys');

        // as per onChartDataClick
        // NB previously, 'event.getDatums().length == 1' did catch 'select all' with only one legend
        if (!event.isSelectedAll()) {

            if (event.getDatums().length > 1) {
                throw new Error("Multiple selections are not implemented on ");
            }

            // get the data from selected set (which can only be one row of the table at the moment)
            // NB this differs to other approaches which get the data using getAnalysisDataFor
            let dataClickedRow = event.getDatums()[0];
            let selectionAnalyser = thisStage.getSelectionAnalyser();
            let key: KeySelector;
            let nextStageAnalysis: Analysis<any>;

            // MatrixReportStage
            if (thisStage instanceof MatrixReportStage) {

                // we can't transform the data from the row into the cell
                // whilst the contract for an analyser is to accept the same type
                // (this shows up when we try to have the analyser use the type returned here)
                // if we relax this, then we'd be able to transform the value here
                // as per below - but we'd also need to catch the pass-through of
                // 'select all' which could be to throw an error - see below
                /*
                // the transform could be done using the keys in the showDataForStage
                // but if feels more appropriate to extract the correct data
                // here alongside where chart and tables do so
                // FORCE CAST the row to a MatrixRow - in a dedicated onMatrixDataClick we could use T extends MatrixRow
                //let matrixAnalysis = <SequenceAnalysis<MatrixRow<any>>>(<any>analysis);
                let dataClickedAsMatrixRow = <MatrixRow<any>>(<any>dataClickedRow);
                // get the first row data for the columns
                let rowColumns: StringToObjectMap<any[]> = dataClickedAsMatrixRow.columns;
                // get the column headings, index and therefore the column data
                let columns = this.getMatrixColumns(rowColumns);
                let columnNameClicked = columns[event.getColumnIndex()];
                let cellData = <any>rowColumns[columnNameClicked];

                // get the transition for the next stage based on eg 'ungroup'
                // NB this doesn't use the common getAnalysisFor method since we already have the row data
                nextStageAnalysis = analysis.onClickWith(thisStage.getSelectionAnalyser(), cellData);
                */

                // instead of transforming, we could simply wrap the result into the original type
                // as we do here, but there is more in having MatrixRow extend Group with GroupedAnalysers etc
                // which we haven't utilised here, we've just wrapped the cell data into the grouped data
                // because it seems less harmful now a MatrixRow is just an array of a group
                let dataClickedAsMatrixRow = <MatrixRow<any>>(<any>dataClickedRow);
                // get the column headings
                let columnNames = dataClickedAsMatrixRow.map(group => group.key);
                // get the column name and therefore the column data
                let columnNameClicked = columnNames[event.getColumnIndex()];
                // use the index over the name, in case of duplicate names
                let cellData = <any>dataClickedAsMatrixRow[event.getColumnIndex()].elements;
                //let cellData = <any>dataClickedAsMatrixRow.filter(group => group.key == columnNameClicked)[0].elements;
                // return the same expected type
                let wrappedCellData: T = <T><any>[{
                    key: columnNameClicked,
                    elements: cellData
                }];

                // get the transition for the next stage based on eg 'ungroup'
                // NB this doesn't use the common getAnalysisFor method since we already have the row data
                nextStageAnalysis = analysis.onClickWith(selectionAnalyser, wrappedCellData);

                // obtain the key for storing in the keySelections as part of the stage progression
                key = new TableCellKeySelector(event.getRowIndex(), event.getColumnIndex());
            }
            // TableReportStage
            else {

                // get the transition for the next stage based on eg 'ungroup'
                // NB this doesn't use the common getAnalysisFor method since we already have the row data
                nextStageAnalysis = analysis.onClickWith(selectionAnalyser, dataClickedRow);

                // obtain the key for storing in the keySelections as part of the stage progression
                // if we 'select all' but there is only one row, we need to ensure an index exists
                let rowIndex = event.getRowIndex() || 0;
                key = new ArrayKeySelector([rowIndex.toString()]);
            }

            // show the next stage
            this.showDataForStage(nextStage, key, nextStageAnalysis);

        // select all
        } else {

            // unlike charts, a table's previous stage is often a chart which receives grouped data
            // but tables typically expect ungrouped data as a result of a click on a chart
            // so choosing the previous stage to the table is not appropriate
            // we could try to select the analyser prior to the chart, but this will miss any 'key' chosen in the now skipped chart
            // so we push 'everything in this table' down instead, because we have received ungrouped data, we just pass it on
            // removing the original behaviour of using the previous analyser makes sense as can't see where this typically worked
            // unless a table received grouped data

            // z1083 had an issue with "support work minutes by service, then by project, then by worker, then breakdown of support work"
            // since the report is defined as visitCountsBy.. -> chart -> table (class ReferralReportItem) -> workFromReferrals -> table (class Work)
            // and choosing 'select all' on the first table meant choosing the previous stage which resulted in completely different data
            // z1083 fix was to not choose the previous stage, but choose the data for this stage - as per the paragraph above

            // z1502 has an issue with "referrals (closed) by service, then by project, then by support work, then breakdown"
            // where the report now breaks on 'select all' from the first table - this is because the first table is represented
            // as ActionDefCount which receives a group - so when we select all, the grouped data is now passed to the next
            // table ActionDef which is not expecting a group.

            // so z1083 expects NO group, and z1502 expects A group
            // THEREFORE we are at the stage where we cannot assume one way or another, and need something to distinguish
            // SO we have some choices, but we would like to re-use the same logic process as onClickWith as this may include
            // processing that we could miss by specifying a separate process. So the default should be to take the group
            // and try to map each of the elements with the onClickWith (which are designed for a single T, not an array).
            // (actually it gets all the elements and then applies onClickWith - see code)
            // IF we want more control, we could specify a selectionMultiAnalyser and addOnClickManyAnalysis

            // (see comments of this commit)

            // SO we check if its a group analyser, and if so we simulate the onClickWith behaviour for each row
            // or if a selectionMultiAnalyser exists, use that... else do default back to onClickWith's default ungroup
            let selector = new AllKeysSelector();
            let thisStageAnalysis = this.analysisByStage[thisStage.getStageIndex()];
            let analysis: Analysis<any>;
            if (thisStageAnalysis instanceof GroupedAnalysis) {
                // we ungroup to regroup and ignore the key (since we're on a table breakdown)
                // this is because whilst processing each of the 'elements' of the grouped analysis is fine
                // we then need to pass an analysis to the next stage, and this is more difficult to construct
                // - we can easily get a group of analysis through a map, but we need to then merge them
                // for now this approach of merging all the elements and then doing an analysis should suffice
                let unGroupedData = thisStageAnalysis.getData().map(grp => grp.elements).flatten<any>();
                // elements get stripped out in 'ungroup', so we don't need key
                let reGroupedData: Group<any> = {key: null, elements: unGroupedData, count: unGroupedData.size()};
                analysis = thisStageAnalysis.onClickWith(thisStage.getSelectionAnalyser(), reGroupedData);
            }
            else if (thisStageAnalysis instanceof MatrixAnalysis) { // NB could use stage instanceof MatrixReportStage
                // ideally we get each cell and push through the selectionAnalyser but (as explained above for GroupedAnalysis)
                // we can't merge the resulting analysis very easily, so instead we gather all cell data together and
                // push through the one analysis.
                // perhaps expecting a 'selectAll' selectionAnalyser would be more appropriate than choosing the one there
                let matrixData = thisStageAnalysis.getData(); // sequence of MatrixRow
                let flattenedRows = Lazy(matrixData.reduce((row1, row2) => row1.concat(row2)));
                analysis = thisStageAnalysis.onClickWith(thisStage.getSelectionAnalyser(), flattenedRows);
            }
            else {
                // we push 'everything in this table' down instead, because we have received ungrouped data, we just pass it on
                // so there is no onClickWith done here
                analysis = this.analysisByStage[thisStage.getStageIndex()];
            }
            // we now pass the request to the common method to get the analysis, but not really needed
            //this.showDataForStage(nextStage, selector, this.getAnalysisFor(thisStage, analysis, selector));
            this.showDataForStage(nextStage, selector, analysis);
        }
    }

    /** Given some report items, get the table including data and matching representation */
    private getAuditDataFor<T>(stage: AuditReportStage, analysis: SequenceAnalysis<T>): Sequence<T> {
        if (!analysis) {
            return null;
        }
        return analysis.getData();
        // let representation = analysis.getRecordRepresentation(stage.getRecordRepresentationName(), [stage.getCommandColumn()]);
        // // get a table of one-column that holds the commands
        // let data = new Table(out ? out.toArray() : [], representation);
        // var indicatorRepresentation = getFieldRepresentation(representation, stage.getMainIndicatorValue());
        // console.debug("Not adding click handler for 'next' analysis: %o", analysis);
        // return data;
    }

    /** Given some report items, get the table including data and matching representation */
    private getCalendarEventDataFor<T>(stage: CalendarEventReportStage, analysis: SequenceAnalysis<T>): Sequence<T> {
        if (!analysis) {
            return null;
        }
        return analysis.getData();
    }

}

export class ReportStagesControlJQuery<INITIAL> extends ReportStagesControlBase<INITIAL> {

    constructor(private container: $.JQuery, private pieChartClasses = "col-md-6 col-lg-4", private tableClasses = "col-xs-12",
                private barChartClasses = "col-xs-12",
                private badgeClasses = "col-sm-6 col-lg-4") {
        super();
    }

    protected createAuditForStage(reportStage: AuditReportStage): AuditReportStageControlI {
        const ctl = new AuditReportStageControl(reportStage);
        ctl.element().addClass(this.tableClasses);
        return ctl;
    }

    protected createCalendarEventForStage(reportStage: CalendarEventReportStage): CalendarEventReportStageControlI {
        const ctl = new CalendarEventReportStageControl(reportStage);
        ctl.element().addClass(this.tableClasses);
        return ctl;
    }

    protected createBadgeForStage(rowCtx: RowContext, reportStage: BadgeReportStage): BadgeReportStageControlI {
        const ctl = new BadgeReportStageControl(rowCtx, reportStage);
        ctl.element().addClass(this.badgeClasses);
        return ctl;
    }

    protected createBadgeMuiForStage(rowCtx: RowContext, reportStage: BadgeReportStage): BadgeMuiReportStageControlI {
        const ctl = new BadgeMuiReportStageControl(rowCtx, reportStage);
        ctl.element().addClass(this.badgeClasses);
        return ctl;
    }

    protected createChartForStage(rowCtx: RowContext, reportDefName: string, reportStage: ChartReportStage): ChartReportStageControlI {
        const ctl = new ChartReportStageControl(rowCtx, reportDefName, reportStage);
        const cssClasses = reportStage.isPieChart() ? this.pieChartClasses : this.barChartClasses;
        ctl.element().addClass(cssClasses);
        return ctl;
    }

    protected createTableForStage(rowCtx: RowContext, reportDefName: string, reportStage: TableReportStage): TableReportStageControlI {
        const ctl = new TableReportStageControl(rowCtx, reportDefName, reportStage);
        ctl.element().addClass(this.tableClasses);
        return ctl;
    }

    protected createTableMuiForStage(rowCtx: RowContext, reportDefName: string, reportStage: TableReportStage): TableReportStageControlI {
        const ctl = new TableMuiReportStageControl(rowCtx, reportDefName, reportStage);
        ctl.element().addClass(this.tableClasses);
        return ctl;
    }

    protected createMatrixForStage(rowCtx: RowContext, reportDefName: string, reportStage: MatrixReportStage): MatrixReportStageControlI {
        const ctl = new MatrixReportStageControl(rowCtx, reportDefName, reportStage);
        ctl.element().addClass(this.tableClasses);
        return ctl;
    }

    /** append the control to the page, somehow */
    protected append(element: BaseReportStageControlI) {
        this.container.append(element.domElement());
    }

}
