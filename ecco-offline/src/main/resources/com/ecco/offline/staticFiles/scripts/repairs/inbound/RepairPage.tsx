import {
    AppBar,
    Toolbar,
} from "@eccosolutions/ecco-mui";

import {applicationRootPath, resourceRootPath} from "application-properties";
import {EccoTheme} from "ecco-components-core";
import * as React from "react";
import * as ReactDom from "react-dom";
import {Route, Switch} from "react-router";
import {BrowserRouter} from "react-router-dom";
import {Wizard} from "./RepairComponent";

const leftIcon =
    <img src={resourceRootPath + "themes/ecco/images/logo_white.png"} height="48"/>;

// see AppBarBase
const greenAppBar = "#36720A";

export function RepairComponent() {

    const search = window.location.search;
    const params = new URLSearchParams(search);
    //const serviceCategorisationId = useParams<RouteParams>().serviceCategorisationId;
    const buildingId = params.get("buildingId");

    return (
        <div>
            <AppBar
                position="fixed"
                style={{top: 0}}
            >
                <Toolbar
                    style={{
                        backgroundColor: greenAppBar
                    }}>
                    {leftIcon}
                </Toolbar>
            </AppBar>
            <div style={{marginTop: 64}}>
                <Wizard schemaUri="repairs/$schema/" buildingId={buildingId ? parseInt(buildingId) : null}/>
                {/*<Wizard schema={schema}/>*/}
            </div>
        </div>
    );
}

ReactDom.render((
    <EccoTheme prefix="refer">
        <BrowserRouter basename={applicationRootPath.substr(0, applicationRootPath.length - 1)}>
            <Switch>
                <Route exact path={["/p/r/repair", "/p/r/repair/:buildingId",
                    "/nav/r/repair", "/nav/r/repair/:buildingId",
                    "/nav/p/r/repair", "/nav/p/r/repair/:buildingId"]}>
                    <RepairComponent />
                </Route>
                <Route path="/">
                    <h3>incorrect wiring</h3>
                </Route>
            </Switch>
        </BrowserRouter>
    </EccoTheme> ),
    document.getElementById("appbar"));