import $ = require("jquery");

import BaseControl = require("../../controls/BaseControl");
import CommandEmittingForm = require("../../cmd-queue/CommandEmittingForm");
import Modal = require("../../controls/Modal");
import ModalMode = require("../../controls/ModalMode");
import {
    ActionInstanceControlData,
    ActionInstanceFeatures,
    EvidenceContext,
    EvidenceDef,
    EvidenceDisplayOptions, HierarchyPosition
} from "ecco-evidence";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue} from "ecco-commands";
import {adminModeEnabled, ActionComponent, SessionData, SmartStepStatus} from "ecco-dto";
import {GoalUpdateCommandDto, SupportAction} from "ecco-dto/evidence-dto";
import {ActionInstanceControl} from "../evidenceControls";
import {AdminMode, EccoDateTime} from "@eccosolutions/ecco-common";
import {showErrorAsAlert} from "ecco-offline-data";


abstract class BaseActionInstanceControl extends BaseControl implements ActionInstanceControl {

    private $containerWrapper = $("<div>");
    protected $goalName: $.JQuery = $("<span>");
    protected $goalPlan: $.JQuery = $("<span>");
    protected $lastUpdate = $("<span>");
    private relatedImage = "link";
    private relatedImageFaded = "link-faded";
    protected dateImage = "datepicker";

    // *** STATE
    // latest.actionInstanceUuid is not populated until emitting (although it could be populated)
    // latest.name is the for display purposes
    // latest.statusChangeReasonId is the list def entry
    protected latest: SupportAction = {id: null, actionInstanceUuid: null, parentActionInstanceUuid: null,
        workId: null, workDate: null, actionId: null, name: null, goalName: null,
        goalPlan: null, score: null, statusChange: null,
        status: null, statusChangeReasonId: null, targetDateTime: null, expiryDate: null,
        likelihood: null, severity: null, hazard: null, intervention: null,
        hierarchy: null, position: null
    };
    protected lastUpdate: EccoDateTime;
    protected defaultGoalNameText = "";
    protected defaultLastUpdateText = "-";
    protected adminMode = false;
    // the below doesn't form part of SupportAction
    protected currentRelated = false; // related / link / chain
    protected currentActivityState: number[] = [];
    // *** STATE

    protected modalForm: Modal;

    constructor(protected context: EvidenceContext, protected sessionData: SessionData, protected serviceRecipientId: number,
                protected evidenceDef: EvidenceDef, protected actionDef: ActionComponent,
                protected readonly initialData: ActionInstanceControlData, protected readonly controlUuid: Uuid,
                protected controlFeatures: ActionInstanceFeatures) {
        super($("<div>").addClass("action-instance"));

        super.element().append(this.$containerWrapper);

        this.adminMode = adminModeEnabled();
        AdminMode.bus.addHandler( (event) => {
            this.adminMode = event.enabled;
            this.draw();
        });
    }

    public init() {
        this.initialise();
    }

    protected initialise() {}

    public getHierarchyPosition(): HierarchyPosition {
        return new HierarchyPosition(this.latest.position);
    }

    public applyCommand(dto: GoalUpdateCommandDto) {
        this.updateStateFromCommand(dto);
        this.applyState();
    }

    public applySnapshot(data: ActionInstanceControlData) {
        this.updateStateFromSnapshot(data);
        this.applyState();
    }

    protected abstract updateStateFromCommand(dto: GoalUpdateCommandDto);
    protected abstract updateStateFromSnapshot(data: ActionInstanceControlData);

    protected applyState() {
        this.$goalName.text(this.latest.goalName || this.defaultGoalNameText);
        let lastUpdateText = this.lastUpdate ? this.lastUpdate.formatPretty() : this.defaultLastUpdateText;
        this.$lastUpdate.text(lastUpdateText);
    }

    protected getContainer() {
        return this.$containerWrapper;
    }

    protected abstract draw();

    protected showForm(form: CommandEmittingForm) {
        if (!this.modalForm) {
            this.modalForm = new Modal("modal-lg", ModalMode.fillScreenHeight);
        }
        form.onSubmit( (commandQueue: CommandQueue) => {
            this.applyCommands(commandQueue)
                .then(() => this.modalForm.dialogHide())
                .catch(showErrorAsAlert);
        });
        this.modalForm.popView(form);
    }

    emitChangesTo(commandQueue: CommandQueue) {
        // we don't emit commands ourselves, the modal's do
    }

    /** After a form returns, save and process the commands on the page */
    abstract applyCommands(commandQueue: CommandQueue): Promise<void>;

    initialActionInstanceUuid(): string {
        return this.initialData && this.initialData.actionInstanceUuid ? this.initialData.actionInstanceUuid.toString() : null;
    }

    /** The actionInstanceUuid for any command - whether its been provided, or generated
     * NB this could also be set on latest.actionInstanceUuid since that doesn't form part of the change
     */
    actionInstanceUuidOrControlUuid(): string {
        return this.initialData && this.initialData.actionInstanceUuid ? this.initialData.actionInstanceUuid.toString() : this.controlUuid.toString();
    }

    parentUuid(): string {
        return this.initialData && this.initialData.parentActionInstanceUuid ? this.initialData.parentActionInstanceUuid.toString() : null;
    }

    getActionDef(): ActionComponent {
        return this.actionDef;
    }
    setActionDef(actionDef: ActionComponent) {
        this.actionDef = actionDef;
    }
    getActionDefId(): number {
        return this.actionDef && this.actionDef.getId();
    }

    public changedDisplayEvidence(displayOptions: EvidenceDisplayOptions): boolean {
        if (displayOptions.filterAchieved && this.initialData.status == SmartStepStatus.Achieved) {
            this.$containerWrapper.hide();
            return true;
        }
        if (displayOptions.filterUndo && this.initialData.status == SmartStepStatus.NoLongerWanted) {
            this.$containerWrapper.hide();
            return true;
        }
        // we could exclude from the history filter
        /*if (displayOptions.filterComments && this.initialData.status == SmartStepStatus.CommentOnly) {
            this.$containerWrapper.hide();
            return true;
        }*/
        this.$containerWrapper.show();
        return false;
    }

    render() {
        this.draw();
    }

    protected relatedSrc(related: boolean): string {
        return related ? this.relatedImage : this.relatedImageFaded;
    }

    public hasHistory() {
        return !!this.initialData.status;
    }

    public abstract isValid();

    public abstract isRelevant();
    public abstract isAchieved();
    public abstract showSummaryOnly();

}
export = BaseActionInstanceControl;
