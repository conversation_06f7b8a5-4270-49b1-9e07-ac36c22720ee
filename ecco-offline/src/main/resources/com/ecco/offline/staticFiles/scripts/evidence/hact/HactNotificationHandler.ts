import * as _ from 'lodash';
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {HactClientCompositeData} from "ecco-dto";
import {Question} from "ecco-dto/service-config-dto";
import {NotificationSummaryData} from "ecco-dto";


/**
 * Performs the calculations to display the appropriate information
 * in the notification area for surveys, and passes this to the wizard
 */
class HactNotificationHandler {

    // updated by events that trigger a pre-survey
    private preSurveyActionDefsLive: number[] = [];
    private preSurveyQuestionDefsLive: number[] = [];

    // computed data that we store for reference (based on history)
    private postSurveyQuestions: Question[] = [];
    private preSurveyQuestionDefIdsFromActionHistory: number[] = [];
    private preSurveyQuestionDefIdsFromQuestionHistory: number[] = [];
    private preSurveyQuestionFromActionExpiresIn: number;
    private preSurveyQuestionFromQuestionExpiresIn: number;
    private notificationSummaryData: NotificationSummaryData;

    constructor(private hactClientCompositeData: HactClientCompositeData,
                now: EccoDateTime, showAllOutstanding = false) {

        this.notificationSummaryData = new NotificationSummaryData(hactClientCompositeData.hactAnswerHistorySummary,
                hactClientCompositeData.triggerPreSurveyActionHistory,
                hactClientCompositeData.triggerPreSurveyQuestionHistory,
                now, showAllOutstanding);

        this.buildPreSurveyQuestionDefIdsFromActionHistory();
        this.buildPreSurveyQuestionDefIdsFromQuestionHistory();
        this.buildPostSurveyQuestions();
    }

    /**
     * A smart step WantToAchieve has been pressed
     */
    public triggerActionDefId(actionDefId: number) {
        if (this.preSurveyActionDefsLive.indexOf(actionDefId) < 0) {
            this.preSurveyActionDefsLive.push(actionDefId);
        }
    }

    // NB we don't 'undo' an already saved smart step that triggered HACT - this only undoes transient triggers
    public unTriggerActionDefId(actionDefId: number) {
        const idx = this.preSurveyActionDefsLive.indexOf(actionDefId);
        if (idx > -1) {
            this.preSurveyActionDefsLive.splice(idx, 1);
        }
    }

    /**
     * A question has been answered
     */
    public triggerQuestionDefId(questionDefId: number) {
        if (this.preSurveyQuestionDefsLive.indexOf(questionDefId) < 0) {
            this.preSurveyQuestionDefsLive.push(questionDefId);
        }
    }

    // NB we don't 'undo' an already saved question that triggered HACT - this only undoes transient triggers
    public unTriggerQuestionDefId(questionDefId: number) {
        const idx = this.preSurveyQuestionDefsLive.indexOf(questionDefId);
        if (idx > -1) {
            this.preSurveyQuestionDefsLive.splice(idx, 1);
        }
    }

    public hasHistory() {
        return this.hactClientCompositeData.hactAnswerHistorySummary.answersAll.length > 0;
    }

    public hasPreSurveysOutstanding() {
        return this.calculatePreSurveyQuestions().length > 0;
    }

    public countPreSurveysOutstanding() {
        return this.calculatePreSurveyQuestions().length;
    }

    public hasPostSurveysOutstanding() {
        return this.postSurveyQuestions.length > 0;
    }

    public hasPreSurveysOutstandingFromHistory() {
        return (this.preSurveyQuestionDefIdsFromActionHistory.length > 0) ||
            (this.preSurveyQuestionDefIdsFromQuestionHistory.length > 0);
    }

    public getQuestionsOutstanding(): Question[] {
        return this.getPreSurveyQuestionsOutstanding().concat(
                this.getPostSurveyQuestionsOutstanding());
    }

    public getPreSurveyQuestionsOutstanding(): Question[] {
        return this.calculatePreSurveyQuestions();
    }

    public getPostSurveyQuestionsOutstanding(): Question[] {
        return this.postSurveyQuestions;
    }

    public getDaysDue() {
        if (this.hasPostSurveysOutstanding()) {
            return this.notificationSummaryData.getMinDaysToNextPostSurveyDue();
        }
        if (this.hasPreSurveysOutstandingFromHistory()) {
            return 0; // pre surveys are always due
        }
        throw new Error("no due days available");
    }

    public getDaysExpire() {
        if (this.hasPostSurveysOutstanding() && this.hasPreSurveysOutstandingFromHistory()) {
            return Math.min(
                this.notificationSummaryData.getMinDaysToNextPostSurveyExpires(),
                // these values can be null
                this.preSurveyQuestionFromActionExpiresIn
                    ? Infinity
                    : this.preSurveyQuestionFromActionExpiresIn,
                this.preSurveyQuestionFromQuestionExpiresIn
                    ? Infinity
                    : this.preSurveyQuestionFromQuestionExpiresIn);
        }
        if (this.hasPostSurveysOutstanding()) {
            return this.notificationSummaryData.getMinDaysToNextPostSurveyExpires()
        }
        if (this.hasPreSurveysOutstandingFromHistory()) {
            return Math.min(
                // these values can be null
                this.preSurveyQuestionFromActionExpiresIn
                    ? Infinity
                    : this.preSurveyQuestionFromActionExpiresIn,
                this.preSurveyQuestionFromQuestionExpiresIn
                    ? Infinity
                    : this.preSurveyQuestionFromQuestionExpiresIn);
        }
        throw new Error("no expire days available");
    }

    /**
     * Return the questions that are appropriate for post survey questions
     */
    private buildPostSurveyQuestions() {

        if (this.notificationSummaryData.hasPostSurveysDueNow()) {
            const questionDefIdsDue = this.notificationSummaryData.getQuestionDefIdsOfPostSurveysDue();

            // get the questions
            const questions = this.hactClientCompositeData.hactSessionData.hactQuestions
                .filter((question) =>
                    questionDefIdsDue.indexOf(question.id) > -1
                );
            this.postSurveyQuestions = questions;
        }

    }

    /**
     * Return the questions that are appropriate
     */
    private calculatePreSurveyQuestions(): Question[] {
        // Pre questions that have been triggered from history
        const preQuestionDefIdsFromActionHistory = this.preSurveyQuestionDefIdsFromActionHistory;
        const preQuestionDefIdsFromQuestionHistory = this.preSurveyQuestionDefIdsFromQuestionHistory;
        // Pre questions that are triggered 'live' during the piece of work
        const preQuestionDefIdsFromActionLive = this.outstandingHactQuestionDefIdsFromActionDefs(this.preSurveyActionDefsLive);
        const preQuestionDefIdsFromQuestionLive = this.outstandingHactQuestionDefIdsFromQuestionDefs(this.preSurveyQuestionDefsLive);

        const preQuestionDefIds = _.union(preQuestionDefIdsFromActionHistory,
            preQuestionDefIdsFromQuestionHistory,
            preQuestionDefIdsFromActionLive,
            preQuestionDefIdsFromQuestionLive);
        const questions = this.questionsFromDefIds(preQuestionDefIds);

        return questions;
    }

    /**
     * Pre questions that are calculated from saved support work
     */
    private buildPreSurveyQuestionDefIdsFromActionHistory() {

        // from the last x days find the WantToAchieve's HACT questions
        // excluding the pre questions already answered
        const triggeredActionsRecent = this.notificationSummaryData.getTriggeredActionDefIdsRecent();
        const recentHactQuestionDefIs = this.outstandingHactQuestionDefIdsFromActionDefs(triggeredActionsRecent);

        // from the expired days find the WantToAchieve's HACT questions
        // whether the pre questions was already answered or not
        const triggeredActionsExpired = this.notificationSummaryData.getTriggeredActionDefIdsNotRecent();
        const expiredHactQuestionDefIds = this.hactClientCompositeData.hactQuestionDefIdsFromActionDefIds(triggeredActionsExpired);

        // exclude the expired questions
        const diff = _.difference(recentHactQuestionDefIs, expiredHactQuestionDefIds);

        this.preSurveyQuestionDefIdsFromActionHistory = diff;

        // work out the earliest the pre questions (from support history) can expire
        // find the actionDefIds that we are allowed to show (from the questionDefIds)
        // and see which of those we wanted in the first place
        const possibleActionDefIds = this.hactClientCompositeData.actionDefIdsFromHactQuestionDefIds(diff);
        const allowedActionDefIds = _.intersection(triggeredActionsRecent, possibleActionDefIds);
        this.preSurveyQuestionFromActionExpiresIn = this.notificationSummaryData
                .getMinDaysToTriggeredActionsExpires(allowedActionDefIds);
    }

    /**
     * Pre questions that are calculated from saved question work
     */
    private buildPreSurveyQuestionDefIdsFromQuestionHistory() {

        // from the last x days find the question's HACT questions
        // excluding the pre questions already answered
        const triggeredQuestionsRecent = this.notificationSummaryData.getTriggeredQuestionDefIdsRecent();
        const recentHactQuestionDefIs = this.outstandingHactQuestionDefIdsFromQuestionDefs(triggeredQuestionsRecent);

        // from the expired days find the WantToAchieve's HACT questions
        // whether the pre questions was already answered or not
        const triggeredQuestionsExpired = this.notificationSummaryData.getTriggeredQuestionDefIdsNotRecent();
        const expiredHactQuestionDefIds = this.hactClientCompositeData.hactQuestionDefIdsFromActionDefIds(triggeredQuestionsExpired);

        // exclude the expired questions
        const diff = _.difference(recentHactQuestionDefIs, expiredHactQuestionDefIds);

        this.preSurveyQuestionDefIdsFromQuestionHistory = diff;

        // work out the earliest the pre questions (from support history) can expire
        // find the actionDefIds that we are allowed to show (from the questionDefIds)
        // and see which of those we wanted in the first place
        const possibleQuestionDefIds = this.hactClientCompositeData.questionDefIdsFromHactQuestionDefIds(diff);
        const allowedQuestionDefIds = _.intersection(triggeredQuestionsRecent, possibleQuestionDefIds);
        this.preSurveyQuestionFromQuestionExpiresIn = this.notificationSummaryData
            .getMinDaysToTriggeredQuestionsExpires(allowedQuestionDefIds);
    }

    /**
     * From the actionDefIds given, find the HACT outcomes and questions as a result
     * but EXCLUDE those already answered
     */
    private outstandingHactQuestionDefIdsFromActionDefs(actionDefIds: number[]): number[] {
        const hactQuestionDefIds = this.hactClientCompositeData.hactQuestionDefIdsFromActionDefIds(actionDefIds);
        return this.removePreQuestionDefIds(hactQuestionDefIds);
    }

    /**
     * From the questionDefIds given, find the HACT outcomes and questions as a result
     * but EXCLUDE those already answered
     */
    private outstandingHactQuestionDefIdsFromQuestionDefs(questionDefIds: number[]): number[] {
        const hactQuestionDefIds = this.hactClientCompositeData.hactQuestionDefIdsFromQuestionDefIds(questionDefIds);
        return this.removePreQuestionDefIds(hactQuestionDefIds);
    }

    private removePreQuestionDefIds(questionDefIds: number[]): number[] {
        // remove from pre-surveys those already answered
        const removePreQuestionDefIds = this.notificationSummaryData.getQuestionDefIdsOfPreSurveyAnswers();

        // get the difference from the arrays
        const diff = _.difference(questionDefIds, removePreQuestionDefIds);

        return diff;
    }

    private questionsFromDefIds(questionDefIds: number[]): Question[] {

        // get the questions
        const questions = this.hactClientCompositeData.hactSessionData.hactQuestions
            .filter((question) =>
                questionDefIds.indexOf(question.id) > -1
            );

        return questions;
    }

}

export = HactNotificationHandler;
