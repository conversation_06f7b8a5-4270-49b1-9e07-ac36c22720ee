import $ = require("jquery");
import RadarChartPath = require("../../draw/RadarChartPath");
import RadarChartSpokes = require("../../draw/RadarChartSpokes");

import services = require("ecco-offline-data");

import BaseGraphControl = require("./BaseGraphControl");
import events = require("../events");
import SupportWork = dto.SupportWork;
import {delay, EccoDateTime, StringUtils} from "@eccosolutions/ecco-common"
import * as dto from "ecco-dto/evidence-dto";
import {SupportSmartStepsSnapshot} from "ecco-evidence";
import {EvidenceGroup, ServiceRecipientWithEntities} from 'ecco-dto';
import {showInModalDom} from 'ecco-components-core';


class BackingData {
    constructor(public serviceRecipient: ServiceRecipientWithEntities, public evidence: dto.SupportWork[]) {
    }
}

class SupportRadarChart extends BaseGraphControl<BackingData> {
    private supportSmartStepsSnapshot: SupportSmartStepsSnapshot;
    private loaded = false;
    private slow = false;
    private paused = false;
    private $controls = $("<span>");
    private $date = $("<span>");
    private $reloadBtn: $.JQuery;

    constructor(private serviceRecipientId?: number) {
        super();
        events.GoalUpdateEvent.bus.addHandler( event => this.handleUpdateEvent(event) );
        this.$reloadBtn = $("<a>").addClass("fa fa-refresh").click( event => this.reloadSlowly() );
        this.$controls
//            .append( $("<a>").addClass("fa fa-pause").click( event => this.pauseResume() ) )
            .append(this.$reloadBtn)
            .append("<br>")
            .append(this.$date);
    }

    public controlsElement() {
        return this.$controls;
    }

    private reloadSlowly() {
        this.slow = true;
        this.$reloadBtn.hide();
        this.load();
    }

    private pauseResume() {
        this.paused = !this.paused;
        if (!this.paused) {
            // kick resume (for activating pause we just interrupt the events being applied)
        }
    }

    protected override onActivated() {
        // NB this.loaded could go - it should now be handled by BaseAsyncDataControl load
        if (!this.loaded) {
            this.load();
        }
    }

    protected fetchViewData() {
        return services.getReferralRepository().findOneServiceRecipientWithEntities(this.serviceRecipientId)
            .then( recipient =>
                // we need all support to draw the spidergraph
                // TODO performance - at least only loading statusChangeOnly but could use snapshot?
                services.getSupportWorkRepository().findSupportWorkByServiceRecipientId(
                    this.serviceRecipientId,
                    EvidenceGroup.needs, undefined, undefined, undefined, true)
                    .then( evidence =>
                        new BackingData(recipient, evidence.filter( work => work.actions && work.actions.length > 0) )
                    )
                );
    }

    protected renderGraph(data: BackingData): void {
        this.supportSmartStepsSnapshot = new SupportSmartStepsSnapshot(data.serviceRecipient.configResolver.getServiceType());
        this.drawAxes(); // draw the axes even if needs assessment not done
        this.applyHistory(data.evidence);
    }

    private drawAxes() {
        var numSpokes = this.supportSmartStepsSnapshot.getOutcomeEvidence().length;

        var labelledSpokes = new RadarChartSpokes(this.paper, this.centreX, this.centreY, "#bbb", "1.5", numSpokes);
        this.supportSmartStepsSnapshot.getOutcomeEvidence().forEach( (oe) => {
            var maxLabelLen = numSpokes < 22 ? 26 - numSpokes : 4; // shorter the more there are
            var shortTxt = StringUtils.abbreviateWords(oe.getOutcome().getName(), maxLabelLen);
            var label = labelledSpokes.addSpoke(this.radius).addLabel(shortTxt);
//            var url = null; // poss TODO get from was $(tipId).find('a').attr('href')
//            label && url && label.href(url);
        });
    }

    /** Apply history and redraw after each piece of support work.
      * this expects items in order of newest first, so starts with the last and works backwards */
    private applyHistory(itemsByNewestFirst: SupportWork[]) {
        var delayMs = this.slow ? 1000 : 1000/itemsByNewestFirst.length;   // 1000 = total animation time
        var handleItem = (i: number): Promise<void> => {
            if (i >= 0) {
                this.applyWork(itemsByNewestFirst[i], i, itemsByNewestFirst.length);
                return delay(delayMs)
                    .then(() => !this.paused && handleItem(i - 1));
            } else {
                this.loaded = true;
                this.$reloadBtn.show();
                return Promise.resolve(null);
            }
        };
        handleItem(itemsByNewestFirst.length - 1);
    }

    private applyWork(work: SupportWork, itemIndex: number, itemCount: number ) {
        var isLastItem = itemIndex == itemCount - 1;
        // the last action wins - if there is more than one actionDefId in the same piece of work
        work.actions.forEach( action => this.supportSmartStepsSnapshot.addSupportActionEvidence(action) );
        if (itemIndex == 0 || isLastItem || this.isModernBrowser() || this.slow) {
            this.redraw();
        }
        if (isLastItem || this.slow) {
            this.$date.text(EccoDateTime.parseIso8601(work.workDate).formatDatePretty());
        }
    }

    public drawSpider() {
        var numSpokes = this.supportSmartStepsSnapshot.getOutcomeEvidence().length;

        var targetPath = new RadarChartPath(this.centreX, this.centreY, "green", "1.5", numSpokes);
        var achievedPath = new RadarChartPath(this.centreX, this.centreY, "#FF9A33", "4.5", numSpokes);

        this.supportSmartStepsSnapshot.getOutcomeEvidence().forEach( (oe) => {
            this.addDataPoint(oe.getMaxNum(), oe.getNumWanted(), oe.getNumAchieved(), targetPath, achievedPath);
        });

        targetPath.render(this.paper);
        achievedPath.render(this.paper);
    }

    private addDataPoint(maxNum: number, numWanted: number, numAchieved: number,
            targetPath: RadarChartPath, achievedPath: RadarChartPath) {

        var minRadius = 4;
        var achievedLength = numAchieved * this.radius / maxNum;
        var wantedLength = minRadius + numWanted * (this.radius - minRadius) / maxNum;

        targetPath.addSpoke(wantedLength);
        achievedPath.addSpoke(achievedLength);
    }

    private handleUpdateEvent(event: events.GoalUpdateEvent) {
        if (!this.supportSmartStepsSnapshot) {
            return; // not loaded yet so no need to update
        }
        const supportBased = EvidenceGroup.fromName(event.command.getEvidenceGroupName()) == EvidenceGroup.needs;
        if (supportBased) {
            this.supportSmartStepsSnapshot.applyCommand(event.command);
            this.redraw();
        }
    }

    private redraw() {
        this.clear();
        this.drawAxes();
        this.drawSpider();
    }

    public static showInModal(serviceRecipientId: number) {
        var form = new SupportRadarChart(serviceRecipientId);
        showInModalDom("Radar Chart" ,form.domElement());
        form.load();
    }
}
export = SupportRadarChart;
