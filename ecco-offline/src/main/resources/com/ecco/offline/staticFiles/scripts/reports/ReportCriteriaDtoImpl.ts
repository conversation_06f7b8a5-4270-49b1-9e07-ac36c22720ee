import {ReportCriteriaDto} from 'ecco-dto/reports/ReportCriteriaDto';

/**
 * DOCUMENTATION on fields - see ReportCriteriaDto (and charts-dto/SelectionCriteria)
 */
class ReportCriteriaDtoImpl implements ReportCriteriaDto {

    serviceId: number;

    projectId: number;

    companyId: number;

    clientGroupId: number;

    serviceGroupId: number;

    serviceRecipientFilter: string;

    userId: number;
    username: string;

    isChild: boolean;

    questionnaireEvidenceGroup: string;
    questionnaireEvidenceGroupArr: string[];

    commandNameArr: string[];

    taskDefName: string;
    supportEvidenceGroup: string;

    customFormEvidenceGroup: string;

    buildingIds: number[] | undefined;

    geographicAreaIdSelected: number;

    geographicAreaIds: Array<number>;

    from: string;

    to: string;

    selectionPropertyPath: string;

    entityStatus: string;

    referralStatus: string;

    newReferralsOnly: boolean;

    includeRelated: boolean;

    public clone() : ReportCriteriaDtoImpl {
        var clone = new ReportCriteriaDtoImpl();
        clone.serviceId = this.serviceId;
        clone.projectId = this.projectId;
        clone.serviceRecipientFilter = this.serviceRecipientFilter;
        clone.userId = this.userId;
        clone.isChild = this.isChild;
        clone.supportEvidenceGroup = this.supportEvidenceGroup;
        clone.questionnaireEvidenceGroup = this.questionnaireEvidenceGroup;
        clone.customFormEvidenceGroup = this.customFormEvidenceGroup;
        clone.buildingIds = this.buildingIds;
        clone.from = this.from;
        clone.to = this.to;
        clone.selectionPropertyPath = this.selectionPropertyPath;
        clone.entityStatus = this.entityStatus;
        clone.referralStatus = this.referralStatus;
        clone.newReferralsOnly = this.newReferralsOnly;
        clone.includeRelated = this.includeRelated;
        clone.geographicAreaIdSelected = this.geographicAreaIdSelected;
        clone.geographicAreaIds = this.geographicAreaIds;
        return clone;
    }
}

export = ReportCriteriaDtoImpl;
