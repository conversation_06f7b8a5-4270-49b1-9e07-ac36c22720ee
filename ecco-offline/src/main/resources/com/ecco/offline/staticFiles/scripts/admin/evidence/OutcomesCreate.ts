import $ = require("jquery");

import ButtonInput = require("../../controls/ButtonInput");

import {OutcomeRepository} from "../../service-config/OutcomeRepository"
import OutcomesDisplay = require("./OutcomesDisplay");
import {Uuid} from "@eccosolutions/ecco-crypto";
import { ActionDto, ActionGroupDto, ActivityType, OutcomeDto, RiskActionDto } from 'ecco-dto';

/** This class is responsible for creating an input box and a button
* where the button triggers the posting of the outcome
*/
class OutcomesCreate {

    private readonly $createCtl: $.JQuery;
    private readonly $createResult: $.JQuery;
    private createButtonInput: ButtonInput;

    constructor(private repository: OutcomeRepository, $inputElement: $.JQuery) {
        // set the placeholders
        this.$createCtl = $("<div>").attr("id", "outcomeCreateControls");
        this.$createResult = $("<div>").attr("id", "outcomeCreateResults");
        $inputElement.append(this.$createCtl).append(this.$createResult);

        // creates the layout for saving outcomes
        this.createButtonInput = new ButtonInput("create", "textarea", (buttonInput) => this.onClickOutcomeCreate(buttonInput));
        this.createButtonInput.attach(this.$createCtl);
    }

    // register so we can control them after creation
    private onClickOutcomeCreate(buttonInput: ButtonInput): void {
        // populate a div with the result of outcome(s)->actions
        const outcomeTxt = buttonInput.getInputValue();
        const repoType = $('input[name=repoType]:checked').val();
        if (!repoType) {
            alert("select support or risk");
            return;
        }
        const outcome = this.outcomeTxtToObject(outcomeTxt, repoType);

        this.displayOutcome(outcome);

        // save the outcome to the server
        const onSuccess = () => {
            this.$createResult.empty();
            this.$createResult.append("success");
        };
        const onError = (error: any) => {
            alert("failed:" + error.toString());
        };

        outcome.uuid = Uuid.randomV4().toString();
        const query = "risk" === repoType
                    ? this.repository.createOutcomeRisk(outcome)
                    : this.repository.createOutcomeSupport(outcome);
        query.then(onSuccess, onError);
    }

    /** Converts a textarea input to an Outcome object to save
    * to mimic of OutcomeRiskAction.java which is reasonbly HORRENDOUS
    * but gets us to a place where we remove the webflow version
    * NB a difference to OutcomeRiskAction.java is the outcome is the first line
    */
    private outcomeTxtToObject(outcomeTxt: string, repoType: string): OutcomeDto {
        const outcome = new OutcomeImpl() as OutcomeDto;
        outcome.uuid = Uuid.randomV4().toString();

        const debug = true;
        const result = outcomeTxt.split('\n');
        let currentGroup: ActionGroupDto | null = null; // null when moving on to new risk after blank line
        for (let x=0; x < result.length; x++) {
            const line: string = result[x];
            //line = StringUtils.chomp(line);
            if (debug) {
                console.log("line: " + x + ": " + line);
            }

            // if we get a label line, detect the outcome.id
            if (line.indexOf("---- outcome: ") == 0) {
                const segments = line.split(" ");
                outcome.id = parseInt(segments[2]);
                // TODO: check we're submitting this .. prob want a UUID really
            }
            else if (outcome.name == null) {
                outcome.name = line;
                if (debug)
                    console.log("\tidentified as outcome");
            }
            else if (currentGroup == null) {
                const r = new ActionGroupImpl() as ActionGroupDto;
                r.uuid = Uuid.randomV4().toString();
                r.name = line;
                outcome.actionGroups.push(r);
                currentGroup = r;
                if (debug)
                    console.log("\tidentified as actiongroup");
            }
            else {
                if (line == "") {
                    currentGroup = null;
                } else {
                    if (debug)
                        console.log("\tidentified as action");
                    let a = "risk" === repoType
                        ? new RiskActionImpl() as ActionDto
                        : new ActionImpl() as ActionDto;
                    a.uuid = Uuid.randomV4().toString();
                    a.name = line;
                    currentGroup.actions.push(a);
                }
            }
        }
        return outcome;
    }

    private displayOutcome(outcome: OutcomeDto) {
        // display the outcome (as we would a 'find' outcome)
        this.$createResult.empty();
        const display = new OutcomesDisplay();
        const outcomes: OutcomeDto[] = new Array();

        outcomes.push(outcome);
        const html = display.render(outcomes);
        this.$createResult.append(html);
    }

}

class OutcomeImpl implements Partial<OutcomeDto> {
    id?: number;
    uuid?: string;
    name?: string;
    actionGroups = new Array<ActionGroupDto>();
}
class ActionGroupImpl implements Partial<ActionGroupDto> {
    id?: number;
    name?: string;
    actions = new Array<ActionDto>();
    orderby = 0; // default to zero. risks are ordered by orderby,id
}
class ActionImpl implements Partial<ActionDto> {
    id?: number;
    name?: string;
    activityTypes = new Array<ActivityType>();
    orderby = 0; // default to zero. Actions are ordered by orderby,id
}
class RiskActionImpl implements Partial<RiskActionDto> {
    id?: number;
    name?: string;
    orderby = 0; // default to zero. Actions are ordered by orderby,id
}

export = OutcomesCreate;
