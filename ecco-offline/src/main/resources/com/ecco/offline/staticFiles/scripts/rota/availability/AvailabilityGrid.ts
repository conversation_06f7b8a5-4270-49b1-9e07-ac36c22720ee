import $ = require("jquery");

import ElementRow = require("../../controls/ElementRow");
import ElementGrid = require("../../controls/ElementGrid");
import TableSelectCellsHandler = require("../../controls/TableSelectCellsHandler");
import Model = require("./availability-model");
import {EccoDate} from "@eccosolutions/ecco-common";
import BaseControl = require("../../controls/BaseControl");
import {AjaxService} from "./availability-ajax";


export class AvailabilityGrid extends BaseControl implements ElementGrid {

    private static minutesPerPeriod = 30;
    private static numPeriods = 24 * 60 / AvailabilityGrid.minutesPerPeriod;

    private model: Model;
    private selectHandler: TableSelectCellsHandler;

    private $elementRows: ElementRow[] = [];

    constructor(private service: AjaxService) {
        super();
        this.model = new Model(service);
        this.selectHandler = new TableSelectCellsHandler(this);
    }

    /**
     * Attach self into container ready to update from model when requested
     */
    public override attach($container: $.JQuery) {
        super.attach($container);

        this.element().empty()
            .append($("<div>").text("Please select a worker"));
    }

    public renderTable() {
        const $table = $("<table>").addClass("center")
            .attr("id", "worker-availability")
            .mousedown({action: "down"}, (event) => this.tableMouseEventHandler(event))
            .mouseup({action: "up"}, (event) => this.tableMouseEventHandler(event))
            .append($("<thead>")
                        .append(this.$tableHeadRow()))
            .append(this.$tableBody());
        this.element().empty()
            .append($table);
    }

    private $tableHeadRow(): $.JQuery {
        const $headTr = $("<tr>")
            .append($("<th>")
                        .addClass("time-headings")
                        .addClass("time-night") // to match what's next to it
                        .text("Day / Time"));

        let periodStartTime = this.model.startTime();

        for (let i = 0; i < AvailabilityGrid.numPeriods; ++i) {
            const dawn = 6 * AvailabilityGrid.numPeriods / 24;
            const dusk = 18 * AvailabilityGrid.numPeriods / 24;
            const dayNightClass =
                (i < dawn || i > dusk) ? "time-night" :
                    (i == dawn) ? "time-night-to-day" :
                        (i == dusk) ? "time-day-to-night" : "time-day";

            $headTr.append($("<th>")
                    .attr("id", "time-period-" + i)
                    .addClass("time-headings")
                    .addClass(dayNightClass)
                    .append($("<span>")
                            .text(periodStartTime.formatHoursMinutes())));

            periodStartTime = periodStartTime.addMinutes(AvailabilityGrid.minutesPerPeriod);
        }

        return $headTr;
    }

    private $tableBody(): $.JQuery {
        const $tbody = $("<tbody>");

        // empty what we did have
        this.$elementRows = [];

        for (let dayStartTime = this.model.startTime();
             dayStartTime.earlierThan(this.model.endTime());
             dayStartTime = dayStartTime.addDays(1)) {

            const $tr = $("<tr>")
                .append($("<th>")
                            .text(dayStartTime.formatDatePretty()));

            if (dayStartTime.isWeekend()) {
                $tr.addClass("weekend");
            }

            if (dayStartTime.earlierThan(EccoDate.todayLocalTime().toDateTimeMidnight())) {
                $tr.addClass("beforeToday");
            }

            // TODO: ElementRow could be more intelligent, .. as it is a Day with a start and end time, poss with +/- 1 hour
            const row = new ElementRow();
            this.$elementRows.push(row);

            for (let i = 0; i < AvailabilityGrid.numPeriods; ++i) {
                const availabilityStartTime = dayStartTime.addMinutes(i * AvailabilityGrid.minutesPerPeriod);
                const available = this.model.isAvailable(availabilityStartTime, AvailabilityGrid.minutesPerPeriod);

                const status = available
                    ? "available"
                    : "unavailable";

                const $td = $("<td>")
                    .addClass(status)
                    .mouseenter({action: "enter"}, (event) => this.tableMouseEventHandler(event));

                $tr.append($td);
                row.push($td);
            }

            $tbody.append($tr);

        }

        return $tbody;
    }

    private tableMouseEventHandler(event) {
        event.preventDefault();

        if (event.buttons == 0) {
            if (event.data.action != "enter") { // TODO DOES THIS EVER HAPPEN?
                console.debug(event.data.action + " " + event.buttons);
            }
            return; // not interested
        }

        const target = event.target;
        const action = event.data.action;

        if (target.cellIndex == null) {
            this.selectHandler.cancel();
            console.debug("cellIndex == null when action = " + action);
            return;
        }

        // Subtract 1 as we don't have first row and first column in the table
        const col = target.cellIndex - 1;
        const row = target.parentElement.rowIndex - 1;

        if (row < 0 || col < 0) {
            this.selectHandler.cancel();
            return;
        }

        switch (action) {
        case "down":
            this.selectHandler.dragStart(row, col, event.shiftKey);
            break;
        case "enter":
            this.selectHandler.dragExtend(row, col);
            break;
        case "up":
            this.selectHandler.dragFinish(row, col);
            break;
        }
    }


    private addCellsAvailable(startOffsetMillisForRow, firstCol, lastCol) {
        const start = startOffsetMillisForRow + firstCol * AvailabilityGrid.minutesPerPeriod * 60000;
        const end = startOffsetMillisForRow + (lastCol + 1) * AvailabilityGrid.minutesPerPeriod * 60000;
        this.model.addAvailable(start, end);
    }

    private updateModelWithAvailablesFromRow(elementRow: ElementRow, startOffsetMillisForRow) {
        let firstAvailCol = null;
        let col = 0;
        elementRow.withEachElement( (cell) => {
            const avail = cell.hasClass("available") && !cell.hasClass("to-unavailable") || cell.hasClass("to-available");
            if (avail && firstAvailCol == null) {
                firstAvailCol = col;
            }
            else if (!avail && firstAvailCol != null) {
                this.addCellsAvailable(startOffsetMillisForRow, firstAvailCol, col - 1);
                firstAvailCol = null;
            }
            col++;
        });
        if (firstAvailCol != null) {
            this.addCellsAvailable(startOffsetMillisForRow, firstAvailCol, elementRow.length() - 1);
        }
    }

    /** pre-save */
    private updateToModel() {
        this.model.clearAvailability();
        let row = 0;
        this.withEachRow( (elementRow) => {
            this.updateModelWithAvailablesFromRow(elementRow, row * 24 * 3600000);
            row++;
        });
    }

    /** post-save */
    private updateFromModel() {
        let dayStartTime = this.model.startTime();

        this.withEachRow( (elementRow) => {
            elementRow.withEachElement( (element, col) => {
                const periodStartTime = dayStartTime.addMinutes(col * AvailabilityGrid.minutesPerPeriod);
                const available = this.model.isAvailable(periodStartTime, AvailabilityGrid.minutesPerPeriod);

                element.removeClass().addClass(available ? "available" : "unavailable");
            });

            dayStartTime = dayStartTime.addDays(1);
        });
    }

    public importPattern(periodInWeeks: number) {
        const prevMonthModel = new Model(this.service);
        prevMonthModel.selectCalendar(this.model.getCalendarId(),
            this.model.startTime().toEccoDate().subtractMonths(1),
            this.model.endTime().toEccoDate().subtractMonths(1))
            .then(() => {
                this.importFromModel(prevMonthModel, periodInWeeks)
            });
    }

    private importFromModel(prevMonthModel: Model, periodInWeeks: number) {
        let dayStartInstant = this.model.startTime();
        let importStartInstant = dayStartInstant;

        // A row is a day
        this.withEachRow( elementRow => {
            // If first day of month is Wed, then we want Wed->Tues n * period weeks ago such that it falls within last month
            if (importStartInstant.laterThanOrEqual(this.model.startTime())) {
                importStartInstant = importStartInstant.subtractDays(7 * periodInWeeks);
            }

            elementRow.withEachElement((element, col) => {
                const periodStartTime = importStartInstant.addMinutes(col * AvailabilityGrid.minutesPerPeriod);
                const available = prevMonthModel.isAvailable(periodStartTime, AvailabilityGrid.minutesPerPeriod);
                element.removeClass().addClass(available ? "available" : "unavailable");
            });

            dayStartInstant = dayStartInstant.addDays(1);
            importStartInstant = importStartInstant.addDays(1);
        });

    }

    public elementAt(row: number, col: number) {
        return this.$elementRows[row].elementAt(col);
    }

    public withEachRow(callback: (elementRow: ElementRow, row: number) => void) {
        for (let row = 0; row < this.$elementRows.length; row++) {
            const $theRow = this.$elementRows[row];
            callback($theRow, row);
        }
    }

    public withEachElement(callback: (element: $.JQuery) => void) {
        for (let row = 0; row < this.$elementRows.length; row++) {
            this.$elementRows[row].withEachElement( (element) => callback(element) );
        }
    }

    public loadCalendar(calendarId: string, startDate: EccoDate, endDate: EccoDate) {
        this.model.selectCalendar(calendarId, startDate, endDate)
            .then(() => {
            this.updateFromModel();
            this.renderTable();
        });
    }

    save() {
        this.updateToModel(); // So we transfer from view to model here, so if we load the view from a different model, we can import
        this.model.save();
        this.updateFromModel();
    }
}

