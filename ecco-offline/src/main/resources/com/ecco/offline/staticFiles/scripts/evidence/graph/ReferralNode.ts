
import {ServiceRecipient} from "ecco-dto/service-recipient-dto";
import ActionNode = require("./ActionNode");
import * as domain from "ecco-dto";
import dynamicTree = require("../../draw/dynamic-tree");
import * as evidenceDto from "ecco-dto/evidence-dto";
import SupportAction = evidenceDto.SupportAction;
import GraphContext = require("./GraphContext");
import OutcomeNode = require("./OutcomeNode");
import Node = dynamicTree.DynamicTreeNode;
import NodeProxy = require("./NodeProxy");

/** TODO: rename to ServiceRecipientNode */
class ReferralNode implements NodeProxy {

    private node: Node;
    private outcomeNodesById = new Map<number, OutcomeNode>();

    constructor(private recipient: ServiceRecipient, private context: GraphContext) {

        this.node = new Node(recipient && recipient.displayName.replace(" ","\n") || "(none)");
        this.node.addClickEventHandler( () => { this.referralClicked();} );
    }

    private referralClicked() {
        this.context.treeControl.setContextNode(this.node);
    }

    /** Add an action to the tree ensuring that parent outcome and action groups are added as necessary.
     */
    public addAction(action: domain.Action, supportAction: SupportAction): void {
        var outcome = action.actionGroup.outcome;
        this.getOutcomeNode(outcome).addAction(action, supportAction);
    }

    /** Get OutcomeNode for supplied def.  Create it if it didn't exist.
     */
    public getOutcomeNode(outcomeDef: domain.Outcome) {
        var node = this.outcomeNodesById.get(outcomeDef.getId());
        if (!node) {
            node = new OutcomeNode(outcomeDef, this.context);
            this.node.addChild(node.getNode());
            this.outcomeNodesById.set(outcomeDef.getId(), node);
        }
        return node;
    }

    public withAllLeafNodes( callback: (leaf: ActionNode) => void): void {
        for (const node of this.outcomeNodesById.values()) {
            node.withAllLeafNodes( callback );
        }
    }

    public getNode(): Node {
        return this.node;
    }
}
export = ReferralNode;