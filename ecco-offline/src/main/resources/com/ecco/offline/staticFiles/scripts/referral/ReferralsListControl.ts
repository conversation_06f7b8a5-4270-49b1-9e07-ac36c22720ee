import $ = require("jquery");
import bloodhound = require("bloodhound");
import events = require("../common/events");
import services = require("ecco-offline-data");
import SearchableListControl = require("../controls/SearchableListControl");
import SearchableListControlOptions = require("../controls/SearchableListControlOptions");
import SummarisedElement = require("../controls/SummarisedElement");
import Bloodhound = bloodhound.Bloodhound;
import {ReferralSummaryControl} from "./ReferralSummaryControl"
import {withAuthErrorHandler} from "ecco-offline-data";
import {ReferralDto, SessionData} from "ecco-dto";
import {ReportAjaxRepository} from "ecco-reports";
import BaseControl from "../controls/BaseControl";
import RadioGroupInput from "../controls/RadioGroupInput";
import {groupBy} from "lodash";
import {Referrals} from "./components/ReferralActionsCard";
import ListContainerControl from "../controls/data/ListContainerControl";

class ReferralsListControlOptions implements SearchableListControlOptions<Referrals> {

    private sessionData: SessionData;

    constructor(private myReferrals: boolean,
                private buildingId: number,
                private forceOnline: boolean,
                private liveReferralsQ?: Promise<ReferralDto[]>) {
    }

    public placeholderText = "e.g. r-id or client name";

    /** Create a control for the given item */
    public createControl(items: Referrals): SummarisedElement<Referrals> {
        return new ReferralSummaryControl(this.sessionData, items, this.forceOnline);
    }

    public loadInner(callback: (items: Referrals[]) => void): void {
        const sessionDataQ = services.getFeatureConfigRepository().getSessionData();

        const referralsQ = this.buildingId
            ? services.getReferralRepository().findAllReferralsInsideBuilding(this.buildingId)
            : this.myReferrals
                ? services.getReferralRepository().findAllReferralsForOffline()
                // TODO: liveReferralsQ should be removed
                : this.liveReferralsQ || services.getReferralRepository().findAllReferralSummary(ReportAjaxRepository.generateLiveReportCriteria())

        const qry = Promise.all([sessionDataQ, referralsQ])
            .then(([sd, r]) => {
                this.sessionData = sd;
                return r;
            });
        withAuthErrorHandler(qry).then( items => {
            const byClient = groupBy(items, item => item.clientId)
            callback(Object.values(byClient).map(referrals => new Referrals(referrals))) // sort items by clientId
        });
    }

    public generateKey(items: Referrals) {
        return String(items.referrals[0].clientId);
    }

    public generateSearchConfig(items: Referrals[]) {
        return {
            datumTokenizer: (referrals: Referrals) => {
                var result = new Array<string>();
                // TODO this forces the search for the start character - which misses nicknames with a '('
                //
                referrals.referrals.forEach(r => result.push(r.referralCode || r.referralId.toString()));
                if (referrals.referrals[0].clientDisplayName) {
                    result = result.concat(Bloodhound.tokenizers.whitespace(referrals.referrals[0].clientDisplayName));
                }
                return result;
            },
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: items,
            limit: 10
        };
    }
}

/** Searchable Referrals list, built on top of SearchableListControl */
type MeOrTeam = "my clients" | "team clients";
export class ReferralsListControl extends SearchableListControl<Referrals> {

    public static fromBuildingSrId(srId: number): Promise<ReferralsListControl> {
        return services.getBuildingRepository().findOneBuildingBySrId(srId).then(bldg => {
            return new ReferralsListControl(false, bldg.buildingId, true);
        });
    }

    private meOrTeamValue: MeOrTeam;

    constructor(myReferrals: boolean,
                private buildingId: number = null,
                private forceOnline = false,
                private liveReferralsQ?: Promise<ReferralDto[]>) {
        super(ListContainerControl, new ReferralsListControlOptions(myReferrals, buildingId, forceOnline, liveReferralsQ));

        this.meOrTeamValue = myReferrals ? "my clients" : "team clients";
        ReferralsListControl.renderTitle(myReferrals, buildingId);
    }

    protected override getParentForm(): Element {
        return this.forceOnline && !this.buildingId
            ? this.createRadioOptions().domElement()
            : null;
    }

    private static renderTitle(myReferrals: boolean, buildingId: number) {
        const label = buildingId
                ? "buildings"
                : myReferrals ? "my live referrals" : "live referrals";
        const $menu = $("<div>").css({"text-align": "center"}).text(label);
        events.MenuUpdateEvent.bus.fire(new events.MenuUpdateEvent("nav", $menu));
    }

    private myOrTeamChange(val: MeOrTeam) {
        this.meOrTeamValue = val;
        val == "my clients"
            ? this.reload(new ReferralsListControlOptions(true, this.buildingId, this.forceOnline, this.liveReferralsQ))
            : this.reload(new ReferralsListControlOptions(false, this.buildingId, this.forceOnline, this.liveReferralsQ));
    }

    private createRadioOptions(): BaseControl {
        const grp = new RadioGroupInput("toggleMeTeam", "radio-inline");

        grp.change((val: MeOrTeam) => {
            return this.myOrTeamChange(val);
        });

        grp.populateFromList(
            ["my clients", "team clients"],
            (item: string) => {
                return {
                    id: `radioId-${item}`,
                    label: item,
                    value: item,
                    selected: item === this.meOrTeamValue
                };
            }
        );

        return grp;
    }

}
export default ReferralsListControl;
