import {ClientAttendanceDto, GroupActivityDto, SessionData} from "ecco-dto";
import {GroupActivityClientListControl} from "./GroupActivityClientListControl";
import {GroupActivityClientRowControl} from "./GroupActivityClientListControl";
import {GroupActivityOptions} from "./GroupActivityList";

export class GroupSupportActivityClientListControl extends GroupActivityClientListControl {

    protected getHeaders() {
        let headings = [];
        headings.push("client");
        headings.push("invited");

        if (!this.activity.course) {
            headings.push("attending");
            headings.push("attended");
            //headings.push("surveys");
            headings.push("support note");
        }

        // TODO we've orphaned this table, so this is useless currently
        /*if (this.activity.activityType.linkedQuestionGroups
                && this.activity.activityType.linkedQuestionGroups.length > 0) {
            headings.push("surveys");
        }*/

        return headings;
    }

    protected createRow(clientAttendance: ClientAttendanceDto) {
        return new SupportClientRowControl(this.sessionData, this.activity, this.options, clientAttendance, this.printable, () => this.$saveButton.prop("disabled", false));
    }
}


class SupportClientRowControl extends GroupActivityClientRowControl {

    constructor(sessionData: SessionData, activity: GroupActivityDto, options: GroupActivityOptions,
                clientAttendance: ClientAttendanceDto, printable: boolean,
                onChangeCallback: () => void) {
        super(sessionData, activity, options, clientAttendance, printable, onChangeCallback);
    }

}
