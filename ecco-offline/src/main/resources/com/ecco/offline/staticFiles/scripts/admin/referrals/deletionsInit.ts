import $ = require("jquery");
import ActionButton = require("../../controls/ActionButton");
//import ReferralsSearch = require("./ReferralsSearch");
import ButtonInput = require("../../controls/ButtonInput");
import ResultTable = require("../../controls/ResultTable");
import {apiClient} from "ecco-components";
import {ClientAjaxRepository, ReferralAjaxRepository} from "ecco-dto";

const referralRepository = new ReferralAjaxRepository(apiClient);
const clientRepository = new ClientAjaxRepository(apiClient);


class DeleteReferralClients {

    /** <PERSON>ton which kicks off attach() once only */
    private static instance = new DeleteReferralClients();

    constructor() {
        $( () => {
            this.attach();
        });
    }

    /** Find the different items we support and attach the appropriate component to each of them */
    private attach() {
        this.enhancePage();
    }

    private enhancePage() {

        const table = new ResultTable(
            ["referralId", "clientId", "referredServiceName", "clientFirstName", "clientLastName", "firstName", "lastName", "statusMessageKey"],
            "table-bordered")
            .cellRenderer("referralId", ResultTable.referralIdCellRenderer)
            .cellRenderer("clientId", ResultTable.clientLinkCellRenderer);
        table.attach($("#results"));
        table.populate([{'referralId': '', 'clientId': '', 'referredServiceName': '', 'clientFirstName': '',
            'clientLastName':'', 'firstName':'','lastName':'', 'statusMessageKey': ''}]);

        const tableRelationships = new ResultTable(
            ["referralId", "clientId", "clientDisplayName", "relationship"],
            "table-bordered")
            .cellRenderer("referralId", ResultTable.referralIdCellRenderer);
        tableRelationships.attach($("#resultsRelationships"));
        tableRelationships.populate([{'referralId': '', 'clientId': '', 'clientDisplayName': '', 'relationship': ''}]);

        const search = new ButtonInput("find r-id", "text", input => {
            const referralId = parseInt(input.getInputValue());
            referralRepository.findOneReferral(referralId)
                .then(referral => {
                    table.populate([referral]);
                    this.findAndPopulateRelationships(referral.referralId, tableRelationships);
                })
                .catch(reason => alert("failed: " + reason));
        }, "btn-primary");
        $("#referralSearch").append(search.element());

        const searchCode = new ButtonInput("find r-id", "text", input => {
            const referralCode = input.getInputValue();
            referralRepository.findOneReferralByCode(referralCode)
                .then(referral => {
                    table.populate([referral]);
                    this.findAndPopulateRelationships(referral.referralId, tableRelationships);
                })
                .catch(reason => alert("failed: " + reason));
        }, "btn-default");
        searchCode.attach($("#referralSearchCode"));

        const searchAllHidden = new ActionButton('find requests')
            .addClass("btn-default")
            .autoDisable(false)
            .click(() => {

                // We maintain the 'hidden' property on the request delete handler so this doesn't break
                // Once non-cmd referrals are deleted and only commands are in use, this can be replaced with a 'findAllDeleteRequests'
                return referralRepository.findAllHiddenReferrals()
                    .then(referrals => {
                        table.populate(referrals);
                    });
            });
        searchAllHidden.element().appendTo($("#referralSearchHidden"));

        const deleteReferralInput = new ButtonInput("delete referral", "text", input => {
            const clientId = parseInt(input.getInputValue());
            const referralId = parseInt(search.getInputValue());
            referralRepository.deleteByIdWithClientId(referralId, clientId)
                .catch(reason => alert("failed: " + reason.reason.message))
                .then(() => alert("deleted referral : " + referralId));
        });
        deleteReferralInput.attach($("#deleteReferral"));

        const deleteClientInput = new ButtonInput("delete client", "text", input => {
            const lastName = input.getInputValue();
            const clientId = parseInt(deleteReferralInput.getInputValue());
            clientRepository.deleteByIdWithLastName(clientId, lastName)
                .catch(reason => {
                    alert("failed: " + reason.reason.message);
                    throw reason;
                })
                .then(() => alert("deleted client : " + clientId));
        });
        deleteClientInput.attach($("#deleteClient"));

        const moveClientInput = new ButtonInput("move", "text", input => {
            const clientId = parseInt(input.getInputValue());
            const referralId = parseInt(search.getInputValue());
            referralRepository.moveReferralToClient(referralId, clientId)
                .catch(reason => {
                    alert("failed: " + reason.reason.message);
                    throw reason;
                })
                .then(() => alert("moved to client : " + clientId));
        });
        moveClientInput.attach($("#moveToClient"));
    }

    private findAndPopulateRelationships(referralId: number, relationships: ResultTable) {
        relationships.empty();
        referralRepository.findRelatedReferrals(referralId)
            .then(relatedReferrals => {
                if (relatedReferrals) {
                    relationships.populate(relatedReferrals);
                }
            })
            .catch(reason => alert("failed: " + reason) );
    }
}
