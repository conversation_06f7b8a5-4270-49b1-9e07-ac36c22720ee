import $ = require("jquery");

import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import CheckboxGroupInput = require("../controls/CheckboxGroupInput");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import SelectList = require("../controls/SelectList");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import ServiceType = domain.ServiceType;
import {apiClient} from "ecco-components";
import * as domain from "ecco-dto";
import {ServiceAjaxRepository, ServiceTypeAjaxRepository} from "ecco-dto";
import {ProjectDto, ServiceDto} from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";

const repository = new ServiceAjaxRepository(apiClient);
const serviceTypeRepository = new ServiceTypeAjaxRepository(apiClient);

class BackingData {
    constructor(public serviceWithRelated: ServiceDto,
            public projects: ProjectDto[],
            public services: ServiceDto[],
            public serviceTypes: ServiceType[]) {
    }
}

class EditServiceForm extends BaseAsyncCommandForm<BackingData> {

    public static showInModal(serviceId: number) {
        var form = new EditServiceForm(serviceId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("service name");
    private origDto: ServiceDto;
    private serviceTypeSelect: SelectList;
    private $projectsList: $.JQuery;
    private projectsCheckboxes: CheckboxGroupInput;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private serviceId: number) {
        super(!serviceId ? "add new service" : "edit service");

        let nameGroup = new InputGroup("service name", this.name);
        nameGroup.enableValidation();
        this.form.append( nameGroup );

        this.serviceTypeSelect = new SelectList("service type");
        let slGroup = new InputGroup("service type", this.serviceTypeSelect);
        slGroup.enableValidation();
        this.form.append( slGroup );

        this.projectsCheckboxes = new CheckboxGroupInput();
        let $rowsPrj = $("<div>").css("text-align", "center").text("projects").append(
                    $("<div>").append(this.projectsCheckboxes.element()));
        this.$projectsList = $("<div>").addClass("form-group").append($rowsPrj);
        this.form.append(this.$projectsList);
    }

    protected fetchViewData(): Promise<BackingData> {
        var serviceWithRelatedDtoQ: Promise<ServiceDto> = Promise.resolve(null);
        if (this.serviceId) {
            serviceWithRelatedDtoQ = repository.findOneServiceDto(this.serviceId);
        }

        var servicesWithProjectsQ = repository.findAllServicesProjects();
        var projectsQ = repository.findAllProjects();

        return Promise.all([serviceWithRelatedDtoQ, servicesWithProjectsQ, projectsQ])
            .then(
            ([serviceWithRelated, servicesWithProjects, allProjects]) =>
                serviceTypeRepository.findAllServiceTypes()
                    .then(serviceTypes =>  new BackingData(serviceWithRelated, allProjects, servicesWithProjects, serviceTypes))
        );
    }

    protected render(data: BackingData) {
        this.origDto = data.serviceWithRelated;

        if (this.origDto) {
            this.name.setVal(data.serviceWithRelated.name);
        }

        this.serviceTypeSelect.populateFromList(data.serviceTypes,
            (st) => ({ key: st.getId().toString(), value: st.getName()}),
            (st) => this.origDto && this.origDto.serviceTypeId == st.getId());

        if (this.origDto) {
            this.serviceTypeSelect.setReadOnly();
        }

        this.populateProjectsBasedOnServiceDto(data);

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }

    protected override submitForm(): Promise<void> {
        if (!this.form.isValid()) {
            return Promise.reject<void>("validation failed - please check required fields");
        }

        let cmd;
        const serviceTypeId = parseInt(this.serviceTypeSelect.val());

        if (this.origDto) {
            cmd = new commands.ServiceCommand("update", this.origDto.id, null) // handler ignores servicetypeId on update
                .changeName(this.origDto.name, this.name.val())
                .setProjectsToAdd(this.newProjects());
        }
        else {
            cmd = new commands.ServiceCommand("add", null, serviceTypeId)
                .changeName(null, this.name.val())
                .setProjectsToAdd(this.newProjects());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

    private populateProjectsBasedOnServiceDto(data: BackingData) {
        var allProjects = data.projects;

        var assignedProjectIds: number[] = this.origDto == null ?
                                [] :
                                this.origDto.projects.map((p) => p.id);

        this.projectsCheckboxes.populateFromList(allProjects,
            (project) => ({
                value: project.id.toString(),
                id: "project_".concat(project.id.toString()),
                label: project.name,
                selected: assignedProjectIds.indexOf(project.id) > -1,
                readOnly: assignedProjectIds.indexOf(project.id) > -1}));
    }

    private newProjects() {
        var allProjectIds = this.projectsCheckboxes.checked()
                                .map((val) => parseInt(val));
        var newProjectIds: number[] = this.origDto == null ?
                                allProjectIds :
                                allProjectIds
                                    .filter((p) => {
                                        return this.origDto.projects.map((p) => p.id)
                                            .indexOf(p) == -1;
                                    });
        return newProjectIds;
    }

}

export = EditServiceForm;
