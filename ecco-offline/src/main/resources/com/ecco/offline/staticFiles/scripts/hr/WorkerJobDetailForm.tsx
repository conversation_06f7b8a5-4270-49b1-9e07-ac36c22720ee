import * as React from "react"
import {FC} from "react"
import {EccoDate} from "@eccosolutions/ecco-common";
import {Grid} from "@eccosolutions/ecco-mui";

import {CommandQueue, CommandSource, WorkerJobDetailCommand} from "ecco-commands";
import {
    AsyncServiceRecipientWithEntities,
    CommandSubform,
    ModalCommandForm,
    ServiceRecipientWithEntitiesContext,
} from "ecco-components";
import {datePickerIso8601Input, numberInput} from "ecco-components-core";
import {SessionData} from "ecco-dto";
import {TaskWithTitle} from "ecco-dto/workflow-dto";

const WorkerJobDetailForm: FC<{serviceRecipientId: number; task: TaskWithTitle; onSaved: () => void}> = ({serviceRecipientId, task, onSaved}) => {

    return (
        <ModalCommandForm
            show={true}
            setShow={() => onSaved()}
            title={"edit job"}
            action="save"
            maxWidth="sm"
        >
            {form => <AsyncServiceRecipientWithEntities.Resolved>
                {(context: ServiceRecipientWithEntitiesContext) =>
                    <WorkerJobSubform
                        serviceRecipientWithEntities={context}
                        sessionData={context.serviceRecipient.features}
                        taskHandle={task.taskHandle}
                        commandForm={form}
                    />
                }
            </AsyncServiceRecipientWithEntities.Resolved>}
        </ModalCommandForm>);
};

interface Props {
    serviceRecipientWithEntities: ServiceRecipientWithEntitiesContext
    sessionData: SessionData
    taskHandle: string;
}

interface State {
    code: string,
    contractedWeeklyHours: number,
    startDate: string,
    endDate: string
}

function isEmpty(str: any) {
    return !str;
}

class WorkerJobSubform extends CommandSubform<Props, State> implements CommandSource {

    constructor(props) {
        super(props);
        this.state = {
            code: this.props.serviceRecipientWithEntities.workerJob.code,
            contractedWeeklyHours: this.props.serviceRecipientWithEntities.workerJob.contractedWeeklyHours,
            startDate: this.props.serviceRecipientWithEntities.workerJob.startDate,
            endDate: this.props.serviceRecipientWithEntities.workerJob.endDate
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const initStart = this.props.serviceRecipientWithEntities.workerJob.startDate;
        const initEnd = this.props.serviceRecipientWithEntities.workerJob.endDate;
        const cmd = new WorkerJobDetailCommand(this.props.serviceRecipientWithEntities.serviceRecipient.serviceRecipientId, this.props.taskHandle);
        cmd.changeCode(this.props.serviceRecipientWithEntities.workerJob.code, this.state.code);
        cmd.changeStartDate(initStart ? EccoDate.parseIso8601(initStart) : null, this.state.startDate ? EccoDate.parseIso8601(this.state.startDate) : null);
        cmd.changeEndDate(initEnd ? EccoDate.parseIso8601(initEnd) : null, this.state.endDate ? EccoDate.parseIso8601(this.state.endDate) : null);
        cmd.changeContractedWeeklyHours(this.props.serviceRecipientWithEntities.workerJob.contractedWeeklyHours, this.state.contractedWeeklyHours);

        if(cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    getErrors(): string[] {
        return ['startDate'].reduce( (errors, field) => {
            if (isEmpty(this.state[field])) {
                errors.push(`${field} is required`);
            }
            if (this.state.startDate && this.state.endDate) {
                if (EccoDate.parseIso8601(this.state.startDate).laterThan(EccoDate.parseIso8601(this.state.endDate))) {
                    errors.push(`start after end`);
                }
            }
            return errors;
        }, []);
    }

    override render() {

        let stateSetter = state => this.setState(state);

        return <Grid container>
                <Grid item xs={12}>
                    {datePickerIso8601Input("startDate", "start", stateSetter, this.state, undefined, true)}
                </Grid>
                <Grid item xs={12}>
                    {datePickerIso8601Input("endDate", "end", stateSetter, this.state, undefined, false)}
                </Grid>
                <Grid item xs={12}>
                    {numberInput("contractedWeeklyHours", "contracted hrs (weekly)", stateSetter, this.state, undefined, 2)}
                </Grid>
            </Grid>;
    }

}
export default WorkerJobDetailForm;
