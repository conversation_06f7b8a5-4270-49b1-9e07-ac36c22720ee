import $ = require("jquery");

import ActionButton = require("../controls/ActionButton");
import AddGoalForm = require("./smartsteps/AddGoalForm");
import ElementContainer = require("../controls/ElementContainer");
import events = require("./events");
import EvidenceCommentForm = require("./EvidenceCommentForm");
import Form = require("../controls/Form");
import HtmlElement = require("../controls/HtmlElement");
import View = require("../controls/View");
import EvidenceEmbeddedSwitcherControl = require("./EvidenceEmbeddedSwitcherControl");
import SessionData = featureDomain.SessionData;
import * as commands from "ecco-commands";
import {
    Command,
    CommandQueue,
    QuestionAnswerCommand,
    Signature,
    SignWorkCommand,
    WorkUuidResolver
} from "ecco-commands";
import {EvidenceDef} from "ecco-evidence";
import * as featureDomain from "ecco-dto";
import {ConfigResolver, EvidencePageType, EvidenceUpdateEvent, ServiceRecipientWithEntities} from "ecco-dto";
import {getCommandQueueRepository, showErrorAsAlert} from "ecco-offline-data";
import {EvidenceControl} from "./evidenceControls";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ActivityType} from "ecco-dto/service-config-dto";
import DialogContent from "../controls/DialogContent";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import MultiEntryEvidenceForm from "./MultiEntryEvidenceForm";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {showEvidenceConfirmation} from "./EvidenceConfirmationStep";

abstract class BaseEvidenceForm implements View {

    private controlContainer = new ElementContainer("evidence-container")
        .append(new ElementContainer().addClass("row"));
    private readonly evidenceControl: EvidenceControl;
    protected commentControl: EvidenceCommentForm;
    private readonly commentControlMore: MultiEntryEvidenceForm;
    private form = new Form().addClass("row");
    private footer = new HtmlElement($("<span>"));
    protected workUuidResolver = () => this.getWorkUuid(); // presumably so that it can be retrieved lazily
    private doneButton: ActionButton;
    private readonly captureSignature: boolean;
    private readonly showConfirmation: boolean;

    /** this will be null if we shouldn't show the button */
    private readonly addGoalLabel: string;

    protected constructor(protected workUuidQResolver: WorkUuidResolver,
        protected commandQueue: CommandQueue,
        protected planAllocatedId: string,
        private embeddedSwitcher: EvidenceEmbeddedSwitcherControl,
        protected serviceRecipient: ServiceRecipientWithEntities,
        protected serviceRecipientActivityTypeInterest: ActivityType[],
        protected evidenceDef: EvidenceDef,
        private options: {addFormButtonLabel?: string},
        private _title: string,
        mainControlClasses?: string,
        additionalCommentControlClasses?: string,
        protected previousCommandQueue?: CommandQueue,
        protected eventId?: string) {

        if (!commandQueue) {this.commandQueue = new CommandQueue(getCommandQueueRepository());}

        const messages = this.serviceRecipient.features.getMessages()
        const taskName = evidenceDef.getTaskName();
        if (!taskName) { throw new Error("invalid taskName - we need a task to save the work against: " + taskName); }
        if (!evidenceDef.getEvidenceGroup()) { throw new Error("invalid evidenceGroup - what history pages is this page meant to show on: " + evidenceDef.getEvidenceGroup()); }
        if (!evidenceDef.getEvidencePageType()) { throw new Error("invalid evidence page type - what is this page meant to do: " + evidenceDef.getEvidencePageType()); }

        const serviceType = this.serviceRecipient.configResolver.getServiceType();

        if (serviceType.taskDefinitionSettingHasFlag(taskName, "showMenus", "addGoal")
                || this.forceAddGoal()) { // because this won't work without it
            this.addGoalLabel = this.options.addFormButtonLabel || "add " + messages["terminology.smartstep"];
        }

        this.captureSignature
            = serviceType.taskDefinitionSettingHasFlag(evidenceDef.getTaskName(), "previewOnSave", "previewAndSign");
        this.showConfirmation
            = serviceType.taskDefinitionSettingHasFlag(evidenceDef.getTaskName(), "previewOnSave", "previewOnly")
            || this.captureSignature;

        // Note we force wide for things like supportStaffNotes which don't have smart steps
        // or has the embedded switcher (so starts blank)
        const wide = serviceType.taskDefinitionSettingHasFlag(taskName, "commentWidth", "wide")
            || this.evidenceDef.getEvidencePageType() == EvidencePageType.commentsOnly
            || embeddedSwitcher != null;

        if (wide && additionalCommentControlClasses) {
            // remove this on wide, as it should not be required and is causing an issue on displaying
            // the outcome star (the star hides behind the comment area)
            // abs-top-right should not be required on wide settings, so we replace here to capture all
            additionalCommentControlClasses = additionalCommentControlClasses.replace(/abs-top-right/i, '');
        }

        this.controlContainer.addClass(mainControlClasses || "col-xs-12" + (wide ? "" : " col-lg-8 top-gap-15"));

        // NB the main/outcomes area is used to load flags - so do load it (into the commentForm) but don't show the outcomes if commentOnly
        this.evidenceControl = this.createMainControl();
        let commentOnly = this.evidenceDef.getEvidencePageType() == EvidencePageType.commentsOnly
                || this.serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(),  "showOutcomes", "none");
        if (!commentOnly) {
            this.controlContainer.setContent(this.evidenceControl);
        }

        this.commentControl = this.createCommentControl();
        this.commentControlMore = this.createCommentControlMore();
        if (additionalCommentControlClasses && this.commentControl) {
            this.commentControl.addClass(additionalCommentControlClasses);
        }
        this.populateForm();
        this.populateFooter();
    }

    protected forceAddGoal() {
        return false;
    }
    protected abstract createMainControl(): EvidenceControl;

    protected createCommentControl(): EvidenceCommentForm {
        return null;
    }

    protected createCommentControlMore(): MultiEntryEvidenceForm {
        return null;
    }

    private getWorkUuid(): Uuid {
        return this.workUuidQResolver.getWorkUuid();
    }

    public resetWorkUuid() {
        this.workUuidQResolver.resetWorkUuid();
    }

    public element() {
        return this.form.element();
    }

    public getFooter() {
        return this.footer.element();
    }

    private populateForm() {
        const serviceType = this.serviceRecipient.configResolver.getServiceType();
        const bottom = serviceType.taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(), "commentLocation", "bottom");

        const commentMore = this.commentControlMore
            ? this.commentControlMore.element().addClass("pull-right").css({marginBottom: "10px", width: "100%"})
            : $("<span>"); // empty

        if (!bottom && this.commentControl) {
            this.form.append(this.commentControl);
            this.form.append(commentMore);
        }
        if (this.evidenceDef.getEvidencePageType() != EvidencePageType.commentsOnly) {
            if (this.embeddedSwitcher) {
                this.form.append(this.embeddedSwitcher);
                this.evidenceControl.element().hide();
            }
            this.form.append(this.controlContainer);
        }
        if (bottom && this.commentControl) {
            this.form.append(this.commentControl);
            this.form.append(commentMore);
        }
    }

    /** Return items to show in a menu/title bar
    public headerItems() {
        if (this.commentControl) {
            return new ActionButton("show/hide comment form")
                .addClass("btn btn-sm btn-link pull-right")
                .autoDisable(false)
                .clickSynchronous( () => this.commentControl.toggleCollapse() );
        }
    }
    */
    private populateFooter() {
        if (this.addGoalLabel != null) {
            this.footer.append(
                new ActionButton(this.addGoalLabel)
                    .addClass("btn pull-left btn-default")
                    .autoDisable(false)
                    .clickSynchronous( () => this.showAddGoalForm() )
                );
        }

        if (this.commentControl) {
            this.doneButton = new ActionButton("save", "saving...")
                .addClass("btn btn-primary")
                .click( () => this.commentControl.submitForm().then( () => {
                    this.doneButton.enable();
                }) );
            this.footer.append(this.doneButton);
        }
    }

    protected showAddGoalForm() {
        showFormInModalDom(this.getAddGoalForm(this.serviceRecipient.features, this.serviceRecipient.configResolver));
    }

    /** This can be overridden for different goal types */
    protected getAddGoalForm(features: SessionData, configResolver: ConfigResolver): DialogContent {
        return new AddGoalForm(this.serviceRecipient, this.evidenceDef);
    }

    /**
     * For graph approach we need to apply these as we successfully send commands as a result of addGoal
     * and for tabular, we want to add them as a result of hitting submit on AddGoalForm (which does not save).
     * NB 'instanceof' relies on the commandQueue providing the Command - but this is often a result of 'adapt'
     * which generates a new instance of Command, and therefore 'instanceof' fails here.
     */
    private applyCommand(command: Command, dto: any) {
        console.info("BaseEvidenceControl cmd: %s", JSON.stringify(dto));

        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (command instanceof commands.GoalUpdateCommand) {
            events.GoalUpdateEvent.bus.fire(new events.GoalUpdateEvent(<commands.GoalUpdateCommand>command, dto));
            // Things like MultiInstanceListControl listen here
            EvidenceUpdateEvent.bus(command.getEvidenceGroupName())
                .fire(new EvidenceUpdateEvent(dto));
        }
        // This is based on replaying DTOs
        else if (command instanceof QuestionAnswerCommand) {
            const cmd = command as unknown as QuestionAnswerCommand; // FIXME: Remove cmd, and make QuestionAnswerCommand extend Command like the others
            events.QuestionAnswerUpdateEvent.bus.fire(new events.QuestionAnswerUpdateEvent(cmd, dto));
            EvidenceUpdateEvent.bus(cmd.getEvidenceGroupName())
                .fire(new EvidenceUpdateEvent(dto));
        }
        // This is based on replaying DTOs
        else if (command instanceof commands.MyPlanUpdateCommand) {
            EvidenceUpdateEvent.bus("my plan")
                .fire(new EvidenceUpdateEvent(dto));
        }
    }

    /** Allows the current content of a command queue to be applied - only really works where that command
     * queue is NOT being sent - as otherwise we expect the command queue to be flushed and therefore be empty.
     * In those scenarios updated will happen via events as added by addCommandQueueHandlers() */
    protected applyCommands(commandQueue: CommandQueue): Promise<void> {
        return commandQueue.getCommands()
            .then(commands => {this.applyCommandArray(commands);});
    }

    protected applyCommandArray(cmds: Command[]) {
        cmds.forEach(cmd => {
            this.applyCommand(cmd, cmd.toCommandDto());
        });
    }

    public title() {
        return this._title;
    }

    /**
     * Typically called from this.commentControl to save the whole evidence.
     * Moved here so that all evidence can participate with signing work.
     */
    public submitForm(): Promise<any> {

        if (this.isValid()) {
            if (this.showConfirmation) {
                return this.showConfirmationAndSubmit();
            }
            return this.submitAll();
        }
        else {
            alert("save not allowed: required information missing");
            return Promise.reject( new Error("Validation error") );
        }
    }

    /** True if required fields are set */
    protected abstract isValid(): boolean;

    protected afterSubmitted() {
    }

    protected getChangesAsCommands() {
        this.populateReturningCommandQueue();

        // this queue gets flushed
        let cmdQueue = new CommandQueue(getCommandQueueRepository());
        this.commentControl.emitChangesTo(cmdQueue);
        this.evidenceControl && this.evidenceControl.emitChangesTo(cmdQueue);
        return cmdQueue;
    }

    /**
     * Populate a commandQueue that will be used as the previousCommandQueue.
     * This is called every time the original command queue is, and exists separately
     * so as not to flush the commands
     */
    protected populateReturningCommandQueue() {
        this.commandQueue.clear();
        this.commentControl.emitChangesTo(this.commandQueue);
    }

    private submitAll(): Promise<void> {
        const cmdQueue = this.getChangesAsCommands();
        return this.submitQueue(cmdQueue);
    }

    // NB there is overlap now with BaseAsyncCommandForm (eg showNoticeAndFadeAway and events)
    // but its not worth refactoring with a further change to react
    private submitQueue(cmdQueue: CommandQueue): Promise<void> {
        return cmdQueue.flushCommands()
            .then(() => this.afterSubmitted())
            .catch( (e) => {
                showErrorAsAlert(e);
                throw e;
            });
    }

    /** Shows confirmation summary with the option to also capture signature, and "continue" or "cancel" buttons */
    private showConfirmationAndSubmit(): Promise<any> {

        return new Promise<void>(resolve => {

            const evidenceAsCmdQueue = this.getChangesAsCommands();

            const onSubmit = (svgXml: string | null) => {
                if (this.captureSignature && svgXml) {
                    const cmd = SignWorkCommand.create(this.serviceRecipient.serviceRecipientId,
                        this.evidenceDef.getEvidenceGroup(), this.evidenceDef.getTaskName(), [Uuid.parse(this.workUuidResolver().toString())],
                        Signature.create(svgXml, EccoDateTime.nowLocalTime()));
                    evidenceAsCmdQueue.addCommand(cmd);
                }
                this.submitQueue(evidenceAsCmdQueue).then(() => resolve(null));
            };

            showEvidenceConfirmation(this.serviceRecipient, evidenceAsCmdQueue, this.captureSignature, onSubmit, () => resolve(null))
        });
    }
}

export = BaseEvidenceForm;
