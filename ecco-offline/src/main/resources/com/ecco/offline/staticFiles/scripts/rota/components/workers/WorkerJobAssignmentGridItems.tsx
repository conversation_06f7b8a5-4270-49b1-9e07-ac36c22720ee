import * as React from "react";
import {ReactElement} from "react";
import {Activity, DemandResource} from "ecco-rota";
import {AllocateWorkerGridItem} from "./AllocateWorkerGridItem";
import {DeallocateWorkerJobsGridItem} from "./DeallocateWorkerJobsGridItem";
import {RecurringAllocateGridItem} from "./RecurringAllocateGridItem";
import {useMutable} from "ecco-components";

export interface Props {
    readonly activity: Activity;
    readonly showRecurring: boolean;
    readonly recurring: boolean;
    readonly onRecurringChange: (recurring: boolean) => void;
    readonly onAllocate: (worker: DemandResource) => void;
    readonly onDeallocate: (worker: DemandResource) => void;
}

/**
 * The actions (in a line/grid) on the activity bar.
 */
export function WorkerJobAssignmentGridItems(props: Props): ReactElement {
    useMutable(props.activity, [
        props.activity.activityAllocatedEventBus,
        props.activity.activityDeallocatedEventBus
    ]);

    // We take a copy of allocatedWorkers because the original array is mutable.
    const allocatedWorkerJobs = Array.from(props.activity.getAllocatedWorkerJobs());

    return <>
        {
            allocatedWorkerJobs.length === 0
                    ? <AllocateWorkerGridItem activity={props.activity}
                                              onAllocate={props.onAllocate}
                                              showRecurring={props.showRecurring}/>
                    : <DeallocateWorkerJobsGridItem allocatedWorkerJobs={allocatedWorkerJobs}
                                                    onDeallocate={props.onDeallocate}/>
        }
        {
            props.showRecurring && <RecurringAllocateGridItem recurring={props.recurring}
                                                              onRecurringChange={props.onRecurringChange}/>
        }
    </>;
}