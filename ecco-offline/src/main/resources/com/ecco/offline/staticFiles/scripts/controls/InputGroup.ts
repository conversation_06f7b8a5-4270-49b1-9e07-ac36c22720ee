import $ = require("jquery");
import HtmlElement = require("./HtmlElement");
import InputControl = require("./InputControl");
import {ValidationErrors, ValidationChecksBuilder} from "../common/validation";

interface ValidControl {
    isValid(): boolean;
}

interface ValidateControl {
    validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors): void;
}

class InputGroup extends HtmlElement {

    private $inputControl: $.JQuery;
    private $helpBlock: $.JQuery;
    private inputControl: InputControl; // May not be set
    private validControl: ValidControl;
    private validateControl: ValidateControl;
    protected $label: $.JQuery;
    private valid: boolean = true;
    private enabledValidation = false;
    private validationChecks: ValidationChecksBuilder = null;
    private errors: ValidationErrors = null;
    private callback: (valid: boolean) => void;

    /**
     * Component to throw components onto the page and be dealt with reasonably using bootstrap classes.
     * This isn't ideal if we want clarity in the layout, in which case use the inputControl directly.
     *
     * inputControl = Element or something <PERSON><PERSON><PERSON><PERSON> is happy to .append()
     * and take a guess if we can add .form-control (if its not div / span)
     * this works well for common input values but allows something more complex
     * to be passed in.
     *
     * If passing in a JQuery element for the label, it must have an attribute 'error-text'
     * to use when describing
     *
     * The parent container 'div' (via the super call) gets .form-group applied
     * and the id is applied to this
     */
    constructor(private label: string|$.JQuery|null,
            inputControl: any,
            private id?: string,
            private inline: boolean = false) {
        super(
            inline
                ? $("<span>")
                : $("<div>"));

        if (label != null && typeof label === "object") {
            if (!(<$.JQuery>label).attr("error-text")) {
                throw new Error("label must have an 'error-text' attribute");
            }
        }

        if ((<ValidControl>inputControl).isValid) {
            this.validControl = inputControl;
        }

        if ((<ValidateControl>inputControl).validate) {
            this.validateControl = inputControl;
            // We assume that we need a 'required' validation with enabledValidation()
            // Without this our ValidControl's which are also ValidateControl's
            // will not show any 'required' validation errors because ValidateControl
            // takes precedence in the validation.
            // However, this will not take part in any global error validation
            // since the errors object is created here - but we haven't until now
            // got a form level validation control, so whatever was being used before
            // will still work
            this.withValidationChecks(ValidationChecksBuilder.REQUIRED, new ValidationErrors(""));
        }

        if ((<InputControl>inputControl).element) {
            this.inputControl = inputControl;
            this.$inputControl = this.inputControl.element();
        }
        else {
            this.$inputControl = $(inputControl);
        }

        this.$helpBlock = $("<span>").addClass("help-block pull-right");
        this.$label = this.label && label != "" && $("<label>").append(this.label);
        super.element().empty();

        super.element()
            .attr("id", this.id)
            .addClass("form-group");

        if (this.$inputControl[0].nodeName != "DIV" && this.$inputControl[0].nodeName != "SPAN" ) {
            this.$inputControl.addClass("form-control");
        }

        if (!this.inline) {
            super.element()
                .append( this.$helpBlock );
        }
        super.element()
            .append( this.$label )
            .append( this.obtainInputToAppend() )
    }

    public override element() {
        return super.element();
    }

    public withLabelClasses(cssClasses: string) {
        this.$label.addClass(cssClasses);
        return this;
    }

    public setLabel(label: string) {
        if (typeof this.label === "string") {
            this.$label.text(label);
        } else {
            (<$.JQuery>this.label).text(label);
        }
        return
    }

    public getLabelName(): string {
        if (!this.label) {
            return null;
        }
        if (typeof this.label === "string") {
            return <string>this.label;
        }
        return (<$.JQuery>this.label).attr("error-text");
    }
    public getInputControl() {
        return this.$inputControl;
    }

    public obtainInputToAppend() {
        return this.$inputControl;
    }

    public isEnableValidation() {
        return this.enabledValidation;
    }

    public ensureValidation() {
        if (!this.enabledValidation) {
            this.enabledValidation = true;
            if (!this.inputControl.change) {
                this.enableValidation();
            }
        }
    }

    public disableValidation() {
        this.enabledValidation = false;
        this.valid = true;
        this.setHelp("");
    }

    /**
     * DEPRECATED - the input controls should have their own withValidationChecks method - and not be mixed in here
     */
    public withValidationChecks(checks: ValidationChecksBuilder, errors: ValidationErrors) {
        this.validationChecks = checks;
        this.errors = errors;
        this.errors.clearErrors(this.getLabelName());
        return this;
    }

    public withCallback(callbackIn: (valid: boolean) => void) {
        this.callback = callbackIn;
        return this;
    }

    public enableValidation() {
        this.enabledValidation = true;
        if (this.inputControl && this.inputControl.change) {
            this.inputControl.change( (val) => {
                this.validate(val);
            }, true);
        } else {
            this.$inputControl.on( "change", "input, select", (event) => {
                var val = $(event.target).val();
                this.validate(val);
            });
        }
        this.validate();
        return this;
    }

    public setHelp(htmlContent: string) {
        if (htmlContent) {
            super.element().addClass("has-error");
        }
        else {
            super.element().removeClass("has-error");
        }
        this.$helpBlock.html(htmlContent);
    }

    /** return the value of the contained input control */
    public val() {
        return this.$inputControl.val();
    }

    public isValid() {
        return this.valid;
    }

    public renderValidation() {
        this.validVars(this.errors.isValid());
    }

    public validateOverride(): boolean | null {
        return null;
    }

    // allow external validation trigger
    public triggerValidation() {
        this.validate();
    }

    /**
     * @param val the onChange value, if triggered by an onChange
     */
    private validate(val?: string) {
        var valid = false;

        if (this.validateControl) {
            // if we retain the errors object, then we need to CLEAR the errors
            // for the field before re-validating
            this.errors.clearErrors(this.getLabelName());
            this.validateControl.validate(this.getLabelName(), this.validationChecks, this.errors);
            valid = this.errors.isValid();

        } else if (this.validControl) {
            valid = this.validControl.isValid();

        } else {
            var value = typeof val === 'undefined' ?
                            this.$inputControl.find("input, select")
                                    .addBack("input, select").val()
                            : val;
            valid = value && value != "-1";
        }

        const validateOverride = this.validateOverride();
        valid = validateOverride == null ? valid : validateOverride;

        this.validVars(valid);

        if (this.callback) {
            this.callback(valid);
        }
    }

    private validVars(valid: boolean) {
        // double check we still are required
        if (this.enabledValidation) {
            if (valid) {
                this.valid = true;
                this.setHelp("");
            }
            else {
                this.valid = false;
                var msg = this.getFirstFailureMessage();
                this.setHelp(msg);
            }
        }
    }

    private getFirstFailureMessage() {
        if (this.errors == null) {
            return "required"; // originally, the only validation was 'required'
        }
        return this.errors.getFirstFailureMessage(this.getLabelName());
    }

}

export = InputGroup;