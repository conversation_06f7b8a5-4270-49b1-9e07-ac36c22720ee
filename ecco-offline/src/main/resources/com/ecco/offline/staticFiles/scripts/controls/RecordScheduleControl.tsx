import $ = require("jquery");
import * as React from "react";

import Element = require("./Element");
import {Uuid} from "@eccosolutions/ecco-crypto";

import {<PERSON><PERSON>, ButtonToolbar, Col, Row} from "react-bootstrap";
import {ClassAttributes} from "react";
import {ActionComponent} from "ecco-dto";
import {Icon} from "@eccosolutions/ecco-mui";
import {EvidenceContext} from "ecco-evidence";
import {SmartStepStatus} from "ecco-dto";
import {reactElementContainer} from "ecco-components-core";

interface RecordScheduleProps extends ClassAttributes<RecordScheduleForm> {
    controlUuid: string;
    context: EvidenceContext;
    actionDef: ActionComponent;
}
interface RecordScheduleState {
    activeId: number;
}

interface RecordStatus {
    name: string,
    id: number,
    iconClasses: string
}

class RecordScheduleForm extends React.Component<RecordScheduleProps, RecordScheduleState> {

    private entries: RecordStatus[];

    constructor(props: RecordScheduleProps) {
        super(props);

        this.state = {
            activeId: null
        };

        const listName = this.props.context.features.getActionChecksListNameSetting(this.props.actionDef,
                this.props.context.configResolver.getServiceType(), this.props.context.evidenceDef.getTaskName());
        this.entries = this.props.context.features.getListDefinitionEntriesByListName(listName)
            .filter(entry => !entry.getDisabled())
            .map(entry => ({name: entry.getDisplayName(), id: entry.getId(), iconClasses: entry.getIconClasses()}));
    }

    override render() {
        return (
            <div className="container-fluid v-gap-15">
                <Row>
                    <Col md={12} lg={10} lgOffset={1}>
                        <ButtonToolbar>
                        {this.entries.map(e =>
                            <Button key={this.props.controlUuid.concat("-btn").concat(e.id.toString())}
                                    active={this.state.activeId == e.id}
                                    onClick={() => this.setState({activeId: e.id})}>
                                {e.name}
                                <Icon className={e.iconClasses}></Icon>
                            </Button>
                        )}
                        </ButtonToolbar>
                    </Col>
                </Row>
            </div>
        );
    }

}

/** Allow schedule to be created and edited */
class RecordScheduleControl implements Element {
    private $container = $("<span>");

    private recordScheduleForm = React.createRef<RecordScheduleForm>();
    private mountPoint: HTMLElement = document.createElement("div");

    public constructor(
        context: EvidenceContext,
        actionDef: ActionComponent,
        key: Uuid) {

        const form = <RecordScheduleForm key={"rec-sched-".concat(key.toString())}
                                         controlUuid={key.toString()}
                                         ref={this.recordScheduleForm}
                                         context={context}
                                         actionDef={actionDef}/>;
        reactElementContainer(form, this.mountPoint);

        this.$container.append(this.mountPoint);
    }

    getStatus(): number {
        return this.recordScheduleForm.current.state.activeId ? SmartStepStatus.AchievedAndStillRelevant : null;
    }

    getStatusChangeReason(): number {
        return this.recordScheduleForm.current.state.activeId;
    }

    element(): $.JQuery {
        return this.$container;
    }

}
export default RecordScheduleControl;
