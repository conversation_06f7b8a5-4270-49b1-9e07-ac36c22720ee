import {SelectListOption} from "@eccosolutions/ecco-common";
import {
    calcValidationState,
    numberFromHtmlInput,
} from "ecco-components-core";
import {FormEventHandler, ReactNode} from "react";
import {Button, ButtonGroup, ControlLabel, FormControl, FormGroup, InputGroup} from "react-bootstrap";
import * as React from "react";

/*
 * This file is an attempt to isolate ourselves from the changing react-bootstrap API, and promote
 * our own standard ways of doing things, such that if we dropped react-bootstrap, we can use our own
 * components instead
 */

function idNameToOption(idName: SelectListOption) {
    return (
        <option key={idName.id}
                value={String(idName.id)}
                disabled={idName.disabled}
        >
            {idName.name}
        </option>);
}


interface AdditionalInputProps {
    labelClassName?: string | undefined,
    wrapperClassName?: string | undefined,
    clearClassName?: string | undefined
}

/**
 * Filter out disabled options that aren't the current value (so the current value displays)
 * NB We don't pass the current value in as the Client model refers to strings.
 * @param {number} currentValue to remain in the list so that the correct data is displayed for existing data
 * @return filtered list or empty list if indexes was null
 */
function getOptionsNotDisabled(indexes: SelectListOption[] | null, currentValue: number | null): SelectListOption[] {
    return indexes ? indexes.filter(item => !item.disabled || item.id == currentValue) : [];
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function dropdownList<STATE>(label: string,
                                    stateSetter: (newState: STATE) => void,
                                    state: STATE,
                                    propertyKey: Extract<keyof STATE, string>,
                                    valueArray: SelectListOption[],
                                    inputProps: AdditionalInputProps = {},
                                    placeholder?: string | undefined,
                                    disabled?: boolean | undefined,
                                    required = false): JSX.Element {
    return createDropdownList(propertyKey, label, placeholder,
        state[propertyKey] as unknown as number,
        value => {stateSetter({...state, [propertyKey]:value})},
        valueArray, inputProps, disabled,
        required);
}


export function createDropdownList(name: string,
                                   label: string,
                                   placeholder: string | undefined,
                                   value: number,
                                   onChange: (id: number | null) => void,
                                   valueArray: SelectListOption[],
                                   defaultInputProps: AdditionalInputProps,
                                   disabled?: boolean | undefined,
                                   required = false): JSX.Element {
    return (
        <SelectGroup
            {...defaultInputProps}
            name={name}
            label={label}
            value={value == null ? "" : String(value)}
            onChange={event => onChange(numberFromHtmlInput(event.target, 0))}
            // onBlur??
            disabled={disabled}
            validationState={calcValidationState(required, value)}
        >
            <option value=''>{placeholder ? "- ".concat(placeholder) : ""} -</option>
            {getOptionsNotDisabled(valueArray, value).map(idNameToOption)}
        </SelectGroup>
    );
}

export function createButtonGroup(name: string,
                                  label: string,
                                  valueMap: {[key:string]: boolean},
                                  onChange: (key: string, state: boolean) => void): JSX.Element {
    return (
        <FormGroup>
            <label>{label}</label>
            <ButtonGroup style={{width: '100%'}}>
                {Object.entries(valueMap).map(([key, value]) =>
                    <Button key={key} onClick={() => onChange(key, !value)}>
                        {key} <i className={"fa fa-check-circle" + (value ? ' selected' : '')}/>
                    </Button>
                )}
            </ButtonGroup>
        </FormGroup>
    );
}


export type Style = "default" | "primary" | "info" | "link" | "warning" | "danger" | "success";

export interface GroupProps {
    name?: string | undefined;
    children?: ReactNode | undefined;
    placeholder?: string | undefined;
    value?: string | number | undefined;
    onBlur?: FormEventHandler<FormControl> | undefined;
    onChange?: FormEventHandler<FormControl> | undefined;

    bsClass?: string | undefined;
    bsSize?: "sm" | "lg" | undefined;
    controlId?: string | undefined;

    disabled?: boolean | undefined;
    validationState?: "success" | "warning" | "error" | null | undefined;
    label: string; // | ReactNode ?

    labelClassName?: string | undefined;

    wrapperClassName?: string | undefined;

    buttonAfter?: ReactNode | undefined;
}

type FieldGroupTypes = 'text' | 'email' | 'file' | 'textarea' | 'number' | 'datetime-local' | 'time';

export interface FieldGroupProps extends GroupProps {
    type: FieldGroupTypes;  // See https://react-bootstrap.github.io/components/forms/ for how to do Radio, Checkbox and Select etc
    maxLength?: number | undefined;
    autoComplete?: string | undefined;
    rows?: number | undefined; // for type=textarea
    step?: string | number | undefined; // for type=number
}

export function SelectGroup(props: GroupProps) {
    let control = <FormControl
        componentClass='select'
        name={props.name}
        onBlur={props.onBlur}
        onChange={props.onChange}
        placeholder={props.placeholder}
        value={props.value}
        disabled={props.disabled}
    >
        {props.children}
    </FormControl>;

    if (props.buttonAfter) {
        control = (
            <InputGroup>
                {control}
                <InputGroup.Button>
                    {props.buttonAfter}
                </InputGroup.Button>
            </InputGroup>
        );
    }

    return (
        <FormGroup validationState={props.validationState}>
            <ControlLabel bsClass={props.labelClassName}>{props.label}</ControlLabel>
            <div className={props.wrapperClassName}>
                {control}
            </div>
        </FormGroup>
    );
}

/** Implements label + input in one component that React Bootstrap used to do as deprecated Input component. */
export function FieldGroup(props: FieldGroupProps) {
    const type = props.type == 'textarea' ? undefined : props.type;
    const componentClass = props.type == 'textarea' ? 'textarea' : undefined;
    return (
        <FormGroup validationState={props.validationState}>
            <ControlLabel bsClass={props.labelClassName}>{props.label}</ControlLabel>
            <div className={props.wrapperClassName}>
                <FormControl
                    bsClass={props.bsClass}
                    name={props.name}
                    onBlur={props.onBlur}
                    onChange={props.onChange}
                    placeholder={props.placeholder}
                    componentClass={componentClass}
                    type={type}
                    value={props.value || ''}
                    disabled={props.disabled}
                    autoComplete={props.autoComplete}
                >
                    {props.children}
                </FormControl>
            </div>
        </FormGroup>
    );
}

export function HorizontalInfoGroup(props: {label: string, info: string}) {
    return <FormGroup>
        <label className="col-sm-5">{props.label}</label>
        <span style={{border: "none", boxShadow: "none"}} className="col-sm-3">{props.info}</span>
    </FormGroup>;
}
