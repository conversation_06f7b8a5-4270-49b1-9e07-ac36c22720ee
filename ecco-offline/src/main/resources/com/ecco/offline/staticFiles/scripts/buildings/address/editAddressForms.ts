import {apiClient} from "ecco-components";
import {Address as AddressDto} from "ecco-dto/contact-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import {ContactsAjaxRepository} from "ecco-dto";
import {AddressedLocationAjaxRepository} from "ecco-dto";
import * as commands from "./commands";
import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import Form = require("../../controls/Form");
import InputGroup = require("../../controls/InputGroup");
import TextInput = require("../../controls/TextInput");

const repository = new AddressedLocationAjaxRepository(apiClient);
const contactsRepository = new ContactsAjaxRepository(apiClient);

abstract class BaseEditAddressForm extends BaseAsyncCommandForm<AddressDto> {
    private form = new Form().addClass("form-30-50");
    // private buildingName = new TextInput("building name");
    // private buildingNumber = new TextInput("building number");
    private line1 = new TextInput("line 1");
    private line2 = new TextInput("line 2");
    private line3 = new TextInput("line 3");
    private town = new TextInput("town");
    private postcode = new TextInput("post code");
    private origAddressDto: AddressDto;


    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    protected constructor(
        private cmdFactory: {new(operation: string, id: number): commands.BaseAddressChangeCommand;},
        private entityId: number) {
        super("edit address");
        this.form
            .append( new InputGroup("line 1", this.line1) )
            .append( new InputGroup("line 2", this.line2) )
            .append( new InputGroup("line 3", this.line3) )
            .append( new InputGroup("town", this.town) )
            .append( new InputGroup("post code", this.postcode) )
    }

    protected abstract override fetchViewData(): Promise<AddressDto>;

    protected render(addressDto: AddressDto) {
        this.origAddressDto = addressDto;

        if (addressDto) {
            this.line1.setVal(addressDto.address[0]);
            this.line2.setVal(addressDto.address[1]);
            this.line3.setVal(addressDto.address[2]);
            this.town.setVal(addressDto.town);
            this.postcode.setVal(addressDto.postcode);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        let cmd;
        if (this.origAddressDto) {
            cmd = new this.cmdFactory("update", this.entityId)
                .changeLine1(this.origAddressDto.address[0], this.line1.val())
                .changeLine2(this.origAddressDto.address[1], this.line2.val())
                .changeLine3(this.origAddressDto.address[2], this.line3.val())
                .changeTown(this.origAddressDto.town, this.town.val())
                .changePostCode(this.origAddressDto.postcode, this.postcode.val());
        }
        else {
            cmd = new this.cmdFactory("add", this.entityId)
                .changeLine1(null, this.line1.val())
                .changeLine2(null, this.line2.val())
                .changeLine3(null, this.line3.val())
                .changeTown(null, this.town.val())
                .changePostCode(null, this.postcode.val())
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }
}

export class EditAddressedLocationForm extends BaseEditAddressForm {

    public static showInModal(addressId: number) {
        const form = new EditAddressedLocationForm(addressId);
        form.load();
        showFormInModalDom(form);
    }

    constructor(private addressId: number) {
        super(commands.AddressedLocationChangeCommand, addressId);
    }

    protected fetchViewData(): Promise<AddressDto> {
        return repository.findOneAddress(this.addressId);
    }
}

export class EditContactAddressForm extends BaseEditAddressForm {

    public static showInModal(contactId: number) {
        const form = new EditContactAddressForm(contactId);
        form.load();
        showFormInModalDom(form);
    }

    constructor(private contactId: number) {
        super(commands.ContactAddressChangeCommand, contactId);
    }

    protected fetchViewData(): Promise<AddressDto> {
        return contactsRepository.findOneContact(this.contactId)
            .then( (contact) => contact.address );
    }
}
