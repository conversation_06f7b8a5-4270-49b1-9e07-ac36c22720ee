import {useAppBarOptions, useServicesContext, withSessionData} from "ecco-components";
import * as React from "react";
import {FC} from "react";
import ClientReferralsPopup, {
    getReferralSrIdHref,
    openReferral
} from "../clientdetails/components/ClientReferralsPopup";
import {ClientList} from "../clientdetails/components/ClientList";
import {Client, ReferralSummaryDto, SessionData} from "ecco-dto";
import {NewReferralWizard} from "../referral/components/NewReferralWizard";
import {GuidanceFormLoad} from "../referral/components/CustomForm";

function getOnSelectedReferral() {
    return (sessionData, referral) => {
        openReferral(sessionData, referral);
    };
}

function getHrefOnSelectedReferral() {
    return (sessionData: SessionData, referral: ReferralSummaryDto) => getReferralSrIdHref(sessionData, referral);
}

function onNewClient(sessionData: SessionData, client: Client) {
    NewReferralWizard.popup(client.clientId, (referral) => openReferral(sessionData, referral))
}

const ClientListSearch: FC<{existingOnly: boolean}> = ({existingOnly}) =>
    withSessionData(sessionData =>
        <ClientList
            sessionData={sessionData}
            subFormsAsModal={true}
            existingOnly={existingOnly}
            onChange={(client, isNew) => isNew && onNewClient(sessionData, client)}
            selectedResultRender={true}
            selectedResultRenderer={client =>
                <ClientReferralsPopup
                    client={client}
                    existingOnly={existingOnly}
                    onSelectedReferralSrIdHref={getHrefOnSelectedReferral()}
                    onSelectedReferral={getOnSelectedReferral()}
                />
            }
        />
    , null);

const WelcomePage: FC = () => {
    useAppBarOptions("")
    const {sessionData} = useServicesContext();
    // useHeader("find / create clients")
    return (
            <div className="container-fluid top-gap-15 client-search col-xs-12 col-lg-offset-2 col-lg-8">
                <ClientListSearch existingOnly={!sessionData.hasRoleReferralNew()}/>
                {sessionData.isEnabled("welcome.notice")
                    && <GuidanceFormLoad formDefinitionUuid={"*************-babe-babe-dadafee1600d"}/>}
            </div>
    );
}

export default WelcomePage;
