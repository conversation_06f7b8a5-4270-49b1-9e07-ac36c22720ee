import {EccoDate} from "@eccosolutions/ecco-common";
import {AvailabilityDto} from "ecco-rota";
import {WorkerMenuItem} from "../domain";
import {getGlobalApiClient} from "ecco-dto";

const apiClient = getGlobalApiClient();

export class AjaxService {

    public loadAvailabilityDateRange(calendarId: string, startDate: EccoDate, endDate: EccoDate): Promise<AvailabilityDto> {
        const uri = `calendar/availability/${calendarId}/${startDate.formatIso8601Basic()}-${endDate.formatIso8601Basic()}/`;

        return apiClient.get<AvailabilityDto>(uri);
    }

    /**
     * API assumes 00:00hrs on that day, so end date needs to be first day of next month
     */
    private deriveEndDate(startDate: EccoDate): EccoDate {
        if (startDate.getDate() == 1) {
            return startDate.addMonths(1);
        } else {
            // show 2 weeks if non-aligned day is selected
            return startDate.addDays(14);
        }
    }

    public loadWorkers(): Promise<WorkerMenuItem[]> {
        return apiClient.get<WorkerMenuItem[]>("workers/")
            .then(workers => workers.filter (worker => !!worker.calendarId));
    }

    /**
     * Save the whole thing using a POST to replace the previous availability.
     * TODO: Prob need a version in here so that we can do optimisitic locking
     */
    public saveAvailability(calendarId: string, data: AvailabilityDto) {

        return apiClient.post(`calendar/availability/${calendarId}/`, data);

    }
}
