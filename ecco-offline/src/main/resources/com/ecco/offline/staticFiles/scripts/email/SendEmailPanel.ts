import $ = require("jquery");
import _ = require("lodash");

import BaseControl = require("../controls/BaseControl");
import CheckboxInput = require("../controls/CheckboxInput");
import InputGroup = require("../controls/InputGroup");
import Panel = require("../controls/Panel");
import TextInput = require("../controls/TextInput");
import TextAreaInput = require("../controls/TextAreaInput");
import {SparseArray} from "@eccosolutions/ecco-common";


/** Simple unintelligent UI component we can use to query for the fields */
class SendEmailSubform extends BaseControl {

    private subject = new TextInput("subject").placeholderText("e.g. failed test on main lift alarm");
    private body = new TextAreaInput("body");

    constructor() {
        super($("<div>"));

        this.append(new InputGroup("subject:", this.subject).enableValidation());

        this.append(new InputGroup("content", this.body).enableValidation());
    }

    public getSubject() {
        return this.subject.val();
    }

    public getBody() {
        return this.body.val();
    }
}

class SummaryControl extends BaseControl {

    private entries: SparseArray<$.JQuery> = {};
    private list = $("<ul>");

    constructor(title: string) {
        super();
        this.append( $("<strong>").text(title) );
        this.append(this.list);
        this.element().hide();
    }

    ensurePresent(id: number, text: string) {
        if (!this.entries[id]) {
            let $entry = $("<li>").text(text);
            this.list.append($entry);
            this.entries[id] = $entry;
            this.element().show();
        }
    }

    ensureAbsent(id: number) {
        if (this.entries[id]) {
            this.entries[id].remove();
            delete this.entries[id];
            if (!this.hasEntries()) { this.element().hide(); }
        }
    }

    hasEntries() {
        return !_.isEmpty(this.entries);
    }
}

class SendEmailPanel extends BaseControl {

    private emailSubform = new SendEmailSubform();
    private emailPanel = new Panel("panel-primary");
    private showEmailCheckbox = new CheckboxInput("Also send an email?", "enableEmail");
    private includeEmail = false;
    private warnSummary = new SummaryControl("warnings:");
    private failSummary = new SummaryControl("failures:");

    /**
     * @param promptInsteadOfEmail - instead of sending an email - show this prompt to say they need to do so
     */
    constructor(private summariseActions = true, private promptInsteadOfEmail?: string) {
        super();
        this.showEmailCheckbox.change( () => {
            this.includeEmail = this.showEmailCheckbox.isChecked();
            this.emailPanel.element().toggle(this.includeEmail);
        });

        if (summariseActions) {
            this.append(this.warnSummary);
            this.append(this.failSummary);
        }
        this.append(this.showEmailCheckbox.element().hide());

        if (promptInsteadOfEmail) {
            this.emailPanel.titleElement().text("action needed");
            this.emailPanel.bodyElement().append($("<p>").text(promptInsteadOfEmail));
        }
        else {
            this.emailPanel.titleElement().text("enter details for email");
            this.emailPanel.bodyElement().append(this.emailSubform.element()).css("padding-bottom", 0);
        }
        this.append(this.emailPanel.element().hide());
    }

    public getEmailSubform() { return this.emailSubform; }

    public shouldIncludeEmail() { return this.includeEmail; }

    public onChange(id: number, label: string, entryText: string) {

        if (!label || label == "pass") {
            this.warnSummary.ensureAbsent(id);
            this.failSummary.ensureAbsent(id);
        }
        else if (label.indexOf("warn") >= 0) {
            this.warnSummary.ensurePresent(id, entryText);
            this.failSummary.ensureAbsent(id);
        }
        else {
            this.warnSummary.ensureAbsent(id);
            this.failSummary.ensurePresent(id, entryText);
        }

        // Force email or make optional depending on whether we have failures and warnings
        const hasWarnings = this.warnSummary.hasEntries();
        if (this.failSummary.hasEntries() || this.promptInsteadOfEmail && hasWarnings) {
            this.includeEmail = !this.promptInsteadOfEmail;
            this.showEmailCheckbox.element().hide();
            this.emailPanel.element().show();
        }
        else if (hasWarnings) {
            this.includeEmail = this.showEmailCheckbox.isChecked();
            this.showEmailCheckbox.element().show();
            this.emailPanel.element().toggle(this.includeEmail);
        }
        else {
            this.includeEmail = false;
            this.showEmailCheckbox.element().hide();
            this.emailPanel.element().hide();
        }
    }
}
export = SendEmailPanel;