import $ = require("jquery");

import BaseControl = require("../BaseControl");


/** A bootstrap btn-group, but with our own data implementation */
class CheckboxButtons extends BaseControl {

    private currentState: boolean[];

    /** equal size arrays, with selected[] being array with true if that entry is selected */
    constructor(labels: string[], selected: boolean[]) {
        super($("<div>").addClass("btn-group"));

        this.currentState = selected.slice();

        labels.forEach( (label, idx) => {

            var $tick = $("<i>").addClass("fa fa-check-circle");
            if (selected[idx]) {
                $tick.addClass("selected");
            }
            var $btn = $("<span>")
                .addClass("btn btn-default")
                .append(label + " ")
                .append($tick)
                .click( () => {
                    this.currentState[idx] = !this.currentState[idx];
                    if (this.currentState[idx]) {
                        $tick.addClass("selected"); // i.fa.selected -> color:lightgreen works
                    }
                    else {
                        $tick.removeClass("selected");
                    }
                });
            this.append($btn);
        });
    }

    public getCurrentState() { return this.currentState; }

    public isValid(): boolean {
        return this.currentState.some(state => state == true);
    }
}
export = CheckboxButtons
