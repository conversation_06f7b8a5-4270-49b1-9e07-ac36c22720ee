import {apiClient} from "ecco-components";
import {InvoiceDto, InvoicesAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import ServiceRecipientInvoiceControl = require("./ServiceRecipientInvoiceControl"); // WARNING online only

const repository = new InvoicesAjaxRepository(apiClient);

class ServiceRecipientEditInvoiceForm extends BaseAsyncCommandForm<InvoiceDto> {

    public static showInModal(invoiceId: number) {
        var form = new ServiceRecipientEditInvoiceForm(invoiceId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form();
    //private setFinal = new Button("finalise invoice");

    constructor(private invoiceId: number) {
        super("edit invoice: " + invoiceId.toString());
        let editControl = new ServiceRecipientInvoiceControl(invoiceId);
        this.form.append(editControl);
        editControl.load();
    }

    protected fetchViewData(): Promise<InvoiceDto> {
        return repository.findOneInvoice(this.invoiceId);
    }

    protected render(invoice: InvoiceDto) {
        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }

    protected override submitForm(): Promise<void> {
        // if (this.setFinal) {
        //     let cmd = new invoice command...
        //     this.commandQueue.addCommand(cmd);
        //}
        return super.submitForm();
    }

}
export = ServiceRecipientEditInvoiceForm;
