import $ = require("jquery");
import raphael = require("raphael");

import BaseControl = require("../controls/BaseControl");
import {bus, EventBus, EventHandler} from "@eccosolutions/ecco-common";
import {Sector, Vector2d} from "ecco-math";

import {Debounce} from "@eccosolutions/ecco-common";
import {StringToStringMap} from "@eccosolutions/ecco-common";

/**
 * A directed acyclic graph of nodes with attached data and behaviour,
 * and associations that connect the nodes.
 */
export class DynamicTreeControl extends BaseControl {
    private width: number;
    private height: number;

    private paper : raphael.RaphaelPaper;

    private contextNode: DynamicTreeNode;
    private contextNodeChangedEventBus: EventBus<ContextNodeChangedEvent>;

    /** Constructs a new DynamicTreeControl with the specified width and height.
     *
     * The context node of the DynamicTreeControl is initially null, and so
     * initially no tree is displayed. */
    constructor(width: number, height: number) {
        var $container = $("<div>").addClass("dynamic-tree");

        super($container);

        this.paper = raphael($container[0], 0, 0);
        this.contextNodeChangedEventBus = bus<ContextNodeChangedEvent>();

        this.setSize(width, height);
    }

    protected createGraphicAndAttachPaper(paper : raphael.RaphaelPaper) {
        let graphic = new DynamicTreeGraphic(this);
        graphic.attach(paper);
    }

    /** Gets the context node of this DynamicTreeControl.
     *
     * The context node may be null, in which case no tree is displayed. */
    public getContextNode(): DynamicTreeNode {
        return this.contextNode;
    }

    /** Sets the context node node of this DynamicTreeControl.
     *
     * The context node may be null, in which case no tree is displayed. */
    public setContextNode(contextNode: DynamicTreeNode): void {
        this.createGraphicAndAttachPaper(this.paper);
        var oldContextNode = this.contextNode;
        this.contextNode = contextNode;
        this.contextNodeChangedEventBus.fire(new ContextNodeChangedEvent(this, oldContextNode, contextNode));
    }

    /** Gets the width of the DynamicTreeControl. */
    public getWidth(): number {
        return this.width;
    }

    /** Gets the height of the DynamicTreeControl. */
    public getHeight(): number {
        return this.height;
    }

    /** Sets the width and height of the DynamicTreeControl. */
    public setSize(width: number, height: number): void {
        if (width < 0 || !isFinite(width)) {
            this.width = 0;
        } else {
            this.width = width;
        }

        if (height < 0 || !isFinite(height)) {
            this.height = 0;
        } else {
            this.height = height;
        }

        this.paper.setSize(width, height);
        this.paper.setViewBox(-Math.floor(width * 0.5), -Math.floor(height * 0.5), width, height);
    }

    /** Adds an event handler that will be called when the context node
     * changes. */
    public addContextNodeChangedEventHandler(handler: EventHandler<ContextNodeChangedEvent>): void {
        this.contextNodeChangedEventBus.addHandler(handler);
    }

    /** Removes an existing ContextNodeChangedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeContextNodeChangedEventHandler(handler: EventHandler<ContextNodeChangedEvent>): void {
        this.contextNodeChangedEventBus.removeHandler(handler);
    }
}

export interface DynamicTreeStyle {
    nodeStyle: DynamicTreeNodeStyle;
    annotationStyle: DynamicTreeNodeStyle;
    edgeStyle: DynamicTreeEdgeStyle;
    maxDepth?: number;
}

export interface DynamicTreeNodeStyle {
    radius: number;
    strokeColor: string;
    strokeWidth: number;
    fillColor: string;
    highlightFillColor: string;
    fontSize: number;
}

export interface DynamicTreeEdgeStyle {
    length: number;
    strokeColor: string;
    strokeWidth: number;
}

export var defaultDynamicTreeStyle: DynamicTreeStyle = {
    nodeStyle: {
        radius: 64,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4",
        fontSize: 14
    },
    annotationStyle: {
        radius: 14,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4",
        fontSize: 12
    },
    edgeStyle: {
        length: 128,
        strokeColor: "#a2b7d8",
        strokeWidth: 1
    },
    maxDepth: 1
};

export var defaultParentNodeStyle: DynamicTreeStyle = {
    nodeStyle: {
        radius: 40,
        strokeColor: "#a2b7d8",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4",
        fontSize: 12
    },
    annotationStyle: {
        radius: 13,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4",
        fontSize: 8
    },
    edgeStyle: {
        length: 128,
        strokeColor: "#a2b7d8",
        strokeWidth: 1
    }
};

/** A node within a DynamicTreeControl. */
export class DynamicTreeNode {
    private caption: string;
    private highlighted = false;
    private property: StringToStringMap = {};
    private parent: DynamicTreeNode = null;
    private annotations = new Array<AnnotationNode>();
    private children = new Array<DynamicTreeNode>();
    private parentChangedEventBus = bus<ParentChangedEvent>();
    private annotationAddedEventBus = bus<AnnotationAddedEvent>();
    private annotationRemovedEventBus = bus<AnnotationRemovedEvent>();
    private childAddedEventBus = bus<ChildAddedEvent>();
    private childRemovedEventBus = bus<ChildRemovedEvent>();
    private clickEventBus = bus<NodeClickEvent>();
    private changeEventBus = bus<NodeChangeEvent>();
    private redrawEventBus = bus<RedrawEvent>();

    /** Constructs a new DynamicTreeNode with the specified caption.
     *
     * If no caption is specified then the default is the empty string. */
    constructor(caption?: string) {
        this.setCaption(caption);
    }

    /** Gets the caption of this DynamicTreeNode. */
    public getCaption(): string {
        return this.caption;
    }

    /** Sets the caption of this DynamicTreeNode.
     *
     * If no caption is specified then the default is the empty string. */
    public setCaption(caption?: string) {
        this.caption = caption || "";
        this.changeEventBus.fire(new NodeChangeEvent(this));
    }

    public isHighlighted(): boolean {
        return this.highlighted;
    }
    public setHighlighted(highlighted: boolean) {
        this.highlighted = highlighted;
        this.changeEventBus.fire(new NodeChangeEvent(this));
    }

    /**
     * Allows properties to be set that are more relevant to trees extending this.
     * Still need to be set on the node, since the node is where the state is set
     * and read from when drawing the tree (the graph nodes 'have' a state node)
     */
    public getProperty(key: string) {
        return this.property[key];
    }
    public setProperty(key: string, value: string) {
        this.property[key] = value;
        // for the usage at the moment, we don't need to trigger another redraw
        // because a whole redraw is calls
        //this.changeEventBus.fire(new NodeChangeEvent(this));
    }

    /** Returns the parent DynamicTreeNode, or null if this node is the root
     * of a tree. */
    public getParent(): DynamicTreeNode {
        return this.parent;
    }

    /** Gets the AnnotationNodes attached to this DynamicTreeNode. */
    public getAnnotations(): AnnotationNode[] {
        return this.annotations.slice(0); // Make a copy of the array.
    }

    /** Attaches the specified AnnotationNode to this DynamicTreeNode.
     *
     * The AnnotationNode will first be removed from its existing
     * parent, if any. */
    public addAnnotation(annotation: AnnotationNode): void {
        if (!annotation) {
            throw new TypeError("Argument 'annotation' must not be null.");
        }

        annotation.remove();

        (<any>annotation).parent = this; // TODO fix this really awful hack.
        this.annotations.push(annotation);

        this.annotationAddedEventBus.fire(new AnnotationAddedEvent(this, annotation));
    }

    /** Removes the specified AnnotationNode from this DynamicTreeNode.
     *
     * Returns true if the annotation was successfully removed, or false if the
     * specified AnnotationNode was not attached to this DynamicTreeNode. */
    public removeAnnotation(annotation: AnnotationNode): boolean {
        if (!annotation) {
            throw new TypeError("Argument 'annotation' must not be null.");
        }

        var i = this.annotations.indexOf(annotation);

        if (i < 0) {
            return false;
        } else {
            (<any>annotation).parent = null; // TODO fix this really awful hack.
            this.annotations.splice(i, 1);

            this.annotationRemovedEventBus.fire(new AnnotationRemovedEvent(this, annotation));

            return true;
        }
    }

    /** Gets the child nodes attached to this DynamicTreeNode. */
    public getChildren(): DynamicTreeNode[] {
        return this.children.slice(0); // Make a copy of the array.
    }

    /** Adds the specified child DynamicTreeNode to this DynamicTreeNode.
     *
     * The child DynamicTreeNode will first be removed from its existing
     * parent, if any. */
    public addChild(child: DynamicTreeNode): void {
        if (!child) {
            throw new TypeError("Child node must not be null.");
        }

        child.remove();

        child.parent = this;
        this.children.push(child);

        this.childAddedEventBus.fire(new ChildAddedEvent(this, child));
    }

    /** Removes the specified child DynamicTreeNode from this DynamicTreeNode.
     *
     * Returns true if the child was successfully removed, or false if the
     * specified DynamicTreeNode was not a child of this DynamicTreeNode. */
    public removeChild(child: DynamicTreeNode): boolean {
        if (!child) {
            throw new TypeError("Child node must not be null.");
        }

        var i = this.children.indexOf(child);

        if (i < 0) {
            return false;
        } else {
            child.parent = null;
            this.children.splice(i, 1);

            this.childRemovedEventBus.fire(new ChildRemovedEvent(this, child));

            return true;
        }
    }

    /** Adds this node as a child of the specified parent DynamicTreeNode.
     *
     * This node will first be removed from its existing parent, if any. */
    public addTo(parent: DynamicTreeNode): void {
        if (!parent) {
            throw new TypeError("Argument 'parent' must not be null.");
        }

        parent.addChild(this);
    }

    /** Removes this node from its parent, if any.
     *
     * If this node does not have a parent, then this method does nothing. */
    public remove(): void {
        if (this.parent) {
            this.parent.removeChild(this);
        }
    }

    /** Removes all children of this DynamicTreeNode. */
    public clearChildren(): void {
        var oldChildren = this.children;
        this.children = [];

        for (var i = 0; i < oldChildren.length; ++i) {
            this.childRemovedEventBus.fire(new ChildRemovedEvent(this, oldChildren[i]));
        }
    }

    /** Fires a click event on this node.
     *
     * This happens whenever the user clicks the corresponding graphic, or
     * click() can be called programmatically to simulate a click. */
    public click() {
        this.clickEventBus.fire(new NodeClickEvent(this));
    }

    /** Adds an event handler that will be called when the parent
     * DynamicTreeNode changes. */
    public addParentChangedEventHandler(handler: EventHandler<ParentChangedEvent>): void {
        this.parentChangedEventBus.addHandler(handler);
    }

    /** Removes an existing ParentChangedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeParentChangedEventHandler(handler: EventHandler<ParentChangedEvent>): void {
        this.parentChangedEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when a new child
     * DynamicTreeNode is added to this DynamicTreeNode. */
    public addChildAddedEventHandler(handler: EventHandler<ChildAddedEvent>): void {
        this.childAddedEventBus.addHandler(handler);
    }

    /** Removes an existing ChildAddedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeChildAddedEventHandler(handler: EventHandler<ChildAddedEvent>): void {
        this.childAddedEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when a child DynamicTreeNode
     * is removed from this DynamicTreeNode. */
    public addChildRemovedEventHandler(handler: EventHandler<ChildRemovedEvent>): void {
        this.childRemovedEventBus.addHandler(handler);
    }

    /** Removes an existing ChildRemovedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeChildRemovedEventHandler(handler: EventHandler<ChildRemovedEvent>): void {
        this.childRemovedEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when a new child
     * AnnotationNode is added to this DynamicTreeNode. */
    public addAnnotationAddedEventHandler(handler: EventHandler<AnnotationAddedEvent>): void {
        this.annotationAddedEventBus.addHandler(handler);
    }

    /** Removes an existing AnnotationAddedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeAnnotationAddedEventHandler(handler: EventHandler<AnnotationAddedEvent>): void {
        this.annotationAddedEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when a child AnnotationNode
     * is removed from this DynamicTreeNode. */
    public addAnnotationRemovedEventHandler(handler: EventHandler<AnnotationRemovedEvent>): void {
        this.annotationRemovedEventBus.addHandler(handler);
    }

    /** Removes an existing AnnotationRemovedEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeAnnotationRemovedEventHandler(handler: EventHandler<AnnotationRemovedEvent>): void {
        this.annotationRemovedEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when the user clicks on the
     * graphic corresponding to this node. */
    public addClickEventHandler(handler: EventHandler<NodeClickEvent>): void {
        this.clickEventBus.addHandler(handler);
    }

    /** Removes an existing NodeClickEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeClickEventHandler(handler: EventHandler<NodeClickEvent>): void {
        this.clickEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when the node changes */
    public addChangeEventHandler(handler: EventHandler<NodeChangeEvent>): void {
        this.changeEventBus.addHandler(handler);
    }

    /** Removes an existing NodeChangeEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeChangeEventHandler(handler: EventHandler<NodeChangeEvent>): void {
        this.changeEventBus.removeHandler(handler);
    }

    /** Programatically trigger a redraw event - see RedrawEvent */
    public redraw() {
        this.redrawEventBus.fire(new RedrawEvent());
    }

    /** Adds a redraw handler that will be called when triggered */
    public addRedrawEventHandler(handler: EventHandler<RedrawEvent>): void {
        this.redrawEventBus.addHandler(handler);
    }

    /** Removes an existing RedrawEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeRedrawEventHandler(handler: EventHandler<RedrawEvent>): void {
        this.redrawEventBus.removeHandler(handler);
    }
}

/** An annotation attached to a DynamicTreeNode.
 *
 * Annotations provide additional information pertaining to the node. They are
 * expected to be small, with an icon or number as the caption. */
export class AnnotationNode {
    private caption: string;
    private parent: DynamicTreeNode = null;
    private clickEventBus = bus<AnnotationClickEvent>();
    private changeEventBus = bus<AnnotationChangeEvent>();

    /** Constructs a new AnnotationNode with the specified caption.
     *
     * If no caption is specified then the default is the empty string. */
    constructor(caption?: string) {
        this.setCaption(caption);
    }

    /** Gets the caption of this AnnotationNode. */
    public getCaption(): string {
        return this.caption;
    }

    /** Sets the caption of this AnnotationNode.
     *
     * If no caption is specified, then the default is the empty string. */
    public setCaption(caption?: string): void {
        this.caption = caption || "";
        this.changeEventBus.fire(new AnnotationChangeEvent(this));
    }

    /** Gets the parent DynamicTreeNode, or null if this annotation is not
     * attached to a dynamic tree. */
    public getParent(): DynamicTreeNode {
        return this.parent;
    }

    /** Attaches this annotation to the specified DynamicTreeNode.
     *
     * This annotation will first be removed from its existing parent, if any. */
    public addTo(parent: DynamicTreeNode): void {
        if (!parent) {
            throw new TypeError("Argument 'parent' must not be null.");
        }

        parent.addAnnotation(this);
    }

    /** Removes this annotation from its parent, if any.
     *
     * If this annotation does not have a parent, then this method does
     * nothing. */
    public remove(): void {
        if (this.parent) {
            this.parent.removeAnnotation(this);
        }
    }

    /** Fires a click event on this annotation.
     *
     * This happens whenever the user clicks the corresponding graphic, or
     * click() can be called programmatically to simulate a click. */
    public click() {
        this.clickEventBus.fire(new AnnotationClickEvent(this));
    }

    /** Adds an event handler that will be called when the user clicks on the
     * graphic corresponding to this node. */
    public addClickEventHandler(handler: EventHandler<AnnotationClickEvent>): void {
        this.clickEventBus.addHandler(handler);
    }

    /** Removes an existing AnnotationClickEvent handler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeClickEventHandler(handler: EventHandler<AnnotationClickEvent>): void {
        this.clickEventBus.removeHandler(handler);
    }

    /** Adds an event handler that will be called when the AnnotationNode
     * changes. */
    public addChangeEventHandler(handler: EventHandler<AnnotationChangeEvent>): void {
        this.changeEventBus.addHandler(handler);
    }

    /** Removes an existing AnnotationChangeEventHandler.
     *
     * If the specified handler is not registered, then this function does
     * nothing. */
    public removeChangeEventHandler(handler: EventHandler<AnnotationChangeEvent>): void {
        this.changeEventBus.removeHandler(handler);
    }
}

/** Event fired when the context node of a DynamicTreeControl changes. */
export class ContextNodeChangedEvent {
    constructor(private control: DynamicTreeControl, private oldContextNode: DynamicTreeNode, private newContextNode: DynamicTreeNode) {
        if (!control) {
            throw new Error("Argument 'control' must not be null.");
        }
    }

    /** Gets the DynamicTreeControl whose context node has changed. */
    public getControl(): DynamicTreeControl {
        return this.control;
    }

    /** The previous context node. */
    public getOldContextNode(): DynamicTreeNode {
        return this.oldContextNode;
    }

    /** The new context node. */
    public getNewContextNode(): DynamicTreeNode {
        return this.newContextNode;
    }
}

/** Event fired when the parent of a DynamicTreeNode changes. */
export class ParentChangedEvent {
    constructor(private node: DynamicTreeNode, private oldParent: DynamicTreeNode, private newParent: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    /** The DynamicTreeNode whose parent has changed. */
    public getNode(): DynamicTreeNode {
        return this.node;
    }

    /** The previous parent DynamicTreeNode. */
    public getOldParent(): DynamicTreeNode {
        return this.oldParent;
    }

    /** The new parent DynamicTreeNode. */
    public getNewParent(): DynamicTreeNode {
        return this.newParent;
    }
}

/** Event fired when a new annotation node is added to a DynamicTreeNode. */
export class AnnotationAddedEvent {
    constructor(private node: DynamicTreeNode, private annotation: AnnotationNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    /** The DynamicTreeNode to which a new annotation node has been added. */
    public getTargetNode(): DynamicTreeNode {
        return this.node;
    }

    /** The new annotation AnnotationNode. */
    public getAddedAnnotation(): AnnotationNode {
        return this.annotation;
    }
}

/** Event fired when an annotation node is removed from its parent DynamicTreeNode. */
export class AnnotationRemovedEvent {
    constructor(private node: DynamicTreeNode, private annotation: AnnotationNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    /** The parent DynamicTreeNode from which the child node has been removed. */
    public getTargetNode(): DynamicTreeNode {
        return this.node;
    }

    /** The child AnnotationNode that has been removed. */
    public getRemovedAnnotation(): AnnotationNode {
        return this.annotation;
    }
}

/** Event fired when a new child node is added to a DynamicTreeNode. */
export class ChildAddedEvent {
    constructor(private node: DynamicTreeNode, private child: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    /** The DynamicTreeNode to which a new child node has been added. */
    public getNode(): DynamicTreeNode { // REVIEW: Would be clearer as getTargetNode(), I (NU) think - can't always assume JSDoc
        return this.node;
    }

    /** The new child DynamicTreeNode. */
    public getChild(): DynamicTreeNode {
        return this.child;
    }
}

/** Event fired when a child node is removed from its parent DynamicTreeNode. */
export class ChildRemovedEvent {
    constructor(private node: DynamicTreeNode, private child: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    /** The parent DynamicTreeNode from which the child node has been removed. */
    public getNode(): DynamicTreeNode {
        return this.node;
    }

    /** The child DynamicTreeNode that has been removed. */
    public getChild(): DynamicTreeNode {
        return this.child;
    }
}

/** Event fired when the user clicks on a graphic representing a dynamic tree
 * node. */
export class NodeClickEvent {
    constructor(private node: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    public getNode(): DynamicTreeNode {
        return this.node;
    }
}

/** Event fired when the content of a DynamicTreeNode changes. */
export class NodeChangeEvent {
    constructor(private node: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }
    }

    public getNode(): DynamicTreeNode {
        return this.node;
    }
}

/** Event fired when the user clicks on a graphic representing an
 * annotation. */
export class AnnotationClickEvent {
    constructor(private annotation: AnnotationNode) {
        if (!annotation) {
            throw new TypeError("Argument 'annotation' must not be null.");
        }
    }

    public getAnnotation(): AnnotationNode {
        return this.annotation;
    }
}

/** Event fired when the content of an AnnotationNode changes. */
export class AnnotationChangeEvent {
    constructor(private annotation: AnnotationNode) {
        if (!annotation) {
            throw new TypeError("Argument 'annotation' must not be null.");
        }
    }

    public getAnnotation(): AnnotationNode {
        return this.annotation;
    }
}

/**
 * Event fired when a node requests some redraw.
 * This is created to handle the outcome star redrawing of the intersectionLine.
 *
 * Normally a redraw can be assumed when something happens to the node itself because the node
 * triggers the event, and the Graph node handles the redraw since the graph nodes are
 * constructed with the node, and so can add to the nodes handlers.
 * However, events are private to each node, so redrawing something beyond the node requires
 * the handlers to be shared across nodes - a global handler mechanism.
 * This has been avoided so far in favour of specific events - eg AnnotationChangedEvent
 * should probably have been a NodeChangedEvent being handled by a different node via a global handler.
 * Alternatively we could use specific propogation, similar to ContextNodeChangedEvent.
 *
 * We simply do what is necessary for now, yet reasonably obvious - create a new event specifically
 * to trigger on the root node, which fires the event for the graph node to pick it up and
 * complete the redraw of the intersectionLine.
 */
export class RedrawEvent {
    constructor() {}
}


/**
 * Graphical elements are separated below from the logical structure above.
 * DynamicTreeGraphic below is responsible for the context node. It listens for
 * a contextNodeChanged, which calls 'attachContextNodeGraphic' which creates a new
 * DynamicTreeNodeGraphic and 'attach'es it to the paper which calls attachChildGraphics.
 */
export class DynamicTreeGraphic {
    private paper: raphael.RaphaelPaper;

    private contextNodeGraphic: DynamicTreeNodeGraphic;
    private parentNodeGraphic: ParentNodeGraphic;

    private destroyed = false;

    constructor(private control: DynamicTreeControl) {
        if (!control) {
            throw new TypeError("Argument 'control' must not be null.");
        }

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        control.addContextNodeChangedEventHandler(this.onContextNodeChanged);

        this.attachContextNodeGraphic(control.getContextNode());
    }

    /**
     * Draw/Redraw the first node
     */
    public attach(paper: raphael.RaphaelPaper) {
        this.paper = paper;

        if (this.contextNodeGraphic) {
            var position = new Vector2d(0, 0);
            this.contextNodeGraphic.attach(paper, position, null);
        }
    }

    private onContextNodeChanged = (contextNodeChangedEvent: ContextNodeChangedEvent): void => {
        this.attachContextNodeGraphic(contextNodeChangedEvent.getNewContextNode());
    };

    private attachContextNodeGraphic(newContextNode: DynamicTreeNode): void {
        if (this.contextNodeGraphic) {
            this.contextNodeGraphic.destroy();
        }

        if (this.parentNodeGraphic) {
            this.parentNodeGraphic.destroy();
        }

        var parentNode: DynamicTreeNode;

        if (newContextNode) {
            this.contextNodeGraphic = new DynamicTreeNodeGraphic(newContextNode, 0);
            var position = new Vector2d(0, 0);
            this.contextNodeGraphic.attach(this.paper, position);

            parentNode = newContextNode.getParent();
        } else {
            this.contextNodeGraphic = null;
        }

        if (parentNode) {
            this.parentNodeGraphic = new ParentNodeGraphic(parentNode);
            this.parentNodeGraphic.attach(this.paper);
        } else {
            this.parentNodeGraphic = null;
        }
    }

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.control.removeContextNodeChangedEventHandler(this.onContextNodeChanged);

        if (this.contextNodeGraphic) {
            this.contextNodeGraphic.destroy();
        }

        this.destroyed = true;
    }
}

var defaultBounds = new Sector(Math.PI, Math.PI * 3);

class DynamicTreeNodeGraphic {
    private paper: raphael.RaphaelPaper;

    private position = new Vector2d(0, 0);
    private bounds: Sector = defaultBounds;

    private annotationNodes = new Array<AnnotationNode>();
    private annotationNodeGraphics = new Array<AnnotationNodeGraphic>();

    private childNodes = new Array<DynamicTreeNode>();
    private childNodeGraphics = new Array<DynamicTreeNodeGraphic>();

    private style = defaultDynamicTreeStyle;

    private nodeSet: raphael.RaphaelSet;
    private edgeSet: raphael.RaphaelSet;
    private captionElement: raphael.RaphaelElement;

    private destroyed = false;

    constructor(private node: DynamicTreeNode, private depth: number) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        node.addAnnotationAddedEventHandler(this.onAnnotationAdded);
        node.addAnnotationRemovedEventHandler(this.onAnnotationRemoved);
        node.addChildAddedEventHandler(this.onChildAdded);
        node.addChildRemovedEventHandler(this.onChildRemoved);
        node.addChangeEventHandler(this.onChange);

        this.addChildGraphics(node.getChildren());
        this.addAnnotationGraphics(node.getAnnotations());
    }

    /**
     * Draw/Redraw this node and any children via attachChildGraphic / attachAnnotationGraphic
     */
    public attach(paper: raphael.RaphaelPaper, position: Vector2d, bounds?: Sector): void {
        if (!position) {
            throw new TypeError("Argument 'position' must not be null.");
        }

        this.paper = paper;
        this.position = position;
        this.bounds = bounds || defaultBounds;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            var x = this.position.getX();
            var y = this.position.getY();

            paper.circle(x, y, this.style.nodeStyle.radius)
                .attr("stroke", this.style.nodeStyle.strokeColor)
                .attr("stroke-width", this.style.nodeStyle.strokeWidth)
                .attr("fill", this.style.nodeStyle.fillColor);

            this.captionElement = paper.text(x, y, this.node.getCaption())
                .attr("font-size", this.style.nodeStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);
        } else {
            this.nodeSet = null;
            this.captionElement = null;
        }

        this.attachChildGraphics();
        this.attachAnnotationGraphics();
    }

    public detach(): void {
        this.attach(null, this.position, this.bounds);
    }

    private onAnnotationAdded = (event: AnnotationAddedEvent): void => {
        this.addAnnotationGraphic(event.getAddedAnnotation());
    };

    private addAnnotationGraphic(annotation: AnnotationNode): void {
        this.addAnnotationGraphics([annotation]);
    }

    private addAnnotationGraphics(annotationNodes: AnnotationNode[]): void {
        if (this.destroyed) {
            return;
        }

        for (var i = 0; i < annotationNodes.length; ++i) {
            var annotation = annotationNodes[i];

            var annotationGraphic = new AnnotationNodeGraphic(annotation);

            this.annotationNodes.push(annotation);
            this.annotationNodeGraphics.push(annotationGraphic);
        }

        this.attachAnnotationGraphics();
    }

    private onAnnotationRemoved = (event: AnnotationRemovedEvent): void => {
        this.removeAnnotationGraphic(event.getRemovedAnnotation());
    };

    private removeAnnotationGraphic(annotation: AnnotationNode): void {
        if (this.destroyed) {
            return;
        }

        var annotationIndex = this.annotationNodes.indexOf(annotation);

        if (annotationIndex >= 0) {
            this.annotationNodeGraphics[annotationIndex].destroy();
            this.annotationNodes.splice(annotationIndex, 1);
            this.annotationNodeGraphics.splice(annotationIndex, 1);
        }

        this.attachAnnotationGraphics();
    }

    private onChildAdded = (event: ChildAddedEvent): void => {
        this.addChildGraphic(event.getChild());
    };

    private addChildGraphic(child: DynamicTreeNode): void {
        this.addChildGraphics([child]);
    }

    private addChildGraphics(childNodes: DynamicTreeNode[]): void {
        if (this.destroyed) {
            return;
        }

        for (var i = 0; i < childNodes.length; ++i) {
            var child = childNodes[i];

            var childGraphic = new DynamicTreeNodeGraphic(child, this.depth + 1);

            this.childNodes.push(child);
            this.childNodeGraphics.push(childGraphic);
        }

        this.attachChildGraphics();
    }

    private onChildRemoved = (event: ChildRemovedEvent): void => {
        this.removeChildGraphic(event.getChild());
    };

    private removeChildGraphic(child: DynamicTreeNode): void {
        if (this.destroyed) {
            return;
        }

        var childIndex = this.childNodes.indexOf(child);

        if (childIndex >= 0) {
            this.childNodeGraphics[childIndex].destroy();
            this.childNodes.splice(childIndex, 1);
            this.childNodeGraphics.splice(childIndex, 1);
        }

        this.attachChildGraphics();
    }

    private onChange = (event: NodeChangeEvent): void => {
        if (this.destroyed) {
            return;
        }

        this.captionElement.attr("text", event.getNode().getCaption());
    };

    private onClick = (): void => {
        if (Debounce.instance.shouldHandle()) {
            this.node.click();
        }
    };

    /**
     * Draw/Redraw this node's children
     */
    private attachChildGraphics(): void {
        if (this.destroyed) {
            throw new Error("DynamicTreeNodeGraphic is destroyed.");
        }

        if (this.edgeSet) {
            this.edgeSet.remove();
            this.edgeSet = null;
        }

        // True if we are attaching graphics. False if we are detaching them.
        var attaching = !!this.paper
            && (this.style.maxDepth == null || this.depth < this.style.maxDepth);

        if (attaching) {
            this.edgeSet = this.paper.set();
        }

        var anglePerChild = (this.bounds.getEndAngle() - this.bounds.getStartAngle()) / this.childNodeGraphics.length;

        for (var i = 0; i < this.childNodeGraphics.length; ++i) {
            var boundsStartAngle = this.bounds.getStartAngle() + i * anglePerChild;
            var boundsEndAngle = boundsStartAngle + anglePerChild;
            var bounds = new Sector(boundsStartAngle, boundsEndAngle);
            var childAngle = boundsStartAngle + anglePerChild / 2;

            var nodePosition = this.position.add(Vector2d.polar(childAngle, this.style.edgeStyle.length + this.style.nodeStyle.radius));

            if (attaching) {
                var edgeStartPosition = this.position.add(Vector2d.polar(childAngle, this.style.nodeStyle.radius));

                var edgePathDefinition = "M " + edgeStartPosition.getX().toFixed(1) + " " + edgeStartPosition.getY().toFixed(1)
                    + " L " + nodePosition.getX().toFixed(1) + " " + nodePosition.getY().toFixed(1);

                this.edgeSet.push(this.paper.path(edgePathDefinition)
                    .attr("stroke", this.style.edgeStyle.strokeColor)
                    .attr("stroke-width", this.style.edgeStyle.strokeWidth));

                this.childNodeGraphics[i].attach(this.paper, nodePosition, bounds);
            } else {
                this.childNodeGraphics[i].detach();
            }
        }
    }

    /**
     * Draw/Redraw this node's annotations
     */
    private attachAnnotationGraphics(): void {
        if (this.destroyed) {
            throw new Error("DynamicTreeNodeGraphic is destroyed.");
        }

        // Annotations are rotated 30 degrees from the child bounds, for now.
        var startAngle = this.bounds.getStartAngle() + Math.PI / 6;
        var endAngle = this.bounds.getEndAngle() + Math.PI / 6;

        var anglePerAnnotation = (endAngle - startAngle) / this.annotationNodeGraphics.length;

        for (var i = 0; i < this.annotationNodeGraphics.length; ++i) {
            var annotationAngle = startAngle + (i + 0.5) * anglePerAnnotation;
            var annotationDistance = this.style.nodeStyle.radius + this.style.annotationStyle.radius;

            var annotationPosition = this.position.add(Vector2d.polar(annotationAngle, annotationDistance));

            this.annotationNodeGraphics[i].attach(this.paper, annotationPosition);
        }
    }

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.node.removeAnnotationAddedEventHandler(this.onAnnotationAdded);
        this.node.removeAnnotationRemovedEventHandler(this.onAnnotationRemoved);
        this.node.removeChildAddedEventHandler(this.onChildAdded);
        this.node.removeChildRemovedEventHandler(this.onChildRemoved);
        this.node.removeChangeEventHandler(this.onChange);

        for (var i = 0; i < this.childNodeGraphics.length; ++i) {
            this.childNodeGraphics[i].destroy();
        }

        for (var i = 0; i < this.annotationNodeGraphics.length; ++i) {
            this.annotationNodeGraphics[i].destroy();
        }

        this.destroyed = true;
    }
}

class AnnotationNodeGraphic {
    private paper: raphael.RaphaelPaper;

    private position: Vector2d;

    private style = defaultDynamicTreeStyle;

    private nodeSet: raphael.RaphaelSet;
    private captionElement: raphael.RaphaelElement;

    private destroyed = false;

    constructor(private annotation: AnnotationNode) {
        if (!annotation) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        annotation.addChangeEventHandler(this.onChange);
    }

    /**
     * Draw/Redraw this node and any children via attachChildGraphic / attachAnnotationGraphic
     */
    public attach(paper: raphael.RaphaelPaper, position: Vector2d): void {
        this.paper = paper;
        this.position = position;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            var x = this.position.getX();
            var y = this.position.getY();

            paper.circle(x, y, this.style.annotationStyle.radius)
                .attr("stroke", this.style.annotationStyle.strokeColor)
                .attr("stroke-width", this.style.annotationStyle.strokeWidth)
                .attr("fill", this.style.annotationStyle.fillColor);

            this.captionElement = paper.text(x, y, this.annotation.getCaption())
                .attr("font-size", this.style.annotationStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);
        } else {
            this.nodeSet = null;
            this.captionElement = null;
        }
    }

    public detach(): void {
        this.attach(null, this.position);
    }

    private onChange = (event: AnnotationChangeEvent): void => {
        if (this.destroyed) {
            return;
        }

        this.captionElement.attr("text", event.getAnnotation().getCaption());
    };

    private onClick = (): void => {
        if (Debounce.instance.shouldHandle()) {
            this.annotation.click();
        }
    };

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.annotation.removeChangeEventHandler(this.onChange);

        this.destroyed = true;
    }
}

class ParentNodeGraphic {
    private paper: raphael.RaphaelPaper;
    private nodeSet: raphael.RaphaelSet;

    private style = defaultParentNodeStyle;

    private destroyed = false;

    constructor(private node: DynamicTreeNode) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
    }

    /**
     * Draw/Redraw this node and any children via attachChildGraphic / attachAnnotationGraphic
     */
    public attach(paper: raphael.RaphaelPaper) {
        this.paper = paper;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            // place in top-left 16 pixels from edge of paper
            var x = -paper.width * 0.5 + 16 + this.style.nodeStyle.radius;
            var y = -paper.height * 0.5 + 16 + this.style.nodeStyle.radius;

            paper.circle(x, y, this.style.nodeStyle.radius)
                .attr("stroke", this.style.nodeStyle.strokeColor)
                .attr("stroke-width", this.style.nodeStyle.strokeWidth)
                .attr("fill", this.style.nodeStyle.fillColor);

            paper.text(x, y, this.node.getCaption())
                .attr("font-size", this.style.nodeStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);
        } else {
            this.nodeSet = null;
        }
    }

    public detach(): void {
        this.attach(null);
    }

    private onClick = (): void => {
        this.node.click();
    };

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.destroyed = true;
    }
}
