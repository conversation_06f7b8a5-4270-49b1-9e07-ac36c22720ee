import $ = require("jquery");
import ActionButton = require("../../controls/ActionButton");
import ElementContainer = require("../../controls/ElementContainer");
import QuestionControl = require("../questionnaire/QuestionControl");
import WizardView = require("../../controls/WizardView");
import WizardForm = require("../../controls/WizardForm");
import WizardState = require("../../controls/WizardState");
import {CommandQueue, CommentCommand, QuestionAnswerCommand} from "ecco-commands";
import {ActionState, EccoDateTime, SparseArray} from "@eccosolutions/ecco-common";
import {EvidenceGroup, SessionData} from "ecco-dto";
import {Question} from "ecco-dto/service-config-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {showModalWithActions} from "../../components/MUIConverterUtils";
import {getCommandQueueRepository, showErrorAsAlert} from "ecco-offline-data";


/**
 * Form to display questions in a wizard
 * @deprecated - need to implement in React
 */
class QuestionnaireWizardForm implements WizardView { // TODO Re-implement as a React component embedding QuestionControl

    private controlContainer = new ElementContainer();
    private titleContainer = $("<span>");
    private titlePageText = "";
    private footerContainer = $("<span>");

    private closeIconAction: ActionState = {
        label: null,
        style: "closeIcon",
        onClick: () => { this.onCloseIcon(); return Promise.resolve();}
    };
    private footerActions: ActionState[] = [];
    private wizardForm: WizardForm;
    private advanceWizardPage: () => void;
    private previousWizardPage: () => void;
    private cancelWizardPage: () => void;
    private renderWizardPage: () => void;
    private closeModal: () => void;
    private afterSubmitted: () => void;
    private afterCancelled: () => void;

    private wizardState: () => WizardState;
    private questionAnswers: SparseArray<string> = {};
    private workUuid = Uuid.randomV4();
    private timeStarted = EccoDateTime.nowLocalTime();

    public static showInModalByIds(
            sessionData: SessionData,
            svcrepId: number,
            questionnaireEvidenceGroup: EvidenceGroup,
            taskName: string,
            titleText: string,
            questions: Question[],
            overrideSubmitIn: (cmdQueue: CommandQueue) => void,
            afterSubmittedIn: () => void,
            afterCancelledIn: () => void) {

        const form = new QuestionnaireWizardForm(sessionData,
            svcrepId,
            questionnaireEvidenceGroup,
            taskName,
            titleText,
            questions,
            (cmdQueue: CommandQueue) => {
                overrideSubmitIn(cmdQueue);
            },
            () => {
                afterSubmittedIn();
            },
            () => {
                afterCancelledIn();
            });
        form.showFormInModal();
    }

    protected showFormInModal() {
        const pages = this.questions.length + 1 + 1; // questions + summary + optional intro page
        this.wizardForm = new WizardForm(pages, this);
        this.wizardForm.loadWizardFirstPage();
        this.updateReactModal();
    }

    private updateReactModal() {
        // the modal stays around when using an embedded 'save' button (not an Action), we need to trigger a close
        const forceClose = (trigger) => {
            this.closeModal = () => {
                trigger();
            }
        };
        // the modal closes and creates a new one on each button
        showModalWithActions(this.titlePageText, this.wizardForm.element(), this.getFooterActions(), "modal-lg", forceClose);
    }

    constructor(private sessionData: SessionData,
                private serviceRecipientId: number,
                private questionnaireEvidenceGroup: EvidenceGroup,
                private taskName: string,
                private titleText: string,
                protected questions: Question[],
                private overrideSubmit: (cmdQueue: CommandQueue) => void,
                afterSubmitted: () => void,
                afterCancelled: () => void) {
        this.afterSubmitted = () => {this.closeModal && this.closeModal(); afterSubmitted();};
        this.afterCancelled = () => {this.closeModal && this.closeModal(); afterCancelled();};
    }

    public title() {
        return this.titleContainer;
    }
    public element(): $.JQuery {
        return this.controlContainer.element();
    }
    public getFooter() {
        return this.footerContainer;
    }
    public getFooterActions() {
        return this.footerActions;
    }

    public setWizardTriggerPage(trigger: () => void) {
        this.advanceWizardPage = trigger;
    }

    public setWizardPreviousPage(trigger: () => void) {
        this.previousWizardPage = trigger;
    }

    public setWizardCancelPage(trigger: () => void) {
        this.cancelWizardPage = trigger;
    }

    public setWizardRerenderPage(trigger: () => void) {
        this.renderWizardPage = trigger;
    }

    public setGetWizardState(getState: () => WizardState) {
        this.wizardState = getState;
    }

    public getWizardInitialPage() {
        return 0;
    }

    protected getWizardContentForIntroPage(): $.JQuery | null {
        return null;
    }

    protected getWizardContentForCancelPage(): $.JQuery | null {
        let hasAnswered = (this.countAnswers() > 0);
        if (hasAnswered) {
            return $("<span>").text("you have unsaved answers");
        }
        return null;
    }

    protected getWizardContentForSummaryPage(): $.JQuery {
        return $("<span>").text(this.questions.length +
            " question(s) were asked and you answered " +
            this.countAnswers());
    }

    public renderWithCancelConfirmation() {

        const $cancelBlurb = $("<div>").css("text-align", "center");
        $cancelBlurb.append($("<span>").css("font-weight", "bold").text("are you sure you with to cancel?"))
                        .append("<br>");

        $cancelBlurb.append(this.getWizardContentForCancelPage()).append("<br>");

        const $cancelButton = new ActionButton("cancel anyway")
            .autoDisable(false)
            .clickSynchronous(() => {
                this.afterCancelled();
            });

        $cancelBlurb.append($cancelButton.element().addClass("btn btn-primary"));
        const $content = this.summaryPageContent();
        $content.append($cancelBlurb);

        this.controlContainer.setContent($content);
        this.titlePageText = "confirm cancel";
        this.titleContainer.text(this.titlePageText);
        this.footerWithRedraw();
    }

    public renderWithPage(i: number) {

        if (i == 0) {

            this.controlContainer.setContent(
                $("<div>").css("text-align", "center").append(this.getWizardContentForIntroPage())
            );

            this.titlePageText = this.titleText.concat(" " + this.questions.length) + " questions";
            this.titleContainer.text(this.titlePageText);
            this.footerWithNext();

        } else {

            if (i > this.questions.length) {

                let $content = this.summaryPageContent();
                this.controlContainer.setContent($content);
                this.titlePageText = this.titleText.concat(" complete");
                this.titleContainer.text(this.titlePageText);
                this.footerWithBack();

            } else {
                const question = this.questions[i - 1];
                const questionControl = new QuestionControl(
                    this.sessionData,
                    question,
                    (questionDefId: number, answer: string) =>
                        this.updatedAnswer(questionDefId, answer));
                questionControl.applyValue(this.questionAnswers[question.id]);
                questionControl.render();
                this.controlContainer.setContent(questionControl);
                this.titlePageText = this.titleText.concat(
                    " question " + i.toString() + " of " + this.questions.length);
                this.titleContainer.text(this.titlePageText);
                this.footerWithBackAndNext();
            }

        }
    }

    private summaryPageContent() : $.JQuery {

        const $summaryBlurb = $("<p>").css("min-height", "50px");


        const $blurb = this.getWizardContentForSummaryPage();

        $summaryBlurb.append($blurb.append("<br>"));
        const $content = $("<div>").css("text-align", "center")
            .append($summaryBlurb);

        if (this.countAnswers() > 0) {
            const label = this.overrideSubmit ? "ok" : "save";
            const $saveButton = new ActionButton(label)
                .autoDisable(true)
                // advance past the end for completion
                .clickSynchronous(this.advanceWizardPage);

            $content.append($saveButton.element()
                    .addClass("btn btn-primary"));
        }

        return $content;
    }

    private updatedAnswer(questionDefId: number, answerValue: string) {
        this.questionAnswers[questionDefId] = answerValue;
    }

    protected hasAnswer(questionId: number) {
        for (let key in this.questionAnswers) {
            if (questionId.toString() == key) {
                return true;
            }
        }
        return false;
    }

    protected countAnswers() {
        return this.questions.filter( qn => {
            return this.hasAnswer(qn.id);
        }).length;
    }

    /**
     * Fulfil our provided promises (eg close support window) on a close
     * trouble is this becomes our 'onFinished' because every dialog closes and isFinished
     * and we can't tell if it was done via 'next' or close icon
     */
    public onCloseIcon() {
        let hasAnswered = (this.countAnswers() > 0);
        let hasQuestions = this.questions.length > 0;
        if (hasAnswered || hasQuestions) {
            this.callCancelWizardPage();
        } else {
            this.callAfterCancelledPage();
        }
    }

    protected callCancelWizardPage() {
        this.cancelWizardPage();
        this.updateReactModal();
    }
    protected callAfterCancelledPage() {
        this.afterCancelled();
    }

    public wizardCompleted() {
        let cmdQueue = new CommandQueue(getCommandQueueRepository());
        this.emitCommentTo(cmdQueue);
        this.emitAnswersTo(cmdQueue);

        if (this.overrideSubmit) {
            this.overrideSubmit(cmdQueue);
        } else {
            // save commands then afterSubmitted closes dialog
            cmdQueue.flushCommands()
                .catch((e: Error) => {
                    showErrorAsAlert(e);
                    throw e;
                })
                .then(() => {
                    this.afterSubmitted();
                });
        }
    }

    private footerWithNext() {
        this.footerActions = [
            this.closeIconAction,
            {
                label: "next",
                onClick: () => { this.advanceWizardPage(); this.updateReactModal(); return Promise.resolve(); },
                style: "primary"}
        ];
    }

    private footerWithRedraw() {
        this.footerActions = [
            this.closeIconAction,
            {
                label: "back",
                onClick: () => { this.renderWizardPage(); this.updateReactModal(); return Promise.resolve(); },
                style: "primary"}
        ];
    }

    private footerWithBack() {
        this.footerActions = [
            this.closeIconAction,
            {
                label: "back",
                onClick: () => { this.previousWizardPage(); this.updateReactModal(); return Promise.resolve(); },
                style: "primary"}
        ];
    }

    private footerWithBackAndNext() {
        this.footerActions = [
            this.closeIconAction,
            {label: "back",
                onClick: () => { this.previousWizardPage(); this.updateReactModal(); return Promise.resolve(); },
                style: "primary"},
            {label: "next",
                onClick: () => { this.advanceWizardPage(); this.updateReactModal(); return Promise.resolve(); },
                style: "primary"}
        ];
        // FIXME: this.updateActions
    }

    private emitCommentTo(queue: CommandQueue) {

        const duration = EccoDateTime.nowLocalTime().subtractDateTime(this.timeStarted);
        const minutesSpent = duration.inMinutes();

        const cmdQ = CommentCommand.create(false, this.workUuid, this.serviceRecipientId,
                this.questionnaireEvidenceGroup, this.taskName)
                .changeComment(null, "")
                .changeWorkDate(null, EccoDateTime.nowLocalTime().formatIso8601())
                .changeMinsSpent(null, minutesSpent)
                .build();

        queue.addCommand(cmdQ);
    }

    private emitAnswersTo(queue: CommandQueue) {
        for (let key in this.questionAnswers) {
            const questionDefId = parseInt(key);
            const answer = this.questionAnswers[questionDefId];
            const cmd = new QuestionAnswerCommand("add", this.workUuid,
                this.serviceRecipientId, questionDefId,
                this.questionnaireEvidenceGroup.name, this.taskName);
            cmd.changeAnswer(null, answer);
            queue.addCommand(cmd);
        }
    }

}
export = QuestionnaireWizardForm;
