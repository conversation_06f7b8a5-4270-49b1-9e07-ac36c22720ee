package com.ecco.offline;

import com.ecco.infrastructure.config.ApplicationProperties;

/**
 * Exposes {@link ApplicationProperties} to JavaScript by generating a
 * JavaScript AMD module named application-properties.</code>.
 * <p/>
 * The AMD module exposes each property of {@link ApplicationProperties} as a
 * property of the module.
 * <p/>
 * As with all offline resources, the script is generated once at start-up time,
 * and is subsequently cached in memory to satisfy further requests.
 *
 * @see ApplicationProperties
 */
public class ApplicationPropertiesOfflineResourceProvider extends JavaScriptPropertiesOfflineResourceProvider {

    public ApplicationPropertiesOfflineResourceProvider(ApplicationProperties applicationProperties) {
        addCachedResource("scripts/application-properties.js", "applicationProperties" , applicationProperties);
    }
}

