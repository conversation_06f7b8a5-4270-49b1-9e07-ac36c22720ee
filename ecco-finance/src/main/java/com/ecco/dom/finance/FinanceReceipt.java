package com.ecco.dom.finance;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Setter
@Getter
@Entity
@Table(name="fin_receipts")
@NoArgsConstructor(force = true)
public class FinanceReceipt extends AbstractIntKeyedEntity {

    BigDecimal amount;

    String description;

    Integer serviceRecipientId;

    LocalDate receivedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "typeDefId", insertable = false, updatable = false)
    private ListDefinitionEntry type;

    @Column(name = "typeDefId")
    private Integer typeDefId;

}
