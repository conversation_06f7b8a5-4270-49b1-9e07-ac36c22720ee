package com.ecco.dom.finance.commands

import com.ecco.dom.contracts.commands.FinanceCommand
import org.joda.time.Instant
import java.util.*
import javax.persistence.DiscriminatorValue
import javax.persistence.Entity

@Entity
@DiscriminatorValue("HbIncomeStatement")
class HousingBenefitIncomeStatementCommand(uuid: UUID?, remoteCreationTime: Instant, userId: Long, body: String) :
    FinanceCommand(uuid, remoteCreationTime, userId, body)