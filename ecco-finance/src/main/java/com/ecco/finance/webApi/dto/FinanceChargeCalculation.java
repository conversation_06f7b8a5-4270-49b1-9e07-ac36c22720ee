package com.ecco.finance.webApi.dto;

import static com.ecco.infrastructure.util.EccoTimeUtils.LONDON;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.contracts.ratecards.RateCardCalculationBuildings;
import com.google.common.collect.Range;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class FinanceChargeCalculation {

    private final FixedContainerRepository buildingRepository;

    public enum ChargeReason {MOVE_IN, MOVE_OUT, RATE, REPORT_END, BREAK}

    public record ChargeChange(
            Instant date,
            ChargeReason reason,
            String description,
            Integer serviceRecipientId, // charge against, eg client
            Integer buildingId,
            RateCard rateCard
    ) {
        public static ChargeChange buildCharge(@NonNull Instant date, @NonNull ChargeReason reason, @NonNull String description, @NonNull Integer srId, @NonNull Integer bldgId) {
            return new ChargeChange(date, reason, description, srId, bldgId, null);
        }
        private static ChargeChange buildRateCard(@NonNull Instant date, @NonNull String description, @NonNull RateCard rateCard) {
            return new ChargeChange(date, ChargeReason.RATE, description, null, null, rateCard);
        }
        private static ChargeChange buildReportEnd(@NonNull Instant date) {
            return new ChargeChange(date, ChargeReason.REPORT_END, null, null, null, null);
        }
        private static ChargeChange buildBreak(@NonNull Instant date, @NonNull String description) {
            return new ChargeChange(date, ChargeReason.BREAK, description, null, null, null);
        }
    }

    /**
     * Calculate the lines of the charges.
     * Starts from the first chargeable point (which is assumed to be data loaded around the from date)
     * and ends at the report end date (otherwise there might be no end - could possibly do financial year etc)
     * NB chargeChanges can be:
     *  - a set of unrelated charges (eg address history last month)
     *  - a set of charges from one client
     *  - a set of charges from one building
     * Notably, this doesn't have a running total - and we should never with paged address history
     * Notably, each paged address history contains everything for the calculations (so state between pages isn't needed)
     *   - because each item has a start/end which is split into complete set of charges until report end
     *   - and we don't do a running total
     * Notably, each line of address history will have consecutive charge lines which contains everything for the calculations
     *   - so won't have other clients in it
     *   - if another's client data could interject this, then we may need to reconsider this
     */
    public List<ClientSalesChargeInvoiceDetailResource.Line> calculateLines(Range<Instant> reportRange, List<ChargeChange> chargeChanges, List<RateCard> rateCards, boolean breakByRateCardPeriod) {

        assert reportRange.hasUpperBound(); // enforcing a report range makes logic easier
        // get all rate cards applicable for this period (including rate cards that follow on)
        // enforcing the calling method provides rate cards of the same type
        Assert.state(rateCards.stream().map(RateCard::getChargeNameId).distinct().count() == 1, () -> "rateCards in one calculation must be of the same charge name");

        Integer currentBldgId = null;
        RateCard currentRateCard = null;
        Integer currentSrId = null;
        final RateCardCalculation calculator = new RateCardCalculationBuildings();

        // multiple rate cards are sequential in time (of the same calculation/chargeName)
        var rateCardsInRange = calculator.getRateCardsInDateRange(reportRange, rateCards);
        var chargeChangeRateCards = rateCardsInRange.stream()
                .map(r -> ChargeChange.buildRateCard(r.getStartInstant(), "rate change (id " + r.getChargeNameId() + ")", r))
                .toList();

        // generate BREAKs in the period of the charges
        var chargeChangeBreaks = breakByRateCardPeriod
                ? generateBreakCharges(reportRange)
                : Collections.<FinanceChargeCalculation.ChargeChange> emptyList();

        // we add the reportRange as a chargeChange item but this does mean it cuts-up the charges exactly to the date
        // however, we only add the end date because that is required else things could go to infinity
        // whereas we just include the nearest charge around the report start date without cutting it up
        var chargeChangeReportEnd = ChargeChange.buildReportEnd(reportRange.upperEndpoint());

        // put the charges together in order, preserving the order of chargeChanges
        // and interspersing rate cards and report end at appropriate points in time
        // !! this makes each addressHistory sequential !!
        var chargeChangeAll = new ArrayList<>(chargeChanges);

        // Intersperse rate cards at appropriate points in time
        for (var rateCardChange : chargeChangeRateCards) {
            int insertIndex = 0;
            // Find the correct position to insert this rate card change
            for (int i = 0; i < chargeChangeAll.size(); i++) {
                if (chargeChangeAll.get(i).date().isAfter(rateCardChange.date())) {
                    insertIndex = i;
                    break;
                } else {
                    insertIndex = i + 1;
                }
            }
            chargeChangeAll.add(insertIndex, rateCardChange);
        }

        // Intersperse BREAK charges at appropriate points in time
        for (var breakChange : chargeChangeBreaks) {
            int insertIndex = 0;
            // Find the correct position to insert this break change
            for (int i = 0; i < chargeChangeAll.size(); i++) {
                if (chargeChangeAll.get(i).date().isAfter(breakChange.date())) {
                    insertIndex = i;
                    break;
                } else {
                    insertIndex = i + 1;
                }
            }
            chargeChangeAll.add(insertIndex, breakChange);
        }

        // Intersperse the report end at the appropriate point in time
        int reportEndIndex = 0;
        for (int i = 0; i < chargeChangeAll.size(); i++) {
            if (chargeChangeAll.get(i).date().isAfter(chargeChangeReportEnd.date())) {
                reportEndIndex = i;
                break;
            } else {
                reportEndIndex = i + 1;
            }
        }
        // we could be paged, so not at the end yet, but this is only used to stop the calculation
        chargeChangeAll.add(reportEndIndex, chargeChangeReportEnd);

        // process the charges
        List<ClientSalesChargeInvoiceDetailResource.Line> lines = new ArrayList<>();
        var lastIndex = chargeChangeAll.size() - 1;
        for (int i = 0; i < chargeChangeAll.size(); i++) {
            var current = chargeChangeAll.get(i);
            // next is the move out or a new rate card or the report end date
            // in fact, we prioritise the report date if it comes up
            var next = current.reason.equals(ChargeReason.REPORT_END)
                    ? null
                    : (i+1) <= lastIndex
                    ? chargeChangeAll.get(i+1)
                    : null;

            currentSrId = current.reason.equals(ChargeReason.MOVE_IN)
                    ? current.serviceRecipientId
                    : current.reason.equals(ChargeReason.MOVE_OUT)
                    ? null
                    : currentSrId;
            currentRateCard = current.reason.equals(ChargeReason.RATE) ? current.rateCard : currentRateCard;
            currentBldgId = current.reason.equals(ChargeReason.MOVE_IN) ? current.buildingId : currentBldgId;

            // to do a charge, we need to not be at the last item, and have a rate card (which could be really far in the past) and a move in available now
            if (next != null && currentRateCard != null && currentSrId != null) {
                var chargeFrom = current.date;
                var chargeTo = next.date != null ? next.date : null;
                var buildingChargeCategoryInfo = findChargeCategoryForBuilding(currentBldgId, currentRateCard);

                // if there is nothing to charge on the bldg for the current rateCardsByChargeNameId, just skip
                if (buildingChargeCategoryInfo != null) {

                    // TODO: This needs to not make assumptions on what rate card entries match or do conversions
                    // find an entry matching matchChargeCategoryId to the building chargeCategoryId
                    var entries = calculator.determineRateCardEntries(currentRateCard, null, buildingChargeCategoryInfo.getChargeCategoryId(), null);

                    // Create a ZonedDateTime range for the charge period - TODO: Clarify if we want UTC or LONDON as the zone
                    if (chargeTo != null) {
                        ZonedDateTime start = chargeFrom.atZone(LONDON);
                        ZonedDateTime end = chargeTo.atZone(LONDON);
                        Assert.state(!start.isAfter(end), "start must not be after end");
                        Range<ZonedDateTime> dateTimeRange = Range.closedOpen(start, end);
                        var charge = calculator.calculateCharge(entries, dateTimeRange);

                        var line = new ClientSalesChargeInvoiceDetailResource.Line(currentSrId,
                                UUID.randomUUID(), null, current.description, currentRateCard.getId(), currentRateCard.getChargeNameId(), charge, BigDecimal.valueOf(20), null, false,
                                current.buildingId, LocalDateTime.ofInstant(chargeFrom, LONDON), LocalDateTime.ofInstant(chargeTo, LONDON));
                        lines.add(line);
                    }
                }
            }
        }

        return lines;
    }

    public FixedContainer.@Nullable ChargeCategoryCombinations findChargeCategoryForBuilding(Integer buildingId, RateCard rateCard) {
        return buildingRepository.findById(buildingId)
                .map(building -> {
                    // Find a charge category combination that matches the rate card's chargeNameId
                    return findChargeCategoryCombinationForRateCard(building, rateCard);
                })
                .orElse(null);
    }

    /**
     * Finds a charge category combination that matches the given RateCard's chargeNameId.
     * Returns the first matching combination (expected to be one per calc per building), or null if no match is found.
     */
    FixedContainer.@Nullable ChargeCategoryCombinations findChargeCategoryCombinationForRateCard(@NonNull FixedContainer building, @NonNull RateCard rateCard) {
        var combinations = building.getNearestChargeCategoryCombinations();

        for (var combination : combinations) {
            if (combination.getChargeNameId() != null && rateCard.getChargeNameId() != null) {
                if (combination.getChargeNameId().equals(rateCard.getChargeNameId())) {
                    return combination;
                }
            } else if (combination.getChargeNameId() == null && rateCard.getChargeNameId() == null) {
                // Both are null, consider it a match
                return combination;
            }
        }

        return null;
    }

    /**
     * Generates BREAK charges for each Monday within the reporting period.
     */
    private List<ChargeChange> generateBreakCharges(Range<Instant> reportRange) {
        List<ChargeChange> breakCharges = new ArrayList<>();

        // Convert report range to London timezone for day-of-week calculations
        ZonedDateTime start = reportRange.lowerEndpoint().atZone(LONDON);
        ZonedDateTime end = reportRange.upperEndpoint().atZone(LONDON);

        // Find the first Monday on or after the start date
        ZonedDateTime current = start;
        while (current.getDayOfWeek() != DayOfWeek.MONDAY) {
            current = current.plusDays(1);
        }

        // Generate BREAK charges for each Monday until the end date
        while (current.isBefore(end)) {
            Instant mondayInstant = current.toInstant();
            String description = "";
            //String description = "Weekly break - " + current.toLocalDate().toString();
            breakCharges.add(ChargeChange.buildBreak(mondayInstant, description));
            current = current.plusWeeks(1); // Move to next Monday
        }

        return breakCharges;
    }

}
