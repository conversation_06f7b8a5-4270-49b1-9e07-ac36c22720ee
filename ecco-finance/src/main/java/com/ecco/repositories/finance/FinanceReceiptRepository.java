package com.ecco.repositories.finance;

import com.ecco.dom.finance.FinanceReceipt;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

public interface FinanceReceiptRepository extends QuerydslPredicateExecutor<FinanceReceipt>, CrudRepositoryWithFindOne<FinanceReceipt, Integer> {

    List<FinanceReceipt> findAllByServiceRecipientIdOrderByReceivedDateDesc(int serviceRecipientId);

}
