/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-kotlin-library")
}

dependencies {
    implementation(project(":ecco-config"))
    implementation(project(":ecco-buildings"))
    implementation(project(":ecco-contracts"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-servicerecipient"))

    implementation("joda-time:joda-time:2.10.8")
    implementation("com.google.guava:guava")
}

description = "ecco-finance"
