package com.ecco.buildings.repositories;

import com.ecco.dom.AddressedLocation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface AddressRepository extends JpaRepository<AddressedLocation,Integer> {

    List<AddressedLocation> findAllByPostCodeAndDisabledIsFalseOrderByLine1(String postCode);

    AddressedLocation findOneByPostCodeAndLine1(String postcode, String string);
}
