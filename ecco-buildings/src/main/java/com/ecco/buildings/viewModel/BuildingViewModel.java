package com.ecco.buildings.viewModel;

import org.jspecify.annotations.Nullable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Simplified building view model that lives in the domain module.
 * This avoids circular dependencies with ecco-web-api while allowing
 * entity-to-viewmodel conversion within transactional boundaries.
 */
public class BuildingViewModel {

    public Integer buildingId;
    public String name;
    public Boolean disabled;
    public String externalRef;
    public Integer resourceTypeId;
    public String resourceTypeName;
    public Integer serviceRecipientId;
    public String calendarId;
    @Nullable
    public Integer parentId;
    @Nullable
    public String parentName;
    public Integer serviceAllocationId;
    public Integer locationId;
    @Nullable
    public AddressedLocationViewModel location;
    @Nullable
    public List<ChargeCategoryCombination> chargeCategoryCombinations;
    public HashMap<String, String> textMap = new HashMap<>();
    public Map<String, Integer> choicesMap = new HashMap<>();

    public static class AddressedLocationViewModel {
        @Nullable
        public Integer addressId;
        @Nullable
        public String buildingName;
        @Nullable
        public String buildingNumber;
        @Nullable
        public String line1;
        @Nullable
        public String line2;
        @Nullable
        public String line3;
        @Nullable
        public String town;
        @Nullable
        public String county;
        @Nullable
        public String postcode;
        @Nullable
        public String country;
        @Nullable
        public Boolean disabled;
    }

    public static class ChargeCategoryCombination {
        @Nullable
        public Integer chargeCategoryId;
        @Nullable
        public Integer chargeNameId;

        public ChargeCategoryCombination() {}

        public ChargeCategoryCombination(@Nullable Integer chargeNameId, @Nullable Integer chargeCategoryId) {
            this.chargeNameId = chargeNameId;
            this.chargeCategoryId = chargeCategoryId;
        }
    }

}
