package com.ecco.buildings.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.buildings.viewModel.BuildingToViewModel;

@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(basePackageClasses=AddressRepository.class)
@ComponentScan(basePackageClasses=BuildingToViewModel.class)
public class BuildingsConfig {

}
