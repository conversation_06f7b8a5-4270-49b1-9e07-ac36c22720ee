package com.ecco.security;

import org.apache.commons.lang3.StringUtils;

import java.security.NoSuchAlgorithmException;
import javax.crypto.KeyGenerator;

public class DefaultKeyGenerator implements com.ecco.security.KeyGenerator {

    private final int bits;

    public DefaultKeyGenerator(final int bits) {
        this.bits = bits;
    }

    @Override
    public byte[] generateRandomKey(String cipher) {
        String cipherType = StringUtils.substringBefore(cipher, "-");
        // generate a key
        try {
            KeyGenerator keygen = KeyGenerator.getInstance(cipherType);
            keygen.init(bits);  // To use 256 bit keys, you need the "unlimited strength" encryption policy files from Sun.
            return keygen.generateKey().getEncoded();
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("No such algorithm for cipher: " + cipher, e);
        }
    }
}
