package com.ecco.security.event;

import com.ecco.calendar.dom.Calendarable;

import org.springframework.context.ApplicationEvent;

public abstract class CalendarableEvent<T extends Calendarable> extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    protected final T contact;

    public CalendarableEvent(Object source, T contact) {
        super(source);
        this.contact = contact;
    }

    public Calendarable getContact() {
        return contact;
    }
}
