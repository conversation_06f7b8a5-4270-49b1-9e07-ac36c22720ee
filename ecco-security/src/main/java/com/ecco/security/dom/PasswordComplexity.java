package com.ecco.security.dom;

import java.util.regex.Pattern;

public enum PasswordComplexity {
    /** Anything that is long enough */
    SIMPLE,

    /** e.g. notS0safe */
    ONE_DIGIT_ONE_UPPER,

    /** e.g. Sp3c!alButSt!lleasy */
    ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8;

    private static final String containsDigit = "(?=.*[0-9])";
    private static final String containsLower = "(?=.*[a-z])";
    private static final String containsUpper = "(?=.*[A-Z])";
    private static final String noWhiteSpace = "(?=\\S+$)";
    private static final Pattern lowerUpperDigit = Pattern.compile("^" + containsDigit + containsLower + containsUpper + noWhiteSpace + ".*");

    public static final String containsSpecial = "(?=.*[!\"`¬£$%^&*()_\\-+={}\\[\\];:@'~#<,>.?/\\\\|€])"; // TODO: Could do with making this an inverse match of the other patterns
    public static final String has8characters = ".{8,}$";
    private Pattern lowerUpperDigitSpecial = Pattern.compile("^" + containsDigit + containsLower + containsUpper + noWhiteSpace + containsSpecial + has8characters);

    public boolean isSecureEnough(String candidatePassword) {
        switch (this) {
            case SIMPLE:
                return true;
            case ONE_DIGIT_ONE_UPPER:
                return lowerUpperDigit.matcher(candidatePassword).matches() || isReasonablePassPhrase(candidatePassword);
            case ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8:
                return lowerUpperDigitSpecial.matcher(candidatePassword).matches();
        }
        throw new IllegalStateException("Unknown Enum " + this);
    }

    private boolean isReasonablePassPhrase(String candidatePassword) {
        if (candidatePassword.length() < 14) {
            return false;
        }

        // exclude repetitions of 4 or more chars (does mean that prettysmallllama would be excluded, but we'd allow prettysmallLlama
        char[] chars = candidatePassword.toCharArray();
        char repeatedChar = chars[0];
        int count = 1;

        for (int pos = 1; pos < chars.length; pos++) {
            if (chars[pos] == repeatedChar) {
                if (count == 3) { // we already have 3, so fail as this is the 4th
                    return false;
                }
                count++;
            }
            else {
                count = 1;
                repeatedChar = chars[pos];
            }
        }
        return true;
    }
}
