package com.ecco.security.dom;

import static org.springframework.http.HttpStatus.Series.SUCCESSFUL;
import static org.springframework.http.HttpStatus.valueOf;

import org.springframework.http.HttpStatus;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.ProxyDtoBuilderProxy;

public interface CommandResponse extends BuildableDto<CommandResponse> {
    int getStatusCode();

    String getBody();

    // This would be better as a default method but ProxyDto isn't yet able to handle that
    static boolean isSuccessful(CommandResponse response) {
        HttpStatus httpStatus = valueOf(response.getStatusCode());

        return httpStatus.series() == SUCCESSFUL;
    }

    interface Builder extends DtoBuilder<CommandResponse> {
        Builder statusCode(int statusCode);
        Builder body(String body);
    }

    class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, CommandResponse.class);
        }
    }
}
