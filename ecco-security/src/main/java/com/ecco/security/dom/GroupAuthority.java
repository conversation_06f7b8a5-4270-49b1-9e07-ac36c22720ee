package com.ecco.security.dom;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "group_authorities")
public class GroupAuthority implements Serializable {

    @Id
    @ManyToOne(fetch = FetchType.LAZY) // See: https://hibernate.onjira.com/browse/HHH-7505
    private Group group;
    @Id
    private String authority;

    protected GroupAuthority() {
    }

    public GroupAuthority(Group group, String authority) {
        this.group = group;
        this.authority = authority;
    }

    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupAuthority that = (GroupAuthority) o;

        if (!authority.equals(that.authority)) return false;
        if (!group.equals(that.group)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = group.hashCode();
        result = 31 * result + authority.hashCode();
        return result;
    }
}
