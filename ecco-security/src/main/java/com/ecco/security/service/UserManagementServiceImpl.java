package com.ecco.security.service;

import com.ecco.config.dom.Setting;
import com.ecco.config.dom.SoftwareModule;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dom.EccoRevision;
import com.ecco.dom.Individual;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.util.FlagMap;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.GroupAuthority;
import com.ecco.security.dom.GroupMember;
import com.ecco.security.dom.User;
import com.ecco.security.event.CalendarableCreated;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.calendar.core.util.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditQuery;
import org.hibernate.query.Query;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.context.ApplicationEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import java.security.InvalidKeyException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;

import static java.util.stream.Collectors.toList;

@SuppressWarnings("JavadocReference")
@Service("userManagementService")
@RequiredArgsConstructor
@WriteableTransaction // CAREFUL!!!
public class UserManagementServiceImpl implements UserManagementService {

    public static final class GrantedAuthorityMatcher implements Predicate<GrantedAuthority> {
        private final String authority;

        public GrantedAuthorityMatcher(String authority) {
            this.authority = authority;
        }

        @Override
        public boolean test(@Nullable GrantedAuthority input) {
            return input.getAuthority().equals(authority);
        }
    }

    private final PasswordEncoder passwordEncoder;
    private final EntityManager entityManager;
    private final MessageBus<ApplicationEvent> messageBus;
    private final SettingsService settingsService;
    private final GroupRepository groupRepository;
    private final SoftwareModuleService softwareModuleService;
    private final TotpService totpService = new TotpService();

    @Override
    public User loadUserByUsername(String username) throws UsernameNotFoundException {
        // we need to match users regardless of case, otherwise 'ecco' is different to 'Ecco' which isn't great
        // also changed userExists below
        // cosmo also uses 'lower' so it rejects the new user - see cosmo/src/main/java/org/osaf/cosmo/model/hibernate/package-info.java and org.osaf.cosmo.dao.hibernate.UserDaoImpl
        // we could change the storage to use lowercase to avoid db functions, but for now it seems cleaner to save what the user wants and to load regardless (also as cosmo appears to do)
        // also it allows us to save case sensitivity - which may be useful with other authentication mechanisms - eg ldap / SSO
        String usernameCI = StringUtils.lowerCase(username);
        var q = getCurrentSession()
                .createQuery("SELECT u FROM User u WHERE lower(u.username) = lower(:username)", User.class);
        q.setParameter("username", usernameCI);
        User user = q.uniqueResult();
        if (user == null) {
            throw new UsernameNotFoundException("No user found with username: " + username);
        }
        return user;
    }

    @Override
    public Long findUserId(String username) {
        var q = getCurrentSession()
                .createQuery("SELECT u.id FROM User u WHERE u.username = :username", Long.class);
        q.setParameter("username", username);
        return q.uniqueResult();
    }

    @Override
    public String findUsernameFromId(long userId) {
        var q = getCurrentSession()
                .createQuery("SELECT u.username FROM User u WHERE u.id = :id", String.class);
        q.setParameter("id", userId);
        return q.uniqueResult();
    }

    private Session getCurrentSession() {
        return (Session)entityManager;
    }

    /**
     * Creates a new user, digesting the {@link User#newPassword`} before persisting
     * @param user with a raw {@link User#newPassword`}, i.e. NOT encoded NOR digested (md5, sha-1 and the like)
     */
    @Override
    public void createUser(UserDetails user) {
        // TODO[ee,20130327]: eliminate down-cast
        User u = (User) user;
        u.encodedNewPassword(passwordEncoder.encode(u.getNewPassword()));
        if (u.getContact() != null) {
            u.getContact().setUser(u);
        }
        getCurrentSession().persist(u);
        if (u.getNewGroups() != null) {
            u = mergeNewGroups(u, new HashSet<>(u.getNewGroups()));
        }

        getCurrentSession().flush();
        // By persisting the user, we will have cascade-persisted a new contact too.
        fireContactCreated(u.getContact());
    }

    private void fireContactCreated(Individual contact) {
        messageBus.publishBeforeTxEnd(new CalendarableCreated(this, contact));
    }

    /**
     * Updates an existing user, digesting the {@link User#newPassword`} before persisting if not null
     * @param user with an optional (nullable) raw {@link User#newPassword`}, i.e. NOT encoded NOR digested (md5, sha-1 and the like)
     */
    @Override
    public void updateUser(UserDetails user) {
        User u = (User) user;
        if (u.getNewPassword() != null) {
            u.encodedNewPassword(encodePassword(u.getNewPassword(), u));
        }
        // update gave an error from ui 'detached entity passed to persist: com.ecco.dom.Individual'
        // http://stackoverflow.com/questions/5459176/difference-between-hibernate-update-by-session-update-and-hibernatetemplate-merg
        if (u.getNewGroups() != null) {
            u = mergeNewGroups(u, new HashSet<>(u.getNewGroups()));
        }
        getCurrentSession().merge(u);
    }

    @Override
    public void deleteUser(String username) {
        getCurrentSession().delete(loadUserByUsername(username));
    }

    @Override
    public void changePassword(String oldPassword, String newPassword) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean userExists(String username) {
        Query<?> q = getCurrentSession()
                .createQuery("SELECT 1 FROM User u WHERE lower(u.username) = lower(:username)");
        q.setParameter("username", username);
        return q.uniqueResult() != null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<String> findAllGroups() {

        var isSysadmin = SecurityUtil.hasAuthority("ROLE_SYSADMIN");
        var isSiteAdmin = SecurityUtil.hasAuthority("ROLE_ADMIN");
        var isAAA = SecurityUtil.hasAuthority("ROLE_AAA");

        Setting groupNamesToExcludeSetting = settingsService.settingFor(SettingsService.SecurityGroups.NAMESPACE,
                SettingsService.SecurityGroups.GROUPS_TO_EXCLUDE);
        FlagMap groupNamesToExclude = groupNamesToExcludeSetting != null ? groupNamesToExcludeSetting.getAsFlagMap() : new FlagMap();

        var rotaEnabled = softwareModuleService.getEnabledModules().get(SoftwareModule.MODULE_ROTA) != null;
        if (!rotaEnabled) {
            groupNamesToExclude.putAll(new FlagMap("rota,carer,finance"));
        }

        var bldgEnabled = softwareModuleService.getEnabledModules().get(SoftwareModule.MODULE_BUILDING) != null;
        if (!bldgEnabled) {
            groupNamesToExclude.putAll(new FlagMap("admin building"));
        }

        // dynamic groups to include, to avoid escalating privileges
        return groupRepository.findAll().stream()
            .map(Group::getName)
            .filter(name -> isSysadmin || !name.equals("sysadmin")) // exclude 'sysadmin' unless privileged
            .filter(name -> isSysadmin || isSiteAdmin || isAAA || !name.equals("all service access")) // exclude 'all service access' unless privileged
            .filter(name -> isSysadmin || isSiteAdmin || !name.equals("site sysadmin")) // exclude 'site sysadmin' unless privileged
            .filter(name -> isSysadmin || isSiteAdmin || !groupNamesToExclude.containsKey(name)) // exclude group unless privileged
            .sorted(String::compareToIgnoreCase)
            .collect(toList());
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<String> findUsersInGroup(String groupName) {
        var q = getCurrentSession()
                .createQuery("SELECT u.username " +
                        "FROM Group g " +
                        "INNER JOIN g.memberships ms " +
                        "INNER JOIN ms.member u " +
                        "WHERE g.name = :groupName ",
                        String.class);
        q.setParameter("groupName", groupName);
        return q.list();
    }

    // TODO this relies on clients being the only 3rd party group... we should strengthen this
    @Override
    public List<String> findUsersNotThirdParty() {
        var q = getCurrentSession()
                .createQuery("SELECT u.username " +
                        "FROM Group g " +
                        "INNER JOIN g.memberships ms " +
                        "INNER JOIN ms.member u " +
                        "WHERE g.name != :groupName",
                        String.class);
        q.setParameter("groupName", CLIENTS);
        return q.list();
    }

    @Override
    public void createGroup(String groupName, List<GrantedAuthority> authorities) {
        Group group = new Group(groupName, authorities);
        getCurrentSession().save(group);
    }

    @Override
    public void deleteGroup(String groupName) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void renameGroup(String oldName, String newName) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void addUserToGroup(String username, String group) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void removeUserFromGroup(String username, String groupName) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<GrantedAuthority> findGroupAuthorities(String groupName) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void addGroupAuthority(String groupName, GrantedAuthority authority) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void removeGroupAuthority(String groupName, GrantedAuthority authority) {
        throw new UnsupportedOperationException();
    }

    @Override
    public User generateClientUser(String displayName, Individual contact) {
        String username;
        username = StringUtils.lowerCase(displayName);
        username = StringUtils.strip(username);
        username = username.replaceAll("[ ,()]", "_");
        username = generateUsername(username + "00");

        // save stuff
        String password = createPassword();
        User user = createDefaultUser();
        user.setContact(contact);
        user.setUsername(username);
        user.setNewPassword(password);
        createUser(user);
        Group clientGroup = findGroupByName(Group.CLIENT_GROUP);
        if (user.getGroupMemberships() == null) {
            user.setGroupMemberships(new HashSet<>());
        }
        user.getGroupMemberships().add(new GroupMember(clientGroup, user));
        return user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<String> getAllUsernames() {
        Query q = getCurrentSession().createQuery("SELECT username FROM User");
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<IndividualUserSummary> getAllUserIndividuals() {
        Query q = getCurrentSession().createQuery(
                "SELECT NEW com.ecco.dom.IndividualUserSummary(i.id, i.firstName, i.lastName, i.user.id, i.user.username, i.calendarId, i.email, i.user.enabled) " +
                "FROM Individual i WHERE i.user IS NOT NULL");
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<String> findUsersWithAuthority(String authority) {
        Query<String> q = getCurrentSession()
                .createQuery("SELECT DISTINCT u.username " +
                             "FROM GroupAuthority ga " +
                             "INNER JOIN ga.group g " +
                             "INNER JOIN g.memberships m " +
                             "INNER JOIN m.member u " +
                             "WHERE ga.authority = :authorityName ");
        q.setParameter("authorityName", authority);
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<IndividualUserSummary> findIndividualsFromUsername(String username) {
        Query<IndividualUserSummary> q = getCurrentSession()
                .createQuery("SELECT DISTINCT NEW com.ecco.dom.IndividualUserSummary(i.id, i.firstName, i.lastName, u.id, u.username, i.calendarId, i.email, u.enabled) " +
                        "FROM User u " +
                        "INNER JOIN u.contact i " +
                        "WHERE u.username = :username");
        q.setParameter("username", username);
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<IndividualUserSummary> findIndividualsFromContactId(int contactId) {
        Query q = getCurrentSession()
                .createQuery("SELECT DISTINCT NEW com.ecco.dom.IndividualUserSummary(i.id, i.firstName, i.lastName, u.id, u.username, i.calendarId, i.email, u.enabled) " +
                        "FROM User u " +
                        "INNER JOIN u.contact i " +
                        "WHERE i.id = :contactId");
        q.setParameter("contactId", Long.valueOf(contactId));
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<IndividualUserSummary> findIndividualsWithAuthority(String authority) {
        // could follow 'findUuidByEventId'
        var qGroupList = getCurrentSession()
                .createQuery("SELECT ga.group.id from GroupAuthority ga WHERE ga.authority = :authority");
        qGroupList.setParameter("authority", authority);
        List<Integer> groupIdsWithAuthority = qGroupList.list();

        var q = getCurrentSession()
                .createQuery("SELECT NEW com.ecco.dom.IndividualUserSummary(i.id, i.firstName, i.lastName, u.id, u.username, i.calendarId, i.email, u.enabled) " +
                         "FROM Group g " +
                         "INNER JOIN g.memberships m " +
                         "INNER JOIN m.member u " +
                         "INNER JOIN u.contact i " +
                         "WHERE g.id IN (:groupIds) " +
                         "ORDER BY i.lastName, i.firstName, u.username");
        q.setParameter("groupIds", groupIdsWithAuthority);
        return q.list().stream().distinct().toList();
    }

    @Override
    public List<IndividualUserSummary> findIndividualsWithGroups(String... groups) {
        Query q = getCurrentSession()
                .createQuery("SELECT DISTINCT NEW com.ecco.dom.IndividualUserSummary(i.id, i.firstName, i.lastName, u.id, u.username, i.calendarId, i.email, u.enabled) " +
                        "FROM Group g " +
                        "INNER JOIN g.memberships m " +
                        "INNER JOIN m.member u " +
                        "INNER JOIN u.contact i " +
                        "WHERE g.name in (:groupNames) ORDER BY i.lastName, i.firstName, u.username");
        q.setParameter("groupNames", Arrays.asList(groups));
        return q.list();
    }

    @SuppressWarnings("unchecked")
    @Override
    // for the user management screen quick help - to describe what groups can actually do
    public List<GroupAuthority> listGroupAuthorities() {
        Query q = getCurrentSession()
                // inner join to get round the lazy problem on GroupAuthority
                .createQuery("FROM GroupAuthority ga LEFT JOIN FETCH ga.group");
        return q.list();
    }

    @Override
    public void recordSuccessfulLoginTime(String username, DateTime time) {
        User u = loadUserByUsername(username);
        u.setLastLoggedIn(time);
        getCurrentSession().merge(u);
    }

    @Override
    public void recordFailedLogin(Authentication authn) {
        // if we fail, we only have the username as the principal (not the user object)
        String username = (String) authn.getPrincipal();
        WebAuthenticationDetails details = (WebAuthenticationDetails) authn.getDetails();
        String ip = "";
        if (details != null) {
            ip = details.getRemoteAddress();
        }
        DateTime now = DateTimeUtils.getUtcNow();
        getCurrentSession()
                .createSQLQuery("insert into failedlogins (ip, username, faileddate) values (:ip, :username, :timestamp)")
                .setParameter("ip", ip)
                .setParameter("username", username)
                .setParameter("timestamp", new Timestamp(now.getMillis()))
                .executeUpdate();
    }

    @Override
    public boolean isIpBlacklisted(String ip) {
        Object result = getCurrentSession()
                .createSQLQuery("select 1 from blacklistedips b where b.ip=:ip")
                .setParameter("ip", ip)
                .uniqueResult();
        return result != null;

    }

    @Override
    @Scheduled(cron="0 2 2 * * SAT") // weekly at 2:02
    public void deleteExpiredPersistentLogins() {
        DateTime now = DateTimeUtils.getUtcNow();
        // be sure to delete old tokens (avoiding timezone issues etc)
        // the longer we leave items in the db - the more chance a hacker can guess
        now = now.minusWeeks(1);
        getCurrentSession()
                .createSQLQuery("delete from persistent_logins where last_used < :datetime")
                .setParameter("datetime", now.toDate())
                .executeUpdate();
    }

    @Override
    public User mergeNewGroups(User user, Set<String> groupNames) {
        Set<Group> targetGroups = new HashSet<>();
        for (String groupName : groupNames) {
            targetGroups.add(new Group(groupName));
        }
        addMissingGroups(user, targetGroups);
        removeObsoleteGroups(user, targetGroups);
        user = (User) getCurrentSession().merge(user);
        user.deriveGroupsFromMemberships(); // ensure we recalculate the groups for the derived properties to be updated
        return user;
    }

    @Override
    public List findRevisions(AuditSearchScope scope) {
        AuditReader auditReader = AuditReaderFactory.get(getCurrentSession());
        AuditQuery q = auditReader.createQuery().forRevisionsOfEntity(scope.getAuditClass(), scope.isAuditClassOnly(), scope.isIncludeDeleted());
        if (scope.getCriterion() != null) {
            q.add(scope.getCriterion());
        }
        if (scope.getProjection() != null) {
            q.addProjection(scope.getProjection());
        }
        if (scope.getOrder() != null) {
            q.addOrder(scope.getOrder());
        }
        return q.getResultList();
    }

    @Override
    public AuditUserSearchResult findUserRevisions(AuditUserSearchScope scope) {
        // we can cast to Object[] because we know we set isAuditClassOnly = false, meaning "selectEntitiesOnly If true, instead of a list of three-element arrays, a list of entites will be returned as a result of executing this query."
        @SuppressWarnings("unchecked")
        List<Object[]> results = findRevisions(scope);

        LinkedHashSet<UserRevision> revisions = new LinkedHashSet<>();
        for (Object[] row : results) {
            User user = (User) row[0];
            user.getGroupMemberships().size(); // Force collection fetch
            revisions.add(new UserRevision(user, (EccoRevision) row[1], (RevisionType) row[2]));
        }
        return new AuditUserSearchResult(scope, revisions);
    }

    @Override
    public Group findGroupByName(String groupName) {
        Query q = getCurrentSession().createQuery("FROM Group WHERE name = :groupName");
        q.setParameter("groupName", groupName);
        return (Group) q.uniqueResult();
    }

    // simply increment the last 2 digits
    private String generateUsername(String username) {

        // We don't try to modify if last 2 chars are not digits
        String last2chars = StringUtils.substring(username, -2);
        if (!StringUtils.isNumeric(last2chars)) {
            return username;
        }

        int suffix = Integer.valueOf(last2chars);
        String usernamePrefix = StringUtils.substring(username, 0, -2);
        boolean taken = userExists(username);
        while(taken) {
            suffix++;
            String suffixStr = suffix < 10 ? "0" + suffix : String.valueOf(suffix);
            username = usernamePrefix + suffixStr;

            taken = userExists(username);
        }
        return username;
    }

    // we stick with numbers for now to avoid character confusion
    @Override
    public String createPassword() {
        Random randomGenerator = new Random();
        StringBuilder digits = new StringBuilder();
        for (int i = 1; i <= 5; i++) {
            int digi = randomGenerator.nextInt(10);
            digits.append(digi);
        }
        String pwd = digits.toString();
        Assert.hasLength(pwd, "Password must not be empty");
        return pwd;
    }

    public static User createDefaultUser() {
        User user = new User();
        // the current locale determines the site to use, currency and country
        // though in registration of med, its the country that dictates the locale
        // for ecco, we can set these to the user since there is only one site

        // we don't need language as that can come from the locale
        Locale locale = new Locale("en", "GB");
        // we can't know where a user is in the world, but we can guess - this is largely redundant
        String country = "GB";
        // timezone is tricky to find from the user locale - so we default to uk
        DateTimeZone tz = DateTimeZone.forID("Europe/London");

        user.setLocale(locale);
        user.setCountry(country);
        user.setTimeZone(tz);
        return user;
    }

    private void removeObsoleteGroups(User user, Set<Group> targetGroups) {
        // Create new HashSet, see: https://hibernate.onjira.com/browse/HHH-3799
        Set<GroupMember> groupMemberships = new HashSet<>(user.getGroupMemberships());
        Set<GroupMember> obsoleteGroupMemberships = new HashSet<>();
        for (GroupMember membership : groupMemberships) {
            if (!targetGroups.contains(membership.getGroup())) {
                obsoleteGroupMemberships.add(new GroupMember(membership.getGroup(), user));
            }
        }
        for (GroupMember gm : obsoleteGroupMemberships) {
            groupMemberships.remove(gm);
        }
        if (user.getGroupMemberships() != null) {
            user.getGroupMemberships().clear();
            user.getGroupMemberships().addAll(groupMemberships);
        } else {
            user.setGroupMemberships(groupMemberships);
        }
    }

    private void addMissingGroups(User user, Set<Group> targetGroups) {
        // Create new HashSet, see: https://hibernate.onjira.com/browse/HHH-3799
        Set<GroupMember> groupMemberships = new HashSet<>();
        if (user.getGroupMemberships() != null) {
            groupMemberships.addAll(user.getGroupMemberships());
        }
        for (Group group : targetGroups) {
            GroupMember groupMembership = new GroupMember(group, user);
            if (!groupMemberships.contains(groupMembership)) {
                groupMembership = new GroupMember(findGroupByName(group.getName()), user);
                groupMemberships.add(groupMembership);
            }
        }
        if (user.getGroupMemberships() != null) {
            user.getGroupMemberships().clear();
            user.getGroupMemberships().addAll(groupMemberships);
        } else {
            user.setGroupMemberships(groupMemberships);
        }

    }

    @Override
    public String encodePassword(String rawPass, User user) {
        return passwordEncoder.encode(rawPass); // Will now generate Salt internally and store it
        // as {salt}encoded

    }

    @Override
    public boolean isPasswordValid(User user, String rawPass) {
        var encoded = user.getPassword();
        return passwordMatchesEncoded(rawPass, user, encoded);
    }

    @Override
    public boolean passwordMatchesEncoded(String rawPass, User user, String encoded) {
        var encodedWithSalt = encoded.startsWith("{")
                ? encoded // is a new format SHA-1 password
                : "{" + user.getSaltSource() + "}" + encoded; // e.g. {2010-06-01T13:11:00.000Z}xxxx

        return passwordEncoder.matches(rawPass, encodedWithSalt);
    }

    @Override
    public String generateMfaSecretKey() {
        return totpService.generateKey();
    }

    @Override
    public void registerMfa(String secretKey, User user) {
        getCurrentSession().createQuery("update versioned User set mfaSecret=:secretKey where id=:id")
                .setString("secretKey", secretKey)
                .setLong("id", user.getId())
                .executeUpdate();
    }

    @Override
    public boolean validateMfa(String totp, User user) throws InvalidKeyException {
        return totpService.validateTotp(totp, user.getMfaSecret());
    }
}
