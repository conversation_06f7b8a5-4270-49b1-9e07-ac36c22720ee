package com.ecco.security.service

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.context.support.MessageSourceAccessor
import org.springframework.security.authentication.DisabledException
import org.springframework.security.core.Authentication
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.web.authentication.WebAuthenticationDetails

class IpNotBlocklistedAuthenticationCheck(messageSource: MessageSource, private val userManagementService: UserManagementService) :
    AuthenticationCheck<UserDetails, Authentication> {
    private val log: Logger = LoggerFactory.getLogger(javaClass)
    private val messages = MessageSourceAccessor(messageSource)

    override fun check(userDetails: UserDetails, authentication: Authentication) {
        val authenticationDetails = authentication.details

        if (authenticationDetails is WebAuthenticationDetails) {
            val ip = authenticationDetails.remoteAddress

            if (userManagementService.isIpBlacklisted(ip)) {
                log.debug("attempt to log in from banned IP address: $ip")
                throw DisabledException(messages.getMessage("AbstractUserDetailsAuthenticationProvider.blacklistedIP"))
            }
        }
    }
}