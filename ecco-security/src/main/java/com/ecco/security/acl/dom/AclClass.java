package com.ecco.security.acl.dom;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "acl_class", uniqueConstraints = @UniqueConstraint(name = "acl_class_unique_class", columnNames = {"class"}))
public class AclClass extends AbstractAclEntity {
    @Column(name="class", length = 100, nullable = false)
    private String className;

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
