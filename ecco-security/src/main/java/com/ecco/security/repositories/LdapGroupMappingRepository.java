package com.ecco.security.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.ecco.security.dom.LdapGroupMapping;


public interface LdapGroupMappingRepository extends JpaRepository<LdapGroupMapping, Long> {

    /** Find everything with the same ordering as we had when we used Dao Filter impl */
    List<LdapGroupMapping> findAllByOrderByLdapGroupAscLocalGroupAscLocalClassAscLocalIdAsc();

    List<LdapGroupMapping> findAllByLdapGroupOrderByLocalGroupAscLocalClassAscLocalIdAsc(String ldapGroup);
}
