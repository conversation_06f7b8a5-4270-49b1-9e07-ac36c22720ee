package com.ecco.security.repositories;

import com.ecco.dom.Individual;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface IndividualRepository extends JpaRepository<Individual, Long>, CrudRepositoryWithFindOne<Individual, Long> {

    List<Individual> findByFirstName(String firstName);
    List<Individual> findByFirstNameAndLastName(String firstName, String lastName);
    List<Individual> findByLastNameStartsWith(String lastNamePrefix);

    List<Individual> findAllByCode(String code);

    List<Individual> findByCompanyId(Long companyId);
    List<Individual> findByCompanyNotNull(Pageable pr);

    @Query("from Individual c WHERE c.company IS NOT NULL AND lower(c.email) = lower(?1)")
    List<Individual> findAllByProfessionalEmail(String email);

    Optional<Individual> findByCalendarId(String calendarId);

    List<Individual> findAllByCalendarIdIsNull();

    List<Individual> findAllByCompanyIsNotNull();
    List<Individual> findAllByCompanyIsNotNullAndArchivedIsNull();

    @Query("SELECT i FROM Individual i "
            + " WHERE i.id IN (?1)")
    List<Individual> findAllById(long[] ids);

    @Modifying
    @Query("UPDATE Individual SET company.id = :newCompanyId WHERE company.id = :oldCompanyId")
    void bulkSwitchCompany(
            @Param("oldCompanyId") long oldCompanyId,
            @Param("newCompanyId") long newCompanyId);
}
