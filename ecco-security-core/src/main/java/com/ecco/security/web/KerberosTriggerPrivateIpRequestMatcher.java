package com.ecco.security.web;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.web.util.matcher.RequestMatcher;

/**
 * A matcher which puts the 'Negotiate ' header on for Kerberos authentication.
 * Currently we assume local networks are for Kerberos authentication.
 */
public class KerberosTriggerPrivateIpRequestMatcher implements RequestMatcher {

    private final Logger log = LoggerFactory.getLogger(getClass());

    // See http://en.wikipedia.org/wiki/IP_address#IPv4_private_addresses
    private final String[] ipPrefixes = {"192.168.", "172.16.", "10."};
    private final String name;

    public KerberosTriggerPrivateIpRequestMatcher(String name) {
        this.name = name;
    }

    @Override
    public boolean matches(HttpServletRequest request) {
        // the ip matches IPv4, but IPv6 does come through in this instance, with IPv4 tagged on the end
        // 2002:3e06:935b:8000:0:5efe:***********
        // we could understand the internal IPv6 ranges, but we simply extract the IPv4 for now
        // or we could do something more drastic as in http://stackoverflow.com/questions/11018182/httpservletrequest-getremoteaddr-in-tomcat-returns-ipv6-formatted-ip-address

        String incomingIp = request.getRemoteAddr();
        log.debug("{} entered with incoming ip: {}", name, incomingIp);

        //InetAddress inetAddress = InetAddress.getByName(incomingIp);
        //if (!(inetAddress instanceof Inet4Address)) {
        if (StringUtils.contains(incomingIp, ":")) {
            incomingIp = StringUtils.substringAfterLast(incomingIp, ":");
               log.debug("{} transforming ip to: {}", name, incomingIp);
        }

        // ignore a specific IP - hard coded reverse proxy for now
        // if we match this we want to return false to matching for spengo
        //if (StringUtils.equals(incomingIp, "***********"))
            //return false;

        for (String ipPrefix : ipPrefixes) {
               log.debug("{} matching ip: {}", name, ipPrefix);
            if (incomingIp.startsWith(ipPrefix)) {
                log.debug("{} MATCHED ip: {}", name, ipPrefix);
                return true;
            }
        }
        log.debug("{} NOT MATCHED", name);

        return false;
    }
}
