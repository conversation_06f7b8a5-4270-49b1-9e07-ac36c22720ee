package com.ecco.calendar.core.webapi

import com.ecco.calendar.core.Recurrence
import java.net.URI
import java.time.LocalDateTime

interface CalendarableEventDto {
    // var links: List<Link>? // ideally extend Hateoas, but we're an interface here, so moved to RotaAppointmentViewModel
    var title: String?
    var serviceRecipientId: Int?
    var location: String?
    var start: LocalDateTime?
    var end: LocalDateTime?
    var allDay: Boolean
    var status: Recurrence.Status?
    var managedByUri: URI?
    var updatedByUri: URI?
}