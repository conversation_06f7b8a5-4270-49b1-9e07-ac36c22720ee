package com.ecco.calendar.core;

import com.ecco.dto.*;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.google.common.collect.Range;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Contract;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;

import java.io.Serializable;
import java.net.URI;
import java.time.Instant;
import java.util.Set;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.instantFromJoda;

/**
 * Represents a recurring calendar entry, together with its instances including exceptions.
 * <p/>
 * The underlying calendar model is described in RFC 5545 (http://tools.ietf.org/html/rfc5545).
 *
 * TODO probably should extend {@link Entry}, if it were an interface.
 *
 * @since 25/08/2013
 */
public interface RecurringEntry extends BuildableDto<RecurringEntry> {

    /** Opaque handle to allow references to be passed as strings e.g. as URL parameters. */
    final class RecurringEntryHandle extends AbstractHandle implements Serializable {

        private static final long serialVersionUID = 1L;

        public RecurringEntryHandle(String handle) {
            super(handle);
        }

        @Contract("null -> null; !null -> !null") // returns null if arg is null
        public static RecurringEntryHandle fromString(String s) {
            return s != null ? new RecurringEntryHandle(s) : null;
        }
    }

    /** Handle of this recurring entry to allow it to be retrieved again. */
    RecurringEntryHandle getHandle();

    /** The title of the entry used for display purposes. */
    String getTitle();

    /** The full description of the entry. */
    String getDescription();

    /** The location URL associated */
    URI getLocationUri();

    /** The start time of the first recurrence of this entry. */
    DateTime getStart();

    /** The length of time the entry lasts for. A duration matching whole days triggers 'allDay' */
    Duration getDuration();

    /**
     * The last date on which this entry could recur.
     * Note that this may be a day on which the entry would not recur in which case the recurrence before this date will be the last.
     * NB The end() of a RecurringEntry is a local date from the schedule, but a calendar system can have time.
     */
    LocalDate getScheduleEndDate();

    /** The set of Calendar constants (1 SUNDAY to 7 SATURDAY) representing the days of the week on which this event recurs. */
    //Set<Integer> getCalendarDays();
    DaysOfWeek getDays();

    String getIntervalType();

    Integer getIntervalFrequency();

    /** URI of entity which manages this recurring event. */
    URI getManagedByUri();

    /** URI of entity which last updated this recurring event. */
    URI getUpdatedByUri();

    /**
     * The calendarId that owns this recurring entry.
     */
    String getOwnerCalendarId();

    Set<Attendee> getAttendees();

    interface Builder extends DtoBuilder<RecurringEntry> {
        Builder handle(RecurringEntryHandle handle);
        Builder title(String title);
        Builder description(String description);
        Builder location(String location);
        Builder locationUri(URI locationURI);
        Builder start(DateTime start);
        Builder duration(Duration duration);
        Builder scheduleEndDate(LocalDate scheduleEndDate);
        Builder days(DaysOfWeek dow);
        Builder intervalType(String type);
        Builder intervalFrequency(int frequency);
        Builder managedByUri(URI managedBy);
        Builder updatedByUri(URI updatedBy);
        Builder ownerCalendarId(String calendarId);
        Builder attendees(Set<Attendee> attendees);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, RecurringEntry.class);
        }

        public static Builder create(RecurringEntry template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }

        public static Builder builderFrom(@NonNull RecurringEntry template) {
            var p = new RecurringEntryProxyImpl();
            p.setHandle(template.getHandle());
            p.setDescription(template.getDescription());
            p.setTitle(template.getTitle());
            p.setStart(template.getStart());
            p.setScheduleEndDate(template.getScheduleEndDate());
            p.setDuration(template.getDuration());
            p.setDays(template.getDays());
            p.setIntervalType(template.getIntervalType());
            p.setIntervalFrequency(template.getIntervalFrequency());
            p.setAttendees(template.getAttendees());
            p.setLocationUri(template.getLocationUri());
            p.setManagedByUri(template.getManagedByUri());
            p.setUpdatedByUri(template.getUpdatedByUri());
            p.setOwnerCalendarId(template.getOwnerCalendarId());
            return create(p);
        }
    }

    static Range<Instant> getRange(RecurringEntry recurringEntry) {
        var entryStart= JodaToJDKAdapters.dateTimeToJdk(recurringEntry.getStart());
        assert entryStart != null;
        return recurringEntry.getScheduleEndDate() != null
                // openClosed means we exclude the next day
                ? Range.openClosed(entryStart.toInstant(), RecurringEntry.getEndPlus1AsInstant(recurringEntry.getScheduleEndDate()))
                : Range.atLeast(entryStart.toInstant());
    }

    static Instant getEndPlus1AsInstant(LocalDate end) {
        return instantFromJoda(end.toDateTimeAtStartOfDay().plusDays(1).toInstant());
    }

    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RecurringEntryProxyImpl implements RecurringEntry {

        RecurringEntryHandle handle;
        String title;
        String description;
        URI locationUri;
        DateTime start;
        Duration duration;
        LocalDate scheduleEndDate;
        DaysOfWeek days;
        String intervalType;
        Integer intervalFrequency;
        URI managedByUri;
        URI updatedByUri;
        String ownerCalendarId;
        Set<Attendee> attendees;

    }
}
