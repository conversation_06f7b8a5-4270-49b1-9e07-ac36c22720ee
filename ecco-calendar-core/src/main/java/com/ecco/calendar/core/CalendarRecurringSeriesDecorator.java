package com.ecco.calendar.core;

import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.google.common.collect.Range;
import org.joda.time.Interval;
import org.joda.time.LocalDate;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import javax.annotation.Nonnull;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * Decorator that handles allocating attendees to part of a recurring series.
 * The corresponding unit test is CalendarRecurringAttendeeUnitTest, to verify the methods called.
 *
 * The iCal spec allows for assigning an attendee (allocation) to a schedule (recurring entry)
 * but not for certain time periods, or days of the schedule. Therefore this class is about making
 * this happen using standard iCal spec and away from calling code.
 * The industry has also adopted a practice, which is becoming a standard, of simplifying more complex recurring
 * rules by restarting the entries but linking them with some series id. This code effectively manages a series.
 *
 * NB There is an assumption (passed to us) that we will not have more than one recurrence of an entry per day
 * but this class works by splitting by days, so it won't produce entries with overlapping days.
 */
public class CalendarRecurringSeriesDecorator extends CalendarRecurringSeriesBase {

    public CalendarRecurringSeriesDecorator(CalendarRecurringService delegator,
                                            CalendarRecurringSeriesService seriesService) {
        super(delegator, seriesService);
    }

    @Override
    public Integer allocateAsyncAfterDays() {
        return null;
    }

    // TODO - this assumes we expose the internal handles
    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntry.RecurringEntryHandle recurringEntryHandle, Range<Instant> range, Recurrence.Status status) {

        java.time.LocalDate lowerBoundAsLocalDate = getLocalDateFromLowerRange(range);
        java.time.LocalDate upperBoundAsLocalDate = getLocalDateFromUpperRange(range);

        var handles = recurringSeriesService.getSeriesHandlesInRangeWithLegacyFallback(recurringEntryHandle, lowerBoundAsLocalDate, upperBoundAsLocalDate);
        return handles.stream().flatMap(handle -> delegator.findRecurrences(handle, range, status));
    }

    // TODO - this assumes we expose the internal handles
    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntry.RecurringEntryHandle recurringEntryHandle, Interval interval, Recurrence.Status status) {
        var lowerLocalDate = JodaToJDKAdapters.localDateToJDk(interval.getStart().toLocalDate());
        var upperLocalDate = JodaToJDKAdapters.localDateToJDk(interval.getEnd().toLocalDate());

        var handles = recurringSeriesService.getSeriesHandlesInRangeWithLegacyFallback(recurringEntryHandle, lowerLocalDate, upperLocalDate);
        return handles.stream().flatMap(handle -> delegator.findRecurrences(handle, interval, status));
    }

    /**
     * COMMENTED - because we shouldn't limit the apts at the display, they shouldn't be produced (see commit).
     * Historically, apts were allocated recurring one at a time to the carerun's calendarId. When a client schedule is ended
     * there may have been single allocations left on the carerun. These will show on the carerun after the schedule has ended.
     * Using this method we can filter the interval to be what the series is expecting.
     * @param calendarId The schedule's calendarId - so that we can get 'attendees' and availability in one go
     * @param rotaInterval The rota interval
     */
    @Override
    public Stream<Recurrence> findRecurrencesFromCalendar(String calendarId, Interval rotaInterval) {
        return delegator.findRecurrencesFromCalendar(calendarId, rotaInterval);
        /*var lowerLocalDate = JodaToJDKAdapters.localDateToJDk(rotaInterval.getStart().toLocalDate());
        var upperLocalDate = JodaToJDKAdapters.localDateToJDk(rotaInterval.getEnd().toLocalDate());

        var recurringEntryHandle = RecurringEntryHandle.fromString(calendarId);
        var entriesInRange = recurringSeriesService.getSeriesEntriesInRange(recurringEntryHandle, lowerLocalDate, upperLocalDate);
        if (entriesInRange.isEmpty()) {
            // if we have no series to manage dates, then we should get the schedule to limit the dates here also
            return delegator.findRecurrencesFromCalendar(calendarId, rotaInterval);
        }
        // if we have series, then use each handle and find its calendarId
        // BUT the series id itself is NOT a recurring entry series - just the calendarId
        return entriesInRange.stream().flatMap(entry -> {
            var start = JodaToJDKAdapters.localDateToJoda(entry.getStartDate()).toDateTimeAtStartOfDay();
            var end = entry.getEndDate() != null ? JodaToJDKAdapters.localDateToJoda(entry.getEndDate()).plusDays(1).toDateTimeAtStartOfDay() : rotaInterval.getEnd();
            var entryInterval = new Interval(start, end).overlap(rotaInterval);
            var calendarIdEntry = delegator.findCalendarIdFromItemId(entry.getEntryHandle().toString());
            return delegator.findRecurrencesFromCalendar(calendarIdEntry, entryInterval);
        });*/
    }

    @Override
    public RecurringEntry.RecurringEntryHandle getRecurringEntrySeriesHandle(RecurringEntry.RecurringEntryHandle recurringEntryHandle) {
        return recurringSeriesService.getSeriesHandle(recurringEntryHandle);
    }

    private java.time.LocalDate getLocalDateFromLowerRange(Range<Instant> range) {
        return range.lowerEndpoint().atZone(EccoTimeUtils.LONDON).toLocalDate();
    }

    private java.time.LocalDate getLocalDateFromUpperRange(Range<Instant> range) {
        // TODO: Replace these with one method that returns a Range too
        return range.hasUpperBound() ? range.upperEndpoint()
                .atZone(EccoTimeUtils.LONDON)
                .toLocalDate() : null;
    }

    /**
     * Allows multiple RecurringEntry's to be related to cope with separate attendees (on different days only).
     * Can have a separate schedule per day, but no day should ever overlap other related schedules.
     * NB The range is useful, but the end date is always provided as a LocalDate (and this is expected in the method)
     */
    @Override
    public int confirmRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range,
                                         @Nullable DaysOfWeek matchDays, URI updatedBy, @Nullable Integer allocateRescheduledMins,
                                         Range<LocalDate> rescheduleBounds, @NonNull String allocateToResourceCalendarId) {
        return handleRecurrencesInRange(recurringEntryHandle, range, matchDays, updatedBy, allocateRescheduledMins,
                rescheduleBounds, allocateToResourceCalendarId, ALLOCATE_STATUS.ALLOCATE);
    }

    @Override
    public int unConfirmRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range,
                                           @Nullable DaysOfWeek matchDays, URI updatedBy,
                                           String deallocateFromResourceCalendarId) {
        return handleRecurrencesInRange(recurringEntryHandle, range, matchDays, updatedBy, null,
                null, deallocateFromResourceCalendarId, ALLOCATE_STATUS.DEALLOCATE);
    }

    @Override
    public void recreateRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range, URI updatedBy) {
        handleRecurrencesInRange(recurringEntryHandle, range, null, updatedBy, null,
                null, null, ALLOCATE_STATUS.RECREATE);
    }

    public int handleRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range,
                                        @Nullable DaysOfWeek matchDays, URI updatedBy, @Nullable Integer allocateRescheduledMins,
                                        Range<LocalDate> rescheduleBounds, @NonNull String resourceCalendarId,
                                        @Nonnull ALLOCATE_STATUS allocateStatus) {

        if (allocateRescheduledMins != null) {
            throw new UnsupportedOperationException("recur reschedule is not currently supported");
        }
        if (!ALLOCATE_STATUS.RECREATE.equals(allocateStatus) && resourceCalendarId == null) {
            throw new UnsupportedOperationException("allocate/deallocate requires resourceCalendarId");
        }

        // validate and extract from range
        // NB incoming dates are LONDON based - so midnight to 1am in DST needs to be translated back to UTC
        // NB the cosmo calendar has 'isfloating' on stamps, meaning the dates are local to the system - which is set as UTC
        validateInput(range);
        var lowerBoundAsInstant = range.lowerEndpoint();
        java.time.LocalDate lowerBoundAsLocalDate = getLocalDateFromLowerRange(range); // LONDON
        java.time.LocalDate upperBoundAsLocalDate = getLocalDateFromUpperRange(range); // LONDON, but was UTC meaning we were short
        // validate and extract from range

        // get the recurring entries that are relevant to the recurringEntryHandle
        // NB the recurring entry may not represent the range, since the series dates are the split-points
        // and the entry themselves may occur on a different day - hence be outside the start range
        // TODO consider what is loaded with the RecurringEntry (can we use getSeriesEntriesInRange instead)
        List<RecurringEntry> recurringEntriesInRangeMaybe = recurringSeriesService.getSeriesEntriesWithLegacyFallback(recurringEntryHandle, lowerBoundAsLocalDate, upperBoundAsLocalDate);

        // filter start/end - now we use the seriesHandle regardless (see commit X), we expose a test case where more data has
        // come back and test matches the series entry range but not the schedule range - because it starts on a day after
        // the series date (which is only the split date) and so we end up manipulating data we don't need to, and shouldn't
        // as it ends up in 'daysRemaining' and creates a schedule for the other days that it doesn't need to
        // NB this doesn't match by time and shouldn't need to because if a recurrence is on the day its matched
        var recurringEntriesInRange = recurringEntriesInRangeMaybe.stream()
            .filter(r -> {
                // see matchesOneExactly for similar matchers
                var endJdk = JodaToJDKAdapters.localDateToJDk(r.getScheduleEndDate());
                var endBeforeStart = endJdk != null && endJdk.compareTo(lowerBoundAsLocalDate) < 0;
                var endMatches = !endBeforeStart;

                    var startBeforeEnd = upperBoundAsLocalDate != null && JodaToJDKAdapters.dateTimeToJdk(r.getStart()).toLocalDate().compareTo(upperBoundAsLocalDate) >= 0;
                    var startMatches = !startBeforeEnd;
                    return startMatches && endMatches;
                }).toList();

        // if recreate - then we can bypass all the below (even though it almost works) because
        // the below is carving a hole in the existing schedules, whereas we are duplicating each one
        if (ALLOCATE_STATUS.RECREATE.equals(allocateStatus)) {
            recurringEntriesInRange.forEach(r -> {
                var clone = RecurringEntry.BuilderFactory.builderFrom(r).build();
                var overrideAttendees = clone.getAttendees().stream().map(Attendee::getCalendarId).toArray(String[]::new);
                var newEntry = createRecurringEntryFrom(clone, null,
                        overrideAttendees, updatedBy, lowerBoundAsLocalDate, upperBoundAsLocalDate, false);
                //delegator.transferRelevantConcreteAndExDateEntries(newEntryDays.getHandle(), recurringEntryMatched.getHandle());
                // dealloc missing??
                makeRoomForTheNewAllocation(newEntry, r, lowerBoundAsLocalDate, upperBoundAsLocalDate, true);
            });
            return 1;
        }

        // sort out matchDays - because we can pass in anything
        // find the subset of recurring entries that match the days
        // the days recoded on the entry are separate to the range (ie could be one day but Mon-Sun days
        // validate 'days' early to match (else we had matchDaysArr on all week, but matchDays on null)
        var matchDaysArrTmp = matchDays == null ? null : DaysOfWeek.toSet(matchDays);
        var recurringEntriesInRangeWithMatchingDays = recurringEntriesInRange.stream()
                .filter(r -> {
                    if (r.getDays() == null || DaysOfWeek.toSet(r.getDays()).isEmpty()) return true;
                    var daysClone = new HashSet<>(DaysOfWeek.toSet(r.getDays())); // r.getDays
                    // removeAll returns true if modified, it means we matched a day
                    return matchDaysArrTmp == null || daysClone.removeAll(matchDaysArrTmp); // null - match all
                })
                .collect(Collectors.toList());
        // correct null 'days' to mean all the days possible before we go and create the entry
        // before this, days incorrectly defaulted to those in cloneFromFirstMatchingEntry (see createRecurringEntryFrom)
        // NB it seems difficult to verify matchDays is correctly in scope of the possible days - we have to trust it
        if (matchDays == null) {
            matchDays = recurringEntriesInRangeWithMatchingDays.stream().map(RecurringEntry::getDays).reduce(DaysOfWeek::merge).get();
        }
        var matchDaysArr = DaysOfWeek.toSet(matchDays);

        // NB we are dealing with recurringEntries, but we leave any manual amendments within them

        // special case: where everything happens to match - so as not to bother creating pointless data
        if (matchesOneExactly(matchDays, updatedBy, resourceCalendarId,
                lowerBoundAsInstant, upperBoundAsLocalDate, matchDaysArr, recurringEntriesInRangeWithMatchingDays, allocateStatus)) {
            return 1;
        }

        // all the entries should be consistent in their data - it's a series of the same details
        // so we choose the first match on the same day to get the details
        // except don't use it for the days, find that below
        // we also don't use it for attendees, since
        var cloneFromFirstMatchingEntry = recurringEntriesInRangeWithMatchingDays.stream()
                //.findFirst().map(r -> RecurringEntry.BuilderFactory.create(r).build())
                .findFirst().map(r -> RecurringEntry.BuilderFactory.builderFrom(r).build())
                .orElseThrow(); // FIXME: cannot be null

        // override with resourceCalendarId
        var resourceCalendarIds = resourceCalendarId == null ? null : new String[]{resourceCalendarId};

        // 8cf44901 [post restart]: WED 4th allocate [missing] / 11th deallocate c92 / 18 allocate [unalloc] / 25 drop [no drop] / 1st allocate [unalloc] / 9th [unalloc]
        // 1 - why the second one 77cc is truncate - surely it's needed
        // 2 - test fails with no client even on it
        /*
        -- test non-recurring allocs / all
        -- 22nd nov alloc (recur), 29th dealloc (single) + alloc to staff up, 6th drop, 13th dealloc (single), 20th recur dealloc (reset), 27th...10th jan alloc up one
        -- split at 29th
        -- POST http://localhost:8888/ecco-war/api/rota/-/recreate/e2929d8f-dbc0-44e9-835c-be57583f98c2/?splitDate=2024-11-29
        -- "message": "cannot create modification 6f5e8a9c-4ffe-42ae-a84a-1fedb3bf6ef9:20250109T110000 in collections 6d919499-b5e5-41a7-a21b-63f84e7fbd95, because master's parents are different: f48e1fae-5494-489d-8cd5-364d3fd8db15,6d919499-b5e5-41a7-a21b-63f84e7fbd95,",
        select * from ical_seriesrelatedentries where seriesHandle='76733959-8c26-4587-911c-94d049c4d3cb';
        -- so, on the last series entry - 19th dec onwards (e2929) [ODD that it's 19th not 20th] will be where the 9th Jan is [ODD that should be 10th?] alloc up one
        -- so, on the last series entry - 19th dec onwards (e2929) [ODD that it's 19th not 20th] will be where the 9th Jan is [ODD that should be 10th?] alloc up one
        */

        var transfer = false;

        // keep the existing attendees - using the mechanism to override
        /*if (ALLOCATE_STATUS.RECREATE.equals(allocateStatus)) {
            // createRecurringEntryFrom leaves things unallocated, so we need to find the attendees
            var demandCalendarId = cloneFromFirstMatchingEntry.getOwnerCalendarId();
            var resourceAttendees = cloneFromFirstMatchingEntry.getAttendees().stream()
                    .map(Attendee::getCalendarId)
                    .filter(calendarId -> !calendarId.equals(demandCalendarId))
                    .toList();
            // get demand and resource attendees as an array of strings
            var allAttendees = new ArrayList<>(Collections.singletonList(demandCalendarId));
            allAttendees.addAll(resourceAttendees);
            resourceCalendarIds = allAttendees.toArray(new String[0]);
        }*/

        // create the schedule we want, so we can transfer exceptions/concrete recurrences to it
        var newEntry = createRecurringEntryFrom(cloneFromFirstMatchingEntry, matchDays,
                resourceCalendarIds, updatedBy, lowerBoundAsLocalDate, upperBoundAsLocalDate, ALLOCATE_STATUS.DEALLOCATE.equals(allocateStatus));

        // now move the existing, related schedules out of the way
        for (RecurringEntry recurringEntryMatched : recurringEntriesInRangeWithMatchingDays) {

            // check it has a day that matches, otherwise leave it
            Set<Integer> entryDaysArr = recurringEntryMatched.getDays() == null ? DaysOfWeek.allWeek() : DaysOfWeek.toSet(recurringEntryMatched.getDays());

            Set<Integer> matchDaysWithEntryArr = matchDaysArr == null // null - match all
                    ? entryDaysArr
                    : entryDaysArr.stream()
                        .filter(matchDaysArr::contains)
                        .collect(Collectors.toSet());
            if (matchDaysWithEntryArr.size() == 0) {
                continue;
            }

            // re-create in the range for other days on the entry which isn't in matchDays
            Set<Integer> daysRemaining = matchDaysArr == null // null - match none
                    ? Collections.emptySet()
                    : entryDaysArr.stream()
                        .filter(day -> !matchDaysArr.contains(day))
                        .collect(Collectors.toSet());
            if (daysRemaining.size() > 0) {
                // NB days remaining get new schedules without attendees.
                // This works okay for allocating because if something is already allocated (which we need to remember)
                // then it won't match to the same handle/day.
                // This works okay for deallocating currently as we assume everything from the date gets unallocated.
                // See CosmoCalendarSeriesIntegrationTest "BUT at the moment we reset to unallocated for every day after the provided date"
                // WE SHOULD FILL IN HERE
                var newEntryDays = createRecurringEntryFrom(recurringEntryMatched, DaysOfWeek.from(daysRemaining), null, null,
                        lowerBoundAsLocalDate, upperBoundAsLocalDate, false);
                // recurringEntryMatched has everything we need... but then isn't found
                if (transfer) {
                    delegator.transferRelevantConcreteAndExDateEntries(newEntryDays.getHandle(), recurringEntryMatched.getHandle());
                } else {
                    delegator.clearRelevantConcreteAndExDateEntries(newEntryDays.getHandle(), recurringEntryMatched.getHandle());
                }
            }

            makeRoomForTheNewAllocation(newEntry, recurringEntryMatched, lowerBoundAsLocalDate, upperBoundAsLocalDate, transfer);
        }

        // TODO still to do
        //      NB deallocate doesn't do async (in non-series - but not really used, users probably re-recur over)
        //      NB de/allocate - we now leave modifications intact - a new feature? (see truncate code for resetting any modifications within the ranges)
        //      NB we don't currently support recurring allocateRescheduledMins (do, or simply prevent?)
        //      allocate/deallocate with options - from/to, part days etc - currently no end date assumed

        // TODO avoid forever running entries building up with exceptions (all dates get hydrated)
        //      exception stamps (dropped) could have a new forever schedule created occasionally (for empty exceptions) - or just use relatedentry/table
        //      concrete (adjustments) could have a new forever schedule created occasionally (for empty exceptions)
        //      concrete (allocated) remove ref integrity FK_supp_pl_wk_event and check any exists before changing schedules - or just use relatedentry/table

        // TODO general improvements to consider:
        //        mimic ThisAndFutureHelper from cosmo, as it does splitting (end of day to the second, for instance)
        //        it could be simpler to set exclusion dates instead of part allocating via truncating and recreating
        //        it could be simpler to delegate some behaviour to original concrete entries

        return 1;
    }

    @Override
    public void updateRecurringEntryBoundsStart(RecurringEntry.RecurringEntryHandle recurringEntryHandle, LocalDate startDate) {
        updateRecurringEntryBounds(recurringEntryHandle, startDate, null, false);
    }

    @Override
    public void updateRecurringEntryBoundsEnd(RecurringEntry.RecurringEntryHandle recurringEntryHandle, LocalDate endDate) {
        updateRecurringEntryBounds(recurringEntryHandle, null, endDate, true);
    }

    @Override
    public void updateRecurringEntryBounds(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @Nullable LocalDate startDate,
                                           @Nullable LocalDate endDate) {
        updateRecurringEntryBounds(recurringEntryHandle, startDate, endDate, true);
    }

    /**
     * When a schedule changes, the whole series needs to be in-line.
     * We get provided with the startDate and endDate of the schedule
     * but the series only needs updating if its outside the range.
     * This is perhaps handleRecurrencesOutsideRange, the opposite of handleRecurrencesInRange
     * @param recurringEntryHandle Schedule handle to modify
     * @param startDate Can be nullable to mean no change
     * @param endDate Can be nullable to mean no change, or set null
     * @param setEndDate Decide whether to set the end date - allows null
     */

    // TODO state of recent work/tests on this/related methods:
    //      - split caused all entries to change start/end - see test in 6bfa6c6a and 61b29cdb (see RotaServiceIntegrationTest)
    //          - and there was a fix for legacy schedules - see test in 3b0243e3
    //      - recurringEntryHandle may not exist (eg replaced by 2 entries), so just use series start/end - see test in 1c3b2c8b and de2692e5
    //      - NPE (from 39cd9df7) due to .max with null dates - see test in ed537af2 (see RotaScheduleAPITest schedules_createPartAllocateRecurringThenUpdateEnd)
    //      - demandSchedule.setEnd caused 'Partial cannot be null' when changing from null end date - see 356ec26a
    //      - concrete entries when no end date - see dd4dff27 and a62de9c6
    //      - editing apts without end sched - see this commit
    //      - no modifications are transferred to new schedules
    //          - transferRelevantConcreteAndExDateEntries was disabled due to cosmo not adding the correct attendee - see DEV-2283
    //          - this commit
    //      Therefore, we need tests to complete the wip impl from 61b29cdb and ef1472cc:
    //          - test care runs clear down manual changes (test from DEV-2323 - leaked apts on care runs) until can 'transfer' again from DEV-2283
    //          - test/reinstate transferRelevantConcreteAndExDateEntries
    //          - test deleteEntry (this commit) for failing if have modifications? (or evidence?)
    //              NB these two above refer to unifying the experience of split/truncate:
    //              - splitting resets concrete recurrences (not with data, but future dated) - so could ignore exceptions! and dropped!!??
    //              - currently truncate simply removes exceptions outside new boundary (but would fail on FK if there was data?)
    //              - split removes recurrences but throws if evidence is attached
    //              - but it would be good to retain any information for subsequent schedules, eg exception dates (dropped) to the correct days
    //              - newer work on series means recurring operations can mean amendments are not carried over (see transferRelevantConcreteAndExDateEntries and deleteEntry)
    //              NB the entries themselves sort out the right dates, see updateRecurringEntryBounds and ensureConcreteRecurrence on resetting exception dates etc
    //          - test Recurrence.kt#modified - is it used client side for relying on as recurrences - rename to allocated?
    //          - test 'Fix recurring part days' where today is missed from the alloc, and then recurring all-days causes day overlap (because days don't trigger -1)
    //          - test 'Fix createCommand possible missing ref' where a command looks up a schedule from a ref which has been removed
    //          - test 'Fix recurring deallocate on legacy item' where legacy item has no attendees
    //          - test concrete entries when no end date
    //          - test editing apts without end sched
    //          - test that the entries on part-day allocations do indeed start / end on the same dates (scenario 1 below)
    //              or maybe we could check they had the same parent it would be okay to move them up/down?
    //              - use RotaServiceIntegrationTest with schedules changing start/end (with part-allocating, with and without end dates)
    //          - test when an entry reduces to nothing (for now we highlight with 'throw new IllegalArgumentException')
    //      NB if this proves hard, we should probably not allow an 'update' but a 'split' instead.

    @Override
    public void updateRecurringEntryBounds(RecurringEntry.RecurringEntryHandle recurringEntryHandle,
                                           @Nullable LocalDate startDate,
                                           @Nullable LocalDate endDate,
                                           boolean setEndDate) {

        boolean hasEntryHandles = recurringSeriesService.hasRecurringRelatedHandles(recurringEntryHandle);
        if (!hasEntryHandles) {
            delegator.updateRecurringEntryBounds(recurringEntryHandle, startDate, endDate, setEndDate);
            return;
        }

        // sort out the dates
        // we get the original start/end from the whole range, not the exact handle - which may no longer exist
        var pair = recurringSeriesService.getSeriesLowestHighest(recurringEntryHandle);
        var seriesStartJdk = pair.first;
        var seriesStart = JodaToJDKAdapters.localDateToJoda(pair.first);
        var seriesEndJdk = pair.second;
        var seriesEnd = JodaToJDKAdapters.localDateToJoda(pair.second);

        var startStatus = BOUND_STATUS.NONE;
        var endStatus = BOUND_STATUS.NONE;
        if (startDate != null && !startDate.isEqual(seriesStart)) {
            startStatus = startDate.isBefore(seriesStart) ? BOUND_STATUS.LOWER : BOUND_STATUS.HIGHER;
        }
        if (setEndDate) {
            boolean endDatesAreSame = (endDate == null && seriesEnd == null) || (endDate != null && seriesEnd != null && endDate.isEqual(seriesEnd));
            if (!endDatesAreSame) {
                endStatus = endDate == null
                        ? BOUND_STATUS.HIGHER
                        : seriesEnd == null
                            ? BOUND_STATUS.LOWER
                            : endDate.isBefore(seriesEnd) ? BOUND_STATUS.LOWER : BOUND_STATUS.HIGHER;
            }
        }

        // We get here by splitting or changing a schedule start/end (nb splitting causes schedule.truncate to change the end date).
        // When extending/truncating a schedule, we need to get it right for each of the entries...
        //      - 1) we should extend/truncate entries - in case they overlap (eg on different days)
        //      - 2) we shouldn't extend/truncate entries - to avoid a split coming back to life
        //
        // The key to 1 and 2 is the logic in matching all entries in the correct dates.
        //
        // update start...
        //      if a start date is being put lower... move down each entry affected (those at the series start)
        //      if a start date is being put higher... move up each entry affected (those at the series start)
        // update end...
        //      if an end date is being put lower/from-null... truncate each entry affected (all above the new date)
        //      if an end date is being put higher/to-null... move up each entry affected (those at the series end)
        // see matchesOneExactly for similar matchers

        var startDateJdk = JodaToJDKAdapters.localDateToJDk(startDate);
        var endDateJdk = JodaToJDKAdapters.localDateToJDk(endDate);

        // simple case - find entries and start them earlier
        //      if a start date is being put lower... move down the lowest ones
        if (startStatus == BOUND_STATUS.LOWER) {
            var entries = recurringSeriesService.getSeriesEntriesStartOnOrBefore(recurringEntryHandle, seriesStartJdk);
            entries.forEach(e -> {
                // just double check we need to do something
                if (startDate.isBefore(e.getStart().toLocalDate())) {
                    updateBoundsToStart(startDate, e);
                }
            });
        }

        // simple case - find entries and end them later
        //      if an end date is being put higher/to-null... move up the highest ones
        if (endStatus == BOUND_STATUS.HIGHER) {
            var entries = recurringSeriesService.getSeriesEntriesEndOnOrAfter(recurringEntryHandle, seriesEndJdk);
            entries.forEach(e -> {
                // just double check we need to do something
                // entry isn't already null, since there would be no going higher otherwise
                if (endDate == null || e.getScheduleEndDate().isAfter(endDate)) {
                    updateBoundsToEnd(endDate, e);
                }
            });
        }

        // more complex case - find entries and start them later
        //      if a start date is being put higher... move up each series to the value
        if (startStatus == BOUND_STATUS.HIGHER) {
            var entries = recurringSeriesService.getSeriesEntriesStartOnOrBefore(recurringEntryHandle, startDateJdk);
            entries.forEach(entry -> {
                // just double check we need to do something
                if (entry.getStart().toLocalDate().isBefore(startDate)) {
                    // if ending before
                    if (entry.getScheduleEndDate().isBefore(startDate)) {
                        // first set the end to the start
                        // but really it should be deleted
                        // updateBoundsToEnd(startDate, entry);
                        throw new IllegalArgumentException("start BOUND_STATUS.HIGHER would require entry to be deleted: " + entry.getHandle() + " for " + startDate);
                    }
                    updateBoundsToStart(startDate, entry);
                }
            });
        }

        // more complex case - find all entries and make them smaller
        //      if an end date is being put lower/from-null... truncate each entry to the value
        if (endStatus == BOUND_STATUS.LOWER) {
            // schedule can be already null if endDate was null and now a value
            var entries = recurringSeriesService.getSeriesEntriesEndOnOrAfter(recurringEntryHandle, endDateJdk);
            // loop all entries in series
            entries.forEach(entry -> {
                // just double check we need to do something
                if (entry.getScheduleEndDate() == null || (entry.getScheduleEndDate() != null && entry.getScheduleEndDate().isAfter(endDate))) {
                    // if starting after, we delete it
                    if (entry.getStart().toLocalDate().isAfter(endDate)) {
                        // TODO this obliterates any modifications - but its what the user wants anyway, but we should warn
                        //  it also matches recent disabling of transferRelevantConcreteAndExDateEntries - that also obliterates modifications
                        //  but we should check for evidence at least... as per resetRecurrencesFrom which calls our findModifiedRecurrences
                        //  NB we could:
                        //      1 - use 'true' and amend cosmo to find modifications with no-end date
                        //      2 - simply find 'hasModifications=true'(?)
                        //      3 - use findModifiedRecurrences(handle, equalOrAfter)
                        //      4 - check for evidence, but that isn't our responsibility here!
                        deleteEntry(entry.getHandle(), false);
                    } else {
                        updateBoundsToEnd(endDate, entry);
                    }
                }
            });
        }
    }

    private void updateBoundsToEnd(LocalDate endDate, RecurringEntry recurringEntry) {
        delegator.updateRecurringEntryBoundsEnd(recurringEntry.getHandle(), endDate);
        recurringSeriesService.updateSeriesEntry(recurringEntry.getHandle(),
                JodaToJDKAdapters.localDateToJDk(recurringEntry.getStart().toLocalDate()), JodaToJDKAdapters.localDateToJDk(endDate));
    }

    private void updateBoundsToStart(@NonNull LocalDate startDate, RecurringEntry recurringEntry) {
        var startAdjusted = CalendarService.adjustStartToDayOfWeek(startDate.toDateTimeAtStartOfDay(), DaysOfWeek.isoToJavaCalendarSet(recurringEntry.getDays())).toLocalDate();
        delegator.updateRecurringEntryBoundsStart(recurringEntry.getHandle(), startAdjusted);
        recurringSeriesService.updateSeriesEntry(recurringEntry.getHandle(), JodaToJDKAdapters.localDateToJDk(startDate), JodaToJDKAdapters.localDateToJDk(recurringEntry.getScheduleEndDate()));
    }


    private enum BOUND_STATUS {
        NONE,
        HIGHER,
        LOWER
    }

    @Override
    public Stream<Recurrence> findModifiedRecurrences(RecurringEntry.RecurringEntryHandle recurringEntryHandle, LocalDate equalOrAfter) {
        var handles = recurringSeriesService.getSeriesHandlesWithLegacyFallback(recurringEntryHandle);
        return handles.stream().flatMap(handle -> delegator.findModifiedRecurrences(handle, equalOrAfter));
    }

    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntry.RecurringEntryHandle recurringEntryHandle, Range<Instant> interval) {
        var handles = recurringSeriesService.getSeriesHandlesInRangeWithLegacyFallback(recurringEntryHandle,
                getLocalDateFromLowerRange(interval), getLocalDateFromUpperRange(interval));
        return handles.stream().flatMap(handle -> delegator.findRecurrenceExceptions(handle, interval));
    }

    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntry.RecurringEntryHandle recurringEntryHandle, Interval interval) {
        var handles = recurringSeriesService.getSeriesHandlesWithLegacyFallback(recurringEntryHandle);
        return handles.stream().flatMap(handle -> delegator.findRecurrenceExceptions(handle, interval));
    }

    // TODO - this assumes we expose the internal handles
    @Override
    public void deleteRecurringEntry(RecurringEntry.RecurringEntryHandle recurringEntryHandle, boolean safely) {
        var handles = recurringSeriesService.getSeriesHandlesWithLegacyFallback(recurringEntryHandle);
        handles.forEach(handle -> {
            deleteEntry(handle, safely);
        });
    }

    private void deleteEntry(RecurringEntry.RecurringEntryHandle handle, boolean checkNotAllocated) {
        delegator.deleteRecurringEntry(handle, checkNotAllocated);
        recurringSeriesService.deleteSeriesEntry(handle);
    }

    @Override
    public Instant recurrenceToInstant(Recurrence.RecurrenceHandle recurrence) {
        return delegator.recurrenceToInstant(recurrence);
    }

}
