package com.ecco.calendar.core;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.DtoBuilderProxy;
import com.ecco.dto.ProxyDtoBuilderProxy;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.LocalDate;

import java.io.Serializable;
import java.net.URI;
import java.util.Set;

/**
 * Provides enough information to define a new recurring entry for creation.
 * This recurring entry model matches the capabilities of the {@code AppointmentSchedule} class - an event
 * of fixed duration repeating at the same time of day on specified days of the week.
 *
 * @since 26/08/2013
 */
public interface RecurringEntryDefinition extends Serializable, BuildableDto<RecurringEntryDefinition> {
    /** The title of the entry used for display purposes. */
    String getTitle();

    /** The full description of the entry. */
    String getDescription();

    /**
     * The start time of the first recurrence of this entry.
     * This should be consistent with {@link #getCalendarDays()} i.e. this date should be one of those days.
     * See DEV-1443 comments: the spec says this should match the days recurring. If it doesn't, then we get an extra
     * day in the schedule - the start date. This is perfectly valid but unusual from a user perspective since it can be that
     * repeating entries are based on contract dates/agreements, not the 'first Tuesday within the contract date'.
     * Therefore we adjust the start date when saving to the calendar system to avoid calling code understanding this distinction.
     * This could have an impact on the end date, which could mean the entry is invalid and an exception thrown.
     */
    DateTime getStart();

    /** The length of time the entry lasts for. A duration matching whole days triggers 'allDay' */
    Duration getDuration();

    /**
     * The last date on which this entry could recur.
     * Note that this may be a day on which the entry would not recur in which case the recurrence before this date will be the last. */
    LocalDate getScheduleEndDate();

    /** The set of Calendar constants (1 SUNDAY to 7 SATURDAY) representing the days of the week on which this event recurs. */
    Set<Integer> getCalendarDays();

    String getIntervalType();

    Integer getIntervalFrequency();

    /** URI of entity which manages this recurring event. */
    URI getManagedByUri();

    /** See Entry#updatedByUri */
    URI getUpdatedByUri();

    interface Builder extends DtoBuilder<RecurringEntryDefinition> {
        Builder title(String title);
        Builder description(String description);
        Builder location(LocationDefinition location);
        Builder start(DateTime start);
        Builder duration(Duration duration);
        Builder scheduleEndDate(LocalDate end);
        Builder calendarDays(Set<Integer> calendarDays);
        Builder intervalType(String intervalType);
        Builder intervalFrequency(Integer intervalFrequency);
        Builder managedByUri(URI managedByUri);
        Builder updatedByUri(URI updatedByUri);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, RecurringEntryDefinition.class);
        }

        public static Builder create(RecurringEntryDefinition template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }

        public static RecurringEntryDefinition create(RecurringEntry entry) {
            return RecurringEntryDefinition.adapt(BuilderFactory.create(), entry);
        }
    }

    static RecurringEntryDefinition adapt(RecurringEntryDefinition.Builder def, RecurringEntry entry) {
        def.title(entry.getTitle())
           .description(entry.getDescription())
           .start(entry.getStart())
           .scheduleEndDate(entry.getScheduleEndDate())
           .duration(entry.getDuration())
           .calendarDays(DaysOfWeek.isoToJavaCalendarSet(entry.getDays()))
           .intervalType(entry.getIntervalType())
           .intervalFrequency(entry.getIntervalFrequency())
           .managedByUri(entry.getManagedByUri())
           .updatedByUri(entry.getUpdatedByUri());
        return def.build();
    }

}
