package com.ecco.calendar.dom;

import com.ecco.dom.contacts.Contact;
import org.joda.time.DateTimeZone;

import java.util.List;

/**
 * Properties required to retrieve event information from non-iCal sources, and provide for new events.
 * @see EventEntryDefinition
 * @see EventEntry
 */
public interface EventEntryBase {

    /**
     * The service recipient this event relates to, if any.
     * See EventResource.serviceRecipientId.
     */
    Integer getServiceRecipientId();

    /**
     * The contact this event relates to, if any.
     * See EventResource.contactId.
     */
    Long getContactId();

    /**
     * allows us to apply generic rule of editable = false for calendar items
     * which is good for the generic impl of the calendar outputConverter (cosmo-event-integraion - ItemOutputConverter)
     * otherwise the original event is not updated when the item gets moved
     * it is also used for a sweeping generalisation that generated events are 'anyTime' in cosmo-event-integraion - ItemEventConverterImpl
     * hateos links in EventResource are replacing this concept
     */
    boolean isGenerated();

    boolean isRecurringMasterEntry();

    EventType getEventType();

    Integer getEventCategory();

    Integer getEventStatusId();

    /**
     * The outcome of the visit charge rate, set via evidence or dropped appointments.
     */
    Integer getEventStatusRateId();

    /** add a uid - for linking to another system - eg cosmo */
    String getUid();
    /** a setter if needed from cosmo - since cosmo generates the uid before the event is saved */
    void setUid(String uid);

    /**
     * anyTime would only be relevant for user generated events
     * but since the calendar shows no difference between allDay and anyTime - we don't bother
     * private boolean anyTime = true;
     * add a timeZone for future possibility - and when using cosmo in standalone mode (current user's timeZone can't be used)
     */
    DateTimeZone getTimeZone();

    /**
     * apply a calendar to attach to an event (at the lowest level)
     * in cosmo, an event is generated and calendarId's are attached to it (the parents of the event)
     * this is not persisted, but used by the aop code to assign the item to a calendar
     */
    List<String> getNewCalendarIds();
    void addNewCalendarId(String calendarId);
    void setOwnerCalendarIdForNewEvent(String calendarId);
    String getOwnerCalendarIdForNewEvent();

    //public boolean isEndOfMonth();

    boolean isRepeatYears();

    String getLocation();
    /** If the event has no explicit location through {@link #getLocation()} then
     * this is used to find a location for iCalendar instead. */
    Contact getLocationContact();
}
