{{#> partials/material-ui-page
        bootstrapRequired="false"
        calendarRequired="false"
        jqplotRequired="false"
        title="create an incident on ECCO"
        importRequireJsModules="repairs/inbound/RepairPage"
}}
    <style>
        .MuiStepButton-vertical {justify-content: flex-start !important}
    </style>
    {{! the extra MuiButtonBase-root causes the cancel/submit buttons out of style
    but we only need to override the containedPrimary, but then we need to consider disabled }}
    <style>
        .MuiButton-containedPrimary.Mui-disabled {
            color: rgba(0, 0, 0, 0.26) !important;
            background-color: rgba(0, 0, 0, 0.12) !important;
        }
    </style>
    <style>
        .MuiButton-containedPrimary {
            color: white !important;
            background-color: #0d83ca !important;
            border-radius: 4px !important;
            padding: 6px 16px !important;
        }
    </style>
    <div id="appbar">
        <div style="padding: 8px; background-color: #0d83ca;">
            <img src="{{applicationProperties.resourceRootPath}}themes/ecco/images/logo_white.png" height="48">
        </div>
        <div class="vertical-center">
            <div style="width:100%; text-align: center;">
                <i class="fa fa-2x fa-spinner fa-spin" style="color:#3b80cb"></i>
            </div>
        </div>
    </div>
    <div id="snackbar"></div>
{{/partials/material-ui-page}}