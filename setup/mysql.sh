#!/bin/bash

SELF_DIR=$(cd $(dirname $0); pwd -P)

# sudo apt-get update

# On Ubuntu 23.04 this installs MySQL 8
mkdir -p /etc/mysql/mysql.conf.d/
sudo cp $SELF_DIR/mysqld.ecco.cnf /etc/mysql/mysql.conf.d/
sudo apt-get -y install mysql-server mysql-client
sudo snap install mysql-workbench-community

echo "=== BEWARE: Setting mysql root password to <empty string> ==="
echo "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '';" | sudo mysql
sudo mysql_tzinfo_to_sql /usr/share/zoneinfo/ | mysql -u root mysql

mysql -uroot < $SELF_DIR/create-acctest-schema-mysql.sql
