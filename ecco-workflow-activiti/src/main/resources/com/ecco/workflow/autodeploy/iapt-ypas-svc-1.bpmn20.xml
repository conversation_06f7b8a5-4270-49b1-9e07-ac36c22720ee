<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="ypas-counselling" isExecutable="true">

        <!-- ========== TASK DEFINITIONS ========== -->

        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="ypascounselling" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin"
                activiti:formKey="flow:/pendingStatus"/>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin"
            activiti:formKey="flow:/assessmentDate"/>

        <userTask id="iaptAttendance" name="iaptAttendance" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.iaptAttendance" writable="false"/>
                <activiti:formProperty id="questions" expression="attendance" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="iaptInitialAssessment" name="iaptInitialAssessment" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.iaptInitialAssessment" writable="false"/>
                <activiti:formProperty id="questions" expression="#{referral.ageAtReferralReceivedDate lt 19 ? 'SDQ,RCADS' : 'CORE-OM'}" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="iaptCurrentView" name="iaptCurrentView" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.iaptCurrentView" writable="false"/>
                <activiti:formProperty id="questions" expression="current view,complexity" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="iaptSessions" name="iaptSessions" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.iaptSessions" writable="false"/>
                <!-- Note http://docs.oracle.com/javaee/6/tutorial/doc/bnahu.html#bnahw says:
                    String: with single and double quotes; " is escaped as \", ' is escaped as \', and \ is escaped as \\
                    We then use \, for escaping commas in the resulting list, so it's \\, to ge \, passed to our stuff and \' to get ' passed

                    We may also wish to use ${} for for immediate evaluation which we'd hope not to find changing over
                    time.
                  -->
                <activiti:formProperty id="questions"
                expression="#{referral.ageAtReferralReceivedDate lt 19 ? 'Depression / low mood,Anxious away from home,Anxious in social situations,Anxious generally,Compelled to do or think things (\'support\'\\,OCD),Panic,Disturbed by traumatic event (PTSD),Out of control behaviour child view,Depression / low mood (PHQ-9),Anxious generally (GAD-7)':'CORE-10'}"
                writable="false">
                </activiti:formProperty>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="iaptFeedback" name="iaptFeedback" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.iaptFeedback" writable="false"/>
                <activiti:formProperty id="questions"
                expression="#{referral.ageAtReferralReceivedDate lt 19 ? 'feedback,ESQ 12-18' :'feedback'}"
                writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsAssessment" name="needsAssessment" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessment" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessment" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementRequired" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="threatAssessmentReduction" name="threatAssessmentReduction" activiti:candidateGroups="admin"
            activiti:formKey="generic:/threatAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.threatAssessmentReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementDealsWith" writable="false"/>
                <activiti:formProperty id="showFlags" expression="y" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomeComponents" expression="rag" writable="false"/>
                <activiti:formProperty id="showSupportActions" expression="n" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="60" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin" activiti:formKey="flow:/decideFinal"/>

        <userTask id="start" name="start" activiti:candidateGroups="admin" activiti:formKey="flow:/start"/>

        <userTask id="scheduleReviews" name="scheduleReviews" activiti:candidateGroups="admin" activiti:formKey="flow:/scheduleReviews">
            <extensionElements>
                <activiti:formProperty id="reviewSchedule" expression="3m" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsReduction" name="needsReduction" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsReduction" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="relevant" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="relevant" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementRequired" writable="false"/>
                <activiti:formProperty id="showOverviewComponents" expression="desc" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="overviewLinks" expression="changes,needsOnly,supportOnly" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsAssessmentReductionReview" name="needsAssessmentReductionReview" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReductionReview" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessmentReductionReview" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction,review" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="changesByOutcome" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status,statusChange" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="byOutcome" writable="false"/>
                <activiti:formProperty id="showWorkComponents" expression="review" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementRequired" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="admin"
            activiti:formKey="flow:/close"/>


        <!-- ========== THE WORKFLOW ========== -->

        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="toLoadReferral" sourceRef="theStart"     targetRef="loadReferral"/>
        <sequenceFlow id="toFork1"        sourceRef="loadReferral" targetRef="fork1"/>


        <!-- Start parallel flows: 1 -->
        <parallelGateway id="fork1"/>
        <!-- flow 1a -->
        <sequenceFlow id="to_referralSource"   sourceRef="fork1"           targetRef="referralSource"/>
        <sequenceFlow id="from_referralSource" sourceRef="referralSource"  targetRef="join1"/>

        <!-- flow 1b -->
        <sequenceFlow id="to_referralDetails"   sourceRef="fork1"             targetRef="referralDetails"/>
        <sequenceFlow id="from_referralDetails" sourceRef="referralDetails"   targetRef="pendingStatus"/>
        <sequenceFlow id="from_pendingStatus"   sourceRef="pendingStatus"     targetRef="referralAccepted"/>
        <sequenceFlow id="from_referralAccepted" sourceRef="referralAccepted" targetRef="assessmentDate"/>

        <sequenceFlow id="toFork2" sourceRef="assessmentDate" targetRef="fork2"/>

        <!-- Start parallel flows: 1 -->
        <parallelGateway id="fork2"/>
        <!-- flow 2a -->
        <sequenceFlow id="to_iaptAttendance"    sourceRef="fork2"           targetRef="iaptAttendance"/>
        <sequenceFlow id="from_iaptAttendance"  sourceRef="iaptAttendance"  targetRef="join1"/>

        <!-- flow 2b -->
        <sequenceFlow id="to_iaptInitialAssessment"   sourceRef="fork2"              targetRef="iaptInitialAssessment"/>
        <sequenceFlow id="from_iaptInitialAssessment" sourceRef="iaptInitialAssessment" targetRef="join1"/>

        <!-- flow 2c -->
        <sequenceFlow id="to_iaptCurrentView"   sourceRef="fork2"           targetRef="iaptCurrentView"/>
        <sequenceFlow id="from_iaptCurrentView" sourceRef="iaptCurrentView" targetRef="join1"/>

        <!-- flow 2d -->
        <sequenceFlow id="to_iaptSessions"      sourceRef="fork2"           targetRef="iaptSessions"/>
        <sequenceFlow id="from_iaptSessions"    sourceRef="iaptSessions"    targetRef="join1"/>

        <!-- flow 2e -->
        <sequenceFlow id="to_needsAssessement"      sourceRef="fork2"           targetRef="needsAssessment"/>
        <sequenceFlow id="from_needsAssessment"     sourceRef="needsAssessment" targetRef="threatAssessmentReduction"/>
        <sequenceFlow id="from_threatAssessmentReduction" sourceRef="threatAssessmentReduction" targetRef="decideFinal"/>
        <sequenceFlow id="from_decideFinal"         sourceRef="decideFinal"     targetRef="start"/>
        <sequenceFlow id="from_start"               sourceRef="start"           targetRef="scheduleReviews"/>
        <sequenceFlow id="from_scheduleReviews"     sourceRef="scheduleReviews" targetRef="needsReduction"/>
        <sequenceFlow id="from_needsReduction"      sourceRef="needsReduction" targetRef="needsAssessmentReductionReview"/>
        <sequenceFlow id="from_needsAssessmentReductionReview" sourceRef="needsAssessmentReductionReview" targetRef="join1"/>

        <!-- flow 2f -->
        <sequenceFlow id="to_iaptFeedback"   sourceRef="fork2"        targetRef="iaptFeedback"/>
        <sequenceFlow id="from_iaptFeedback" sourceRef="iaptFeedback" targetRef="join1"/>

        <!-- end parallel tasks -->
        <parallelGateway id="join1"/>
        <sequenceFlow id="to_closeReferral" sourceRef="join1"         targetRef="closeReferral"/>
        <sequenceFlow id="to_theEnd"        sourceRef="closeReferral" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
