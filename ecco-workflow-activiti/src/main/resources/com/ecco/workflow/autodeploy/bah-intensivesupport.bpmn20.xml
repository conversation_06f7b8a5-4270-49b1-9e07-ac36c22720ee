<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="intensivesupport" isExecutable="true">
        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <!-- ** IMPORTANT: Update this expression with the ordered list when you add tasks to the workflow ** -->
        <serviceTask id="setTasksToShowClientView" name="set tasksToShowClientView"
            activiti:resultVariable="tasksToShowClientView"
            activiti:expression="emergencyDetails,needsReduction"/>

        <serviceTask id="setTasksToShowRestricted" name="set tasksToShowRestricted"
            activiti:resultVariable="tasksToShowRestricted"
            activiti:expression=
            "projectRegion,from,referralDetails,initial questionnaire,tenancy questionnaire,agreement,assessmentAccepted,assessmentDate,riskManagement,needsAssessment,start,scheduleReviews,needsReduction,needsAssessmentReductionReview,close,exitSurvey"/>

        <userTask id="projectRegion" name="projectRegion" activiti:candidateGroups="admin"
                activiti:formKey="flow:/projectRegion"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="agreement" name="agreement" activiti:candidateGroups="admin" activiti:formKey="flow:/consent">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="bah" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="case-manager"
                activiti:formKey="flow:/assessmentDate"/>

        <userTask id="initialQ" name="initial questionnaire" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="signup questionnaire" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="150" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="150" writable="false"/>
                <activiti:formProperty id="questions" expression="moving in,stay healthy,stay safe,economic wellbeing,enjoy achieve,decision making" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="emergencyDetails" name="emergencyDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/emergencyDetails"/>

        <userTask id="tenancyQ" name="tenancy questionnaire" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="tenancy checklist" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="151" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="151" writable="false"/>
                <activiti:formProperty id="questions" expression="tenancy qn" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="needsAssessment" name="needsAssessment" activiti:candidateGroups="case-manager"
            activiti:formKey="generic:/needsAssessment" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessment" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment" writable="false"/>
                <activiti:formProperty id="showActionGroups" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="27" writable="false"/>
                <activiti:formProperty id="outcomes" expression="stay healthy,stay safe,econ wellbeing,pos contrib,early help" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="riskManagement" name="riskManagement" activiti:candidateGroups="case-manager"
            activiti:formKey="generic:/threatAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.threatAssessmentReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent,showRiskManagementDealsWith" writable="false"/>
                <activiti:formProperty id="showOutcomeComponents" expression="rag, triggerControl" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showFlags" expression="y" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="60" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="28" writable="false"/>
                <activiti:formProperty id="outcomes" expression="access,people and pets,internal and external,external environment" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="case-manager"
                activiti:formKey="flow:/decideFinal"/>

        <userTask id="start" name="start" activiti:candidateGroups="case-manager"
                activiti:formKey="flow:/start"/>

        <userTask id="scheduleReviews" name="scheduleReviews" activiti:candidateGroups="case-manager" activiti:formKey="flow:/scheduleReviews">
            <extensionElements>
                <activiti:formProperty id="reviewSchedule" expression="6m" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsReduction" name="needsReduction" activiti:candidateGroups="case-manager"
            activiti:formKey="generic:/needsReduction" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="showActionGroups" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="relevant" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="relevant" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="showRiskManagementRequired,type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="19" writable="false"/>
                <activiti:formProperty id="outcomes" expression="stay healthy,stay safe,econ wellbeing,pos contrib,early help" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsAssessmentReductionReview" name="needsAssessmentReductionReview" activiti:candidateGroups="case-manager"
            activiti:formKey="generic:/needsAssessmentReductionReview" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessmentReductionReview" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction,review" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="changesByOutcome" expression="y" writable="false"/>
                <activiti:formProperty id="showActionGroups" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="byOutcome" writable="false"/>
                <activiti:formProperty id="showWorkComponents" expression="review" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementRequired" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="54" writable="false"/>
                <activiti:formProperty id="outcomes" expression="stay healthy,stay safe,econ wellbeing,pos contrib,early help" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="case-manager" activiti:formKey="flow:/close"/>

        <userTask id="exitSurvey" name="exitSurvey" activiti:candidateGroups="case-manager"
            activiti:formKey="generic:/feedbackQuestionnaire">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="exit survey" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="86" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="86" writable="false"/>
                <activiti:formProperty id="questions" expression="exit survey" writable="false"/>
             </extensionElements>
        </userTask>

        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_setTasksToShowRestricted" sourceRef="theStart"                 targetRef="setTasksToShowRestricted"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="setTasksToShowRestricted" targetRef="loadReferral"/>
        <sequenceFlow id="to_fork1"                     sourceRef="loadReferral"             targetRef="fork1"/>

        <parallelGateway id="fork1"/>
            <sequenceFlow id="to_projectRegion"   sourceRef="fork1" targetRef="projectRegion"/>
            <sequenceFlow id="from_projectRegion" sourceRef="projectRegion" targetRef="join1"/>

            <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
            <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

            <sequenceFlow id="to_referralDetails"   sourceRef="fork1" targetRef="referralDetails"/>
            <sequenceFlow id="from_referralDetails" sourceRef="referralDetails" targetRef="join1"/>

            <sequenceFlow id="to_agreemeent" sourceRef="fork1" targetRef="agreement"/>
            <sequenceFlow id="from_agreement" sourceRef="agreement" targetRef="join1"/>

            <sequenceFlow id="to_initialQ"   sourceRef="fork1"     targetRef="initialQ"/>
            <sequenceFlow id="from_initialQ" sourceRef="initialQ" targetRef="join1"/>

            <sequenceFlow id="to_emergencyDetails"   sourceRef="fork1"     targetRef="emergencyDetails"/>
            <sequenceFlow id="from_emergencyDetails" sourceRef="emergencyDetails" targetRef="join1"/>

            <sequenceFlow id="to_tenancyQ"   sourceRef="fork1"     targetRef="tenancyQ"/>
            <sequenceFlow id="from_tenancyQ" sourceRef="tenancyQ" targetRef="join1"/>

            <sequenceFlow id="to_decideFinal"       sourceRef="fork1" targetRef="decideFinal"/>
            <sequenceFlow id="from_decideFinal"     sourceRef="decideFinal" targetRef="fork2"/>

            <parallelGateway id="fork2"/>

                <sequenceFlow id="to_assessmentDate"       sourceRef="fork2" targetRef="assessmentDate"/>
                <sequenceFlow id="from_assessmentDate"     sourceRef="assessmentDate" targetRef="join2"/>

                <sequenceFlow id="to_needsAssessment"    sourceRef="fork2" targetRef="needsAssessment"/>
                <sequenceFlow id="from_needsAssessment"  sourceRef="needsAssessment" targetRef="join2"/>

                <sequenceFlow id="to_riskManagement"    sourceRef="fork2" targetRef="riskManagement"/>
                <sequenceFlow id="from_riskManagement"  sourceRef="riskManagement" targetRef="join2"/>

                <sequenceFlow id="to_start"       sourceRef="fork2" targetRef="start"/>
                <sequenceFlow id="from_start"     sourceRef="start" targetRef="fork3"/>

                <parallelGateway id="fork3"/>

                    <sequenceFlow id="to_scheduleReviews"    sourceRef="fork3" targetRef="scheduleReviews"/>
                    <sequenceFlow id="from_scheduleReviews"  sourceRef="scheduleReviews" targetRef="join3"/>

                    <sequenceFlow id="to_needsReduction"     sourceRef="fork3" targetRef="needsReduction"/>
                    <sequenceFlow id="from_needsReduction"   sourceRef="needsReduction" targetRef="join3"/>

                    <sequenceFlow id="to_needsAssessmentReductionReview" sourceRef="fork3" targetRef="needsAssessmentReductionReview"/>
                    <sequenceFlow id="from_needsAssessmentReductionReview" sourceRef="needsAssessmentReductionReview" targetRef="join3"/>

                    <sequenceFlow id="to_closeReferral" sourceRef="fork3" targetRef="closeReferral"/>
                    <sequenceFlow id="from_closeReferral" sourceRef="closeReferral" targetRef="join3"/>

                    <sequenceFlow id="to_exitSurvey" sourceRef="fork3" targetRef="exitSurvey"/>
                    <sequenceFlow id="from_exitSurvey" sourceRef="exitSurvey" targetRef="join3"/>

                <parallelGateway id="join3"/>

            <parallelGateway id="join2"/>

        <parallelGateway id="join1"/>

        <sequenceFlow id="to_theEnd" sourceRef="closeReferral" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
