<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
    <process id="scnhs.care" isExecutable="true">

        <serviceTask id="loadReferral" name="load referral"
            activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
            activiti:resultVariable="referral"/>

        <!-- ** IMPORTANT: Update this expression with the ordered list when you add tasks to the workflow ** -->
        <serviceTask id="setTasksToShowClientView" name="set tasksToShowClientView"
            activiti:resultVariable="tasksToShowClientView"
            activiti:expression="supportStaffNotes"/>

        <serviceTask id="setTasksToShowRestricted" name="set tasksToShowRestricted"
            activiti:resultVariable="tasksToShowRestricted"
            activiti:expression=
            "sourceWithIndividual,referralDetails,referralAccepted,pendingStatus,assessmentDate,supportStaffNotes,initial process (admins),initial process (duty),decideFinal,decision followup,placement (case manager),package (case manager),placement (admin),deliveredBy,start,funding,close"/>


        <userTask id="referralSource" name="from" activiti:candidateGroups="admin" activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <documentation>32</documentation>
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="nhs" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="deliveredBy" name="deliveredBy" activiti:candidateGroups="admin"
                activiti:formKey="flow:/deliveredBy"/>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin" activiti:formKey="flow:/pendingStatus"/>
        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin" activiti:formKey="flow:/assessmentDate"/>

        <userTask id="caseNotes" name="supportStaffNotes" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="case notes" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showActions" expression="none" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="none" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showOverviewComponents" expression="desc" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <!-- sourcePageGroup is an integer id that evidence is saved against, such that the history of this work
                     can be shown, thus allowing multiple work streams (e.g. multiple needs assessments/threat
                     assessment etc to have their own evidence trail -->
                <activiti:formProperty id="sourcePage" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="initialProcess-admin" name="initial process (admins)" activiti:candidateGroups="admin"
                  activiti:formKey="generic:/initialProcess" activiti:dueDate="${now.plusDays(14).toDate()}">
            <documentation>80</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="create,supporting docs,duty handover" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="2" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="initialProcess-duty" name="initial process (duty)" activiti:candidateGroups="duty" activiti:formKey="generic:/initialProcess-duty" activiti:dueDate="${now.plusDays(14).toDate()}">
            <documentation>81</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="update,ready,team panel,panel" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="3" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin" activiti:formKey="flow:/decideFinal"/>
        <userTask id="decisionFollowup" name="decision followup" activiti:candidateGroups="admin" activiti:formKey="generic:/decisionfollowup">
            <documentation>82</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="accepted,rejected,deferred" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="4" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="placement-case-manager" name="placement (case manager)" activiti:candidateGroups="case-manager" activiti:formKey="generic:/placement">
            <documentation>83</documentation><!-- <- referral aspect Id, just for information it seems -->
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="quotes" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="5" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="package" name="package (case manager)" activiti:candidateGroups="case-manager" activiti:formKey="generic:/generalQuestionnaire">
            <documentation>79</documentation>
            <extensionElements>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="placement-admin" name="placement (admin)" activiti:candidateGroups="admin" activiti:formKey="generic:/placement-admin">
            <documentation>84</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="placement (admin)" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="6" writable="false"/>
            </extensionElements>
        </userTask>
        <userTask id="startService" name="start" activiti:candidateGroups="admin" activiti:formKey="flow:/start"/>
        <userTask id="funding" name="funding" activiti:candidateGroups="admin" activiti:formKey="flow:/funding"/>

        <userTask id="scheduleReviews" name="scheduleReviews" activiti:candidateGroups="admin" activiti:formKey="flow:/scheduleReviews">
            <extensionElements>
                <activiti:formProperty id="reviewSchedule" expression="3m" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="allocations" activiti:formKey="flow:/close"/>


        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_setTasksToShowClientView" sourceRef="theStart"                 targetRef="setTasksToShowClientView"/>
        <sequenceFlow id="to_setTasksToShowRestricted" sourceRef="setTasksToShowClientView" targetRef="setTasksToShowRestricted"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="setTasksToShowRestricted" targetRef="loadReferral"/>
        <sequenceFlow id="from_loadReferral"           sourceRef="loadReferral"             targetRef="fork1"/>

        <parallelGateway id="fork1"/>
        <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
        <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

        <sequenceFlow id="to_caseNotes"       sourceRef="fork1"     targetRef="caseNotes"/>
        <sequenceFlow id="from_caseNotes"     sourceRef="caseNotes" targetRef="join1"/>

        <sequenceFlow id="to_referralDetails"      sourceRef="fork1" targetRef="referralDetails"/>
        <sequenceFlow id="to_referralAccepted"     sourceRef="referralDetails" targetRef="referralAccepted"/>
        <sequenceFlow id="to_pendingStatus"        sourceRef="referralAccepted" targetRef="pendingStatus"/>
        <sequenceFlow id="to_assessmentDate"       sourceRef="pendingStatus" targetRef="assessmentDate"/>
        <sequenceFlow id="to_initialProcess-admin" sourceRef="assessmentDate" targetRef="initialProcess-admin"/>
        <sequenceFlow id="to_initialProcess-duty"  sourceRef="initialProcess-admin" targetRef="initialProcess-duty"/>
        <sequenceFlow id="to_decideFinal"   sourceRef="initialProcess-duty" targetRef="decideFinal"/>
        <sequenceFlow id="to_decisionFollowup"     sourceRef="decideFinal" targetRef="decisionFollowup"/>
        <sequenceFlow id="to_checkAccepted"        sourceRef="decisionFollowup" targetRef="checkAccepted"/>

        <exclusiveGateway id="checkAccepted" name="accepted on service?"/>
        <sequenceFlow id="checkAccepted_no" name="no" sourceRef="checkAccepted" targetRef="closeReferral">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${!referral.acceptedOnService}]]></conditionExpression>
        </sequenceFlow>

        <!-- ACCEPTED -->
        <sequenceFlow id="checkAccepted_yes" name="yes" sourceRef="checkAccepted" targetRef="placement-case-manager">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${referral.acceptedOnService}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="to_package"         sourceRef="placement-case-manager" targetRef="package"/>
        <sequenceFlow id="to_placement-admin" sourceRef="package" targetRef="placement-admin"/>
        <sequenceFlow id="to_deliveredBy"     sourceRef="placement-admin" targetRef="deliveredBy"/>
        <sequenceFlow id="to_startService"    sourceRef="deliveredBy" targetRef="startService"/>
        <sequenceFlow id="to_funding"         sourceRef="startService" targetRef="funding"/>
        <sequenceFlow id="to_scheduleReviews" sourceRef="funding" targetRef="scheduleReviews"/>
        <sequenceFlow id="to_closeReferral"   sourceRef="scheduleReviews" targetRef="closeReferral"/>
        <sequenceFlow id="from_closeReferral" sourceRef="closeReferral" targetRef="join1"/>

        <parallelGateway id="join1"/>

        <sequenceFlow id="to_theEnd" sourceRef="join1" targetRef="theEnd"/>
        <endEvent id="theEnd" name="End Event"/>
    </process>

  <bpmndi:BPMNDiagram id="BPMNDiagram_scnhs.care">
    <bpmndi:BPMNPlane bpmnElement="scnhs.care" id="BPMNPlane_scnhs.care">
      <bpmndi:BPMNShape bpmnElement="loadReferral" id="BPMNShape_loadReferral">
        <omgdc:Bounds height="80.0" width="100.0" x="1050.0" y="69.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="setTasksToShowClientView" id="BPMNShape_setTasksToShowClientView">
        <omgdc:Bounds height="80.0" width="100.0" x="673.0" y="69.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="setTasksToShowRestricted" id="BPMNShape_setTasksToShowRestricted">
        <omgdc:Bounds height="80.0" width="100.0" x="839.0" y="69.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="referralSource" id="BPMNShape_referralSource">
        <omgdc:Bounds height="80.0" width="100.0" x="306.5" y="427.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="referralDetails" id="BPMNShape_referralDetails">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="405.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="referralAccepted" id="BPMNShape_referralAccepted">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="569.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="deliveredBy" id="BPMNShape_deliveredBy">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2200.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="pendingStatus" id="BPMNShape_pendingStatus">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="700.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="assessmentDate" id="BPMNShape_assessmentDate">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="850.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="caseNotes" id="BPMNShape_caseNotes">
        <omgdc:Bounds height="80.0" width="100.0" x="529.0" y="422.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initialProcess-admin" id="BPMNShape_initialProcess-admin">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="990.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initialProcess-duty" id="BPMNShape_initialProcess-duty">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="1150.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="decideFinal" id="BPMNShape_decideFinal">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="1300.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="decisionFollowup" id="BPMNShape_decisionFollowup">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="1450.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="placement-case-manager" id="BPMNShape_placement-case-manager">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="1750.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="package" id="BPMNShape_package">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="1900.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="placement-admin" id="BPMNShape_placement-admin">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2050.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startService" id="BPMNShape_startService">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2340.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="funding" id="BPMNShape_funding">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2500.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="scheduleReviews" id="BPMNShape_scheduleReviews">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2650.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="closeReferral" id="BPMNShape_closeReferral">
        <omgdc:Bounds height="80.0" width="100.0" x="751.5" y="2800.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="theStart" id="BPMNShape_theStart">
        <omgdc:Bounds height="30.0" width="30.0" x="302.0" y="94.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="fork1" id="BPMNShape_fork1">
        <omgdc:Bounds height="40.0" width="40.0" x="559.0" y="251.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="checkAccepted" id="BPMNShape_checkAccepted">
        <omgdc:Bounds height="40.0" width="40.0" x="990.0" y="1605.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="join1" id="BPMNShape_join1">
        <omgdc:Bounds height="40.0" width="40.0" x="404.5" y="698.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="theEnd" id="BPMNShape_theEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="285.0" y="916.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="from_loadReferral" id="BPMNEdge_from_loadReferral">
        <omgdi:waypoint x="1100.0" y="149.0"></omgdi:waypoint>
        <omgdi:waypoint x="1100.0" y="271.0"></omgdi:waypoint>
        <omgdi:waypoint x="599.0" y="271.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_decisionFollowup" id="BPMNEdge_to_decisionFollowup">
        <omgdi:waypoint x="801.5" y="1380.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1450.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_loadReferral" id="BPMNEdge_to_loadReferral">
        <omgdi:waypoint x="939.0" y="109.0"></omgdi:waypoint>
        <omgdi:waypoint x="1050.0" y="109.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_placement-admin" id="BPMNEdge_to_placement-admin">
        <omgdi:waypoint x="801.5" y="1980.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2050.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_package" id="BPMNEdge_to_package">
        <omgdi:waypoint x="801.5" y="1830.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1900.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_scheduleReviews" id="BPMNEdge_to_scheduleReviews">
        <omgdi:waypoint x="801.5" y="2580.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2650.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_initialProcess-duty" id="BPMNEdge_to_initialProcess-duty">
        <omgdi:waypoint x="801.5" y="1070.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1150.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_decideFinal" id="BPMNEdge_to_decideFinal">
        <omgdi:waypoint x="801.5" y="1230.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_caseNotes" id="BPMNEdge_to_caseNotes">
        <omgdi:waypoint x="579.0" y="291.0"></omgdi:waypoint>
        <omgdi:waypoint x="579.0" y="422.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_initialProcess-admin" id="BPMNEdge_to_initialProcess-admin">
        <omgdi:waypoint x="801.5" y="930.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="990.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_referralSource" id="BPMNEdge_to_referralSource">
        <omgdi:waypoint x="559.5" y="271.5"></omgdi:waypoint>
        <omgdi:waypoint x="356.5" y="271.5"></omgdi:waypoint>
        <omgdi:waypoint x="356.5" y="427.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="from_caseNotes" id="BPMNEdge_from_caseNotes">
        <omgdi:waypoint x="529.0" y="462.0"></omgdi:waypoint>
        <omgdi:waypoint x="424.0" y="462.0"></omgdi:waypoint>
        <omgdi:waypoint x="424.46101364522417" y="698.0389863547758"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_referralAccepted" id="BPMNEdge_to_referralAccepted">
        <omgdi:waypoint x="801.5" y="485.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="569.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_theEnd" id="BPMNEdge_to_theEnd">
        <omgdi:waypoint x="404.5" y="718.0"></omgdi:waypoint>
        <omgdi:waypoint x="299.0" y="718.0"></omgdi:waypoint>
        <omgdi:waypoint x="299.0" y="916.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="checkAccepted_no" id="BPMNEdge_checkAccepted_no">
        <omgdi:waypoint x="1010.5" y="1644.5"></omgdi:waypoint>
        <omgdi:waypoint x="1010.5" y="2840.0"></omgdi:waypoint>
        <omgdi:waypoint x="851.5" y="2840.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_checkAccepted" id="BPMNEdge_to_checkAccepted">
        <omgdi:waypoint x="851.5" y="1490.0"></omgdi:waypoint>
        <omgdi:waypoint x="1010.0" y="1490.0"></omgdi:waypoint>
        <omgdi:waypoint x="1010.0" y="1605.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_startService" id="BPMNEdge_to_startService">
        <omgdi:waypoint x="801.5" y="2280.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2340.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_funding" id="BPMNEdge_to_funding">
        <omgdi:waypoint x="801.5" y="2420.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2500.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_setTasksToShowRestricted" id="BPMNEdge_to_setTasksToShowRestricted">
        <omgdi:waypoint x="773.0" y="109.0"></omgdi:waypoint>
        <omgdi:waypoint x="839.0" y="109.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_closeReferral" id="BPMNEdge_to_closeReferral">
        <omgdi:waypoint x="801.5" y="2730.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2800.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="from_closeReferral" id="BPMNEdge_from_closeReferral">
        <omgdi:waypoint x="751.5" y="2840.0"></omgdi:waypoint>
        <omgdi:waypoint x="440.0" y="2840.0"></omgdi:waypoint>
        <omgdi:waypoint x="424.6450292397661" y="737.8549707602339"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_setTasksToShowClientView" id="BPMNEdge_to_setTasksToShowClientView">
        <omgdi:waypoint x="550.0" y="109.0"></omgdi:waypoint>
        <omgdi:waypoint x="673.0" y="109.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_assessmentDate" id="BPMNEdge_to_assessmentDate">
        <omgdi:waypoint x="801.5" y="780.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="850.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_pendingStatus" id="BPMNEdge_to_pendingStatus">
        <omgdi:waypoint x="801.5" y="649.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="700.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_deliveredBy" id="BPMNEdge_to_deliveredBy">
        <omgdi:waypoint x="801.5" y="2130.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="2200.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="from_referralSource" id="BPMNEdge_from_referralSource">
        <omgdi:waypoint x="406.5" y="467.0"></omgdi:waypoint>
        <omgdi:waypoint x="424.5" y="467.0"></omgdi:waypoint>
        <omgdi:waypoint x="424.5" y="698.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="checkAccepted_yes" id="BPMNEdge_checkAccepted_yes">
        <omgdi:waypoint x="990.5" y="1625.5"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1625.5"></omgdi:waypoint>
        <omgdi:waypoint x="801.5" y="1750.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="to_referralDetails" id="BPMNEdge_to_referralDetails">
        <omgdi:waypoint x="595.028880866426" y="274.971119133574"></omgdi:waypoint>
        <omgdi:waypoint x="801.0" y="326.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.3319327731092" y="405.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
