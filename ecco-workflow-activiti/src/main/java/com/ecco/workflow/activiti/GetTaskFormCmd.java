package com.ecco.workflow.activiti;

import org.activiti.bpmn.model.FormProperty;
import org.activiti.engine.ActivitiIllegalArgumentException;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.form.TaskFormData;
import org.activiti.engine.impl.TaskQueryImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.form.DefaultTaskFormHandler;
import org.activiti.engine.impl.form.FormDataImpl;
import org.activiti.engine.impl.form.FormHandler;
import org.activiti.engine.impl.form.TaskFormDataImpl;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.DeploymentEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.task.TaskDefinition;
import org.activiti.engine.task.Task;
import org.springframework.util.ReflectionUtils;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;


/**
 * Command for retrieving start or task form data from the task definition. Work for active and historic tasks since
 * the execution is specified directly.
 * Based on {@link org.activiti.engine.impl.cmd.GetFormKeyCmd} and {@link org.activiti.engine.impl.cmd.GetTaskFormCmd}.
 *
 * <AUTHOR> Boden
 */
public class GetTaskFormCmd implements Command<TaskFormData>, Serializable {
    private static final long serialVersionUID = 1L;
    protected String taskDefinitionKey;
    protected String processInstanceId;

    /**
     * Retrieves task form data.
     */
    public GetTaskFormCmd(String processInstanceId, String taskDefinitionKey) {
        if (processInstanceId == null || processInstanceId.length() < 1) {
            throw new ActivitiIllegalArgumentException("The process instance id is mandatory, but '" + processInstanceId + "' has been provided.");
        }
        if (taskDefinitionKey == null || taskDefinitionKey.length() < 1) {
            throw new ActivitiIllegalArgumentException("The task definition key is mandatory, but '" + taskDefinitionKey + "' has been provided.");
        }
        this.taskDefinitionKey = taskDefinitionKey;
        this.processInstanceId = processInstanceId;
    }

    @Override
    public TaskFormData execute(CommandContext commandContext) {
        ExecutionEntity execution;

        // Try to find the active task
        final TaskQueryImpl taskQuery = new TaskQueryImpl(commandContext);
        taskQuery.processInstanceId(processInstanceId).taskDefinitionKey(taskDefinitionKey);
        final List<Task> tasks = commandContext.getTaskEntityManager().findTasksByQueryCriteria(taskQuery);
        if (!tasks.isEmpty()) {
            // Use the actual execution context off the active task
            execution = commandContext.getExecutionEntityManager().findExecutionById((tasks.get(0)).getExecutionId());
        } else {
            // Use the process instance itself as the execution context
            execution = commandContext.getExecutionEntityManager().findExecutionById(processInstanceId);
        }
        ProcessDefinitionEntity processDefinition;
        if (execution == null) {
            // The process instance is no longer active, but never mind, we can still manage after a fashion (no context variables will be available for expressions though).
            final HistoricProcessInstanceEntity historicProcessInstance = commandContext.getHistoricProcessInstanceEntityManager().findHistoricProcessInstance(processInstanceId);
            processDefinition = Context
                    .getProcessEngineConfiguration()
                    .getDeploymentManager()
                    .findDeployedProcessDefinitionById(historicProcessInstance.getProcessDefinitionId());
            execution = new ExecutionEntity(); // Dummy execution otherwise we can't retrieve expressions.
        } else {
            processDefinition = (ProcessDefinitionEntity) execution.getProcessDefinition();
        }

        TaskDefinition taskDefinition = processDefinition.getTaskDefinitions().get(taskDefinitionKey);
        // TODO: Maybe add getFormKey() and getDeploymentId() to TaskFormHandler interface to avoid the following cast
        DefaultTaskFormHandler formHandler = (DefaultTaskFormHandler) taskDefinition.getTaskFormHandler();

        return new ExecutionFormHandler(formHandler).createFormData(execution);
    }

    /** Like {@link org.activiti.engine.impl.form.DefaultTaskFormHandler} but uses the execution directly rather than relying on an active task. */
    @SuppressWarnings("serial")
    static class ExecutionFormHandler implements FormHandler {
        private final DefaultTaskFormHandler delegate;
        private final Method initializeFormPropertiesDelegate;

        ExecutionFormHandler(DefaultTaskFormHandler delegate) {
            this.delegate = delegate;
            this.initializeFormPropertiesDelegate = ReflectionUtils.findMethod(delegate.getClass(), "initializeFormProperties", FormDataImpl.class, ExecutionEntity.class);
            ReflectionUtils.makeAccessible(initializeFormPropertiesDelegate);
        }

        public TaskFormData createFormData(final ExecutionEntity execution) {
            TaskFormDataImpl taskFormData = new TaskFormDataImpl();
            if (getFormKey() != null) {
              Object formValue = getFormKey().getValue(execution);
              if (formValue != null) {
                taskFormData.setFormKey(formValue.toString());
              }
            }
            taskFormData.setDeploymentId(delegate.getDeploymentId());
            ReflectionUtils.invokeMethod(initializeFormPropertiesDelegate, delegate, taskFormData, execution);
            return taskFormData;
        }

        public Expression getFormKey() {
            return delegate.getFormKey();
        }

        @Override
        public void parseConfiguration(List<FormProperty> formProperties, String formKey, DeploymentEntity deployment, ProcessDefinitionEntity processDefinition) {
            delegate.parseConfiguration(formProperties, formKey, deployment, processDefinition);
        }

        @Override
        public void submitFormProperties(Map<String, String> properties, ExecutionEntity execution) {
            delegate.submitFormProperties(properties, execution);
        }
    }
}
