package com.ecco.workflow.activiti;

import com.ecco.workflow.WorkflowLinkedResource;

import java.text.MessageFormat;
import java.text.ParsePosition;

/**
 * Activiti's attachment's description field is used for properties of the link which do not have explicit fields.
 * Currently this is used to store the 'rel'.
 *
 * @since 07/01/2014
 */
public class AttachmentDescriptionFormatter {
    private final MessageFormat format = new MessageFormat("rel={0}");

    public String formatDescription(WorkflowLinkedResource linkedResource) {
        return format.format(new Object[]{linkedResource.getRel()});
    }

    public WorkflowLinkedResource.Builder parseDescription(String description, final WorkflowLinkedResource.Builder builder) {
        final ParsePosition parsePosition = new ParsePosition(0);
        final Object[] objects = format.parse(description, parsePosition);
        if (objects == null) {
            throw new IllegalArgumentException("Description (" + description + ") did not match format pattern (" + format.toPattern() + ") failed at position: " + parsePosition.getErrorIndex());
        }
        if (objects.length < 1) {
            throw new IllegalStateException("Description (" + description + ") parsed but did not yield sufficient fields: " + objects.length + "; pattern was: " + format.toPattern() + ")");
        }
        return builder.rel((String) objects[0]);
    }
}
