# We don't want the files that get generated here (perhaps use outfile?)
.gradle/
# .node is where frontend Gradle plugin puts node install. TODO: Use a Github image with the one we need
.node/
.yarn/
.frontend-gradle-plugin/
/dependency-check/

# We shouldn't be generating anything here, but for now I'm going to let it generate an empty JAR rather than get side-tracked
/build/

/ecco-ui/build
/ecco-ui/*/build-tsc
/ecco-ui/*/debug/
/ecco-ui/*/dist/
/ecco-ui/*/cypress/videos/*.mp4
/ecco-ui/*/cypress/screenshots/
**/staticFiles/build
*~
rebel.xml
/target/
*/target/
*/generated/
*/build/
/out/
*/out/
*/bin/
*.log
/*/META-INF/
/*/*/META-INF/
/*/src/main/java/META-INF/
.DS_Store
/docker/mysql-db

# have to be careful to add files that are new .js files
**/scripts/jquery*.js
**/scripts/lib/**/*.js
**/scripts/jquery*/**/*.js
**/scripts/polyfill/**/*.js
**/scripts/__tests__/**/*.js
**/scripts/coverage/
*.local

setup/acctest.sql

# non-compiled JavaScript files
!Gulpfile.js
!webpack.config.js
!jest.config.js

.eslintcache

WIP.txt


ecco/logs
ecco-importing/src/main/resources/data/*
ecco*.db
.factorypath
*.ipr
*.iml
*.iws
.idea/*
!/.idea/runConfigurations/
.springBeans
/logs
/atlassian-ide-plugin.xml
/typescript-build.files
node_modules/
# Avoid committing tsBuildInfo (as it's only relevant to dev builds)
tsBuildInfo
tsconfig.tsbuildinfo

# Visual Studio user settings and build output
*.suo
*.user
bin
obj
/ecco-offline/src/main/resources/com/ecco/offline/staticFiles/.trigger-package
application-local.yml

# Temporary files created by Kotlin compiler
.kotlin